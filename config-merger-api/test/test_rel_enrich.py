import json
from unittest.mock import Mock, patch, mock_open
import pytest
from typing import Dict, Any

from config_merger_api.utils.entity_rel_enrich_utils import (
    merge_count_configs,
    merge_entity_rel_enrich
)

class TestRelEnrich:
#     # Successfully extracts lineage when valid data_source_name and data_feed_name are provided
# Merges configurations correctly when keys match in both configs
    def test_merge_with_matching_keys(self):
        solution_config = {
            'key1': [{'id': 1, 'value': 10}],
            'key2': [{'id': 2, 'value': 20}]
        }
        client_config = {
            'key1': [{'id': 1, 'value': 15}],
            'key2': [{'id': 2, 'value': 25}]
        }
        key_dict = {
            'key1': ['id'],
            'key2': ['id']
        }
        expected_output = {
            'key1': [{'id': 1, 'value': 15}],
            'key2': [{'id': 2, 'value': 25}]
        }
        result = merge_count_configs(solution_config, client_config, key_dict)
        assert result == expected_output

        # Merges solution and client configurations correctly when both have 'entity_rel_enrich' items
    def test_merge_with_entity_rel_enrich_items(self):
        solution_configs = [
            {
                "config_item_type": "entity_rel_enrich",
                "config_value": {
                    "countEnriches": [
                        {"sourceFieldName": "field1", "targetFieldName": "field2", "extra": "value1"}
                    ]
                }
            }
        ]
        client_configs = [
            {
                "config_item_type": "entity_rel_enrich",
                "config_value": {
                    "countEnriches": [
                        {"sourceFieldName": "field1", "targetFieldName": "field2", "extra": "value2"}
                    ]
                }
            }
        ]
        expected_result = {
            "countEnriches": [
                {"sourceFieldName": "field1", "targetFieldName": "field2", "extra": "value2"}
            ]
        }
        result = merge_entity_rel_enrich(solution_configs, client_configs)
        assert result == expected_result