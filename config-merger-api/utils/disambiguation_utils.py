import copy
import re
from config_merger_api.utils.common_utils import merge_config
from config_merger_api.utils.inventory_models_utils import file_to_spec_mapper, filter_by_computation_phase


# Helper function to recursively replace confidence matrix in a dictionary
def replace_confidence_matrix_recursively(obj: dict, remove_str: str):
    key = "confidenceMatrix"
    if key in obj:
        if remove_str in obj[key]:
            obj[key].remove(remove_str)

    if isinstance(obj, dict):
        for key, value in obj.items():
            if isinstance(value, dict):
                item = replace_confidence_matrix_recursively(value, remove_str)
                if item is not None:
                    obj[key] = item
            if isinstance(value, list) and value:
                if isinstance(value[0], dict):
                    value = [replace_confidence_matrix_recursively(v, remove_str) for v in value]
                    obj[key] = value
    return obj


# Filters out disambiguation folders from solution configs based on client configs
def filter_disambiguation_folder(solution_configs, client_configs):
    sol_configs = []
    for solution_config in solution_configs:
        for client_config in client_configs:
            for inventory_model in solution_config.get("inventoryModelInput", []):
                path = inventory_model.get("path")
                if path and any(client_inventory_model.get("path", "") == path for client_inventory_model in
                                client_config.get("inventoryModelInput", [])):
                    solution_config["inventoryModelInput"] = [model for model in solution_config["inventoryModelInput"]
                                                              if model.get("path") != path]
                    name = inventory_model.get("name")
                    solution_config = replace_confidence_matrix_recursively(solution_config, name)
        sol_configs.append(solution_config)
    return sol_configs


# Extracts and combines strategy values from the provided data
def extract_and_combine_strategy_values(data):
    strategy = data.get("disambiguation", {}).get("strategy", {})
    fields = {
        "fieldLevelConfidenceMatrix": [item['field'] for item in strategy.get("fieldLevelConfidenceMatrix", [])],
        "valueConfidence": [item['field'] for item in strategy.get("valueConfidence", [])],
        "aggregation": [item['field'] for item in strategy.get("aggregation", [])],
        "rollingUpFields": strategy.get("rollingUpFields", [])
    }
    return list(set(fields["fieldLevelConfidenceMatrix"] + fields["valueConfidence"] + fields["aggregation"] + fields[
        "rollingUpFields"]))


# Process and merge strategy values for solution configuration
def process_and_write_to_solution_file(client_config, solution_config):
    client_strategy_values = extract_and_combine_strategy_values(client_config)
    solution_strategy_values = extract_and_combine_strategy_values(solution_config)

    common_fields = set(client_strategy_values).intersection(solution_strategy_values)
    for common_field in common_fields:
        strategies = solution_config.get("disambiguation", {}).get("strategy", {})
        for key, value in strategies.items():
            if isinstance(value, list) and key in ["fieldLevelConfidenceMatrix", "aggregation", "valueConfidence"]:
                strategies[key] = [item for item in value if item.get('field') != common_field]

    solution_rolling_up_fields = solution_config.get("disambiguation", {}).get("strategy", {}).get("rollingUpFields",
                                                                                                   [])
    solution_config["disambiguation"].setdefault("strategy", {}).setdefault("rollingUpFields", []).extend(
        [field for field in solution_rolling_up_fields if field not in common_fields])

    solution_config["disambiguation"]["strategy"]["rollingUpFields"] = list(
        set(solution_config["disambiguation"]["strategy"]["rollingUpFields"]))
    return [solution_config]


# Extract candidate keys from disambiguation model
def inter_candidate_keys_mapper(intersource_disambiguated_model):
    candidate_keys_list = [
        item['name'] if isinstance(item, dict) else item
        for item in intersource_disambiguated_model["disambiguation"].get("candidateKeys", [])
        if not (isinstance(item, dict) and item.get("disable_key", False))
    ]
    return list(set(candidate_keys_list))


# Extract rollup fields and candidate keys from intra-source disambiguated models
def intra_rollup_fields_mapper(intrasource_disambiguated_models):
    candidate_keys_list = []
    rollup_fields_list = []
    for config in intrasource_disambiguated_models:
        candidate_keys_list.extend([
            item['name'] if isinstance(item, dict) else item
            for item in config["disambiguation"]["candidateKeys"]
            if not (isinstance(item, dict) and item.get("disable_key", False))
        ])
        rollup_fields_list.extend([item for item in
                                   config.get("disambiguation", {}).get("strategy", {}).get(
                                       "rollingUpFields", [])])
    return list(set(rollup_fields_list)), list(set(candidate_keys_list))


def merge_candidate_keys(solution_configs, client_configs, config_item_type):
    extract_keys = lambda configs: next((c.get('config_value', {}).get('disambiguation', {}).get('candidateKeys', [])
                                         for c in configs if c.get('config_item_type') == config_item_type), [])

    extract_disambiguation_type = lambda configs: next((c.get('config_value', {}).get('disambiguation', {}).get('disambiguationType')
                                                        for c in configs if c.get('config_item_type') == config_item_type), None)

    solution_keys, client_keys = extract_keys(solution_configs), extract_keys(client_configs)
    solution_disambiguation_type = extract_disambiguation_type(solution_configs)
    client_disambiguation_type = extract_disambiguation_type(client_configs)

    # Raise exception if solution has candidateKeys and client has disambiguationType as "Union"
    if solution_keys and client_disambiguation_type == "Union":
        raise Exception("Solution has disambiguation for this config, so cannot add Union in client configs")

    # Remove disambiguationType from solution if it has disambiguationType as "Union" and client has candidateKeys
    if solution_disambiguation_type == "Union" and client_keys:
        for config in solution_configs:
            if config.get('config_item_type') == config_item_type:
                config['config_value']['disambiguation'].pop('disambiguationType', None)
                break

    get_key_name = lambda k: k['name'] if isinstance(k, dict) and 'name' in k else k
    is_complex = lambda k: isinstance(k, dict) and 'name' in k
    merge_filters = lambda f1, f2: f"({f1}) OR ({f2})" if f1 and f2 and f1 != f2 else (f1 or f2)

    merged_keys_map = {get_key_name(k): copy.deepcopy(k) for k in solution_keys}

    for key in client_keys:
        key_name = get_key_name(key)
        if key_name in merged_keys_map and is_complex(merged_keys_map[key_name]) and is_complex(key):
            merged_key = merged_keys_map[key_name]
            client_filter = key.get('exceptionFilter')
            solution_filter = merged_key.get('exceptionFilter')
            merged_key.update({k: v for k, v in key.items() if k != 'exceptionFilter' and v is not None})

            if 'exceptionFilter' in merged_key or client_filter:
                merged_key['exceptionFilter'] = merge_filters(client_filter, solution_filter)
        elif key_name not in merged_keys_map or (is_complex(key) and not is_complex(merged_keys_map[key_name])):
            merged_keys_map[key_name] = copy.deepcopy(key)

    updated_client_configs = copy.deepcopy(client_configs)
    [c.setdefault('config_value', {}).setdefault('disambiguation', {}).update(
        {'candidateKeys': list(merged_keys_map.values())})
     for c in updated_client_configs if c.get('config_item_type') == config_item_type]
    return solution_configs,updated_client_configs


# Helper function to process fields for candidate keys and roll-up fields
def expression_change(candidate_keys_list, roll_up_fields, intra_source_configs, is_intra, properties):
    fields_dict = {item["colName"]: item.get("colExpr", item["colName"]) for item in properties}
    for field_name, field_expr in fields_dict.items():
        # for candidate_key in candidate_keys_list:
        #     field_expr = replace_candidate_key(field_expr, candidate_key)
        # for roll_up_field in roll_up_fields:
        #     field_expr = replace_roll_up_field(field_expr, roll_up_field)
        fields_dict[field_name] = field_expr
        if not is_intra:
            fields_dict[field_name] = apply_intra_fields(field_expr, intra_source_configs, roll_up_fields)
    return fields_dict


# Replace candidate key in field expression
def replace_candidate_key(field_expr, candidate_key):
    pattern = f"[^\w]{candidate_key}[^\w]|,\s*{candidate_key}\s*,|[,]*\s*{candidate_key}\s*[,]*"
    if re.search(pattern, field_expr) and f"{candidate_key}__resolved" not in field_expr:
        return re.sub(rf"\b{candidate_key}\b", f"{candidate_key}__resolved", field_expr)
    return field_expr


# Replace roll-up field in field expression
def replace_roll_up_field(field_expr, roll_up_field):
    if re.search(f"[^\w]{roll_up_field}[^\w]|,\s*{roll_up_field}\s*,", field_expr):
        return re.sub(rf"\b{roll_up_field}\b", f"{roll_up_field}[0]", field_expr)
    return field_expr


# Apply intra rollup and candidate key replacements
def apply_intra_fields(field_expr, intra_source_configs, roll_up_fields):
    intra_rollup_keys, intra_candidate_keys = intra_rollup_fields_mapper(intra_source_configs)
    # for field in intra_rollup_keys:
    #     if re.search(f"[^\w]{field}[^\w]|,\s*{field}\s*,", field_expr) and field not in roll_up_fields:
    #         field_expr = re.sub(rf"\b{field}\b", f"{field}[0]", field_expr)
    # for candidate_key in intra_candidate_keys:
    #     if re.search(f"[^\w]{candidate_key}[^\w]|,\s*{candidate_key}\s*,", field_expr) and candidate_key != "primary_key" and f"{candidate_key}__resolved" not in field_expr and candidate_key not in roll_up_fields:
    #         field_expr = re.sub(rf"\b{candidate_key}\b", f"{candidate_key}__resolved", field_expr)
    return field_expr


def update_entity_config_dis(inventory_data, default_spec, last_update_fields):
     # Ensure 'entity' exists, otherwise initialize it as an empty dictionary
    inventory_data.setdefault("entity", {})
    inventory_data["entity"].update({
        "fieldSpec": default_spec,
        "lastUpdateFields": last_update_fields
    })


# Merge disambiguation configurations based on source types
def disambiguation_config_merger(intra_source_configs, inter_source_configs, global_entity_config, is_intra=True):
    entity_config = file_to_spec_mapper(global_entity_config)
    # Check if the lists are not empty before accessing the first element
    source_config = intra_source_configs[0] if is_intra and intra_source_configs else inter_source_configs[
        0] if inter_source_configs else {}
    default_spec = entity_config.get("default_spec", {})
    last_update_fields = entity_config.get("last_update_fields", {})
    update_entity_config_dis(source_config, default_spec, last_update_fields)
    candidate_keys_list = [
        item['name'] if isinstance(item, dict) else item
        for item in source_config["disambiguation"].get("candidateKeys", [])
        if not (isinstance(item, dict) and item.get("disable_key", False))
    ]
    if is_intra:
        inter_candidate_keys = inter_candidate_keys_mapper(inter_source_configs[0])
        disambiguation_spec = source_config.get("disambiguation", {})
        disambiguation_spec.setdefault("strategy", {}).setdefault("rollingUpFields", []).extend(
            [key for key in inter_candidate_keys if key != "primary_key" and key not in candidate_keys_list]
        )
        source_config.setdefault("disambiguation", disambiguation_spec)

    common_props = entity_config.get("common_properties")
    entity_specific_props = entity_config.get("entity_specific_props", [])
    roll_up_fields = source_config.get("disambiguation", {}).get("strategy", {}).get("rollingUpFields", [])

    common_fields_dict = expression_change(candidate_keys_list, roll_up_fields, intra_source_configs, is_intra,
                                           common_props)
    entity_specific_fields_dict = expression_change(candidate_keys_list, roll_up_fields, intra_source_configs, is_intra,
                                                    entity_specific_props)

    if not is_intra:
        source_config["entity"]["commonProperties"] = [
            {"colName": key} if common_fields_dict[key] == key else {"colName": key, "colExpr": common_fields_dict[key]}
            for key in common_fields_dict
        ]
        source_config["entity"]["entitySpecificProperties"] = [
            {"colName": key} if entity_specific_fields_dict[key] == key else {"colName": key,
                                                                              "colExpr": entity_specific_fields_dict[
                                                                                  key]} for key in
            entity_specific_fields_dict
        ]

    return source_config


def extract_config_values(configs, config_item_type):
    return [i.get("config_value", {}) for i in configs if i.get('config_item_type') == config_item_type]


def add_entity(merged_inter_source_config):
    if isinstance(merged_inter_source_config, list):
        for item in merged_inter_source_config:
            if isinstance(item, dict):
                if 'entity' not in item:
                    item['entity'] = {}
    return merged_inter_source_config


def update_fragment_locations(updated_config):
    if updated_config.get("output", {}).get("isFragmentOLAPTable", True) and updated_config.get("output", {}).get(
            "fragmentLocation") is None:
        updated_config.setdefault("output", {})["fragmentLocation"] = updated_config["output"][
            "disambiguatedModelLocation"].replace("<%EI_SCHEMA_NAME%>.sds_ei",
                                                  "<%KG_FRAGMENT_SCHEMA%>.sds_ei__fragment")
    if updated_config.get("output", {}).get("resolverLocation") is None:
        updated_config.setdefault("output", {})["resolverLocation"] = updated_config["output"][
            "disambiguatedModelLocation"].replace("<%EI_SCHEMA_NAME%>.sds_ei",
                                                  "<%KG_FRAGMENT_SCHEMA%>.sds_ei__resolver")
    return updated_config


def merge_intra_source_disambiguated_models(solution_configs, client_configs):
    solution_updated_configs,client_updated_configs = merge_candidate_keys(solution_configs, client_configs, "intrasource_disambiguated_models")
    sol_intra_source_config = [i.get("config_value", {}) for i in solution_updated_configs if
                               i.get('config_item_type') == 'intrasource_disambiguated_models']
    client_intra_source_config = [i.get("config_value", {}) for i in client_updated_configs if
                                  i.get('config_item_type') == 'intrasource_disambiguated_models']
    sol_global_entity_config = [i.get("config_value", {}) for i in solution_updated_configs if
                                i.get('config_item_type') == 'global_entity_config']
    client_global_entity_config = [i.get("config_value", {}) for i in client_updated_configs if
                                   i.get('config_item_type') == 'global_entity_config']
    if sol_intra_source_config != [] and client_intra_source_config != []:
        sol_intra_source_config = process_and_write_to_solution_file(
            client_intra_source_config[0], sol_intra_source_config[0])
    sol_inter_source_config = [i.get("config_value", {}) for i in solution_updated_configs if
                               i.get('config_item_type') == 'intersource_disambiguated_models']
    client_inter_source_config = [i.get("config_value", {}) for i in client_updated_configs if
                                  i.get('config_item_type') == 'intersource_disambiguated_models']
    if sol_inter_source_config != [] and client_inter_source_config != []:
        sol_inter_source_config = process_and_write_to_solution_file(
            client_inter_source_config[0], sol_inter_source_config[0])
    merged_inter_source_config = merge_config(sol_inter_source_config,
                                              client_inter_source_config)
    merged_inter_source_config = add_entity(merged_inter_source_config)
    merged_intra_source_config = merge_config(sol_intra_source_config,
                                              client_intra_source_config)
    merged_intra_source_config = add_entity(merged_intra_source_config)
    for item in merged_intra_source_config:
        if "disambiguation" in item and "candidateKeys" in item["disambiguation"]:
            item["disambiguation"]["candidateKeys"] = [
                k for k in item["disambiguation"]["candidateKeys"]
                if not (isinstance(k, dict) and k.get("disable_key"))
            ]
    merged_global_entity_config = merge_config(sol_global_entity_config, client_global_entity_config)
    merged_global_entity_config = merged_global_entity_config[0] if merged_global_entity_config != [] else {}
    updated_config = disambiguation_config_merger(merged_intra_source_config, merged_inter_source_config,
                                                  merged_global_entity_config)
    updated_config.setdefault("output", {})["isFragmentOLAPTable"] = False
    return updated_config


def merge_inter_source_disambiguated_models(solution_configs, client_configs):
    solution_updated_configs,client_updated_configs=merge_candidate_keys(solution_configs, client_configs,"intersource_disambiguated_models")
    sol_intra_source_configs_dict = {}
    merged_intra_source_configs = []
    [sol_intra_source_configs_dict.update({i["name"]: i}) for i in solution_updated_configs if
     i['config_item_type'] == 'intrasource_disambiguated_models']
    client_intra_source_configs_dict = {}
    [client_intra_source_configs_dict.update({i["name"]: i}) for i in client_updated_configs if
     i['config_item_type'] == 'intrasource_disambiguated_models']

    sol_global_entity_config = [i for i in solution_updated_configs if
                                i['config_item_type'] == 'global_entity_config']
    client_global_entity_config = [i for i in client_updated_configs if
                                   i['config_item_type'] == 'global_entity_config']
    sol_inter_source_config = [i for i in solution_updated_configs if
                               i['config_item_type'] == 'intersource_disambiguated_models']

    client_inter_source_config = [i for i in client_updated_configs if
                                  i['config_item_type'] == 'intersource_disambiguated_models']
    sol_inter_source_config_copy = copy.deepcopy(sol_inter_source_config)
    client_inter_source_config_copy = copy.deepcopy(client_inter_source_config)
    for intra_config in list(set(list(sol_intra_source_configs_dict) + list(client_intra_source_configs_dict))):
        sol_configs_intra = [sol_intra_source_configs_dict.get(intra_config,
                                                               {})] + sol_global_entity_config + sol_inter_source_config_copy
        client_configs_intra = [client_intra_source_configs_dict.get(intra_config,
                                                                     {})] + client_global_entity_config + client_inter_source_config_copy
        merged_intra_source_config = merge_intra_source_disambiguated_models(sol_configs_intra, client_configs_intra)
        merged_intra_source_configs.append(merged_intra_source_config)
    sol_inter_source_config = [i.get("config_value", {}) for i in sol_inter_source_config]
    client_inter_source_config = [i.get("config_value", {}) for i in client_inter_source_config]
    updated_sol_inter_source_config = filter_disambiguation_folder(sol_inter_source_config,
                                                                   [i.get("config_value", {}) for i in list(
                                                                       client_intra_source_configs_dict.values())]) if sol_inter_source_config != [] else sol_inter_source_config
    if updated_sol_inter_source_config != [] and client_inter_source_config != []:
        updated_sol_inter_source_config = process_and_write_to_solution_file(
            client_inter_source_config[0], updated_sol_inter_source_config[0])
    merged_inter_source_config = merge_config(updated_sol_inter_source_config,
                                              client_inter_source_config)
    if isinstance(merged_inter_source_config, list):
        for item in merged_inter_source_config:
            if isinstance(item, dict):
                if 'entity' not in item:
                    item['entity'] = {}

    for item in merged_inter_source_config:
        if "disambiguation" in item and "candidateKeys" in item["disambiguation"]:
            item["disambiguation"]["candidateKeys"] = [
                k for k in item["disambiguation"]["candidateKeys"]
                if not (isinstance(k, dict) and k.get("disable_key"))
            ]
    merged_global_entity_config = merge_config(sol_global_entity_config, client_global_entity_config)
    merged_global_entity_config = merged_global_entity_config[0].get("config_value",
                                                                     {}) if merged_global_entity_config else {}
    merged_global_entity_config['entitySpecificProperties'] = filter_by_computation_phase(
        merged_global_entity_config.get('entitySpecificProperties', []), 'inter')
    merged_global_entity_config['commonProperties'] = filter_by_computation_phase(
        merged_global_entity_config.get('commonProperties', []), 'inter')
    print("Merged Entity Specific Properties:", merged_global_entity_config.get('entitySpecificProperties', []))
    print("********************************************")
    print("Merged Common Properties:", merged_global_entity_config.get('commonProperties', []))
    updated_config = disambiguation_config_merger(merged_intra_source_configs, merged_inter_source_config,
                                                  merged_global_entity_config, is_intra=False)
    inter_config = update_fragment_locations(updated_config)
    return inter_config
