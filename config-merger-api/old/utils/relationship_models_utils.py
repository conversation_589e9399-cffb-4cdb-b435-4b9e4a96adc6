import os

from config_merger_api.old.utils.common_utils import merge_config


def update_relationship_models(relationship_model, inventory_models_list):
    if inventory_models_list:
        input_source_info = relationship_model.get("inputSourceInfo", [])
        updated_input_source_info = []

        for source_info in input_source_info:
            source_loader_conf = source_info.get("sourceLoaderConfPath", [])
            target_loader_conf = source_info.get("targetLoaderConfPath", [])

            # Extract the last file name from sourceLoaderConfPath and targetLoaderConfPath
            # Check if the filenames are not present in valid_inventory_models
            source_loader_filenames = ["sds_ei__" + i.split("sds_ei__")[1].split('?')[0] for i in
                                       source_loader_conf if
                                       "sds_ei__" + i.split("sds_ei__")[1].split('?')[0] in inventory_models_list]
            target_loader_filenames = ["sds_ei__" + i.split("sds_ei__")[1].split('?')[0] for i in
                                       target_loader_conf if
                                       "sds_ei__" + i.split("sds_ei__")[1].split('?')[0] in inventory_models_list]

            if source_loader_filenames and target_loader_filenames:
                updated_input_source_info.append(source_info)

        # Update the inputSourceInfo with the modified paths
        relationship_model["inputSourceInfo"] = updated_input_source_info
    return relationship_model

def merge_enrichments(solution_config, client_config):
    solution_enrichments = solution_config.get("inputSourceInfo", [])[0].get("enrichments", [])
    client_enrichments = client_config.get("inputSourceInfo", [])[0].get("enrichments", [])
    client_table_names = {enrichment.get("lookupInfo", {}).get("tableName") for enrichment in client_enrichments}
    solution_enrichments = [enrichment for enrichment in solution_enrichments
                            if enrichment.get("lookupInfo", {}).get("tableName") not in client_table_names]
    merged_enrichments = solution_enrichments + client_enrichments
    if merged_enrichments!=[]:
        client_config.get("inputSourceInfo", [])[0]["enrichments"] = merged_enrichments
        
    return client_config


def merge_relationship_models(solution_configs, client_configs):
    sol_relationship_model = [i.get("config_value", {}) for i in solution_configs if
                              i['config_item_type'] == 'relationship_models']
    client_relationship_model = [i.get("config_value", {}) for i in client_configs if
                                 i['config_item_type'] == 'relationship_models']
    if(sol_relationship_model==[]):
        merged_configs=client_relationship_model
    else:
        merged_configs = merge_config(sol_relationship_model, client_relationship_model)
    inventory_models_list = list(
        set([i["name"] for i in solution_configs + client_configs if i['config_item_type'] == 'inventory_models']))
    updated_relationship_model = update_relationship_models(merged_configs[0], inventory_models_list)
    return updated_relationship_model
