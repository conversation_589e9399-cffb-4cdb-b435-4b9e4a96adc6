from typing import Dict

key_dict = {
    "commonProperties": "colName",
    "entitySpecificProperties": "colName",
    "sourceSpecificProperties": "colName",
    "temporaryProperties": "colName",
    "inventoryModelInput": "path",
    "aggregation": "field",
    "fieldLevelConfidenceMatrix": "field",
    "valueConfidence": "field",
    "optionalAttributes": "name",
    "derivedProperties": "colName",
    "inputSourceInfo": "sdmPath",
    "dimensions": "name",
    "relationshipModels":"tableName"
}


def merge_dictionaries(parent_dict: Dict, child_dict: Dict):
    child_dict = {} if not child_dict else child_dict
    parent_dict = {} if not parent_dict else parent_dict

    for key in child_dict:
        if key in parent_dict:
            if isinstance(child_dict[key], list) and isinstance(parent_dict[key], list):
                # Get the merge key based on the dictionary
                merge_key = key_dict.get(key)

                if merge_key:
                    # Extract dictionaries and non-dictionary elements
                    parent_dicts = [item for item in parent_dict[key] if isinstance(item, dict)]
                    child_dicts = [item for item in child_dict[key] if isinstance(item, dict)]
                    parent_non_dicts = [item for item in parent_dict[key] if not isinstance(item, dict)]
                    child_non_dicts = [item for item in child_dict[key] if not isinstance(item, dict)]

                    # Remove fieldspec from solution based on colName in client
                    for child_item in child_dicts:
                        col_name = child_item.get(merge_key)
                        matching_parent_item = next(
                            (p_item for p_item in parent_dicts if p_item.get(merge_key) == col_name), None)

                        if matching_parent_item and 'fieldsSpec' in matching_parent_item:
                            del matching_parent_item['fieldsSpec']

                    # Merge dictionaries based on the merge key
                    merged_dicts = []
                    merged_keys = set()

                    for child_item in child_dicts:
                        col_name = child_item.get(merge_key)
                        matching_parent_item = next(
                            (p_item for p_item in parent_dicts if p_item.get(merge_key) == col_name), None)

                        if matching_parent_item:
                            # Merge the dictionaries based on the merge key
                            merged_item = merge_dictionaries(matching_parent_item, child_item)
                            merged_dicts.append(merged_item)
                            merged_keys.add(col_name)
                        else:
                            merged_dicts.append(child_item)

                    # Append dictionaries that were not merged
                    for parent_item in parent_dicts:
                        col_name = parent_item.get(merge_key)
                        if col_name not in merged_keys:
                            merged_dicts.append(parent_item)
                            merged_keys.add(col_name)

                    # Concatenate dictionaries and non-dictionary elements
                    parent_dict[key] = merged_dicts + parent_non_dicts + child_non_dicts
                else:
                    parent_dict[key] = child_dict[
                        key]  # If the key doesn't exist in key_dict, just replace the value
            elif not isinstance(child_dict[key], Dict) and not isinstance(parent_dict[key], Dict):
                parent_dict[key] = child_dict[key]
            else:
                parent_dict[key] = merge_dictionaries(parent_dict[key], child_dict[key])
        else:
            parent_dict[key] = child_dict[key]

    return parent_dict


def merge_config(solution_config_list, client_config_list):
    merged_list = []
    solution_config_dict = {}
    client_config_dict = {}
    [solution_config_dict.update({f"{i.get('config_item_type', '')}__{i.get('name')}": i}) if i.get(
        'config_item_type') else solution_config_dict.update({"config": i}) for i in solution_config_list]
    [client_config_dict.update({f"{i.get('config_item_type', '')}__{i.get('name')}": i}) if i.get(
        'config_item_type') else client_config_dict.update({"config": i}) for i in client_config_list]

    for key, solution_config in solution_config_dict.items():
        if key in client_config_dict.keys():
            merged_config = merge_dictionaries(solution_config, client_config_dict.get(key))
            merged_list.append(merged_config)
        else:
            merged_list.append(solution_config)
    for client_config in [value for key, value in client_config_dict.items() if key not in solution_config_dict.keys()]:
        merged_list.append(client_config)
    return merged_list
