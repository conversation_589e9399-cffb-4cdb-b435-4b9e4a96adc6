def merge_rel_configs(solution_config, client_config, key_dict):
    merged_config = {}
    for key in solution_config.keys():
        if key not in key_dict:
            merged_config[key] = solution_config[key]
    for key, merge_keys in key_dict.items():
        merged_config[key] = []
        for count_enrich in solution_config.get(key, []):
            merged_config[key].append(count_enrich)
        for client_count_enrich in client_config.get(key, []):
            merged = False
            for merged_count_enrich in merged_config[key]:
                if all(merged_count_enrich[k] == client_count_enrich[k] for k in merge_keys):
                    merged_count_enrich.update(client_count_enrich)
                    merged = True
                    break

            if not merged:
                merged_config[key].append(client_count_enrich)
    return merged_config

def merge_entity_rel_enrich(solution_configs, client_configs):
    sol_entity_rel_enrich = [i.get("config_value", {}) for i in solution_configs if
                              i['config_item_type'] == 'entity_rel_enrich']
    client_entity_rel_enrich = [i.get("config_value", {}) for i in client_configs if
                                 i['config_item_type'] == 'entity_rel_enrich']
    key_dict = {
        "countEnriches": ["sourceFieldName", "targetFieldName"]
    }
    merged_config = merge_rel_configs(sol_entity_rel_enrich[0] if sol_entity_rel_enrich!=[] else {} , client_entity_rel_enrich[0] if client_entity_rel_enrich !=[] else {}, key_dict)
    return merged_config

