from config_merger_api.config_upgrade_utils.common_utils import Config<PERSON>anager,logger


def update_config(config_value,config_item_level):
    """
    Modifies the output section of the configuration based on the config_item_level.
    """
    if config_item_level=='solution':
        config_value.setdefault("output", {})["fragmentLocation"] = config_value["output"]["disambiguatedModelLocation"].replace("<%EI_SCHEMA_NAME%>.sds_ei__rel","<%KG_FRAGMENT_SCHEMA%>.sds_ei__rel__fragment")
        config_value.setdefault("output", {})["resolverLocation"] = config_value["output"]["disambiguatedModelLocation"].replace("<%EI_SCHEMA_NAME%>.sds_ei__rel","<%KG_FRAGMENT_SCHEMA%>.sds_ei__rel__resolver")
        config_value.get("output", {}).pop("nonResolvedModelLocation", None)
        return config_value
    if config_item_level == 'client':
        config_value.get("output",{}).pop("nonResolvedModelLocation",None)
        config_value.get("output", {}).pop("resolverLocation", None)
        return config_value



def upgrade_relationship_disambiguation_models(dict_params):
    """
        Updates the configuration dictionary based on the provided config_item_level by adding or removing specific keys under the output section.
        Args:
            dict_params (dict): A dictionary containing parameters such as name,config_item_type,solution_edition and config_item_level.
        Returns:
            dict (Upgraded configuration dictionary).
    """
    config_manager = ConfigManager()
    name = dict_params['name']
    config_item_level= dict_params['config_item_level']
    rel_config = config_manager.get_config(name, {**dict_params})
    final_config = {**rel_config, "upgraded_state": "config_schema_updated", 'pushed_to_git': False,"config_value": update_config(rel_config["config_value"],config_item_level) if rel_config.get("config_value",{}).get("output") is not None else rel_config["config_value"]}
    return final_config

