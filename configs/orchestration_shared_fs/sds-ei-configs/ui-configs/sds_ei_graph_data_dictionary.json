{"eiGraph": {"Cloud Compute": {"attribute": "native_type", "configs": {"AWS EC2 Instance": {"attribute": "data_source_subset_name", "configs": {"AWS EMR EC2 Instance": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "MapReduce Cluster", "name": "EMR Cluster ID", "iconName": "icon-cluster", "accessorKey": "emr_cluster_id"}]}, {"id": "MapReduce Cluster", "name": "EMR Cluster ID", "iconName": "icon-cluster", "accessorKey": "emr_cluster_id", "children": [{"id": "Compute Instance Group", "name": "EC2 Fleet ID", "iconName": "icon-group", "accessorKey": "ec2fleet_id"}]}, {"id": "Compute Instance Group", "name": "EC2 Fleet ID", "iconName": "icon-group", "accessorKey": "ec2fleet_id", "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}, {"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}, "default": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "name": "Kubernetes Cluster Name", "iconName": "icon-cluster", "accessorKey": "kubernetes_cluster_name"}]}, {"id": "Kubernetes Cluster", "iconName": "icon-cluster", "name": "Kubernetes Cluster Name", "accessorKey": "kubernetes_cluster_name", "children": [{"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name"}]}, {"id": "Node Group", "name": "Kubernetes Node Group Name", "iconName": "icon-group", "accessorKey": "kubernetes_node_group_name", "children": [{"id": "Compute Instance Group", "iconName": "icon-group", "name": "Scaling Group Name", "accessorKey": "scaling_group_name"}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Scaling Group Name", "accessorKey": "scaling_group_name", "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}, {"id": "Compute Instance Group ", "name": "EC2 Fleet ID", "iconName": "icon-group", "accessorKey": "ec2fleet_id", "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}, {"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}}}, "AWS AutoScaling Group": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "name": "Kubernetes Cluster Name", "iconName": "icon-cluster", "accessorKey": "kubernetes_cluster_name"}]}, {"id": "Kubernetes Cluster", "name": "Kubernetes Cluster Name", "iconName": "icon-cluster", "accessorKey": "kubernetes_cluster_name", "children": [{"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name"}]}, {"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name", "children": [{"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "scaling_instance_count", "id": "Scaling Instance Count"}, {"accessorKey": "scaling_instance_type", "id": "Scaling Instance Type", "data_structure": "list"}]}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "scaling_instance_count", "id": "Scaling Instance Count"}, {"accessorKey": "scaling_instance_type", "id": "Scaling Instance Type", "data_structure": "list"}], "children": [{"id": "Virtual Machine", "name": "Scaling Instance IDs", "iconName": "icon-vm", "accessorKey": "scaling_instance_ids", "isArray": true}]}, {"id": "Virtual Machine", "name": "Scaling Instance IDs", "iconName": "icon-vm", "accessorKey": "scaling_instance_ids", "isArray": true}]}, "Azure Virtual Machine": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name"}]}, {"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name", "children": [{"id": "Compute Instance Group", "iconName": "icon-group", "name": "Scaling Group Name", "accessorKey": "scaling_group_name"}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Scaling Group Name", "accessorKey": "scaling_group_name", "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}, {"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "cloud_instance_type", "id": "Cloud Instance Type", "data_structure": "list"}]}]}, "Azure Virtual Machine Scaleset": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name"}]}, {"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name", "children": [{"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "scaling_instance_count", "id": "Scaling Instance Count"}, {"accessorKey": "scaling_instance_type", "id": "Scaling Instance Type", "data_structure": "list"}]}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "scaling_instance_count", "id": "Scaling Instance Count"}, {"accessorKey": "scaling_instance_type", "id": "Scaling Instance Type", "data_structure": "list"}]}]}, "Azure Managed Cluster": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "iconName": "icon-cluster", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "kubernetes_node_pool_vm_count", "id": "Kubernetes Node Pool VM Count"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "kubernetes_version", "id": "Kubernetes Version"}]}]}, {"id": "Kubernetes Cluster", "iconName": "icon-cluster", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "children": [{"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name", "isArray": true}], "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "kubernetes_node_pool_vm_count", "id": "Kubernetes Node Pool VM Count"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "kubernetes_version", "id": "Kubernetes Version"}]}, {"id": "Node Group", "iconName": "icon-group", "name": "Kubernetes Node Group Name", "accessorKey": "kubernetes_node_group_name", "isArray": true}]}, "AWS EC2 Fleet": {"attribute": "data_source_subset_name", "configs": {"AWS EMR EC2 Fleet": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "MapReduce Cluster", "name": "EMR Cluster ID", "iconName": "icon-cluster", "accessorKey": "emr_cluster_id"}]}, {"id": "MapReduce Cluster", "name": "EMR Cluster ID", "iconName": "icon-cluster", "accessorKey": "emr_cluster_id", "children": [{"id": "Compute Instance Group", "name": "Display Label", "iconName": "icon-group", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ec2fleet_total_target_capacity", "id": "AWS EC2 Fleet Total Target Capacity"}, {"accessorKey": "aws_ec2fleet_type", "id": "AWS EC2 Fleet Type"}]}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ec2fleet_total_target_capacity", "id": "AWS EC2 Fleet Total Target Capacity"}, {"accessorKey": "aws_ec2fleet_type", "id": "AWS EC2 Fleet Type"}]}]}, "default": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ec2fleet_total_target_capacity", "id": "AWS EC2 Fleet Total Target Capacity"}, {"accessorKey": "aws_ec2fleet_type", "id": "AWS EC2 Fleet Type"}]}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ec2fleet_total_target_capacity", "id": "AWS EC2 Fleet Total Target Capacity"}, {"accessorKey": "aws_ec2fleet_type", "id": "AWS EC2 Fleet Type"}]}]}}}, "AWS EMR Cluster": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "MapReduce Cluster", "name": "Display Label", "iconName": "icon-cluster", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "aws_emr_version", "id": "AWS EMR Version"}]}]}, {"id": "MapReduce Cluster", "name": "Display Label", "iconName": "icon-cluster", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "aws_emr_version", "id": "AWS EMR Version"}]}]}, "AWS ECS Cluster": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container Service", "name": "Display Label", "iconName": "icon-group", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}, {"id": "Container Service", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}, "AWS EKS Cluster": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "name": "Display Label", "iconName": "icon-cluster", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "kubernetes_version", "id": "Kubernetes Version"}]}]}, {"id": "Kubernetes Cluster", "name": "Display Label", "accessorKey": "display_label", "iconName": "icon-cluster", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "kubernetes_version", "id": "Kubernetes Version"}]}]}, "Azure Container Group": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container Group", "name": "Display Label", "iconName": "icon-cluster", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "os_family", "id": "OS Family"}]}]}, {"id": "Container Group", "iconName": "icon-cluster", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "os_family", "id": "OS Family"}], "children": [{"accessorKey": "aci_container_services_active_containers", "name": "ACI Active Containers", "iconName": "icon-container", "id": "Container", "isArray": true}]}, {"accessorKey": "aci_container_services_active_containers", "name": "ACI Active Containers", "iconName": "icon-container", "id": "Container", "isArray": true}]}, "AWS Lambda Function": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Serverless", "iconName": "icon-serverless", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "aws_lambda_memory_size", "id": "AWS Lambda Memory Size"}, {"accessorKey": "aws_lambda_runtime", "id": "AWS Lambda Runtime"}]}]}, {"id": "Serverless", "iconName": "icon-serverless", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "aws_lambda_memory_size", "id": "AWS Lambda Memory Size"}, {"accessorKey": "aws_lambda_runtime", "id": "AWS Lambda Runtime"}]}]}, "AWS ECS Service": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ecs_platform_version", "id": "AWS ECS Platform Version"}, {"accessorKey": "aws_ecs_scheduling_strategy", "id": "AWS ECS Scheduling Strategy"}]}]}, {"id": "Compute Instance Group", "iconName": "icon-group", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ecs_platform_version", "id": "AWS ECS Platform Version"}, {"accessorKey": "aws_ecs_scheduling_strategy", "id": "AWS ECS Scheduling Strategy"}]}]}, "AWS SageMaker Model": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "AI Model", "iconName": "icon-model", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_sagemaker_model_primary_container_mode", "id": "AWS Sagemaker Model Mode"}]}]}, {"id": "AI Model", "iconName": "icon-model", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_sagemaker_model_primary_container_mode", "id": "AWS Sagemaker Model Mode"}]}]}, "AWS SageMaker Notebook Instance": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Data Workload", "iconName": "icon-model", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}, {"id": "Data Workload", "iconName": "icon-model", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}, "AWS Lightsail Instance": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}, {"id": "Virtual Machine", "iconName": "icon-vm", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}}, "relationshipGraph": {"color": "#8888DD", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Type", "accessorKey": "type", "isExtra": true}, {"id": "Cloud Native Type", "accessorKey": "native_type", "isExtra": true}]}}, "Cloud Storage": {"attribute": "native_type", "configs": {"AWS EC2 Volume (EBS)": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "name": "Kubernetes Cluster Name", "iconName": "icon-kubernetes", "accessorKey": "kubernetes_cluster_name"}]}, {"id": "Kubernetes Cluster", "name": "Kubernetes Cluster Name", "iconName": "icon-kubernetes", "accessorKey": "kubernetes_cluster_name", "children": [{"id": "Instance Name", "name": "Cloud Instance ID", "iconName": "icon-instance", "accessorKey": "cloud_instance_id"}]}, {"id": "Instance Name", "name": "Cloud Instance ID", "iconName": "icon-instance", "accessorKey": "cloud_instance_id", "children": [{"id": "Volume Name", "name": "Display Label", "iconName": "icon-volume", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "volume_size", "id": "Volume Size"}, {"accessorKey": "volume_type", "id": "Volume Type"}]}]}, {"id": "Volume Name", "name": "Display Label", "iconName": "icon-volume", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "volume_size", "id": "Volume Size"}, {"accessorKey": "volume_type", "id": "Volume Type"}]}]}, "Azure Disk": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Instance Name", "name": "Cloud Instance ID", "iconName": "icon-instance", "accessorKey": "cloud_instance_id"}]}, {"id": "Instance Name", "name": "Cloud Instance ID", "iconName": "icon-instance", "accessorKey": "cloud_instance_id", "children": [{"id": "Volume Name", "iconName": "icon-volume", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "volume_size", "id": "Volume Size"}, {"accessorKey": "volume_type", "id": "Volume Type"}]}]}, {"id": "Volume Name", "iconName": "icon-volume", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "volume_size", "id": "Volume Size"}, {"accessorKey": "volume_type", "id": "Volume Type"}]}]}, "AWS S3 Bucket": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Bucket Name", "iconName": "icon-bucket", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}, {"accessorKey": "aws_s3_owner_id", "id": "AWS S3 Owner ID"}]}]}, {"id": "Bucket Name ", "iconName": "icon-bucket", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}, {"accessorKey": "aws_s3_owner_id", "id": "AWS S3 Owner ID"}]}]}, "Azure Blob Storage Container": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Storage Account Name", "accessorKey": "storage_account_name"}]}, {"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Storage Account Name", "accessorKey": "storage_account_name", "children": [{"id": "Bucket Name", "iconName": "icon-bucket", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}, {"accessorKey": "azure_bsc_public_access", "id": "Azure BSC Public Access"}]}]}, {"id": "Bucket Name", "iconName": "icon-bucket", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}, {"accessorKey": "azure_bsc_public_access", "id": "Azure BSC Public Access"}]}]}, "Azure Table Storage": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Storage Account Name", "accessorKey": "storage_account_name"}]}, {"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Storage Account Name", "accessorKey": "storage_account_name", "children": [{"id": "Table Name", "iconName": "icon-table", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}]}]}, {"id": "Table Name", "iconName": "icon-table", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}]}]}, "Azure Queue Storage": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Storage Account Name", "accessorKey": "storage_account_name"}]}, {"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Storage Account Name", "accessorKey": "storage_account_name", "children": [{"id": "Queue Service Name", "iconName": "icon-queue", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}]}]}, {"id": "Queue Service Name", "iconName": "icon-queue", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}]}]}, "Azure Storage Account": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "storage_account_access_tier", "id": "Storage Account Access Tier"}, {"accessorKey": "azure_storage_account_type", "id": "Azure Storage Account Type"}]}]}, {"id": "Storage Account Name", "iconName": "icon-storageacc", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "storage_account_access_tier", "id": "Storage Account Access Tier"}, {"accessorKey": "azure_storage_account_type", "id": "Azure Storage Account Type"}]}]}, "Azure File Share": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "File Service Name", "name": "Display Label", "iconName": "icon-document", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "capacity", "id": "Capacity"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}, {"accessorKey": "azure_file_share_enabled_protocols", "id": "Azure File Share Enabled Protocols"}]}]}, {"id": "File Service Name", "name": "Display Label", "iconName": "icon-document", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "capacity", "id": "Capacity"}, {"accessorKey": "encryption_in_transit", "id": "Encryption in Transit"}, {"accessorKey": "azure_file_share_enabled_protocols", "id": "Azure File Share Enabled Protocols"}]}]}, "AWS Elastic File System (EFS)": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "File Service Name", "name": "Display Label", "iconName": "icon-document", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "aws_efs_performance_mode", "id": "AWS EFS Performance Mode"}, {"accessorKey": "aws_efs_backup_policy_status", "id": "AWS EFS Backup Policy Status"}]}]}, {"id": "File Service Name", "name": "Display Label", "iconName": "icon-document", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_at_rest", "id": "Encryption at Rest"}, {"accessorKey": "aws_efs_performance_mode", "id": "AWS EFS Performance Mode"}, {"accessorKey": "aws_efs_backup_policy_status", "id": "AWS EFS Backup Policy Status"}]}]}}, "relationshipGraph": {"color": "#3A96C4", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Type", "accessorKey": "type", "isExtra": true}, {"id": "Cloud Native Type", "accessorKey": "native_type", "isExtra": true}]}}, "Finding": {"attribute": "activity_status", "configs": {"Active": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Finding", "iconName": "icon-finding", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "finding_sub_type", "id": "Sub Type"}, {"accessorKey": "finding_status", "id": "Status Normalized"}, {"accessorKey": "finding_severity", "id": "Severity Normalized"}, {"accessorKey": "compliance_status", "id": "Compliance Status"}]}]}, {"id": "Finding", "iconName": "icon-finding", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "finding_sub_type", "id": "Sub Type"}, {"accessorKey": "finding_status", "id": "Status Normalized"}, {"accessorKey": "finding_severity", "id": "Severity Normalized"}, {"accessorKey": "compliance_status", "id": "Compliance Status"}], "children": [{"id": "Affected Resource Type", "iconName": "icon-resourcetype", "name": "Affected Resource Type", "accessorKey": "affected_resource_type_normalized", "tooltip": [{"accessorKey": "affected_resource_type", "id": "Affected Vendor Resource Type"}]}]}, {"id": "Affected Resource Type", "iconName": "icon-resourcetype", "name": "Affected Resource Type", "accessorKey": "affected_resource_type_normalized", "tooltip": [{"accessorKey": "affected_resource_type", "id": "Affected Vendor Resource Type"}]}]}, "Inactive": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Finding", "iconName": "icon-finding", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "finding_sub_type", "id": "Sub Type"}, {"accessorKey": "finding_status", "id": "Status Normalized"}, {"accessorKey": "finding_severity", "id": "Severity Normalized"}, {"accessorKey": "compliance_status", "id": "Compliance Status"}]}]}, {"id": "Finding", "iconName": "icon-finding", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "finding_sub_type", "id": "Sub Type"}, {"accessorKey": "finding_status", "id": "Status Normalized"}, {"accessorKey": "finding_severity", "id": "Severity Normalized"}, {"accessorKey": "compliance_status", "id": "Compliance Status"}], "children": [{"id": "Affected Resource Type", "iconName": "icon-resourcetype", "name": "Affected Resource Type", "accessorKey": "affected_resource_type_normalized", "tooltip": [{"accessorKey": "affected_resource_type", "id": "Affected Vendor Resource Type"}]}]}, {"id": "Affected Resource Type", "iconName": "icon-resourcetype", "name": "Affected Resource Type", "accessorKey": "affected_resource_type_normalized", "tooltip": [{"accessorKey": "affected_resource_type", "id": "Affected Vendor Resource Type"}]}]}, "default": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Finding", "iconName": "icon-finding", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "finding_sub_type", "id": "Sub Type"}, {"accessorKey": "finding_status", "id": "Status Normalized"}, {"accessorKey": "finding_severity", "id": "Severity Normalized"}, {"accessorKey": "compliance_status", "id": "Compliance Status"}]}]}, {"id": "Finding", "iconName": "icon-finding", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "finding_sub_type", "id": "Sub Type"}, {"accessorKey": "finding_status", "id": "Status Normalized"}, {"accessorKey": "finding_severity", "id": "Severity Normalized"}, {"accessorKey": "compliance_status", "id": "Compliance Status"}], "children": [{"id": "Affected Resource Type", "iconName": "icon-resourcetype", "name": "Affected Resource Type", "accessorKey": "affected_resource_type_normalized", "tooltip": [{"accessorKey": "affected_resource_type", "id": "Affected Vendor Resource Type"}]}]}, {"id": "Affected Resource Type", "iconName": "icon-resourcetype", "name": "Affected Resource Type", "accessorKey": "affected_resource_type_normalized", "tooltip": [{"accessorKey": "affected_resource_type", "id": "Affected Vendor Resource Type"}]}]}}, "relationshipGraph": {"color": "#7958C4", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Finding Type", "accessorKey": "finding_type", "isExtra": true}, {"id": "Finding Sub Type", "accessorKey": "finding_sub_type", "isExtra": true}]}}, "Cloud Container": {"attribute": "native_type", "configs": {"AWS ECS Container": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container Service", "name": "ECS Cluster Name", "iconName": "icon-cluster", "accessorKey": "ecs_cluster_name"}]}, {"id": "Container Service", "name": "ECS Cluster Name", "iconName": "icon-cluster", "accessorKey": "ecs_cluster_name", "children": [{"id": "Container Task", "iconName": "icon-group", "name": "Container ECS Task ARN", "accessorKey": "container_ecs_task_arn"}]}, {"id": "Container Task", "iconName": "icon-group", "name": "Container ECS Task ARN", "accessorKey": "container_ecs_task_arn", "children": [{"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "aws_container_launch_type", "id": "AWS Container Launch Type"}, {"accessorKey": "aws_container_health_status", "id": "AWS Container Health Status"}]}]}, {"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "aws_container_launch_type", "id": "AWS Container Launch Type"}, {"accessorKey": "aws_container_health_status", "id": "AWS Container Health Status"}]}]}, "Azure ACI Container": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container Group", "name": "ACI Cluster Name", "iconName": "icon-cluster", "accessorKey": "aci_cluster_name"}]}, {"id": "Container Group", "name": "ACI Cluster Name", "iconName": "icon-cluster", "accessorKey": "aci_cluster_name", "children": [{"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "container_memory", "id": "Container Memory"}, {"accessorKey": "container_cpu", "id": "Container CPU"}]}]}, {"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "container_memory", "id": "Container Memory"}, {"accessorKey": "container_cpu", "id": "Container CPU"}]}]}, "AWS ECR Repository": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container Registry", "iconName": "icon-registry", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ecr_encryption_type", "id": "AWS ECR Encryption Type"}, {"accessorKey": "encryption_status", "id": "Encryption Status"}, {"accessorKey": "aws_ecr_scan_on_push", "id": "AWS ECR Scan On Push"}]}]}, {"id": "Container Registry", "iconName": "icon-registry", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "aws_ecr_encryption_type", "id": "AWS ECR Encryption Type"}, {"accessorKey": "encryption_status", "id": "Encryption Status"}, {"accessorKey": "aws_ecr_scan_on_push", "id": "AWS ECR Scan On Push"}]}]}, "Azure Container Registry": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container Registry", "iconName": "icon-registry", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_status", "id": "Encryption Status"}]}]}, {"id": "Container Registry", "iconName": "icon-registry", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "encryption_status", "id": "Encryption Status"}]}]}, "Azure AKS Container": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "iconName": "icon-cluster", "name": "Kubernetes Cluster ID", "accessorKey": "kubernetes_cluster_id"}]}, {"id": "Kubernetes Cluster", "name": "Kubernetes Cluster ID", "iconName": "icon-cluster", "accessorKey": "kubernetes_cluster_id", "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Container Hostname", "accessorKey": "container_hostname"}]}, {"id": "Virtual Machine", "iconName": "icon-vm", "name": "Container Hostname", "accessorKey": "container_hostname", "children": [{"id": "Pod", "iconName": "icon-pod", "accessorKey": "kubernetes_pod_name", "name": "Kubernetes Pod Name"}]}, {"id": "Pod", "accessorKey": "kubernetes_pod_name", "iconName": "icon-pod", "name": "Kubernetes Pod Name", "children": [{"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}]}]}, {"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}]}]}, "AWS EKS Container": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Kubernetes Cluster", "iconName": "icon-cluster", "name": "Kubernetes Cluster ID", "accessorKey": "kubernetes_cluster_id"}]}, {"id": "Kubernetes Cluster", "iconName": "icon-cluster", "name": "Kubernetes Cluster ID", "accessorKey": "kubernetes_cluster_id", "children": [{"id": "Virtual Machine", "iconName": "icon-vm", "name": "Container Hostname", "accessorKey": "container_hostname"}]}, {"id": "Virtual Machine", "iconName": "icon-vm", "name": "Container Hostname", "accessorKey": "container_hostname", "children": [{"id": "Pod", "iconName": "icon-pod", "accessorKey": "kubernetes_pod_name", "name": "Kubernetes Pod Name"}]}, {"id": "Pod", "name": "Kubernetes Pod Name", "iconName": "icon-pod", "accessorKey": "kubernetes_pod_name", "children": [{"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}]}]}, {"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}, {"accessorKey": "activity_status", "id": "Activity Status"}]}]}, "Hosted Container": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true}]}, {"id": "Cloud Account ID", "accessorKey": "account_id", "iconName": "icon-account", "name": "Cloud Account ID", "disableHeading": true, "children": [{"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}, {"id": "Container", "iconName": "icon-container", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "native_type", "id": "Cloud Native Type"}]}]}}, "relationshipGraph": {"color": "#B49CCD", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Type", "accessorKey": "type", "isExtra": true}, {"id": "Cloud Native Type", "accessorKey": "native_type", "isExtra": true}]}}, "Person": {"attribute": "type", "configs": {"Permanent": {"attributeList": [{"id": "Location Country", "accessorKey": "location_country", "iconName": "icon-country", "name": "Location Country", "disableHeading": true, "children": [{"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company"}]}, {"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company", "children": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit"}]}, {"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Division", "accessorKey": "department", "iconName": "icon-department", "name": "Department", "disableHeading": true}]}, {"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Division", "accessorKey": "department", "iconName": "icon-department", "name": "Department", "disableHeading": true}]}, {"id": "Division", "iconName": "icon-department", "name": "Department", "accessorKey": "department", "children": [{"id": "Manager", "name": "Manager", "iconName": "icon-manager", "accessorKey": "manager"}]}, {"id": "Manager", "iconName": "icon-manager", "name": "Manager", "accessorKey": "manager", "children": [{"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}]}]}, {"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}], "children": [{"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, {"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, "Contract": {"attributeList": [{"id": "Location Country", "accessorKey": "location_country", "iconName": "icon-country", "name": "Location Country", "disableHeading": true, "children": [{"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company"}]}, {"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company", "children": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit"}]}, {"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Division", "accessorKey": "department", "iconName": "icon-department", "name": "Department", "disableHeading": true}]}, {"id": "Division", "iconName": "icon-department", "name": "Department", "accessorKey": "department", "children": [{"id": "Manager", "name": "Manager", "iconName": "icon-manager", "accessorKey": "manager"}]}, {"id": "Manager", "iconName": "icon-manager", "name": "Manager", "accessorKey": "manager", "children": [{"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}]}]}, {"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}], "children": [{"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, {"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, "Guest": {"attributeList": [{"id": "Location Country", "accessorKey": "location_country", "iconName": "icon-country", "name": "Location Country", "disableHeading": true, "children": [{"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company"}]}, {"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company", "children": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit"}]}, {"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Division", "accessorKey": "department", "iconName": "icon-department", "name": "Department", "disableHeading": true}]}, {"id": "Division", "iconName": "icon-department", "name": "Department", "accessorKey": "department", "children": [{"id": "Manager", "name": "Manager", "iconName": "icon-manager", "accessorKey": "manager"}]}, {"id": "Manager", "iconName": "icon-manager", "name": "Manager", "accessorKey": "manager", "children": [{"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}]}]}, {"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}], "children": [{"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, {"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, "Member": {"attributeList": [{"id": "Location Country", "accessorKey": "location_country", "iconName": "icon-country", "name": "Location Country", "disableHeading": true, "children": [{"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company"}]}, {"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company", "children": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit"}]}, {"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Division", "accessorKey": "department", "iconName": "icon-department", "name": "Department", "disableHeading": true}]}, {"id": "Division", "iconName": "icon-department", "name": "Department", "accessorKey": "department", "children": [{"id": "Manager", "name": "Manager", "iconName": "icon-manager", "accessorKey": "manager"}]}, {"id": "Manager", "iconName": "icon-manager", "name": "Manager", "accessorKey": "manager", "children": [{"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}]}]}, {"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}], "children": [{"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, {"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, "default": {"attributeList": [{"id": "Location Country", "accessorKey": "location_country", "iconName": "icon-country", "name": "Location Country", "disableHeading": true, "children": [{"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company"}]}, {"id": "Company", "name": "Company", "iconName": "icon-company", "accessorKey": "company", "children": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit"}]}, {"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Division", "accessorKey": "department", "iconName": "icon-department", "name": "Department", "disableHeading": true}]}, {"id": "Division", "iconName": "icon-department", "name": "Department", "accessorKey": "department", "children": [{"id": "Manager", "name": "Manager", "iconName": "icon-manager", "accessorKey": "manager"}]}, {"id": "Manager", "iconName": "icon-manager", "name": "Manager", "accessorKey": "manager", "children": [{"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}]}]}, {"id": "Employee", "iconName": "icon-employee", "name": "Display Label", "accessorKey": "display_label", "isActive": true, "tooltip": [{"accessorKey": "employee_status", "id": "Employee Status"}, {"accessorKey": "type", "id": "Type"}], "children": [{"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}, {"id": "Job Details", "name": "Job Title", "iconName": "icon-job", "accessorKey": "job_title", "tooltip": [{"accessorKey": "job_position_id", "id": "Job Position ID"}, {"accessorKey": "legal_entity", "id": "Legal Entity"}, {"accessorKey": "cost_center", "id": "Cost Center"}]}, {"id": "Person Details", "name": "Full Name", "iconName": "icon-name", "accessorKey": "full_name", "tooltip": [{"accessorKey": "phone_number", "id": "Phone Number"}, {"accessorKey": "external_email_id", "id": "Personal Email", "data_structure": "list"}]}, {"id": "Identity", "name": "Email ID", "iconName": "icon-email", "accessorKey": "email_id", "data_structure": "list", "tooltip": [{"accessorKey": "owns_host_count", "id": "Host Owned By Person"}, {"accessorKey": "login_last_date", "id": "Last Login", "isDate": true}]}]}}, "relationshipGraph": {"color": "#3E8098", "tooltip": [{"id": "Job Title", "accessorKey": "job_title", "isExtra": true}, {"id": "Company", "accessorKey": "company", "isExtra": true}]}}, "Vulnerability": {"attribute": "type", "configs": {"Vulnerability": {"attributeList": [{"id": "Vulnerability Type", "accessorKey": "type", "iconName": "icon-vultype", "name": "Type", "disableHeading": true, "data_structure": "list", "children": [{"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}]}]}, {"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}], "children": [{"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, {"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, "Informational": {"attributeList": [{"id": "Vulnerability Type", "accessorKey": "type", "iconName": "icon-vultype", "name": "Type", "disableHeading": true, "data_structure": "list", "children": [{"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}]}]}, {"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}], "children": [{"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, {"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, "Weakness": {"attributeList": [{"id": "Vulnerability Type", "accessorKey": "type", "iconName": "icon-vultype", "name": "Type", "disableHeading": true, "data_structure": "list", "children": [{"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}]}]}, {"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}], "children": [{"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, {"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, "default": {"attributeList": [{"id": "Vulnerability Type", "accessorKey": "type", "iconName": "icon-vultype", "name": "Type", "disableHeading": true, "data_structure": "list", "children": [{"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}]}]}, {"id": "Vulnerability", "accessorKey": "cve_id", "iconName": "icon-cve", "name": "CVE ID", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Patch Available", "accessorKey": "patch_available"}, {"id": "Vendor ID", "accessorKey": "vendor_id", "data_structure": "list"}, {"id": "Title", "accessorKey": "title"}, {"id": "Status", "accessorKey": "activity_status"}, {"id": "Count of Hosts with Open Vulnerability Findings", "accessorKey": "associated_hosts_with_open_findings_count"}, {"id": "Published On", "accessorKey": "published_date", "isDate": true}], "children": [{"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}, {"id": "CWE", "iconName": "icon-cwe", "name": "CWE", "accessorKey": "cwe", "data_structure": "list"}, {"id": "Severity", "iconName": "icon-severity", "name": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "tooltip": [{"id": "CVSSv3.1 Score", "accessorKey": "v31_score"}, {"id": "CVSSv3.1 Vector", "accessorKey": "v31_vector"}, {"id": "CVSSv3.1 Severity", "accessorKey": "v31_severity"}, {"id": "CVSSv3.1 Impact Score", "accessorKey": "v31_impact_score"}, {"id": "CVSSv3.0 Temporal Score", "accessorKey": "temporal_cvss_score"}, {"id": "CVSSv3.1 Exploitability", "accessorKey": "v31_exploitability"}]}, {"id": "Exploit", "iconName": "icon-exploit", "name": "Exploit Available", "accessorKey": "exploit_available", "tooltip": [{"id": "EPSS", "accessorKey": "epss"}, {"id": "EPSS Percentile", "accessorKey": "epss_percentile"}]}]}}, "relationshipGraph": {"color": "#AE5757", "tooltip": [{"id": "<PERSON><PERSON><PERSON>", "accessorKey": "vendor_severity", "isExtra": true}, {"id": "Title", "accessorKey": "title", "isExtra": true}]}}, "Host": {"attribute": "type", "configs": {"Workstation": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "Server": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "Mobile": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "Network": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "Other": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "Hypervisor": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "Printer": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, "default": {"attributeList": [{"id": "Business Unit", "accessorKey": "business_unit", "iconName": "icon-business", "name": "Business Unit", "disableHeading": true, "children": [{"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}]}]}, {"id": "Tier Details", "accessorKey": "host_criticality_tier", "iconName": "icon-tier", "name": "Host Criticality Tier", "disableHeading": true, "tooltip": [{"accessorKey": "asset_criticality", "id": "Asset Criticality"}, {"accessorKey": "asset_security_posture", "id": "Security Posture"}, {"accessorKey": "host_meet_security_posture", "id": "Host Meet Security Posture"}], "children": [{"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}]}]}, {"id": "Host Type", "name": "type", "iconName": "icon-hosttype", "data_structure": "list", "accessorKey": "type", "tooltip": [{"accessorKey": "accessibility", "id": "Accessibility"}], "children": [{"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}]}]}, {"id": "Host", "iconName": "icon-display", "name": "Display Label", "accessorKey": "display_label", "tooltip": [{"accessorKey": "os", "id": "OS"}, {"accessorKey": "os_family", "id": "OS Family"}, {"accessorKey": "fqdn", "id": "FQDN", "data_structure": "list"}, {"accessorKey": "ip", "id": "IP", "data_structure": "list"}, {"accessorKey": "activity_status", "id": "Activity Status"}, {"accessorKey": "first_seen_date", "id": "First Seen", "isDate": true}], "children": [{"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number", "data_structure": "list"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}, {"id": "Hardware", "iconName": "icon-hardware", "name": "Hardware Chassis Type", "accessorKey": "hardware_chassis_type", "isActive": true, "tooltip": [{"accessorKey": "hardware_serial_number", "id": "Serial Number"}, {"accessorKey": "hardware_bios_manufacturer", "id": "BIOS Manufacturer"}, {"accessorKey": "hardware_bios_version", "id": "BIOS Version"}]}, {"id": "EDR", "iconName": "icon-edr", "name": "EDR Onboarding Status", "accessorKey": "edr_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "edr_product", "id": "EDR Product"}, {"accessorKey": "edr_last_scan_date", "id": "EDR <PERSON>", "isDate": true}]}, {"id": "VM", "iconName": "icon-vm", "name": "VM Onboarding Status", "accessorKey": "vm_onboarding_status", "isActive": true, "tooltip": [{"accessorKey": "vm_product", "id": "VM Product", "data_structure": "list"}, {"accessorKey": "vm_last_scan_date", "id": "VM <PERSON>", "isDate": true}, {"accessorKey": "has_open_vulnerability_finding_count", "id": "Count of Open Vulnerability Findings"}]}]}}, "relationshipGraph": {"color": "#7FA6D8", "tooltip": [{"id": "Type", "accessorKey": "type", "isExtra": true}, {"id": "Host Criticality Tier", "accessorKey": "host_criticality_tier", "isExtra": true}, {"id": "Host Meet Security Posture", "accessorKey": "host_meet_security_posture", "isExtra": true}]}}, "Application": {"attribute": "activity_status", "configs": {"Active": {"attributeList": [{"id": "<PERSON><PERSON><PERSON>", "accessorKey": "app_vendor", "iconName": "icon-appvendor", "name": "Application Vendor", "disableHeading": true, "children": [{"id": "Application", "accessorKey": "app_name", "iconName": "icon-appname", "name": "Application Name", "data_structure": "list", "disableHeading": true, "isActive": true, "tooltip": [{"id": "Application Version", "accessorKey": "app_version"}, {"id": "Type", "accessorKey": "type"}, {"id": "Count of Host Hosting Application", "accessorKey": "running_on_host_count"}]}]}, {"id": "Application", "accessorKey": "app_name", "iconName": "icon-appname", "name": "Application Name", "data_structure": "list", "disableHeading": true, "isActive": true, "tooltip": [{"id": "Application Version", "accessorKey": "app_version"}, {"id": "Type", "accessorKey": "type"}, {"id": "Count of Host Hosting Application", "accessorKey": "running_on_host_count"}], "children": [{"id": "Details", "iconName": "icon-details", "name": "Application Category", "accessorKey": "category", "tooltip": [{"id": "Application Operational Status", "accessorKey": "operational_status"}, {"id": "Activity Status", "accessorKey": "activity_status"}]}, {"id": "Lifecycle", "iconName": "icon-applifecycle", "name": "Application Lifecycle", "accessorKey": "lifecycle", "tooltip": [{"id": "Retired Date", "accessorKey": "retired_date", "isDate": true}]}, {"id": "Risk Info", "iconName": "icon-riskcategory", "name": "Application Risk Category", "accessorKey": "risk_category", "tooltip": [{"id": "Internet Facing", "accessorKey": "internet_facing"}, {"id": "Sensitive Information", "accessorKey": "sensitive_information"}, {"id": "Criticality", "accessorKey": "derived_criticality"}, {"id": "Count Of Open Vulnerabilities", "accessorKey": "has_open_vulnerability_finding_count"}]}]}, {"id": "Details", "iconName": "icon-details", "name": "Application Category", "accessorKey": "category", "tooltip": [{"id": "Application Operational Status", "accessorKey": "operational_status"}, {"id": "Activity Status", "accessorKey": "activity_status"}]}, {"id": "Lifecycle", "iconName": "icon-applifecycle", "name": "Application Lifecycle", "accessorKey": "lifecycle", "tooltip": [{"id": "Retired Date", "accessorKey": "retired_date", "isDate": true}]}, {"id": "Risk Info", "iconName": "icon-riskcategory", "name": "Application Risk Category", "accessorKey": "risk_category", "tooltip": [{"id": "Internet Facing", "accessorKey": "internet_facing"}, {"id": "Sensitive Information", "accessorKey": "sensitive_information"}, {"id": "Criticality", "accessorKey": "derived_criticality"}, {"id": "Count Of Open Vulnerabilities", "accessorKey": "has_open_vulnerability_finding_count"}]}]}, "Inactive": {"attributeList": [{"id": "<PERSON><PERSON><PERSON>", "accessorKey": "app_vendor", "iconName": "icon-appvendor", "name": "Application Vendor", "disableHeading": true, "children": [{"id": "Application", "accessorKey": "app_name", "iconName": "icon-appname", "name": "Application Name", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Application Version", "accessorKey": "app_version"}, {"id": "Type", "accessorKey": "type"}, {"id": "Count of Host Hosting Application", "accessorKey": "running_on_host_count"}]}]}, {"id": "Application", "accessorKey": "app_name", "iconName": "icon-appname", "name": "Application Name", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Application Version", "accessorKey": "app_version"}, {"id": "Type", "accessorKey": "type"}, {"id": "Count of Host Hosting Application", "accessorKey": "running_on_host_count"}], "children": [{"id": "Details", "iconName": "icon-details", "name": "Application Category", "accessorKey": "category", "tooltip": [{"id": "Application Operational Status", "accessorKey": "operational_status"}, {"id": "Activity Status", "accessorKey": "activity_status"}]}, {"id": "Lifecycle", "iconName": "icon-applifecycle", "name": "Application Lifecycle", "accessorKey": "lifecycle", "tooltip": [{"id": "Retired Date", "accessorKey": "retired_date", "isDate": true}]}, {"id": "Risk Info", "iconName": "icon-riskcategory", "name": "Application Risk Category", "accessorKey": "risk_category", "tooltip": [{"id": "Internet Facing", "accessorKey": "internet_facing"}, {"id": "Sensitive Information", "accessorKey": "sensitive_information"}, {"id": "Criticality", "accessorKey": "derived_criticality"}, {"id": "Count Of Open Vulnerabilities", "accessorKey": "has_open_vulnerability_finding_count"}]}]}, {"id": "Details", "iconName": "icon-details", "name": "Application Category", "accessorKey": "category", "tooltip": [{"id": "Application Operational Status", "accessorKey": "operational_status"}, {"id": "Activity Status", "accessorKey": "activity_status"}]}, {"id": "Lifecycle", "iconName": "icon-applifecycle", "name": "Application Lifecycle", "accessorKey": "lifecycle", "tooltip": [{"id": "Retired Date", "accessorKey": "retired_date", "isDate": true}]}, {"id": "Risk Info", "iconName": "icon-riskcategory", "name": "Application Risk Category", "accessorKey": "risk_category", "tooltip": [{"id": "Internet Facing", "accessorKey": "internet_facing"}, {"id": "Sensitive Information", "accessorKey": "sensitive_information"}, {"id": "Criticality", "accessorKey": "derived_criticality"}, {"id": "Count Of Open Vulnerabilities", "accessorKey": "has_open_vulnerability_finding_count"}]}]}, "default": {"attributeList": [{"id": "<PERSON><PERSON><PERSON>", "accessorKey": "app_vendor", "iconName": "icon-appvendor", "name": "Application Vendor", "disableHeading": true, "children": [{"id": "Application", "accessorKey": "app_name", "iconName": "icon-appname", "name": "Application Name", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Application Version", "accessorKey": "app_version"}, {"id": "Type", "accessorKey": "type"}, {"id": "Count of Host Hosting Application", "accessorKey": "running_on_host_count"}]}]}, {"id": "Application", "accessorKey": "app_name", "iconName": "icon-appname", "name": "Application Name", "data_structure": "list", "disableHeading": true, "tooltip": [{"id": "Application Version", "accessorKey": "app_version"}, {"id": "Type", "accessorKey": "type"}, {"id": "Count of Host Hosting Application", "accessorKey": "running_on_host_count"}], "children": [{"id": "Details", "iconName": "icon-details", "name": "Application Category", "accessorKey": "category", "tooltip": [{"id": "Application Operational Status", "accessorKey": "operational_status"}, {"id": "Activity Status", "accessorKey": "activity_status"}]}, {"id": "Lifecycle", "iconName": "icon-applifecycle", "name": "Application Lifecycle", "accessorKey": "lifecycle", "tooltip": [{"id": "Retired Date", "accessorKey": "retired_date", "isDate": true}]}, {"id": "Risk Info", "iconName": "icon-riskcategory", "name": "Application Risk Category", "accessorKey": "risk_category", "tooltip": [{"id": "Internet Facing", "accessorKey": "internet_facing"}, {"id": "Sensitive Information", "accessorKey": "sensitive_information"}, {"id": "Criticality", "accessorKey": "derived_criticality"}, {"id": "Count Of Open Vulnerabilities", "accessorKey": "has_open_vulnerability_finding_count"}]}]}, {"id": "Details", "iconName": "icon-details", "name": "Application Category", "accessorKey": "category", "tooltip": [{"id": "Application Operational Status", "accessorKey": "operational_status"}, {"id": "Activity Status", "accessorKey": "activity_status"}]}, {"id": "Lifecycle", "iconName": "icon-applifecycle", "name": "Application Lifecycle", "accessorKey": "lifecycle", "tooltip": [{"id": "Retired Date", "accessorKey": "retired_date", "isDate": true}]}, {"id": "Risk Info", "iconName": "icon-riskcategory", "name": "Application Risk Category", "accessorKey": "risk_category", "tooltip": [{"id": "Internet Facing", "accessorKey": "internet_facing"}, {"id": "Sensitive Information", "accessorKey": "sensitive_information"}, {"id": "Criticality", "accessorKey": "derived_criticality"}, {"id": "Count Of Open Vulnerabilities", "accessorKey": "has_open_vulnerability_finding_count"}]}]}}, "relationshipGraph": {"color": "#CDB99C", "tooltip": [{"id": "Application Vendor", "accessorKey": "app_vendor", "isExtra": true}, {"id": "Application version", "accessorKey": "app_version", "isExtra": true}]}}, "Identity": {"attribute": "type", "configs": {"Person": {"attributeList": [{"id": "Identity Provider", "accessorKey": "identity_provider", "iconName": "icon-<PERSON><PERSON><PERSON><PERSON>", "name": "Identity Provider", "disableHeading": true, "children": [{"id": "Identity Type", "accessorKey": "type", "iconName": "icon-identitytype", "name": "Type"}]}, {"id": "Identity Type", "accessorKey": "type", "iconName": "icon-identitytype", "name": "Type", "children": [{"id": "Ownership", "accessorKey": "ownership", "iconName": "icon-ownership", "name": "Ownership", "disableHeading": true}]}, {"id": "Ownership", "accessorKey": "ownership", "iconName": "icon-ownership", "name": "Ownership", "disableHeading": true, "children": [{"id": "Identity", "accessorKey": "display_label", "iconName": "icon-identity", "name": "Display Label", "tooltip": [{"accessorKey": "user_principal_name", "id": "User Principal Name"}, {"accessorKey": "email_id", "id": "Email ID"}]}]}, {"id": "Identity", "accessorKey": "display_label", "iconName": "icon-identity", "name": "Display Label", "tooltip": [{"accessorKey": "user_principal_name", "id": "User Principal Name"}, {"accessorKey": "email_id", "id": "Email ID"}]}]}, "Non-Person": {"attributeList": [{"id": "Identity Provider", "accessorKey": "identity_provider", "iconName": "icon-<PERSON><PERSON><PERSON><PERSON>", "name": "Identity Provider", "disableHeading": true, "children": [{"id": "Identity Type", "accessorKey": "type", "iconName": "icon-identitytype", "name": "Type"}]}, {"id": "Identity Type", "accessorKey": "type", "iconName": "icon-identitytype", "name": "Type", "children": [{"id": "Ownership", "accessorKey": "ownership", "iconName": "icon-ownership", "name": "Ownership", "disableHeading": true}]}, {"id": "Ownership", "accessorKey": "ownership", "iconName": "icon-ownership", "name": "Ownership", "disableHeading": true, "children": [{"id": "Identity", "accessorKey": "display_label", "iconName": "icon-identity", "name": "Display Label", "tooltip": [{"accessorKey": "user_principal_name", "id": "User Principal Name"}, {"accessorKey": "email_id", "id": "Email ID"}]}]}, {"id": "Identity", "accessorKey": "display_label", "iconName": "icon-identity", "name": "Display Label", "tooltip": [{"accessorKey": "user_principal_name", "id": "User Principal Name"}, {"accessorKey": "email_id", "id": "Email ID"}]}]}, "default": {"attributeList": [{"id": "Identity Provider", "accessorKey": "identity_provider", "iconName": "icon-<PERSON><PERSON><PERSON><PERSON>", "name": "Identity Provider", "disableHeading": true, "children": [{"id": "Identity Type", "accessorKey": "type", "iconName": "icon-identitytype", "name": "Type"}]}, {"id": "Identity Type", "accessorKey": "type", "iconName": "icon-identitytype", "name": "Type", "children": [{"id": "Ownership", "accessorKey": "ownership", "iconName": "icon-ownership", "name": "Ownership", "disableHeading": true}]}, {"id": "Ownership", "accessorKey": "ownership", "iconName": "icon-ownership", "name": "Ownership", "disableHeading": true, "children": [{"id": "Identity", "accessorKey": "display_label", "iconName": "icon-identity", "name": "Display Label", "tooltip": [{"accessorKey": "user_principal_name", "id": "User Principal Name"}, {"accessorKey": "email_id", "id": "Email ID"}]}]}, {"id": "Identity", "accessorKey": "display_label", "iconName": "icon-identity", "name": "Display Label", "tooltip": [{"accessorKey": "user_principal_name", "id": "User Principal Name"}, {"accessorKey": "email_id", "id": "Email ID"}]}]}}, "relationshipGraph": {"color": "#9D15D6", "tooltip": [{"id": "Type", "accessorKey": "type", "isExtra": true}, {"id": "Identity Provider", "accessorKey": "identity_provider", "isExtra": true}, {"id": "User Principal Name", "accessorKey": "user_principal_name", "isExtra": true}, {"id": "Activity Status", "accessorKey": "activity_status", "isExtra": true}]}}, "Account": {"attribute": "type", "configs": {"Person": {"attributeList": [{"id": "Account Type", "accessorKey": "type", "iconName": "icon-accounttype", "name": "Type", "children": [{"id": "Service", "accessorKey": "service", "iconName": "icon-service", "name": "Service", "disableHeading": true}]}, {"id": "Service", "accessorKey": "service", "iconName": "icon-service", "name": "Service", "disableHeading": true, "children": [{"id": "Account Name", "accessorKey": "account_name", "iconName": "icon-account", "name": "Account Name", "disableHeading": true, "tooltip": [{"accessorKey": "is_mfa_enabled", "id": "MFA Enabled"}, {"accessorKey": "privilege_account", "id": "Privilege Account"}, {"accessorKey": "last_active_date", "id": "Last Active Date", "isDate": true}, {"accessorKey": "account_status", "id": "Account Status"}]}]}, {"id": "Account Name", "accessorKey": "account_name", "iconName": "icon-account", "name": "Account Name", "disableHeading": true, "tooltip": [{"accessorKey": "is_mfa_enabled", "id": "MFA Enabled"}, {"accessorKey": "privilege_account", "id": "Privilege Account"}, {"accessorKey": "last_active_date", "id": "Last Active Date", "isDate": true}, {"accessorKey": "account_status", "id": "Account Status"}], "children": [{"id": "Entitlement", "accessorKey": "entitlement_value", "iconName": "icon-entitlement", "name": "Entitlement Value"}]}, {"id": "Entitlement", "accessorKey": "entitlement_value", "iconName": "icon-entitlement", "name": "Entitlement Value"}]}, "Non-Person": {"attributeList": [{"id": "Account Type", "accessorKey": "type", "iconName": "icon-accounttype", "name": "Type", "children": [{"id": "Service", "accessorKey": "service", "iconName": "icon-service", "name": "Service", "disableHeading": true}]}, {"id": "Service", "accessorKey": "service", "iconName": "icon-service", "name": "Service", "disableHeading": true, "children": [{"id": "Account Name", "accessorKey": "account_name", "iconName": "icon-account", "name": "Account Name", "disableHeading": true, "tooltip": [{"accessorKey": "is_mfa_enabled", "id": "MFA Enabled"}, {"accessorKey": "privilege_account", "id": "Privilege Account"}, {"accessorKey": "last_active_date", "id": "Last Active Date", "isDate": true}, {"accessorKey": "account_status", "id": "Account Status"}]}]}, {"id": "Account Name", "accessorKey": "account_name", "iconName": "icon-account", "name": "Account Name", "disableHeading": true, "tooltip": [{"accessorKey": "is_mfa_enabled", "id": "MFA Enabled"}, {"accessorKey": "privilege_account", "id": "Privilege Account"}, {"accessorKey": "last_active_date", "id": "Last Active Date", "isDate": true}, {"accessorKey": "account_status", "id": "Account Status"}], "children": [{"id": "Entitlement", "accessorKey": "entitlement_value", "iconName": "icon-entitlement", "name": "Entitlement Value"}]}, {"id": "Entitlement", "accessorKey": "entitlement_value", "iconName": "icon-entitlement", "name": "Entitlement Value"}]}, "default": {"attributeList": [{"id": "Account Type", "accessorKey": "type", "iconName": "icon-accounttype", "name": "Type", "children": [{"id": "Service", "accessorKey": "service", "iconName": "icon-service", "name": "Service", "disableHeading": true}]}, {"id": "Service", "accessorKey": "service", "iconName": "icon-service", "name": "Service", "disableHeading": true, "children": [{"id": "Account Name", "accessorKey": "account_name", "iconName": "icon-account", "name": "Account Name", "disableHeading": true, "tooltip": [{"accessorKey": "is_mfa_enabled", "id": "MFA Enabled"}, {"accessorKey": "privilege_account", "id": "Privilege Account"}, {"accessorKey": "last_active_date", "id": "Last Active Date", "isDate": true}, {"accessorKey": "account_status", "id": "Account Status"}]}]}, {"id": "Account Name", "accessorKey": "account_name", "iconName": "icon-account", "name": "Account Name", "disableHeading": true, "tooltip": [{"accessorKey": "is_mfa_enabled", "id": "MFA Enabled"}, {"accessorKey": "privilege_account", "id": "Privilege Account"}, {"accessorKey": "last_active_date", "id": "Last Active Date", "isDate": true}, {"accessorKey": "account_status", "id": "Account Status"}], "children": [{"id": "Entitlement", "accessorKey": "entitlement_value", "iconName": "icon-entitlement", "name": "Entitlement Value"}]}, {"id": "Entitlement", "accessorKey": "entitlement_value", "iconName": "icon-entitlement", "name": "Entitlement Value"}]}}, "relationshipGraph": {"color": "#9982BB", "tooltip": [{"id": "Service", "accessorKey": "service", "isExtra": true}, {"id": "Activity Status", "accessorKey": "activity_status", "isExtra": true}]}}, "Compliance Standard": {"attribute": "activity_status", "configs": {"Active": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "id", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "id", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control Count", "name": "Overall Security Control Count", "iconName": "icon-security-control", "accessorKey": "associated_security_control_count"}]}, {"id": "Security Control Count", "name": "Overall Security Control Count", "iconName": "icon-security-control", "accessorKey": "associated_security_control_count"}]}, "Inactive": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "id", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "id", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control Count", "name": "Overall Security Control Count", "iconName": "icon-security-control", "accessorKey": "associated_security_control_count"}]}, {"id": "Security Control Count", "name": "Overall Security Control Count", "iconName": "icon-security-control", "accessorKey": "associated_security_control_count"}]}, "default": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "id", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "id", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control Count", "name": "Overall Security Control Count", "iconName": "icon-security-control", "accessorKey": "associated_security_control_count"}]}, {"id": "Security Control Count", "name": "Overall Security Control Count", "iconName": "icon-security-control", "accessorKey": "associated_security_control_count"}]}}, "relationshipGraph": {"color": "#5313E6", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Type", "accessorKey": "type", "isExtra": true}]}}, "Security Control": {"attribute": "activity_status", "configs": {"Active": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control", "accessorKey": "id", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "tooltip": [{"accessorKey": "title", "id": "Title"}]}]}, {"id": "Security Control", "accessorKey": "id", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "tooltip": [{"accessorKey": "title", "id": "Title"}], "children": [{"id": "Assessment Count", "name": "Overall Assessment Count", "iconName": "icon-assessment", "accessorKey": "associated_assessment_count"}]}, {"id": "Assessment Count", "name": "Overall Assessment Count", "iconName": "icon-assessment", "accessorKey": "associated_assessment_count"}]}, "Inactive": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control", "accessorKey": "id", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "tooltip": [{"accessorKey": "title", "id": "Title"}]}]}, {"id": "Security Control", "accessorKey": "id", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "tooltip": [{"accessorKey": "title", "id": "Title"}], "children": [{"id": "Assessment Count", "name": "Overall Assessment Count", "iconName": "icon-assessment", "accessorKey": "associated_assessment_count"}]}, {"id": "Assessment Count", "name": "Overall Assessment Count", "iconName": "icon-assessment", "accessorKey": "associated_assessment_count"}]}, "default": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control", "accessorKey": "id", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "tooltip": [{"accessorKey": "title", "id": "Title"}]}]}, {"id": "Security Control", "accessorKey": "id", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "tooltip": [{"accessorKey": "title", "id": "Title"}], "children": [{"id": "Assessment Count", "name": "Overall Assessment Count", "iconName": "icon-assessment", "accessorKey": "associated_assessment_count"}]}, {"id": "Assessment Count", "name": "Overall Assessment Count", "iconName": "icon-assessment", "accessorKey": "associated_assessment_count"}]}}, "relationshipGraph": {"color": "#EF7F9B", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Type", "accessorKey": "type", "isExtra": true}, {"id": "Cloud Native Type", "accessorKey": "native_type", "isExtra": true}]}}, "Assessment": {"attribute": "activity_status", "configs": {"Active": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control", "accessorKey": "associated_controls", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true}]}, {"id": "Security Control", "accessorKey": "associated_controls", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "children": [{"id": "Assessment", "name": "Display Label", "iconName": "icon-assessment", "accessorKey": "display_label"}]}, {"id": "Assessment", "name": "Display Label", "iconName": "icon-assessment", "accessorKey": "display_label"}]}, "Inactive": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control", "accessorKey": "associated_controls", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true}]}, {"id": "Security Control", "accessorKey": "associated_controls", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "children": [{"id": "Assessment", "name": "Display Label", "iconName": "icon-assessment", "accessorKey": "display_label"}]}, {"id": "Assessment", "name": "Display Label", "iconName": "icon-assessment", "accessorKey": "display_label"}]}, "default": {"attributeList": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "iconName": "icon-provider", "name": "Cloud Provider", "disableHeading": true, "children": [{"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true}]}, {"id": "Compliance Standard", "accessorKey": "associated_standards", "iconName": "icon-compliance-standard", "name": "Compliance Standard", "disableHeading": true, "children": [{"id": "Security Control", "accessorKey": "associated_controls", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true}]}, {"id": "Security Control", "accessorKey": "associated_controls", "iconName": "icon-security-control", "name": "Security Control", "disableHeading": true, "children": [{"id": "Assessment", "name": "Display Label", "iconName": "icon-assessment", "accessorKey": "display_label"}]}, {"id": "Assessment", "name": "Display Label", "iconName": "icon-assessment", "accessorKey": "display_label"}]}}, "relationshipGraph": {"color": "#AD7444", "tooltip": [{"id": "Cloud Provider", "accessorKey": "cloud_provider", "isExtra": true}, {"id": "Associated Standard", "accessorKey": "associated_standards", "isExtra": true}, {"id": "Type", "accessorKey": "type", "isExtra": true}]}}}}