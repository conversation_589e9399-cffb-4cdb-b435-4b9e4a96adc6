{"name": "Application Running On Host", "caption": "Application Running On Host", "navigator_enabled": true, "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": false, "inverse_relationship_name": "Host Hosting Application", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "os_family": {"caption": "OS Family", "description": "Classifies the OS into its corresponding platforms such as Windows, Linux, macOS, Android, iOS.\nIt refers to a group of operating systems that share a common core or foundation.\nFor example 'Windows'.", "type": "string", "visualization_enabled": true}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "type": "string", "visualization_enabled": true}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "navigator_enabled": true}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the host has the application.", "type": "timestamp", "navigator_enabled": true}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the host has last observed application.", "type": "timestamp", "navigator_enabled": true}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which host has application.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "vulnerability_status": {"caption": "Vulnerability Status", "description": "Is vulnerability present in software or not.", "type": "string"}}, "target_entity": "Host", "dashboard_identifier": "EI", "source_entity": "Application"}