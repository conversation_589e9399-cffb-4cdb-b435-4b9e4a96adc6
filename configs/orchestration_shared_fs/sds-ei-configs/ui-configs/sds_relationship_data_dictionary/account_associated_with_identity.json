{"name": "Account Associated With Identity", "caption": "Account Associated With Identity", "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Identity Has Account", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. The logic is as follows: if the time since the last inventory update is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. For Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "type": "string"}, "operational_status": {"caption": "Operational Status", "description": "Operational Status of the entity derived from the source.", "type": "string"}, "ownership": {"caption": "Ownership", "description": "Specifies the ownership category of the Identity.", "type": "string"}, "identity_provider": {"caption": "Identity Provider", "description": "Information regarding the provider of identity.", "type": "string"}, "last_logged_in_location": {"caption": "Successful Login Location", "description": "Country from which latest successful login attempt was made.", "type": "string"}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the identity was first observed for the person.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the identity was last observed for the person.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the identity was seen for the person.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Identity", "dashboard_identifier": "EI", "source_entity": "Account"}