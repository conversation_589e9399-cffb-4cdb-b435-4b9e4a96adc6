{"name": "Assessment Measuring Security Control", "caption": "Assessment Measuring Security Control", "detail_view_caption": "Assessment Measuring Security Control", "sorting_columns": [{"field": "control_id", "type": "string", "desc": true}], "isInverse": false, "inverse_relationship_name": "Security Control Measured By Assessment", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "associated_standards": {"caption": "Associated Standards", "description": "Security Standards to which the control is associated with.", "type": "string"}}, "relationship_attributes": {"control_id": {"caption": "Control ID", "description": "Unique Identifier of the security control.", "type": "string"}, "relationship_origin": {"caption": "Origin", "description": "Sources in which the assessment to security control relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the assessment connected to security control.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the assessment to security control relation is inferred from data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the assessment belongs to security control.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}}, "target_entity": "Security Control", "dashboard_identifier": "EI", "source_entity": "Assessment"}