{"name": "Cloud Container Has Vulnerability Finding", "caption": "Cloud Container Has Vulnerability Finding", "detail_view_caption": "Cloud Container Has Open Vulnerability Finding", "sorting_columns": [{"field": "current_status", "type": "string", "desc": true}, {"field": "recency_relationship", "type": "integer", "desc": false}, {"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "customFilter": [{"filterType": "current_status", "filterData": ["Open"], "isExtra": false, "isDate": false}], "isInverse": true, "inverse_relationship_name": "Vulnerability Finding On Cloud Container", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "description": "Severity of the vulnerability.", "type": "string"}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the vulnerability was first seen on Cloud Container as inferred from data source.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the vulnerability was last seen on Cloud Container as inferred from data source.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "How long the vulnerability was present on the Cloud Container.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the vulnerability last observed on the Cloud Container.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "current_status": {"caption": "Finding Status", "description": "Current Status of the vulnerability on Cloud Container.", "type": "string"}, "initial_status": {"caption": "Initial Status", "description": "Initial Status of the vulnerability on Cloud Container.", "type": "string", "detailed_view_hide": true}, "software_vendor": {"caption": "Software Vendor", "description": "Software vendor is a company or individual that develops, sells, or licenses software products or services. These vendors can range from large multinational corporations to smaller boutique firms. They provide a variety of software solutions.", "type": "string", "detailed_view_hide": true}, "software_name": {"caption": "Software Name", "description": "A software name is a unique identifier or label given to a particular software application. It serves as a way to identify, reference, and distinguish the software from others.", "type": "string", "detailed_view_hide": true}, "software_version": {"caption": "Software Version", "description": "A software version is a unique identifier that indicates a specific release or iteration of a software application. It helps track changes, updates, and bug fixes made to the software over time.", "type": "string", "detailed_view_hide": true}, "software_full_name": {"caption": "Software", "description": "Concatenated form of Software Vendor, Software Name and Software Version", "type": "string"}, "software_product": {"caption": "Software Product", "description": "Concatenated form of Software Vendor and Software Name", "type": "string"}, "path_details": {"caption": "Path Details", "description": "Specify the exact path or location within the software/system where the vulnerability exists. This might include file paths, URL endpoints, or specific components/modules.", "type": "string", "data_structure": "list"}}, "target_entity": "Vulnerability", "dashboard_identifier": "EI", "source_entity": "Cloud Container"}