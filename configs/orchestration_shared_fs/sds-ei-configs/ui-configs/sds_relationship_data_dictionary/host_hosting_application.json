{"name": "Host Hosting Application", "caption": "Host Hosting Application", "sorting_columns": [{"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "isInverse": true, "inverse_relationship_name": "Application Running On Host", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "derived_criticality": {"caption": "Application Criticality", "description": "Defines the critical nature of application.\nIt is based on risk category.\nReturns true for CAT1 and CAT2 applications. Else False.", "visualization_enabled": true}, "app_vendor": {"caption": "Application Vendor", "description": "The name or identifier of the vendor or provider associated with the entity application.\nFor example McAfee Australia.", "type": "string", "visualization_enabled": true}, "app_version": {"caption": "Application Version", "description": "The information about the changes, updates, and improvements made in a specific version of software or an application.\nFor example '0.9.3-2ubuntu2.3'. ", "type": "string"}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list"}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the application started running on host.", "type": "timestamp"}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Latest time at which the application to host relation is observed in data.", "type": "timestamp"}, "lifetime_relationship": {"caption": "Duration", "description": "Duration in days for which the host is associated with application.", "type": "integer", "range_selection": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the relation observed.", "type": "integer", "range_selection": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "target_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "source_display_label": {"caption": "Target Display Label", "description": "Name of target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "vulnerability_status": {"caption": "Vulnerability Status", "description": "Is vulnerability present in software or not.", "type": "string"}}, "target_entity": "Application", "dashboard_identifier": "EI", "source_entity": "Host"}