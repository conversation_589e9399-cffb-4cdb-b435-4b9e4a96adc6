{"name": "Vulnerability Finding On Application", "caption": "Vulnerability Finding On Application", "navigator_enabled": true, "detail_view_caption": "Open Vulnerability Finding On Application", "sorting_columns": [{"field": "current_status", "type": "string", "desc": true}, {"field": "recency_relationship", "type": "integer", "desc": false}, {"field": "relationship_last_seen_date", "type": "timestamp", "desc": true}, {"field": "display_label", "type": "string", "desc": true}], "customFilter": [{"filterType": "current_status", "filterData": ["Open"], "isExtra": false, "isDate": false}], "isInverse": false, "inverse_relationship_name": "Application Has Vulnerability Finding", "entity_attributes": {"display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "type": "string"}, "type": {"caption": "Type", "description": "The specific type of the entity.", "type": "string", "data_structure": "list", "visualization_enabled": true}, "derived_criticality": {"caption": "Application Criticality", "description": "Defines the critical nature of application.\nIt is based on risk category.\nReturns true for CAT1 and CAT2 applications. Else False.", "visualization_enabled": true}, "app_vendor": {"caption": "Application Vendor", "description": "The name or identifier of the vendor or provider associated with the entity application.\nFor example McAfee Australia.", "type": "string", "visualization_enabled": true}, "app_version": {"caption": "Application Version", "description": "The information about the changes, updates, and improvements made in a specific version of software or an application.\nFor example '0.9.3-2ubuntu2.3'. ", "type": "string"}}, "relationship_attributes": {"relationship_origin": {"caption": "Origin", "description": "Sources in which the relation is observed.", "type": "string", "data_structure": "list", "navigator_enabled": true}, "relationship_first_seen_date": {"caption": "First Seen", "description": "Time at which the vulnerability was first seen on application as inferred from data source.", "type": "timestamp", "navigator_enabled": true}, "relationship_last_seen_date": {"caption": "Last Seen", "description": "Time at which the vulnerability was last seen on application as inferred from data source.", "type": "timestamp", "navigator_enabled": true}, "lifetime_relationship": {"caption": "Duration", "description": "How long the vulnerability was present on the application.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "recency_relationship": {"caption": "Recency", "description": "How long ago (in days) was the vulnerability last observed on the application.", "type": "integer", "range_selection": true, "navigator_enabled": true}, "relationship_fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of a relationship.", "type": "integer", "range_selection": true, "is_enrichment": true}, "source_display_label": {"caption": "Source Display Label", "description": "Name of the source entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "target_display_label": {"caption": "Target Display Label", "description": "Name of the target entity as obtained from source data.", "type": "string", "detailed_view_hide": true}, "current_status": {"caption": "Finding Status", "description": "Current Status of the vulnerability on application.", "type": "string", "navigator_attribute_distinct_values": ["Closed", "Open"], "navigator_enabled": true}, "initial_status": {"caption": "Initial Status", "description": "Initial Status of the vulnerability on application.", "type": "string", "detailed_view_hide": true, "navigator_enabled": true}, "software_vendor": {"caption": "Software Vendor", "description": "Software vendor is a company or individual that develops, sells, or licenses software products or services. These vendors can range from large multinational corporations to smaller boutique firms. They provide a variety of software solutions.", "type": "string"}, "software_name": {"caption": "Software Name", "description": "A software name is a unique identifier or label given to a particular software application. It serves as a way to identify, reference, and distinguish the software from others.", "type": "string"}, "software_version": {"caption": "Software Version", "description": "A software version is a unique identifier that indicates a specific release or iteration of a software application. It helps track changes, updates, and bug fixes made to the software over time.", "type": "string"}, "path_details": {"caption": "Path Details", "description": "Specify the exact path or location within the software/system where the vulnerability exists. This might include file paths, URL endpoints, or specific components/modules.", "type": "string", "data_structure": "list", "navigator_enabled": true}}, "target_entity": "Application", "dashboard_identifier": "EI", "source_entity": "Vulnerability"}