{"caption": "Person", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "navigator_enabled": true, "navigator_description": "\n# Person Table Summary\n\n## Overview\nThe Person table provides a comprehensive repository of all individual personnel associated with the organization, tracking employees, contractors, and other human resources across their entire lifecycle. It integrates data from HR systems, identity platforms, and access management solutions to create a unified view of personnel, their attributes, relationships, and inherent risk factors.\n\n## Data Categories\n\n### Personnel Identification\n- **Personal Details**: Full names, email addresses, phone numbers, physical addresses\n- **Employee Information**: Employee IDs, job titles, job functions, position IDs\n- **Organizational Attribution**: Company affiliation, legal entity, cost center\n- **Contact Information**: Corporate and external email addresses, phone numbers\n\n### Employment Context\n- **Employment Classification**: Types (Permanent, Contractor, Intern, Fixed Term)\n- **Employment Status**: Active or terminated state\n- **Hierarchical Position**: Employee levels (L1-L8) and organizational structure\n- **Job Details**: Titles, functions, positions, and organizational units\n\n### Professional Relationships\n- **Management Structure**: Direct manager relationships\n- **System Relationships**: Hosts owned, identities associated\n- **Organizational Placement**: Department, business unit, cost center\n\n### Lifecycle Information\n- **Employment Timeline**: Recruitment dates, contract end dates, termination dates\n- **Activity Tracking**: First seen, last active, last login dates\n- **Status Assessment**: Current activity status, employment status\n- **Duration Metrics**: Lifetime and observed lifetime calculations\n\n### Geographic Context\n- **Location Information**: Country and city placement\n- **Regional Association**: Physical or assigned location\n\n### Risk Assessment\n- **Employee Type Risk**: Risk evaluation based on employment classification\n- **Employee Level Risk**: Risk stratification based on organizational level\n- **Status-Based Risk**: Evaluation based on active vs. terminated status\n- **Identity Complexity Risk**: Assessment based on number of associated identities\n- **Composite Risk Scoring**: Consolidated person_inherent_score based on multiple factors\n\n### Authentication & Access\n- **Login Information**: Last login dates, password usage\n- **Identity Associations**: Linked identity counts and relationships\n- **System Access**: Related systems and platforms\n\n### Data Provenance\n- **Source Systems**: Original data sources (BambooHR, SuccessFactors, Azure AD, etc.)\n- **Data Integration**: Fragment counts, source attribution\n- **Origin Classification**: Unique vs. corroborated data points\n- **Data Verification**: Data source subset names and API feeds\n\n## Key Features\n\n1. **Personnel Lifecycle Management**: Complete tracking from recruitment through termination\n\n2. **Multi-Source Integration**: Consolidation of data from HR, identity, and access management systems\n\n3. **Organizational Context**: Detailed mapping of departmental and hierarchical relationships\n\n4. **Risk-Based Personnel Assessment**: Multi-dimensional risk scoring for personnel prioritization\n\n5. **Relationship Mapping**: Connections to hosts, identities, and systems\n\n6. **Employment Classification Insights**: Detailed categorization by employment type and status\n\n7. **Temporal Tracking**: Historical visibility of personnel activity and status changes\n\nThis Person table serves as a critical foundation for personnel risk assessment, access governance, and identifying potential insider threats. It enables security teams to prioritize attention based on employee levels, employment status, identity complexity, and other risk factors.\n", "navigator_graph_node_description": "The Person entity represents an individual within an organization, focusing on their employment details, role, organizational relationships, and asset ownership. It is essential for HR, compliance, security, and asset management.Key attributes include personal identification (Employee ID, Name, Email), job details (title, department, business unit), employment status (hire date, termination date), and reporting structure (manager details).It tracks ownership of hosts and assets, system interactions, and risk factors based on role sensitivity. Data comes from HR systems, directory services, and asset management tools. This entity ensures personnel tracking without overlapping with identity authentication.", "navigator_entity_description": "A Person in cybersecurity represents an individual within an organization, focusing on their employment, role, organizational relationships, and asset ownership. The Person entity is primarily relevant to HR, compliance, organizational security, and asset management, rather than technical authentication or system-level access control.\\n\\n    The Person entity tracks an individual's employment-related attributes, including their job function, department, and interactions with enterprise assets, ensuring comprehensive oversight of personnel within the organization.\\n    \\n    A Person entity does NOT encompass system authentication, access permissions, or authorization processes.\\n    \\n    Key Attributes Defining a Person:\\n    Personal Identification:\\n    A Person is uniquely identified through HR and administrative attributes that distinguish them within the organization.\\n    \\n    Examples: employee_id, full_name, email, workday_id, payroll_number, contact_number.\\n    Not Included: System-based access identifiers such as aad_object_id, sam_account_name, oauth_client_id.\\n    Employment & Organizational Context:\\n    Attributes related to an individual's role and position within the company, such as:\\n    \\n    Job-related details: job_title, employment_type (permanent/contract), department, business_unit, cost_center.\\n    Hierarchy: Reporting relationships (e.g., manager details), organizational affiliations, and cost center allocations.\\n    Not Included: Login credentials, multi-factor authentication (MFA) settings, or sign-in activity.\\n    Lifecycle Events:\\n    Tracks employment status and key dates:\\n    \\n    Relevant attributes: hire_date, employment_status (active/terminated), termination_date, first_seen_date.\\n    Not Included: Authentication timestamps, last login activity, or system-based expiration policies.\\n    Asset Ownership & Interaction:\\n    A Person is responsible for assets such as:\\n    \\n    Assigned devices, workstations, and company-issued resources.\\n    Direct linkage to organizational resources and ownership accountability.\\n    Not Included: Access roles or permissions to digital resources.\\n    System Interaction (Non-Technical):\\n    Tracks personnel interactions from an organizational perspective, including:\\n    \\n    Engagement with company systems at a high level (e.g., policy compliance, mandatory training).\\n    Not Included: Authentication logs, session history, or security event monitoring.\\n    Risk Profiling:\\n    Risk is assessed based on:\\n    \\n    Job role sensitivity, access to confidential business data, and potential insider threats.\\n    Not Included: Security posture, identity risk scores, or anomalous login behavior.\\n    Data Sources:\\n    A Person\\u2019s information is sourced from HR and administrative systems such as:\\n    \\n    HRIS (Workday, SAP), directory services (Azure AD profile, LDAP), and asset management systems.\\n    Not Included: IAM platforms like Azure AD or AWS IAM focusing on authentication and authorization.\\n    Common Ways a Person Is Referred To:\\n    By laypeople: \\'employee,\\' \\'staff member,\\' \\'team member,\\' \\'users.\\'\\n    By HR teams: \\'employee profile,\\' \\'personnel record.\\'\\n    By IT teams: \\'user account,\\' \\'personnel record.\\'\\n    By security teams: \\'user entity,\\' \\'individual identity profile.\\'\\n    Key Differentiation from Identity:\\n    A Person focuses on employment, role, and organizational context, while Identity focuses solely on system access, authentication, and security aspects. Queries related to the following topics should be classified under Person, NOT Identity:\\n    \\n    Employment-related queries: Job title, department, employment status, and historical employment records.\\n    Asset ownership: Devices assigned to individuals, their responsibility over resources.\\n    Organizational structure: Reporting lines, department affiliation, and business units.\\n    Work history: Tracking former employees and their past interactions.\\n    Compliance and HR tracking: Employee onboarding/offboarding, adherence to security policies.\\n    \\n\\n    To prevent confusion with Identity, the following aspects are NOT part of the Person entity:\\n    \\n    Authentication and authorization: MFA setup, login credentials, access roles, permissions.\\n    Sign-in behavior: Last login timestamps, account lockout events, anomalous sign-in detection.\\n    IAM-related queries: Azure AD, AWS IAM, and related access management platforms.\\n    Device vulnerabilities and host-based security assessments.\\n    IMPORTANT:Active users in a directory service mean Person entity\\n\\n    Additionally,Person table contains below details for each Person/Employee/Owner/Indivitual\\n    - **owns_host_count (without filter,becase there is so many categories this only give sum of all)**: The count of hosts/machines/devices associated/owned by a Person/Employee/Owner/Indivitual,this field will give how many hosts/machines/devices are associated with a Person/Employee/Owner/Indivitual\\n    - **has_identity_count (without filter,becase there is so many categories like active,inavtive this only give sum of all)**: The number of identities associated with a Person/Employee/Owner/Indivitual, this field will give how many identities are associated with a Person/Employee/Owner/Indivitual\\n    - if user asking about max/high/largest min/least/lower or filter like less than greater than of above (host count/identity count of person) return Person only, dont take above additional attribute for get total count of owners/identities because redundancy\\n        - When a user queries for maximum, minimum, highest, lowest, or filtered values (e.g., greater than or less than) related to the above quantitative attributes, results should focus only on the Person entity without including other entities for that specific part of the query. However, for other aspects of the query, additional entities may be included as needed. \\n        - The above quantitative attributes should not be used when calculating the total count of hosts or identities to avoid redundancy and we dont have *sum* operation support", "navigator_examples": ["User Query: 'Show all data analysts' Output: 'person'", "User Query: 'Find all terminated users' Output: 'person'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Entity Inventory.\nIt is generated using the primary key, origin, class, and the attribute name of the primary_key, as obtained from the data.", "group": "common", "enable_hiding": true, "type": "string", "category": "General Information", "navigator_attribute_description": "Unique identifier for each Person, created from the primary key, origin, class, and attribute name.", "navigator_attribute_sample_data_values": ["d2f1b226534cb5507aa6ca3eae9ff33890bb920187534bc6bae51a9fab10fcf9", "5eccf898016b59d5dd3dfaff0ef010575a425c7d21c177cdbf4e3b3604c51dbb", "81c9f93f0da01673fb50753d46f2c1dfc67114277fc412fe21b9f7349b1b3ff7", "5060139f05197c4e9b43152d4aef0fc2c2de279b3956ba3074faced2a067dea2", "1a13202a93861aad63a6d0179c7b2f5a01447515d6fb4a30f464db7339b5ca0f", "fb262bd80e59699106e60998aa3bbee05c15bc040c40caaee582f9855305d2fa", "a531983d1acac38537afb641f18c736c99faf0472bcf7b018444716b0a538f83", "576fb622d18ad9e7ebb6b3fe3ccc2198034ec607a902033228f498114a3b4b62", "ff02441ab0a26796803468fa1053c2495736313d10f8c5319ed2390df24d4c7a", "de20b1c6520dcb8dda64427ec84f8706bda2addbc15489c79efd15a04a67a1f1"]}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.\nFor Person Entity fullname and primary_key  are some of the attributes that contribute to display label.", "group": "common", "enable_hiding": false, "type": "string", "navigator_attribute_description": "The display label is the best known identifier, uniquely identifying Person like a person's fullname or primary_key.(UPPERCASE)", "navigator_attribute_sample_data_values": ["JEFFREY J KENNEDY", "DEBRA D ROGERS", "AUSTIN A RYAN", "JOSHUA J SALINAS", "DEBORAH D TAYLOR", "SANDRA S HALL", "JUSTIN J EVANS", "SUSAN S OSBORNE", "KIMBERLY K HOLT", "ANTHONY A CAMPOS"]}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself.\nExamples include Host, Person, Vulnerability etc.", "group": "common", "enable_hiding": false, "type": "string", "category": "General Information"}, "type": {"caption": "Type", "description": "The specific classifications of person that helps to manage access, roles, and responsibilities within various systems or organizations effectively.\nPerson entity encompass types such as Permanent, Contract, Member, Guest, and others.", "group": "common", "enable_hiding": false, "type": "string", "navigator_attribute_description": "The type attribute classifies persons into roles like Permanent, Contract, Member, and Guest for managing access and responsibilities within systems.", "navigator_attribute_distinct_values": ["Permanent", "Guest", "Contractor", "Member", "Fixed Term", "Contract", "Intern"], "navigator_examples": ["Permanent", "Contractor", "Fixed Term", "Intern"], "navigator_deep_thinking_categorical": true}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.\nFrom Person entity data sources are AWS IAM,BambooHR,MS Azure AD,MS Intune,SuccessFactors,MS Active Directory etc.", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Origin refers to the data sources where the Person was present, such as AWS IAM, BambooHR, MS Azure AD, and MS Intune.", "navigator_attribute_sample_data_values": [["BambooHR", "MS Active Directory", "MS Azure AD", "AWS IAM Users", "AWS IAM Center"], ["MS Azure AD"], ["SuccessFactors", "MS Azure AD"], ["BambooHR", "MS Active Directory"], ["SuccessFactors"], ["BambooHR"], ["MS Active Directory"]]}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity is extracted.\nFor instance, in the case of a host, the count_of_origin signifies the total number of data sources from which that specific host is resolved or identified.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "The count_of_origin refers to the number of data sources used to identify an Person, such as the total sources for a host."}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nIt is the actual api name from which the data is ingested.\nFor example  MS Azure AD Users.", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "The data_source_subset_name refers to the API feed used for data ingestion, such as MS Azure AD Users.", "navigator_attribute_sample_data_values": [["SuccessFactors", "BambooHR", "MS Active Directory", "MS Intune", "MS Azure AD Users", "MS Azure AD Registered Users", "MS Azure AD Sign-in Logs", "AWS IAM Center"]]}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "enable_hiding": false, "type": "timestamp", "navigator_attribute_description": "First found date is the earliest date an Person was discovered in the ingested data during an inventory run."}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.\nThis is the earliest date across all data sources which are contributing to first seen date from each data sources.", "group": "common", "enable_hiding": false, "type": "timestamp", "navigator_attribute_description": "The first_seen_date is the earliest observation date of the Person from all contributing data sources."}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\\nDefaults to First Found.\\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "enable_hiding": false, "derived_field": true, "navigator_attribute_description": "Most recent date an Person was updated, defaulting to the first found, triggered by changes to any relevant attribute."}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\n.This will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "enable_hiding": false, "type": "timestamp", "navigator_attribute_description": "The last_found_date is the most recent date the Person was observed in the ingested data during the inventory run."}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "derived_field": true, "enable_hiding": false, "navigator_attribute_description": "The last active date is the most recent date an Person engaged, derived from sources like activity logs and event timestamps."}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nThe logic is as follows: If the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. When there is no information available to determine the activity of person, its status is recorded as No Data.\nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "derived_field": true, "enable_hiding": false, "type": "string", "category": "General Information", "navigator_attribute_description": "The activity status indicates whether an Person is active or inactive based on the last inventory update and last active dates, with a 2-day inactivity period for Cloud sources and 180 days for others.", "navigator_attribute_distinct_values": ["Active", "Inactive"]}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.\nThis represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "navigator_attribute_description": "Lifetime is the total active duration in days, calculated from the First Seen Date to the Last Active Date."}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of days since the Person's last activity, calculated from the difference between the last inventory update date and the last active date."}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Observed lifetime is the number of days an Person was present in data sources, calculated from the first to the last found date."}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Recency measures the number of days since the Person was last discovered, calculated from the difference between the last inventory update date and the last found date."}, "description": {"caption": "Description", "description": "Detailed description of the person.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "navigator_attribute_description": "A comprehensive overview of the individual's characteristics and background."}, "business_unit": {"caption": "Business Unit", "description": "A business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "Organisational Details", "navigator_attribute_description": "A business unit is a department or team in an organization focused on specific functions, products, or markets.", "navigator_attribute_sample_data_values": ["Product Development", "Network Management", "Business Development", "InfoSec", "Platform & Product", "Project Management", "Executive Management", "System Administration", "Research & Development", "Platform Engineer"], "navigator_examples": ["Product Management", "Project Management", "Network Management", "Network Engineering", "Business Development", "InfoSec", "IT Support", "Sales & Marketing", "HR", "Project Execution and Monitoring", "Product Development", "Research & Development", "Product Innovation", "DevOps", "Housekeeping", "Finance Management", "Prevalent AI India Private Ltd", "Finance", "Cloud Services Management", "Executive Management", "Prevalent AI Ltd (UK)", "System Administration", "Employee Training", "Incident Response and Management", "Platform & Product", "Digital Marketing", "Customer Relationship Management", "Risk Assessment and Mitigation", "Platform Engineering"], "navigator_deep_thinking_categorical": true}, "location_country": {"caption": "Location Country", "description": "Location country of the person.\nFor example 'South Africa'.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "navigator_attribute_description": "Country of the person's location, such as 'South Africa'.", "navigator_attribute_distinct_values": ["United Kingdom", "United Kingdom of Great Britain and Northern Ireland", "India"], "navigator_examples": ["United Kingdom of Great Britain and Northern Ireland", "India"], "navigator_deep_thinking_categorical": true}, "location_city": {"caption": "Location City", "description": "Location city of the person.\nFor example 'Sydney'.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "navigator_attribute_description": "City where the person is located, such as 'Sydney'.", "navigator_attribute_distinct_values": ["Manchester", "Belfast", "Cochin", "Glasgow", "London", "Cardiff"], "navigator_examples": ["Cardiff", "Glasgow", "Belfast", "Manchester", "Cochin", "London"], "navigator_deep_thinking_categorical": true}, "department": {"caption": "Department", "description": "Name of the department within the business unit.\nFor example 'Product Management'.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "navigator_attribute_description": "Department refers to the specific area within a business unit, such as 'Product Management'.", "navigator_attribute_sample_data_values": ["Database Administration", "Service Desk", "Product Management", "Housekeeping", "Business Support", "Management", "Product Strategy and Planning", "SDS Academy 2023", "Product Development", "Solution Delivery"], "navigator_examples": ["Product Development", "Project Execution and Monitoring", "Product Innovation", "Network Engineering", "Project Planning and Scheduling", "Product Strategy and Planning", "Business Development", "Incident Response and Management", "System Administration", "Customer Relationship Management", "Database Administration", "Digital Marketing", "Recruitment", "Business Partner", "Employee Training", "Information Security", "Technology Research", "Risk Assessment and Mitigation", "Service Desk", "Cloud Services Management", "Onboarding and Offboarding", "Housekeeping", "Tax and Compliance", "Client Delivery", "Accounts", "Solution Development", "Finance", "Business Support", "Platform Engineering", "Board of Directors", "Client Solutions", "Product Management", "Architecture", "Platform & Product", "Solution Delivery", "Marketing", "Management", "Board", "Engineering", "SDS Academy 2023"], "navigator_deep_thinking_categorical": true}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "navigator_attribute_description": "Fragments refer to the count of partial records or evidence for an Person, determined by the number of resolved sources."}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.If any of this field gets updated then that entity got a change.\nFor example host_name from host entity,job title from person entity,cve_id from vulnerability entity.", "group": "common", "enable_hiding": true, "type": "string", "ui_visibility": false, "data_structure": "struct"}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.\nFor example 180 days.", "group": "common", "ui_visibility": false, "enable_hiding": true, "type": "integer"}, "has_identity_count": {"caption": "Count of Identities", "description": "Number of identities associated with the Person.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "The number of identities associated with a person.", "navigator_deep_thinking_numerical": true}, "owns_host_count": {"caption": "Count of Owns Host", "description": "Number of hosts owned by the Person.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of hosts/devices/machines owned by the person/employee", "navigator_deep_thinking_numerical": true}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "enable_hiding": true, "type": "string", "navigator_attribute_description": "Origin can be either unique or corroborated.", "navigator_attribute_distinct_values": ["Corroborated", "Unique"]}, "full_name": {"caption": "Full Name", "description": "Full name of the person.\nIt is a combination of first name,middle name(if applicable) and last name.\nFor example '<PERSON><PERSON>'.", "group": "entity_specific", "type": "string", "category": "Personal Information", "navigator_attribute_description": "Full name is the complete name of a person, including first name, middle name (if any), and last name, such as '<PERSON><PERSON>'.", "navigator_attribute_sample_data_values": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, "first_name": {"caption": "First Name", "description": "First name of the person obtained from fullname.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "middle_name": {"caption": "Middle Name", "description": "Middle name of the person obtained from fullname.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "last_name": {"caption": "Last Name", "description": "Last name of the person obtained from fullname.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "company": {"caption": "Company", "description": "Name of the Company an employee belongs to.\nFor example 'Google','Skrill'.", "group": "entity_specific", "type": "string", "category": "Employment Details", "navigator_attribute_description": "Name of the company an employee belongs to", "navigator_attribute_distinct_values": ["Prevalent AI", "acna"]}, "employee_id": {"caption": "Employee ID", "description": "Employee ID collected from multiple sources.\nIt is a unique identifier assigned to person within an organization for tracking and administrative purposes.", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "category": "Employment Details", "navigator_attribute_description": "Employee ID is a unique identifier for tracking individual/person/employee within an organization, collected from various sources."}, "employee_status": {"caption": "Employee Status", "description": "Employee status of person defines whether the employee is active or terminated from a company,based on the status value.\nFor example 'Terminated'.", "group": "entity_specific", "type": "string", "category": "Employment Details", "navigator_attribute_description": "Employee status indicates if a person is active or terminated, such as 'Terminated'.", "navigator_attribute_distinct_values": ["Active", "Terminated"], "navigator_deep_thinking_categorical": true}, "employee_level": {"caption": "Employee Level", "description": "Employee level of person defines the hierarchical level of the employee within the organization.\nFor example 'L1'.", "group": "entity_specific", "type": "string", "category": "Employment Details", "navigator_attribute_description": "Employee level indicates an employee's hierarchical position in the organization, such as 'L1'.", "navigator_attribute_distinct_values": ["L5", "L1", "L3", "L8", "L2", "NA", "L6", "L7", "L4"], "navigator_examples": ["L1", "L3", "L4", "L2", "L5", "L6", "NA", "L7", "L8"]}, "employment_type": {"caption": "Employment Type", "description": "Employment type defines the classification of Person's work arrangement with the organization .\nFor example 'Permanent'.", "group": "entity_specific", "type": "string", "category": "Employment Details", "navigator_attribute_description": "Employment type classifies a person's work arrangement with the organization, such as 'Permanent'.", "navigator_attribute_distinct_values": ["Contractor", "Intern", "Fixed Term", "Permanent"], "navigator_examples": ["Permanent", "Contractor", "Fixed Term", "Intern"], "navigator_deep_thinking_categorical": true}, "email_id": {"caption": "Email ID", "description": "Email ID also known as an email address, is a unique identifier assigned to an individual for sending and receiving electronic messages over the internet.\nAn email ID is a combination of a unique username and a domain name, separated by the '@' symbol.\nFor example '<EMAIL>'.", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "category": "Personal Information", "navigator_attribute_description": "Email ID's of the person/employee,provided in array format", "navigator_attribute_sample_data_values": [["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"], ["<EMAIL>"]]}, "manager": {"caption": "Manager", "description": "Name of the manager.\nManager is a designated individual within a company who oversees and directs the work, performance, and development of the employee under their supervision.", "group": "entity_specific", "type": "string", "category": "Job Details", "navigator_attribute_description": "Manager name of the person/employee", "navigator_attribute_sample_data_values": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]}, "manager_id": {"caption": "Manager ID", "description": "ID assigned to the Manager.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "recruit_date": {"caption": "Recruited On", "description": "The recruit date of an employee, also known as the hire date or start date, refers to the specific calendar date on which the individual officially began their employment with company.", "group": "entity_specific", "type": "timestamp", "category": "Employment Details", "navigator_attribute_description": "The recruit date, or hire/start date, is the specific calendar date when an person/employee officially began their employment with the company."}, "last_known_termination_date": {"caption": "Last Known Termination Date", "description": "Termination date of the person.\nMost recent date at which an employee's active employment status with a company ended.", "group": "entity_specific", "type": "timestamp", "category": "Employment Details", "ui_visibility": true}, "contract_end_date": {"caption": "Contract End", "description": "Date when an employee's contract expires.\nIt is the predetermined end date of the employment contract or agreement between an employee and their employer.", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "The contract_end_date is the predetermined expiration date of an person/employee's employment agreement."}, "job_title": {"caption": "Job Title", "description": "Specific designation or role that the employee holds within a company.\nFor example 'Data Scientist'.", "group": "entity_specific", "type": "string", "category": "Job Details", "navigator_attribute_description": "Job title refers to the specific designation or role of an person/employee, such as 'Data Scientist'.", "navigator_attribute_sample_data_values": ["Senior Data Scientist & Client Solutions Consultant", "Lead Security Domain Analyst", "Data Analyst", "Senior Security Domain Analyst", "Client Solutions Manager", "Senior UI Engineer", "Junior Data Scientist", "Director Of Engineering", "Senior Domain Analyst", "DevOps Engineer"], "navigator_examples": ["Backend Developer", "Full Stack Developer", "Quality Assurance Engineer", "DevOps Engineer", "Product Analyst", "Network Engineering", "UI\\UX Designer", "Project Manager", "Frontend Developer", "Product Manager", "Sales Development Representative", "SOC Analyst", "System Administration", "Salesforce Developer", "Machine Learning Engineer", "Database Administration", "Copy writers", "Rec<PERSON>er", "HR BP", "Product Designer", "Intern", "Network Security Specialist", "Research Associate", "Data Analyst", "VAPT Analyst", "Client Delivery Lead", "IT Support Specialist", "Cloud Engineer", "HR", "Attendant", "Web Designer", "Auditor", "Product Strategist", "Cyber Security Specialist", "Junior Security Domain Analyst", "Sales and Marketing Manager", "Finance Officer", "Senior UI Engineer", "Lead Domain Analyst", "Lead QA Engineer", "Junior QA Engineer", "BI Analyst", "Finance Contractor", "Finance Manager", "Dev Ops Engineer", "Cloud Architect", "Business Analyst", "UI Engineer", "UI Architect", "Board Member", "Junior Data Engineer", "Client Solutions Director", "Data Engineer", "UX/UI Designer", "QA Engineer", "Administration", "Senior Data Engineer", "Director Of Engineering", "Junior Data Scientist", "Solution Owner", "Junior UI Engineer", "Data Scientist", "Cyber Solutions Architect", "Domain Analyst", "Client Delivery Director", "Lead UI Engineer", "Junior Security Engineer", "Senior Business Analyst", "Senior DevOps Engineer", "Senior HR Business Partner", "Junior DevOps Engineer", "Head of Engineering", "Dev Ops Architect", "Architect", "Lead Data Engineer", "Splunk Architect", "Junior Domain Analyst", "Lead Security Domain Analyst", "Scrum Master", "VP People and Business Support", "Senior UX/UI Designer", "Senior QA Engineer", "Tableau Developer", "Director of Product Management", "Big Data Architect", "Junior Support Engineer", "Senior Data Scientist", "Administration Manager", "VP - Client Solutions", "Security Domain Analyst", "Junior Site Reliability Engineer", "Senior Human Resource Manager", "Senior Site Reliability Engineer", "Quality Assurance Intern", "Lead - Visual Analytics", "Research & Marketing Manager", "Chief Technical Officer", "Supervisor", "Lead Data Scientist", "Chief Executive Officer", "Senior Data Scientist & Client Solutions Consultant", "Junior Data Analyst", "Lead Data Analyst", "HR Business Partner", "Senior Recruiter", "Client Solutions Manager", "Junior Business Analyst", "Chairman", "Senior Security Data Scientist", "Consultant", "In house Recruiter", "Junior UX Designer", "Solution Development Lead", "Senior Domain Analyst", "Human Resources Manager", "Chairman of the Board", "Senior Security Domain Analyst", "Technical Architect", "Security Architect", "Visualization Intern", "Security Engineer", "Senior Data Analyst", "Chief Operating Officer", "Solution Development Director", "Chief Operations Officer", "UX Consultant", "Administrative Associate", "Senior Tableau Developer", "Data Architect", "Non-Executive Director", "Principal Architect", "Chief Information Security Officer", "Head of Architecture", "HR Executive - Talent Acquisition", "Lead Information Security", "Senior Delivery Manager"]}, "job_position_id": {"caption": "Job Position ID", "description": "Job position ID of the employee.\nUnique identifier that distinguishes one job position from another within an organization's hierarchy or structure.", "group": "entity_specific", "type": "string", "category": "Job Details", "navigator_attribute_description": "Unique identifier for distinguishing person/employee job positions within an organization's hierarchy.", "navigator_attribute_sample_data_values": ["4360", "9219"]}, "job_function": {"caption": "Job Function", "description": "Job function details of an employee.\nIt describes the primary activities and responsibilities that an employee is expected to perform as part of their role within an organization.\nFor example 'Account Excecutive','Business Development Officer'.", "group": "entity_specific", "type": "string", "category": "Job Details", "navigator_attribute_description": "Job function refers to the primary activities and responsibilities of an person/employee, such as 'Account Executive' or 'Business Development Officer'.", "navigator_attribute_sample_data_values": ["Engineering", "Core HR", "Domain Analysis", "Quality Assurance", "Data Science", "Business Analysis", "Data Analysis", "Dev Ops Engineering", "Product", "UI Engineering"], "navigator_examples": ["Domain Analysis", "UI Engineering", "Quality Assurance", "Finance", "Dev Ops Engineering", "Data Engineering", "Client Solutions", "Administration", "Data Science", "Client Delivery Management", "Infosec", "Business Analysis", "Core HR", "Engineering", "Product", "Systems Engineering", "Management", "Data Analysis", "Talent Acquisition", "Architecture"]}, "legal_entity": {"caption": "Legal Entity", "description": "Defines the country and default Pay Group, Location, currency, and standard hours for employees within that company.\nFor example 'Aviation & Travel Credit Risk'.", "group": "entity_specific", "type": "string", "category": "Organisational Details", "navigator_attribute_description": "Defines the country, default Pay Group, Location, currency, and standard hours for employees, such as in 'Aviation & Travel Credit Risk'.", "navigator_attribute_distinct_values": ["acna_BG"]}, "organisation_unit_id": {"caption": "Organisation Unit ID", "description": "ID of the organisation unit.\nRepresents the organizational unit or department to which the individual belongs within an organization.\nFor example 'E-cash: card (PSC)','Google 4 Business (P4B)'.", "group": "entity_specific", "type": "string", "category": "Organisational Details", "navigator_attribute_description": "ID representing the organizational unit or department, such as 'E-cash: card (PSC)' and 'Google 4 Business (P4B)'."}, "cost_center": {"caption": "Cost Center", "description": "Department or function in which the costs occur inside an organization.\nExamples are 'Business Development (1020)','Communications (OP_42)'.", "group": "entity_specific", "type": "string", "category": "Organisational Details", "navigator_attribute_description": "Cost center refers to a department or function within an organization where costs are incurred, such as 'Business Development (1020)' and 'Communications (OP_42)'.", "navigator_attribute_distinct_values": ["ORG-Global(3011)", "ORG-Global(3013)", "ORG-Global(5015)", "ORG-Global(2001)", "ORG-Global(3012)"]}, "login_last_date": {"caption": "Last Login", "description": "Date at which the person was last logged in.\nIt is the last login activity performed by the person on a particular system or application.", "group": "entity_specific", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "Date of the person's last login activity on a system or application."}, "termination_date": {"caption": "Terminated On", "description": "Termination date of the person.\nMost recent date at which an employee's active employment status with a company ended.", "group": "entity_specific", "type": "timestamp", "category": "Employment Details", "navigator_attribute_description": "Termination date marks the last day an person/employee was actively employed by a company."}, "external_email_id": {"caption": "External Email ID", "description": "External email ID of the employee.\nIt is the email address associated with an individual outside of the organization.\nFor example '<EMAIL>'.", "group": "entity_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "External email ID of the employee, such as '<EMAIL>', is the email address associated with individuals outside the organization.", "navigator_attribute_sample_data_values": [["<EMAIL>"], ["ramirezhernandez.98@nlsa.Acna_Global.com", "laura.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@Acna_Global.com"]]}, "phone_number": {"caption": "Phone Number", "description": "Phone number of an employee.\nThis number is associated with the employee's desk phone, mobile phone, or other telecommunication devices.", "group": "entity_specific", "type": "string", "category": "Personal Information", "navigator_attribute_description": "Phone number of an employee.", "navigator_attribute_sample_data_values": ["+44 20 5056 2033", "+44 20 5422 2521"]}, "address": {"caption": "Address", "description": "Address of an employee.\nIt is the physical location where the employee resides or the mailing address associated with their place of residence or work.\nExample is '159 Vision Park'.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Address refers to the physical or mailing location of an employee, such as '159 Vision Park'."}, "password_last_used": {"caption": "Last Password Used Date", "description": "Latest date at which the password is used.", "examples": ["1518723532000", "1642105144000"], "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "Latest date the password was used, indicating the last login or access time."}, "ad_operational_status": {"caption": "AD Operational Status", "description": "Refers to the current operational status of an object within Active Directory (AD). This attribute indicates whether the user is active or disabled.", "group": "source_specific", "type": "string", "category": "General Information", "derived_field": true, "ui_visibility": true, "to_be_deprecated": true}, "ad_created_date": {"caption": "AD Created", "description": "Date when an object, such as a user account or a group, was created within Active Directory (AD).", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when an person's user account was created in Active Directory (AD)."}, "ad_last_sync_date": {"caption": "AD Last Sync date", "description": "Latest date when the Active Directory (AD) data was last synchronized with the account.It provides visibility into when the last synchronization occurred, allowing administrators to track changes, troubleshoot synchronization issues, and maintain data integrity.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Latest date of Active Directory data synchronization, indicating the last update of person's information."}, "ad_last_password_change_date": {"caption": "AD Last Password Change Date", "description": "Latest date when the password associated with an Active Directory (AD) user account was last changed.When a user changes their password, either voluntarily or as part of a mandatory password policy, this field gets updated to reflect the timestamp of the most recent password change.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The ad_last_password_change_date is the timestamp of the most recent password change for a person's Active Directory user account "}, "earliest_ad_account_disabled_date": {"caption": "Earliest AD Account Disabled Date", "description": "The Earliest date when an Active Directory (AD) user account was disabled in the scope of data captured.", "group": "source_specific", "type": "string", "ui_visibility": false}, "ad_account_disabled_date": {"caption": "AD Account Disabled Date", "description": "Date when an Active Directory (AD) user account was disabled.When an AD user account is disabled, it means that the account is no longer active and cannot be used to log in or access resources within the Active Directory domain.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when an person's Active Directory user account was disabled"}, "account_enabled_status": {"caption": "Account Enabled Status", "description": "The Status of Account whether it is enabled or disabled. Values extracted in this field will be True or False", "group": "source_specific", "type": "string", "ui_visibility": false}, "aad_operational_status": {"caption": "AAD Operational Status", "description": "Refers to the operational status of a user account in Azure Active Directory (AAD). Indicates whether the user account is active, disabled.", "group": "source_specific", "type": "string", "derived_field": true, "ui_visibility": false, "to_be_deprecated": true}, "aad_profile_type": {"caption": "AAD Profile Type", "description": "", "group": "source_specific", "type": "string", "derived_field": true, "ui_visibility": false, "to_be_deprecated": true}, "aad_created_date": {"caption": "AAD Created", "description": "Date when a user object was created in Azure Active Directory (AAD). In Azure AD, when a new user account is added to the directory, whether manually by an administrator or through automated provisioning processes, a timestamp is recorded to indicate when the user object was created.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The aad_created_date is the timestamp recorded when a person's user account is created in Azure Active Directory"}, "aad_deleted_date": {"caption": "AAD Deleted Date", "description": "Date when a user object was deleted from Azure Active Directory (AAD). It provides visibility into when a user account was removed from the directory, aiding in tracking user lifecycle events and ensuring proper data governance practices.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The aad_deleted_date indicates when a person's user account was removed from Azure Active Directory"}, "aad_user_id": {"caption": "AAD User ID", "description": "Unique identifier assigned to a user within Azure Active Directory (AAD).AAD user IDs are alphanumeric strings generated by Azure AD during user creation and are immutable, meaning they do not change over time.\nFor example '0d804c40-c98e-45a9-a0a1-cbbc6417bd42'.", "group": "source_specific", "type": "string", "candidate_key": true, "data_structure": "list", "navigator_attribute_description": "Azure Active Directory user id of the person/employee", "navigator_attribute_sample_data_values": [["8e8b70d2-ab32-4c37-ayzb-b1c12142089r", "user_8e8b70d2-ab32-4c37-ayzb-b1c12142089r"], ["8e8b70d2-ab32-4c37-ayzb-b1c12143889r", "user_8e8b70d2-ab32-4c37-ayzb-b1c12143889r"], ["user_8e8b70d2-ab32-4c37-ayzb-b1c12144820r"], ["8e8b70d2-ab32-4c37-ayzb-b1c12134870r", "user_8e8b70d2-ab32-4c37-ayzb-b1c12134870r"]]}, "aws_created_date": {"caption": "AWS Created Date", "description": "Date when a user object was created in AWS", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The aws_created_date is the date when a person's user object was created in AWS"}, "aws_iam_center_user_id": {"caption": "AWS IAM Center User Id", "description": "The user id for the IAM Center Serivce", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "sf_employee_status": {"caption": "SF Employee Status", "description": "SuccessFactors operational status of an entity to determine whether the employee is active or terminated.\nFor example Active.", "group": "source_specific", "type": "string", "navigator_attribute_description": "SuccessFactors status indicating if an employee/person status in succuess factor data source is active or terminated", "navigator_attribute_distinct_values": ["Terminated", "Active"]}, "iga_operational_status": {"caption": "IGA Operational Status", "description": "The current operational state from saviynt IGA to determine whether the account associated is active or disabled.\nFor example Active,Disabled.", "group": "source_specific", "type": "string", "derived_field": true, "ui_visibility": false, "to_be_deprecated": true}, "emp_level_score": {"caption": "Employee Level Risk Score", "description": "Risk Score derived for the Person based on the Employee Level.", "group": "enrichment", "type": "double", "ui_visibility": false, "to_be_deprecated": true}, "emp_type_score": {"caption": "Employee Type Risk Score", "description": "Risk Score derived for the Person based employee type.\nA contractor is considered to be of higher risk than permanent employee ", "group": "enrichment", "type": "double", "ui_visibility": false, "to_be_deprecated": true}, "identity_count_score": {"caption": "Employee Identity Count Risk Score", "description": "Risk Score derived for the Person based on the number of identities owned. Person with more number of identities are considered to be of higher risk", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "emp_status_score": {"caption": "Employee Status Risk Score", "description": "Risk Score derived for the Person based on the employment status.\nTerminated employee is considered to be of lower risk than active one with respect to access to company information.", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "person_inherent_score": {"caption": "Person Inherent Risk Score", "description": "Inherent Risk Score derived for the Person based on risk score associated to Person as below.\nEmployee Level:higher level employees are considered to have higher risk exposure.\nEmployee Type:A contractor is considered to be of higher risk than permanent employee.\nIdentity Count:Person with more number of identities are considered to be of higher risk.\nEmployee Status:Terminated employee is considered to be of lower risk than active one with respect to access to company information. ", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}}, "dashboard_identifier": "EI"}