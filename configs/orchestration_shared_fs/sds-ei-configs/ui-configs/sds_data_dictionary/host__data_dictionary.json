{"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "navigator_enabled": true, "navigator_constraints": "Don't include any 'type' filters when user asks just about hosts/devices/machines like show all devices/machines/hosts,only include them if specifically mentioned in user query like workstation/Mobile/Servers.\\n If user asks about exposed devices/hosts, provide hosts/devices with high exposure_score.\\n If user asks about EOL or End of Life devices, provide devices with end of life, older, or outdated operating systems from the list of available operating systems. \\n If the property is about domain joined hosts/devices, filter MS Active Directory as origin  \\n If the property is about rougue devices filter hosts which are not inventoried", "navigator_description": "\n# Host Table Summary\n\n## Overview\nThe Host table serves as a central repository for tracking and managing all compute devices across an organization's environment. It provides a unified view of servers, workstations, mobile devices, network appliances, and cloud-based virtual machines with comprehensive details about their configuration, security posture, and operational status.\n\n## Data Categories\n\n### Identity & Classification\n- **Hostnames & Identifiers**: Various name formats (FQDN, NetBIOS, DNS names) and unique identifiers\n- **IP & MAC Addresses**: Network identifiers and hardware addressing\n- **Resource IDs**: Cloud-specific instance and resource identifiers\n- **Hardware Details**: Serial numbers, IMEI, models, manufacturers, and chassis information\n- **Classification**: Host types (Server, Workstation, Mobile, Network, etc.)\n\n### Infrastructure Context\n- **Location Information**: Physical and logical placement (country, city, region)\n- **Network Context**: Domain membership, accessibility (internal/external)\n- **Infrastructure Type**: On-premise vs. cloud-based deployments\n- **Cloud-Specific Attributes**: Provider, account details, instance types, and resource information\n- **Virtualization Status**: Container hosting capabilities and relationships\n\n### Operating System Information\n- **OS Details**: Full OS name, family, version, architecture, and build information\n- **System Configuration**: BIOS details, operational parameters\n- **Boot Information**: Last reboot timestamps and operational history\n\n### Security Posture\n- **Security Agents**: Coverage status for EDR, antivirus, and vulnerability management tools\n- **Security Tool Status**: Last scan dates, signature updates, and operational status\n- **Compliance State**: MDM compliance status and security configurations\n- **Firewall & Protection**: Status of security controls and defensive capabilities\n- **Internet Exposure**: Accessibility from internet and exposure metrics\n- **Exposure Score**: Numerical value (0-1000) quantifying a host's accessibility to potential threats, with higher scores indicating greater risk. Tracks both active and inactive hosts, maintaining scores even after deactivation to identify hosts requiring remediation despite inactive status.\n\n### Activity & Lifecycle\n- **Temporal Information**: First seen, last found, and last active dates\n- **Activity Status**: Current operational state (active/inactive)\n- **Login Information**: Last login user and timestamp\n- **Lifecycle Metrics**: Lifetime duration and observed timeline\n- **Provisioning Status**: Deployment and operational state information\n\n### Management Coverage\n- **MDM Information**: Mobile device management enrollment, sync status\n- **Vulnerability Management**: Scanning methods and coverage details\n- **Security Product Coverage**: Types and statuses of security tools deployed\n\n### Security Posture Assessment\n- **Risk Scoring**: Host inherent risk scoring across multiple dimensions\n- **Exposure Metrics**: Measures of the host's exposure to potential threats\n- **Domain & Device Scoring**: Risk evaluations based on domain status and device type\n\n### Relationship Mapping\n- **Ownership Information**: Person-to-device relationships\n- **Identity Associations**: Linked user identities count\n- **Vulnerability Context**: Associated vulnerability findings (total and open)\n- **Application Relationships**: Hosted application counts and relationships\n- **Cloud Resource Connections**: Associated cloud infrastructure elements\n\n### Data Provenance\n- **Source Attribution**: Origin systems contributing data (MS Defender, CrowdStrike, Qualys, etc.)\n- **Data Source Details**: Specific API endpoints and data feeds\n- **Corroboration Information**: Whether data points are unique or corroborated across sources\n- **Fragment Counts**: Number of partial records resolved for an entity\n\n### Business Context\n- **Organizational Placement**: Business unit and department assignments\n- **Asset Purpose**: Functional role within the organization\n- **Asset Compliance Scope**: Regulatory frameworks (PCI DSS, SOX, etc.)\n- **Inventory Status**: Confirmation of proper asset recording\n\n## Key Features\n\n1. **Comprehensive Device Catalog**: Consolidates data about all compute devices regardless of type or location\n\n2. **Multi-Dimensional Security Assessment**: Combines security agent status, vulnerability data, compliance information, and exposure scoring\n\n3. **Cloud and On-Premise Integration**: Unifies data across traditional and cloud infrastructure\n\n4. **Risk-Based Prioritization**: Provides multiple scoring dimensions for risk-based security approaches, including exposure scores that persist for inactive devices\n\n5. **Relationship Context**: Maps connections between hosts and other entities (users, applications, vulnerabilities)\n\n6. **Temporal Tracking**: Maintains historical timeline of device lifecycle and activity\n\n7. **Security Posture Evaluation**: Assesses implementation of security controls and protective measures\n\n8. **Enriched Context**: Adds business relevance through criticality scores and compliance scope\n\nThis Host table functions as the foundation of the security analytics platform, providing the essential device context needed for vulnerability management, threat detection, compliance reporting, and overall security risk assessment.\n\nIMPORTANT: Query: Show devices with critical vulnerability, filter for host will be '' because there is no filter related to host in the query. End user host is a filter.\n", "navigator_graph_node_description": "The Host entity represents any computing device within an organization's IT infrastructure, including servers, workstations, virtual machines, network appliances, and cloud instances. It captures key attributes such as hostname, IP addresses, OS details, hardware specifications, unique identifiers (serial numbers, cloud instance IDs), and lifecycle timestamps (first seen, last active).Security attributes include installed security software, vulnerability status, compliance tracking, and integrations with tools like Defender, CrowdStrike, and Tenable. For cloud hosts, it records provider details (AWS, Azure), regions, and resource types. This entity helps assess security posture, risk, and compliance while enabling device monitoring and management.", "navigator_entity_description": "\\nA Host is a key entity in cybersecurity, representing an independent compute instance where visibility or management of the operating system is available. This includes a variety of devices such as servers, workstations, mobile devices, network appliances, and cloud-based virtual machines.  \\n\\nThe Host entity contains essential details about the device\\u2019s identity, configuration, security posture, and operational status. It includes attributes such as hostname, IP addresses, operating system information, hardware specifications, and unique identifiers like serial numbers or cloud instance IDs. It also tracks timestamps related to the host\\u2019s lifecycle, such as when it was first discovered, last seen, or last active.  \\n\\nSecurity-related attributes provide insight into the device\\u2019s protection status. These include details on installed security software like antivirus or EDR agents, vulnerability management data, and compliance with security policies. The entity aggregates information from various security tools and platforms such as Microsoft Defender, CrowdStrike, Qualys, and Tenable, offering a comprehensive security view.  \\n\\nFor cloud-based hosts, additional attributes include cloud provider-specific details like AWS or Azure account IDs, regions, and resource types, ensuring effective cloud security management.  \\n\\nThe Host entity serves as a central point for consolidating and correlating data from multiple sources, offering a holistic perspective on the IT infrastructure.  \\n\\nIn addition, the Host entity includes specific quantitative attributes:  \\n\\n- Number/Count of Owners: The total count of individuals associated with ownership or usage of the host, including all categories such as active, inactive, or permanent.  \\n- Number/Count of Identities: The total number of identities linked to the host, covering all states like active or inactive.  \\n- Number/Count of Applications Running on the Host: The total number of installed or actively running applications.  \\n- Number/Count of Cloud Resources Linked to the Host: The number of cloud-based assets associated with the host.  \\n- Number/Count of Vulnerability Findings: The total number of security vulnerabilities identified on the host.  \\n- Number/Count of Open Vulnerability Findings: The number of unresolved security vulnerabilities.  \\n    - When a user queries for maximum, minimum, highest, lowest, or filtered values (e.g., greater than or less than) related to the above quantitative attributes, results should focus only on the Host entity without including other entities for that specific part of the query. However, for other aspects of the query, additional entities may be included as needed. \\n    - The above quantitative attributes should not be used when calculating the total count of owners, identities, applications, or vulnerabilities to avoid redundancy and we dont have *sum* operation support.\\n", "navigator_examples": ["User Query: 'Show windows devices' Output: 'host'", "User Query: 'Show linux servers' Output: 'host'"], "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Entity Inventory.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data", "group": "common", "enable_hiding": true, "type": "string", "category": "General Information", "navigator_attribute_description": "Unique identifier for each host created from the primary key, origin, class, and attribute name."}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.\nFor Host Entity instance name, DNS name ,host name etc are some of the attributes that contribute to display label.  ", "group": "common", "enable_hiding": false, "type": "string", "category": "General Information", "navigator_attribute_description": "The display label is a unique identifier derived from attributes like Host Entity instance name, DNS name, and host name.", "navigator_attribute_sample_data_values": ["PAI-SOL-INTEGRATION-CAST-FF49EFA9", "WORK-AEH124263.ACNA.CORP.COM", "WORK-XNI641.ACNA.CORP.COM", "WORK-GFL494.ACNA.CORP.COM", "*********", "*********", "SOL-SDS3-PERFEXT-CAST-E8BD21F4", "WORK-YMB468.ACNA.CORP.COM", "WORK-SWP328223.ACNA.CORP.COM", "VM-TSR25761.ACNA.CORP.COM"]}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host, Person, Vulnerability etc.", "group": "common", "enable_hiding": false, "type": "string", "category": "General Information"}, "type": {"caption": "Type", "description": "The specific role of the entity based on its functionality within an organisation.\n\nPossible distinct values includes Server,Workstation,Network,Printer,Hypervisor,Mobile and Other.If Type information is not available, this field is marked as No Data.", "group": "common", "enable_hiding": false, "type": "string", "category": "General Information", "navigator_attribute_description": "The type attribute defines the category of host in an organization: Server, Workstation (also called Endpoint), Network, Printer, Hypervisor, Mobile, or Other. If Type information is not available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["Hypervisor", "Other", "Workstation", "Server", "Network", "Printer", "Mobile", "No Data"], "navigator_examples": ["Server", "Mobile", "Workstation", "Network", "Hypervisor", "Other", "Printer"], "navigator_deep_thinking_categorical": true}, "origin": {"caption": "Origin", "description": "Data source(s) from which the entity has been extracted.\nFor example AWS, Qualys etc.", "group": "common", "enable_hiding": false, "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Origin refers to the data sources from which the host entity records is extracted, such as AWS and Qualys.", "navigator_attribute_sample_data_values": [["MS Intune", "MS Azure AD"], ["MS Active Directory", "MS Azure AD", "ServiceNow ITSM"], ["Wiz"], ["MS Active Directory", "MS Intune", "Qualys", "Windows Security Logs", "MS Azure AD", "ServiceNow ITSM"]]}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of data sources, such as databases or APIs, from which the host entity records has been extracted."}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.\nIt is the actual api name from which the data is ingested.\n Some of the examples for data feed in Host entity are Qualys Host List, MS Azure AD Devices etc.", "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "The data_source_subset_name refers to the API feed used for data ingestion, such as Qualys Host List and MS Azure AD Devices.", "navigator_attribute_sample_data_values": [["MS Defender Device List", "MS Defender Device Software Inventory", "Qualys Host List", "Wiz Cloud Resource"], ["MS Active Directory", "CrowdStrike Host", "MS Azure AD Devices", "ServiceNow ITSM"], ["MS Active Directory", "CrowdStrike Host", "MS Azure AD Devices", "Tenable.sc Asset", "ServiceNow ITSM"], ["MS Active Directory", "MS Defender Device List", "MS Defender Device Software Inventory", "MS Defender Device TVM", "MS Intune", "Qualys Host List", "WinEvents 4624", "MS Azure AD Devices", "ServiceNow ITSM"]]}, "first_found_date": {"caption": "First Found", "description": "The date at which the entity was first observed in the ingested data.This will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "enable_hiding": false, "type": "timestamp", "navigator_attribute_description": "The first_found_date is the earliest observation of the host in the ingested data, marking the minimum time of its presence in the inventory run."}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.\n By default first seen date date is calculated based on the minimum value between the last active date and the first found date of the entity.\nIf the data sources provides information regarding first seen activity of the entity for example AD created date in Host, they take precedence over the default logic.", "group": "common", "enable_hiding": false, "type": "timestamp", "category": "General Information", "navigator_attribute_description": "The first_seen_date is the earliest observation date of an host, calculated as the minimum of the last active date and first found date, but takes precedence from specific data like AD created date in Hosts."}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "group": "common", "type": "timestamp", "enable_hiding": false, "navigator_attribute_description": "The last_updated_date is the most recent date an host was updated, based on changes to its relevant attributes."}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\n.This will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "enable_hiding": false, "type": "timestamp", "navigator_attribute_description": "The last_found_date indicates the most recent observation of the entity in the ingested data during the inventory run."}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which entity was active as inferred from available data sources.\nThis date is determined by considering the maximum value of dates contributed by each data source for the entity in question.\nThis includes data such as activity logs, event timestamps, or any other indicators of the entity's recent engagement or interaction.", "group": "common", "type": "timestamp", "category": "General Information", "enable_hiding": false, "navigator_attribute_description": "The last active date is the most recent date an host was active, determined by the maximum value from various data sources like activity logs and event timestamps."}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \nIf the difference between last inventory update date and last active date is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. When there is no information available to determine a host's activity, its status is recorded as No Data.\nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "enable_hiding": false, "category": "General Information", "navigator_attribute_description": "The activity status indicates if an host is active or inactive based on the last inventory and active dates, with a 2-day inactivity period for Cloud sources and 180 days for others. If no information is available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["Inactive", "Active"]}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "navigator_attribute_description": "Lifetime is the total span in days between the First Seen Date and Last Active Date of an host."}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source. ", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of days since the host's last activity, calculated from the difference between the last inventory update date and the last active date."}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Observed lifetime is the number of days an host was present in data sources, calculated from its first to last appearance."}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "navigator_attribute_description": "Recency indicates the number of days since the host was last discovered, calculated from the last inventory update date and the last found date in the ingested data."}, "description": {"caption": "Description", "description": "Detailed explanation of the asset.\nFor example it includes details about role served by the host,location details etc.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "navigator_attribute_description": "Comprehensive details about the host, including its role and location."}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets. If no information is available, this field is marked as No Data.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information", "navigator_attribute_description": "A business unit is a department or team in an organization responsible for specific functions, products, or markets. If no information is available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["Zone B Omega Systems", "Zone A wokstations", "Shared unity", "Zone A Server", "Zone B Workstations", "Global", "Production Server", "PriorityAccess", "Networking", "Zone A Protect", "No Data"], "navigator_examples": ["Production Server", "Zone A wokstations", "Global", "Networking", "Zone A Protect", "Zone B Omega Systems", "Zone B Workstations", "Zone A Server", "PriorityAccess", "Shared unity", "No Data"], "navigator_deep_thinking_categorical": true}, "location_country": {"caption": "Location Country", "description": "Specifies the country where the asset is located.\nFor example 'South Africa'.If the country information is not available, this field is marked as No Data.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "General Information", "navigator_attribute_description": "Specifies the country of the host's location, such as 'South Africa'. If the country information is not available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["India", "No Data"], "navigator_examples": ["India", "No Data"]}, "location_city": {"caption": "Location City", "description": "Specifies the city where the asset is located.\nFor example 'Sydney'.", "group": "common", "ui_visibility": true, "category": "General Information", "enable_hiding": true, "type": "string", "navigator_attribute_description": "Specifies the city of the host's location, such as 'Sydney'.", "navigator_attribute_distinct_values": ["Pune", "Mumbai"], "navigator_examples": ["Mumbai", "Pune"], "navigator_deep_thinking_categorical": true}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string", "navigator_attribute_description": "Department refers to the specific area within the business unit, such as Sales or Marketing."}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true, "navigator_attribute_description": "Fragments refer to the count of partial records or pieces of evidence for an host, determined by the number of sources resolved for that entity."}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "group": "common", "type": "string", "data_structure": "struct", "ui_visibility": false, "enable_hiding": true}, "has_identity_count": {"caption": "Count of Identities", "description": "Number of identities associated with Host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "Relationship Counts", "navigator_attribute_description": "Number of identities linked to the Host."}, "owned_person_count": {"caption": "Count of Owned Persons", "description": "Number of persons owning the host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "Relationship Counts", "navigator_attribute_description": "The owned_person_count refers to the number of individuals who own the host."}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "description": "Number of vulnerability findings associated with host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "Relationship Counts", "navigator_attribute_description": "Count of vulnerability findings linked to the host."}, "associated_cloud_resource_count": {"caption": "Count of Corresponding Cloud Resources", "description": "Number of cloud resources corresponding to the host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "Relationship Counts", "navigator_attribute_description": "Count of cloud resources linked to the host."}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "description": "Number of open vulnerability findings associated with host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "Relationship Counts", "navigator_attribute_description": "Count of open vulnerability findings linked to the host.", "navigator_deep_thinking_numerical": true}, "hosting_application_count": {"caption": "Count of Hosting Applications", "description": "Number of Applications running on Host.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "Relationship Counts", "navigator_attribute_description": "Count of applications running on the host.", "navigator_deep_thinking_numerical": true}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "enable_hiding": true, "type": "string", "navigator_attribute_description": "Indicates if the origin is unique or corroborated.", "navigator_attribute_distinct_values": ["Corroborated", "Unique"]}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "type": "integer", "enable_hiding": true}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "description": "Indicates the number of days after which the cloud entity become inactive.\nFor example 2 days.", "group": "common", "type": "integer", "ui_visibility": false, "enable_hiding": true}, "host_name": {"caption": "Hostname", "description": "Label assigned to a device on a computer network, used for its identification and management. This field facilitates user access to network resources. Example values include 'aks-agentpool-10218713-vmss000000' and 'HYD-JOHDOE-MOB'.\n", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Label assigned to a network-connected device for identification, such as 'HYD-JOHDOE-MOB'.", "navigator_attribute_sample_data_values": [["lserver-e3924a"], ["sol-sds3-perfext-cast-3f94d328"], ["pai-sol-dev-cast-a6c056f8"], ["pai-sol-qa-cast-edfee887"], ["vm-tsr67937"], ["pai-sol-qa-cast-e49b1c6c"], ["pai-demo-prod-cast-c9ad6cb4"], ["pai-sol-integration-cast-571aae04"], ["work-wzd619"], ["pai-sol-dev-cast-2cee3095"]]}, "fqdn": {"caption": "FQDN", "description": "The Fully Qualified Domain Name (FQDN) specifies the complete domain name for a computer, encompassing both its hostname and the domain name hierarchy. It provides an absolute path within the Domain Name System (DNS), ensuring precise identification of the device's location. For example, 'dc.prevalent.com' and 'ec2-13-201-152-15.ap-south-1.compute.amazonaws.com'.\n", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "category": "General Information", "navigator_attribute_description": "FQDN (Fully Qualified Domain Name) is the complete domain name for a host, such as \"hyd-johdoe-mob.corp.abcc.com,\" including its hostname and DNS hierarchy.", "navigator_attribute_sample_data_values": [["work-bdi491128.acna.corp.com"], ["work-pag240.acna.corp.com"], ["work-kne633.acna.corp.com"], ["server-kye698265.acna.corp.com"], ["work-miq665.acna.corp.com"], ["work-esq414.acna.corp.com"], ["work-ebz432.acna.corp.com"], ["server-enr418.acna.corp.com"], ["work-gzq503227.acna.corp.com"], ["work-syj357223.acna.corp.com"]]}, "domain": {"caption": "Domain", "description": "Domains represent the unique identifiers within the Domain Name System (DNS) that translate human-readable names into IP addresses. They can vary in specificity, encompassing top-level domains (TLDs) such as .com and .org, as well as subdomains and hostnames. For example, 'corp.abcd.com' illustrates a subdomain structure.\n", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "Domains are essential in the DNS for translating names like 'corp.abcd.com' into IP addresses, with levels including TLDs like .com, .org, and .net.", "navigator_attribute_distinct_values": ["acna.corp.com", "ap-south-1.compute.amazonaws.com", "prevalent.com.prevalent.com", "prevalent.com"]}, "asset_role": {"caption": "Asset Role", "description": "Describes the specific function or purpose of an asset within an organization.Contributed attributes are OS Family, Description, Type,Hostname, Asset Tag etc. Common roles include types such as 'Database', 'Production Server', and 'Domain Controller'. \n", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Asset role defines an host's function in an organization, such as Database, Production Server, Domain Controller, or Proxy, along with attributes like OS Family and Asset Tag.", "navigator_attribute_sample_data_values": [["Web Server", "Other"], ["Database", "ERP System", "Other"], ["Printer"], ["Jump Host", "Network Switch"], ["Other", "Router"], ["Domain Controller", "Network Switch"], ["Network Switch"], ["General Server", "Other"], ["FTP", "Other"], ["Other", "Cloud Instance"]]}, "ip": {"caption": "IP", "description": "An IP address is a unique numerical identifier assigned to devices on a network that uses the Internet Protocol for communication. This field contains a collection of IP addresses associated with the host, which may be sourced from various data points. Examples include '********' and '************'.\n", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Network", "navigator_attribute_description": "IP addresses of the host", "navigator_attribute_sample_data_values": [["************"], ["**************"], ["**************"], ["************"], ["**************"], ["*************"], ["**************"], ["**************", "**************"], ["**************"], ["*************"]]}, "dns_name": {"caption": "DNS Name", "description": "The DNS name is a human-readable label that identifies a specific resource on the Internet, functioning within the Domain Name System (DNS). It allows users to access resources using easily memorable names instead of numerical IP addresses. Example values include 'DC.prevalent.com' and 'ec2-13-201-152-15.ap-south-1.compute.amazonaws.com'.\n", "group": "entity_specific", "type": "string", "category": "Network", "data_structure": "list", "navigator_attribute_description": "A DNS name, like 'hyd-johdoe-mob.corp.prevalent.com', is a human-readable label that identifies a specific Internet location or resource within the Domain Name System.", "navigator_attribute_sample_data_values": [["vm-tsr30335.acna.corp.com"], ["server-caj713.acna.corp.com"], ["work-nkw448239.acna.corp.com"], ["ip-100-64-106-119.ap-south-1.compute.internal"], ["work-tkb267.acna.corp.com"], ["server-pfn341.acna.corp.com"], ["work-egy173.acna.corp.com"], ["ip-100-64-107-11.ap-south-1.compute.internal"], ["ip-100-64-72-248.ap-south-1.compute.internal"], ["server-ruo440.acna.corp.com"]]}, "netbios": {"caption": "NetBIOS", "description": "NetBIOS (Network Basic Input/Output System)  is a legacy networking protocol that facilitates communication between devices on a local area network (LAN), primarily in Windows environments. It enables computers to discover and interact with one another within the same network segment. Example: 'HYD-JOHDOE-MOB'.\n", "group": "entity_specific", "type": "string", "data_structure": "list", "candidate_key": true, "category": "Network", "navigator_attribute_description": "NetBIOS is a legacy networking protocol that enables device communication on a local area network, primarily in Windows environments, exemplified by names like 'HYD-JOHDOE-MOB'.", "navigator_attribute_sample_data_values": [["work-pst629"], ["server-sjm642"], ["server-sua356"], ["work-bcc499"], ["server-wsc869"], ["server-dtq540"], ["work-jfi274"], ["server-ibo347"], ["work-nbn657"], ["work-mcy977"]]}, "accessibility": {"caption": "Accessibility", "description": "Indicates the accessibility type of the host, specifying whether it is reachable from the public internet or restricted to internal network access. Possible values include \"External\" for hosts accessible to the public, such as web or email servers, and \"Internal\" for hosts that are only accessible within the organization's private network.\n", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "Accessibility defines if a host is externally facing (accessible from the public internet, like web or email servers) or internally facing (not accessible from the internet, meant for internal use).", "navigator_attribute_distinct_values": ["Internal", "External"]}, "mac_address": {"caption": "MAC Address", "description": "Unique identifier for a network interface controller (NIC) used for communication at the data link layer. This hardware address uniquely distinguishes each device on a network, such as computers, routers, or switches. \n\nExample: 00:1A:2B:3C:4D:5E.\n", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Network", "navigator_attribute_description": "MAC addresses of the host/device on a network, such as 00:1A:2B:3C:4D:5E.", "navigator_attribute_sample_data_values": [["00:00:97:52:A0:2C"], ["00:16:6F:2F:69:B9"], ["00:16:EB:6C:76:F2"], ["00:00:97:05:26:3A"], ["00:00:97:89:AA:E1"], ["00:00:97:E5:1C:D5"], ["00:00:97:C1:20:87"], ["00:00:97:0E:B1:CB"], ["00:00:97:78:1D:0E"], ["00:00:97:1D:0A:7D", "00:06:5B:34:F4:44"]]}, "mdm_mac_address": {"caption": "MDM MAC Address", "description": "The unique identifier for a mobile device, representing its Ethernet or Wi-Fi MAC address as collected from the Mobile Device Management (MDM) solution. This field is essential for tracking and managing devices within the network. Example: '0A:15:F3:85:B8:6A'.\n", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Network", "navigator_attribute_description": "MAC address of a managed mobile device, such as '0A15F385B86A', collected from MDM.", "navigator_attribute_sample_data_values": [["00:AA:01:C2:F7:4E"], ["00:02:B3:E1:8C:1B"], ["00:E0:4C:ED:73:FD"], ["00:20:7B:1F:B1:7B"], ["00:20:7B:E7:E3:FC"], ["00:AA:01:A0:A9:F7"], ["00:AA:01:BB:AE:0C"], ["00:16:EA:81:E5:D6", "00:02:B3:5F:16:31"], ["00:20:7B:0C:BD:3C"], ["00:20:7B:63:28:1B"]]}, "login_last_date": {"caption": "Last Login", "description": "The most recent date on which the host logged in.  For example, '1549879018000' corresponds to a specific login date.\n", "group": "entity_specific", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "The login_last_date represents the most recent date of user activity on the host, influenced by attributes like last logon and last signin."}, "login_last_user": {"caption": "Last Logged in User", "description": "The name of the last user who logged into the system. This field captures the username or identifier of the most recent user session. Examples include '<PERSON>' and 'SYST<PERSON>'.\n", "group": "entity_specific", "category": "General Information", "type": "string", "navigator_attribute_description": "Last user logged in to the host.", "navigator_attribute_sample_data_values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mistymcook", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "anneawebb", "stevensjordan", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "description": "The most recent date on which any vulnerability was detected on the host. This date signifies the last instance when the host was identified as being at risk of security threats or exploits.  \nExamples: '1705024143000', '1714419971000'\n", "group": "entity_specific", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "Latest date a vulnerability was active on the host, indicating susceptibility to security risks."}, "os": {"caption": "OS", "description": "The operating system that manages hardware components and resources of a computer system. Examples include 'Windows 10 Pro 10.0 (19041)' and 'Ubuntu 64-bit x64'.\n", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "Operating system (os) installed in host, such as 'Microsoft Windows 10 Enterprise LTSC 2019 64-bit'.", "navigator_attribute_sample_data_values": ["WindowsServer2016 14393 64-bit x64", "Windows 8.1 6.3", "Windows Vista 6", "macOS Monterey 12.6.5", "Windows 10.0.17763.0", "Ubuntu 18.0", "Windows 10 Pro 10.0 (19042)"], "navigator_examples": ["android 13", "Windows 10.0.19043.1889", "iOS 14", "macOS Mojave 10146.0", "macOS Big Sur", "macOS Monterey", "macOS Ventura", "Windows 10 Pro 10.0 (18362)", "Windows 10 1909 10.0.18363", "macOS High Sierra", "Windows 10 1809 IOT Enterprise 10.0.17763", "Ubuntu 22.0", "Windows Server 2008 6 6001 64-bit x64", "macOS High Sierra 10.13.6", "android 11", "Fortinet FortiOS", "Ubuntu 20.04.2", "macOS Catalina 10.15.7 10157 64-bit x64", "Windows 10 1507 10.0.10240 10240 64-bit x64", "Windows Server 2019 10.0.17", "Windows 10 1507 10.0.10240", "Windows Server 2016 10.0.14", "Windows 10 1809 10.0.17763", "Windows Server 2022 10.0.203", "macOS High Sierra 10.13.6 10136 64-bit x64", "Windows 10 1607 10.0.14393 14393 64-bit x64", "Ubuntu 20.04.2 20 64-bit x64", "Windows Server 2003 5.2 3790 64-bit x64", "Windows Server 2012 R2 6.3 9600 64-bit x64", "Windows 10 1909 IOT Enterprise 10.0.18363 18363 64-bit x64", "Windows 10 1809 IOT Enterprise 10.0.17763 17763 64-bit x64", "Windows Server 2008 R2 6.1 7600 64-bit x64", "Ubuntu 20.04", "Windows 10.0.19042.928", "Windows 10 Pro 10.0 (19043)", "Windows Server 2022 10.0.203 20348 64-bit x64", "Windows 10 21H1 10.0.19043 19043 64-bit x64", "macOS Mojave 10.14.6 10146 64-bit x64", "macOS Catalina", "Windows11 22621 64-bit x64", "iOS 12", "Windows 10 1607 10.0.14393", "Windows Server Core 2019 10.0.17", "Ubuntu 22.04 22 64-bit x64", "Windows Server 2008 R2 SP1 6.1.7 6003 64-bit x64", "Windows Server Core 2016 10.0.14 14393 64-bit x64", "Linux", "macOS Monterey 12.6.5 1266 64-bit x64", "iOS 15.1", "macOS Catalina 10157.0", "iOS 16", "Windows 7 Embedded 6.1 7602 64-bit x64", "Windows 10 1809 10.0.17763 17767 64-bit x64", "Windows Server 2012 6.2", "SonicOS", "Windows 11 21H2 10.0.19044", "macOS Sierra", "macOS", "iOS 15.5", "Other", "macOS Ventura 13.3", "WindowsServer2016 14393 64-bit x64", "Windows 10.0.19043.2193", "Windows 7 SP1 6.1 7601 64-bit x64", "Windows Server Core 2019 10.0.17 107 64-bit x64", "Windows 11 21H2 10.0.19044 438 64-bit x64", "macOS Big Sur 11.7.6 1177 64-bit x64", "Windows 7 6.1", "Windows XP 5.1 2600 64-bit x64", "Windows Server Core 2016 10.0.14", "Windows10 19045 64-bit x64", "Windows 10 21H2 10.0.19044 3086 64-bit x64", "Windows 10.0.22621.963", "Windows Storage Server 2012 R2 6.3 9700 64-bit x64", "Windows 10.0.22000.675", "Windows 10.0.22000.795", "Windows Server 2000 5 2195 64-bit x64", "Windows 10 1909 IOT Enterprise 10.0.18363", "Ubuntu 20.04.6", "Redhat 7", "Ubuntu 20.04 20 64-bit x64", "Windows 10.0.19043.1949", "Windows 8.1 6.3 9200 64-bit x64", "macOS Monterey 12.6.5", "Windows 10 20H2 10.0.19042", "macOS Mojave 10.14.6", "Windows Server Core 2022 10.0.203", "Windows 10.0.19044.1706", "android 12", "Windows Server 2012 6.2 9200 64-bit x64", "Windows10 19043 64-bit x64", "Windows 10 Pro 10.0 (19044)", "Windows 10.0.18363.0", "Windows Vista 6 6000 64-bit x64", "Windows Server 2008 6", "Ubuntu", "macOS Mojave", "Windows 10 Pro 10.0 (17134)", "Windows 10 Pro 10.0 (16299)", "macOS Sierra 10126.0", "Windows XP 5.1", "macOS Ventura 134.0", "Windows 7 SP1 6.1", "macOS Monterey 1266.0", "<PERSON>", "Android 14", "Windows Server 2000 5", "iOS 15", "macOS Sierra 10.12.6 10126 64-bit x64", "Juniper Junos", "Windows 8.1 6.3", "Windows 10 Pro 10.0 (18363)", "macOS High Sierra 10136.0", "Windows Server 2008 R2 6.1", "Cisco IOS", "Red Hat Enterprise Linux 8.6 8 64-bit x64", "Windows 10 Pro 10.0 (19041)", "Red Hat Enterprise Linux", "Windows Server 2003 5.2", "Windows Server Core 2022 10.0.203 20348 64-bit x64", "Windows 7 6.1 7600 64-bit x64", "Ubuntu Desktop Linux 14.04", "Debian", "Windows 10 20H2 10.0.19042 19042 64-bit x64", "macOS Big Sur 1177.0", "Windows 10 Pro 10.0 (19042)", "Windows 10 21H2 10.0.19044", "Windows Server 2016 10.0.14 14393 64-bit x64", "Windows 10 1909 10.0.18363 18363 64-bit x64", "iOS 13", "Windows 11 22H2 10.0.19044 1105 64-bit x64", "macOS Catalina 10.15.7", "Windows 10.0.22621.1265", "Windows 10 Pro 10.0 (17763)", "Ubuntu 22.04", "Ubuntu 18.04.6 18 64-bit x64", "macOS Ventura 13.3 134 64-bit x64", "Windows 10 Pro 10.0 (19045)", "macOS Big Sur 11.7.6", "Windows 11 21H2 10.0.19044 19044 64-bit x64", "Windows 10 21H1 10.0.19043 1766 64-bit x64", "iOS 11", "Debian 11.7 11 64-bit x64", "Ubuntu 20.04.6 20 64-bit x64", "Android 11", "Windows 11 22H2 10.0.19044", "Ubuntu 18.04.6", "Windows 10 21H2 10.0.19044 19044 64-bit x64", "Debian 10", "Windows Storage Server 2012 R2 6.3", "Windows Server 2012 R2 6.3", "Windows 7 Embedded 6.1", "Windows Vista 6", "Windows Server 2008 R2 SP1 6.1.7", "Windows11 22631 64-bit x64", "Windows 11 22H2 10.0.19044 19044 64-bit x64", "Windows", "Windows Server 2019 10.0.17 10 64-bit x64", "macOS Sierra 10.12.6", "Windows 10 21H1 10.0.19043", "Ubuntu 64-bit x64", "Ubuntu 20.0", "Windows11", "Windows 10.0.19044.1741", "Windows 10.0.19042.1348", "Windows 10.0.22000.318", "Windows10", "Windows 10.0.19044.1806", "Android 9", "Windows Server 2019 Standard 10.0 (17763)", "Windows 10.0.19045.3324", "Windows10 19044 64-bit x64", "Windows 10.0.22621.525", "EmbeddedOs", "macOS 64-bit x64", "WindowsServer2019 17763 64-bit x64", "Windows 10.0.22000.708", "Windows 10.0.17763.0", "iOS 17.5.1", "Android 13", "Ubuntu 18.0", "Windows 10.0.22621.1848", "Windows 10.0.19043.1266", "Windows 10.0.22621.1105"]}, "os_family": {"caption": "OS Family", "description": "Classifies the operating system into its respective family, indicating the platform it belongs to, such as Windows, Linux, macOS, Android, or iOS.It refers to a group of operating systems that share a common core or foundation. For example, 'Linux'. If OS information is not available, this field is marked as No Data.", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "normalized operating system name of the host,categories into platforms like Windows, Linux, macOS, Android, and iOS based on their common core. If OS information is not available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["Windows", "Linux", "iOS", "Other", "macOS", "Network OS", "Android", "No Data"], "navigator_deep_thinking_categorical": true}, "os_version": {"caption": "OS Version", "description": "Version number of the operating system, indicating the specific iteration or release. This field typically follows a format of major.minor.build.revision. For example, '10.0.19045.2486'.\n", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "os version of the os installed in the host like 10.0.19042.0"}, "os_architecture": {"caption": "OS Architecture", "description": "Refers to the architecture of the operating system, indicating whether it is 32-bit or 64-bit. \n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Refers to the design and structure of an operating system", "navigator_attribute_distinct_values": ["64-bit"]}, "os_build": {"caption": "OS Build", "description": "Represents the specific build number of an operating system, indicating a particular version within a major release. This field is used to track updates and development milestones following the initial OS version release. For example, '19043' or '22621'.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Represents a specific OS version within a major release, indicating a development milestone, such as '18209'."}, "cloud_provider": {"caption": "Cloud Provider", "description": "Indicates the name of the cloud service provider hosting the cloud asset. Values include 'AWS' and 'Azure'. If no information is available, this field is marked as No Data.\n", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "Indicates the cloud service provider of the host, such as AWS or Azure. If no information is available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["AWS", "Azure", "No Data"]}, "account_id": {"caption": "Cloud Account ID", "description": "Cloud Account ID is a unique identifier for an account in a cloud service.\nThis ID is used to distinguish one account from another within the cloud provider's system.\nFor example '***********'.", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "Cloud Account ID of the host account in a cloud service provider"}, "cloud_account_name": {"caption": "Cloud Account Name", "description": "A unique identifier for a cloud service account, used to differentiate between accounts within the provider's system. Example: '***********'.\n", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "The cloud_account_name is a unique login label for accessing subscribed cloud services, such as '<EMAIL>'."}, "region": {"caption": "Cloud Region", "description": "Geographic area where a cloud provider has established data centers and infrastructure to deliver services. Each region represents a specific physical location containing clusters of servers and related resources. Examples include 'eu-west-1' and 'ap-south-1'.If no information is available, this field is marked as No Data.", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "Region refers to the geographic location of a cloud provider's data centers, such as 'eu-west-1' and 'ca-central-1'. If no information is available, this field is marked as No Data.", "navigator_attribute_distinct_values": ["eu-west-2", "eastus", "ap-south-1", "northeurope", "No Data"]}, "zone_availability": {"caption": "Cloud Zone Availability", "description": "Indicates the availability zone configuration of the resource, specifying whether it is deployed in a single zone, multiple zones, or is regional in scope. Possible values include 'Single', 'Multiple', 'Regional', and 'Not Applicable'. \n", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "Indicates if the resource is in 'Single', 'Multiple', 'Regional', or 'Not Applicable' availability zones."}, "resource_id": {"caption": "Cloud Resource ID", "description": "A unique identifier assigned to a specific resource by the cloud provider, typically formatted as a URI or UUID. For example include '0b2b9e0b-8036-460a-9f4e-7f58ce27ff93'\n", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "navigator_attribute_description": "unique ID for a cloud resource assigned by a cloud provider"}, "cloud_resource_type": {"caption": "Cloud Resource Type", "description": "Indicates the category or classification of computing resources and services provided by cloud providers, based on their functionality. Examples include 'Virtual Machine' and 'Storage Service'.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Cloud resource types are categories of computing resources and services from cloud providers, such as 'Virtual Machine'.", "navigator_attribute_distinct_values": ["VIRTUAL_MACHINE", "Virtual Machine"]}, "native_type": {"caption": "Cloud Native Type", "description": "Specifies the name of the cloud service, including the prefix indicating the cloud provider. For example, 'AWS EC2 Instance' or 'Azure Virtual Machine'.\n", "group": "entity_specific", "type": "string", "category": "Network", "navigator_attribute_description": "The native_type indicates the name of the cloud service with its provider prefix, such as 'AWS EC2 Instance'.", "navigator_attribute_distinct_values": ["EC2 Instance", "Azure Virtual Machine", "AWS EC2 Instance"]}, "operational_state": {"caption": "Cloud Last Known Operational State", "description": "The current operational status of the resource, indicating whether it is functioning or not. Possible values include 'Active' and 'Inactive'. For example, 'Active' signifies that the resource is operational, while 'Inactive' indicates it is not.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "operational state of the host set by administarator/security person, such as 'Active'.", "navigator_attribute_distinct_values": ["Inactive", "Active"]}, "active_operational_date": {"caption": "Active Operational Date", "description": "Date indicating when the resource was first found in an active operational state.  For example, '1701486043970' or '1701572443610'.\n", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "Date when the resource's operational state was confirmed as active."}, "provisioning_state": {"caption": "Provisioning State", "description": "Indicates the current status of a resource  such as a virtual machine (VM), storage volume, network interface, or other cloud service,  during the provisioning process within a cloud computing environment. Possible values include 'Succeeded', 'Failed', 'Creating', and 'Deleting'.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Provisioning state indicates the status of a resource like a VM or storage volume during deployment, such as 'Succeeded'.", "navigator_attribute_distinct_values": ["Failed", "Provisioned", "Deleting", "Updating", "Creating", "Succeeded"]}, "cloud_instance_id": {"caption": "Cloud Instance ID", "description": "Unique identifier assigned to each instance provisioned within cloud infrastructure. This identifier serves as a reference for the instance and is utilized by management systems and APIs for identification and management purposes. Example: 'i-0f0e460159df52d82'.\n", "group": "entity_specific", "type": "string", "ui_visibility": true, "candidate_key": true, "data_structure": "list", "navigator_attribute_description": "Unique identifier for each cloud instance, such as 'i-0f0e460159df52d82', used for management and API interactions."}, "cloud_instance_type": {"caption": "Cloud Instance Type", "description": "Specifications and characteristics of a virtual machine or instance within a cloud service provider's infrastructure. Examples include 'm5a.large' and 'Standard_B2s'.\n", "group": "entity_specific", "type": "string", "ui_visibility": true, "data_structure": "list", "navigator_attribute_description": "Specifications of a cloud virtual machine, such as 'm5a.large'."}, "image": {"caption": "Cloud Instance Image ID", "description": "An image represents a pre-configured snapshot or template of a virtual machine (VM), containing essential components such as the operating system, software, configurations, and potentially data required to instantiate a computing resource. For example  ' ami-0336b0504eb53fcdb' \n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "An image is a pre-configured snapshot of a virtual machine containing the operating system and necessary components, such as 'ami-0336b0504eb53fcdb'."}, "cloud_instance_lifecycle": {"caption": "Cloud Instance Lifecycle", "description": "Indicates the different stages or states that a virtual machine (VM) or compute instance can occupy throughout its lifecycle in a cloud computing environment. Typical values include 'scheduled' and 'spot'.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Cloud instance lifecycle includes stages like 'scheduled' and 'spot' that a VM undergoes in a cloud environment.", "navigator_attribute_distinct_values": ["scheduled", "spot"]}, "instance_name": {"caption": "Cloud Instance Name", "description": "Name assigned to a specific virtual machine (VM) or compute instance within a cloud computing environment. This identifier is used to differentiate the instance from others in the same infrastructure. Examples include 'demo-staging-vpc-nat-instance-01' and 'pai-demo-prod-cast-004bc9e6'.\n", "group": "entity_specific", "category": "General Information", "type": "string", "navigator_attribute_description": "Name assigned to a specific virtual machine in a cloud environment, such as 'pai-demo-staging-cast-of30a15c', to distinguish it from others."}, "is_ephemeral": {"caption": "Is Ephemeral", "description": "Indicates whether the resource is temporary and short-lived, existing only for a brief period or until a specific event occurs. They are often created dynamically or on-the-fly to serve a temporary purpose and are not intended for long-term use or storage.This is a boolean value, where 'true' signifies that the resource is ephemeral, and 'false' indicates it is not.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "This boolean property indicates whether a resource is temporary and short-lived, created for a brief purpose, such as dynamically generated data.", "navigator_attribute_distinct_values": [false, true]}, "is_container_host": {"caption": "Is Container Host", "description": "Indicates that this resource is used to host containers in managed Kubernetes clusters.\nIt is boolean value indicater.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Indicates whether the resource, such as a node in a managed Kubernetes cluster, is used to host containers (boolean value).", "navigator_attribute_distinct_values": [false]}, "edr_onboarding_status": {"caption": "EDR Onboarding Status", "description": "Indicates the onboarding status of the EDR agent on the host system. A value of `true` signifies successful installation, while `false` indicates that the agent is either not installed or that the onboarding process has encountered issues. Examples: `true`, `false`.\n", "group": "entity_specific", "type": "string", "category": "Security and Compliance", "navigator_attribute_description": "The edr_onboarding_status attribute is a boolean value that indicates whether the EDR agent is installed on a host, device, or machine. A value of true signifies successful installation, while false indicates that the installation was unsuccessful or incomplete.", "navigator_attribute_distinct_values": [true, false], "navigator_deep_thinking_categorical": true}, "edr_product": {"caption": "EDR Product", "description": "The name of the software or solution installed on a host system that delivers Endpoint Detection and Response (EDR) capabilities. EDR solutions are designed to monitor, detect, investigate, and respond to security threats and suspicious activities on endpoints, including desktops, laptops, servers, and mobile devices. Example: 'MS Defender'.\n", "group": "entity_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Contains EDR products names that is installed in hosts, like MS Defender, are software solutions that monitor and respond to security threats on hosts", "navigator_attribute_distinct_values": [["MS Defender"], ["CrowdStrike"], null]}, "edr_last_scan_date": {"caption": "EDR Last Scan Date", "description": "The date of the most recent scan performed by an Endpoint Detection and Response (EDR) solution on monitored endpoints.  For example, '1670586855910' or '1700113283810'.\n", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "The edr_last_scan_date indicates the latest scan date from EDR solutions like Defender and CrowdStrike on monitored endpoints."}, "edr_mac_address": {"caption": "EDR Mac <PERSON>dress", "description": "The Media Access Control (MAC) address associated specifically with the Endpoint Detection and Response (EDR) agent installed on a device.\nCrowdstrike is the source contributing to this field.\nFor example '00:00:5e:00:53:af'.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "MAC address of the endpoint detection agent on a host, such as '00:00:5e:00:53:af', contributed by Crowdstrike."}, "av_status": {"caption": "Anti Virus Scan Completed", "description": "Indicates the completion status of an antivirus scan on the host system. A value of `true` signifies that the scan has been successfully completed, while a value of `false` indicates that the scan has not been completed or encountered issues. Examples: `true`, `false`.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "AV status indicates whether an antivirus scan on a host has completed successfully (true) or not (false).", "navigator_attribute_distinct_values": [true, false], "navigator_deep_thinking_categorical": true}, "av_last_scan_date": {"caption": "AV Last Scan Date", "description": "Indicates the timestamp of the most recent completion of an antivirus scan on the system. This field provides insight into the last time the system was checked for malware and other security threats.  \nExample Values: 1683849599999, 1694131199999\n", "group": "entity_specific", "type": "timestamp", "category": "Security and Compliance", "navigator_attribute_description": "Indicates the date the last antivirus scan completed on the host."}, "av_signature_update_date": {"caption": "AV Signature Update Date", "description": "Indicates the timestamp of the most recent update to antivirus signatures, which are essential for identifying and detecting known malware and threats. This field provides insight into the currency of the antivirus definitions, reflecting when the signatures were last refreshed to address emerging threats. \n\nExample values: '1698969599999', '1705017599999'\n", "group": "entity_specific", "type": "timestamp", "category": "Security and Compliance", "navigator_attribute_description": "Indicates the most recent date when antivirus signatures were updated to detect known malware and threats."}, "av_block_malicious_code_status": {"caption": "AV Block Malicious Code Setting Enabled", "description": "Indicates whether the antivirus software's functionality to block malicious code is currently enabled. A value of \"true\" means that the antivirus is actively scanning and blocking known malicious code, while a value of \"false\" indicates that this specific blocking feature is disabled, although other protective functions may still be operational. Example values include: true, false.\n", "group": "entity_specific", "type": "string", "category": "Security and Compliance", "navigator_attribute_description": "Indicates if the antivirus's ability to block malicious code is enabled (true) or disabled (false), with Defender as the sole source.", "navigator_attribute_distinct_values": [true]}, "fw_status": {"caption": "Firewall Enabled", "description": "Indicates whether the firewall feature or service is enabled (true) or disabled (false) on the system. This field is a boolean value, with 'true' signifying that the firewall is operational and 'false' indicating it is inactive. Example values include 'true' and 'false'.\n", "group": "entity_specific", "type": "string", "category": "Security and Compliance", "navigator_attribute_description": "Firewall status indicates whether the firewall service is enabled (true) or disabled (false), and is a boolean value sourced solely from Defender.", "navigator_attribute_distinct_values": [false, true], "navigator_deep_thinking_categorical": true}, "vm_product": {"caption": "VM Product", "description": "The name of the software or solution deployed on a host system for vulnerability management. This field identifies the platform responsible for detecting, assessing, and managing security vulnerabilities within the host environment. Examples include Qualys and Tenable.\n", "group": "entity_specific", "category": "Security and Compliance", "type": "string", "data_structure": "list", "navigator_attribute_description": "vulnerability management product names installed in the host, The vm_product is the software like Qualys, Tenable, or Wiz used for vulnerability management on a host system.", "navigator_attribute_distinct_values": [["MS Defender", "Wiz"], ["MS Defender", "Qualys", "Wiz"]]}, "vm_onboarding_status": {"caption": "VM Onboarding Status", "description": "Indicates the onboarding status of the VM agent on the host, represented as a boolean value. A value of 'true' signifies that the agent is onboarded, while 'false' indicates it is not. Example values include 'true' and 'false'.\n", "group": "entity_specific", "type": "string", "category": "Security and Compliance", "navigator_attribute_description": "Indicates if the VM agent is onboarded in the host as a boolean value, with contributions from Defender, Qualys, Tenable.sc, and Wiz.", "navigator_attribute_distinct_values": [false, true], "navigator_deep_thinking_categorical": true}, "vm_tracking_method": {"caption": "VM Tracking Method", "description": "Indicates the method used by the virtual machine agent for tracking. This field is essential for identifying security vulnerabilities, ensuring compliance, and managing associated risks.  \nExamples: 'MS Defender Agent'\n", "group": "entity_specific", "type": "string", "category": "Security and Compliance", "data_structure": "list", "navigator_attribute_description": "Indicates the VM agent's tracking method for identifying security weaknesses and ensuring compliance in the host, such as 'MS Defender Agent'.", "navigator_attribute_distinct_values": [["MS Defender Agent"], ["MS Defender Agent", "agent"], ["Qualys Cloud Agent"], ["Qualys IP"]]}, "vm_last_scan_date": {"caption": "VM Last Scan Date", "description": "The date and time when the most recent vulnerability scan was completed on the host. This field captures the timestamp generated by scanning tools, indicating the last assessment of the host's security posture. Example: '1707001524381'.\n", "group": "entity_specific", "type": "timestamp", "category": "Security and Compliance", "navigator_attribute_description": "Latest date a vulnerability scan, using tools like Qualys or tenable.sc, has completed on the host."}, "host_last_reboot_date": {"caption": "Host Last Reboot Date", "description": "Indicates the date and time of the most recent reboot of the computing device. This information reflects when the operating system was last shut down and restarted, whether initiated manually or automatically.  This field is populated exclusively from Crowdstrike.  \n", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "Indicates the last date and time a computing device, such as a server or workstation, was rebooted, sourced from Crowdstrike Host."}, "mdm_product": {"caption": "MDM Product", "description": "The product utilized for Mobile Device Management on the host. This field is populated exclusively by Microsoft Intune. Example: 'MS Intune'.\n", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Mobile Device Management product name installed in host, primarily Microsoft Intune", "navigator_attribute_distinct_values": ["MS Intune"]}, "mdm_status": {"caption": "MDM Registration Status", "description": "Indicates the registration status or condition of a host within the Mobile Device Management (MDM) system. Possible values include 'registered', 'certificateReset', 'approvalPending', 'notRegisteredPendingEnrollment,' and 'notRegistered'. This field is populated exclusively from Microsoft Intune.  \n", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "The MDM status indicates a host's registration condition in Microsoft Intune", "navigator_attribute_distinct_values": ["true", "false"]}, "mdm_compliance_state": {"caption": "MDM Compliance State", "description": "Indicates the compliance status of a device within a Mobile Device Management (MDM) system, reflecting adherence to established policies and rules. Possible values include 'compliant', 'inGracePeriod', 'non-compliant', and 'Config Manager'.\n", "group": "entity_specific", "type": "string", "category": "General Information", "navigator_attribute_description": "Indicates a device's compliance status in MDM systems, with values 'compliant', 'inGracePeriod', 'non-compliant', and sourced from Microsoft Intune.", "navigator_attribute_distinct_values": ["Config Manager", "Compliant", "Non Compliant", "In Grace Period"]}, "mdm_enrolled_date": {"caption": "MDM Enrolled", "description": "The date when the host was enrolled in the Mobile Device Management (MDM) system. This field is populated exclusively from Microsoft Intune.  \nExample: '2023-01-15'\n", "group": "entity_specific", "type": "timestamp", "navigator_attribute_description": "The date the host was enrolled in MDM, sourced only from Microsoft Intune."}, "mdm_last_sync_date": {"caption": "MDM Last Sync", "description": "Date indicating the most recent successful synchronization of the host with the MDM system. This field is populated exclusively by data from Microsoft Intune. Example: '2023-10-01'.\n", "group": "entity_specific", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "Date when the host last successfully synced with MDM, with Microsoft Intune as the sole contributor."}, "hardware_manufacturer": {"caption": "Hardware Manufacturer", "description": "The name of the company or organization that produces and distributes physical computing devices, including computers, servers, networking equipment, and smartphones. Examples include 'Dell Inc.' and 'LENOVO'.\n", "group": "entity_specific", "type": "string", "category": "Hardware", "navigator_attribute_description": "hardware manufacturer of the host, such as 'Dell' or 'Lenovo'.", "navigator_attribute_sample_data_values": ["<PERSON><PERSON>", "Black Shark", "Sony", "Apple", "Motorola", "Nothing", "Lenovo", "Google", "<PERSON><PERSON>", "Dell"]}, "hardware_model": {"caption": "Hardware Model", "description": "Unique identifier for the specific type or variant of a physical computing device, encompassing attributes such as manufacturer, series, product line, and specifications. For example, 'Latitude 3400' or 'Samsung m51'.\n", "group": "entity_specific", "type": "string", "category": "Hardware", "navigator_attribute_description": "hardware model of the host,Unique identifier for a physical computing device, such as 'Samsung m51', including attributes like manufacturer and specifications.", "navigator_attribute_sample_data_values": ["Xiaomi 12 Lite 5G", "Lenovo Flex System", "iPhone 14", "Google Pixel Fold", "Lenovo ThinkSystem Tower Servers", "iPhone 13 mini", "Xiaomi 12T Pro", "Lenovo ThinkPad X1 Carbon", "iPhone 12 mini", "21E3S06T00"]}, "hardware_serial_number": {"caption": "Hardware Serial Number", "description": "Unique identifier assigned to a specific physical server or hardware device, ensuring distinct identification within a network or infrastructure. This serial number, typically provided by the manufacturer, is unique to each device and can often be found on a label or sticker attached to the device's chassis. For example, '2c9380a223057ece'.\n", "group": "entity_specific", "type": "string", "candidate_key": true, "data_structure": "list", "category": "Hardware", "navigator_attribute_description": "Unique identifier for each physical server or hardware device, such as '2c9380a223057ece', used for easy identification and tracking."}, "hardware_imei": {"caption": "Hardware IMEI", "description": "International Mobile Equipment Identity of the host.\nThe IMEI is a globally unique identifier assigned to individual mobile devices, such as smartphones, feature phones, and cellular-enabled tablets.\nIt serves as a means of identifying and tracking mobile devices on cellular networks worldwide.\nFor example '350123451234560'.\n", "group": "entity_specific", "type": "integer", "navigator_attribute_description": "The hardware IMEI number of the host like mobile devices "}, "hardware_bios_manufacturer": {"caption": "Hardware BIOS Manufacturer", "description": "The BIOS manufacturer is responsible for creating, designing, and distributing the BIOS firmware that is installed on the motherboard of a computer or other hardware devices. \nFor example 'Dell','Lenovo'.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "The BIOS manufacturer, such as Dell or Lenovo, creates and distributes the BIOS firmware for computer motherboards and hardware devices.", "navigator_attribute_distinct_values": ["Lenovo", "Dell"]}, "hardware_bios_version": {"caption": "Hardware BIOS Version", "description": "The version number assigned to the Basic Input/Output System (BIOS) currently running on the Host.\nFor example 'R1EET57W'.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "The hardware BIOS version is the version number of the currently running BIOS on the Host, such as 'R1EET57W'."}, "hardware_chassis_type": {"caption": "Hardware Chassis Type", "description": "Physical structure or form factor of a computer or server enclosure.\n It defines the layout and size of the chassis, determining what components can be installed and how they are arranged within the system.\nFor example 'Workstation'.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "The hardware chassis type refers to the physical structure and form factor of a computer or server enclosure, such as a 'Workstation', which determines component installation and arrangement.", "navigator_attribute_distinct_values": ["Workstations", "Server", "Laptop"]}, "is_accessible_from_internet": {"caption": "Internet Exposure", "description": "Indicates whether wiz resource is accessible via internet .\nIt is a boolean value.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Indicates if the wiz resource is accessible from the internet (true/false).", "navigator_attribute_distinct_values": [true, false]}, "open_to_all_internet": {"caption": "Open To All Internet", "description": "Indicates whether wiz resource is accessible from at least one internet address.\nIt is a boolean value.", "group": "entity_specific", "type": "string", "navigator_attribute_description": "Indicates if the hosts from wiz data source is accessible from at least one internet address", "navigator_attribute_distinct_values": [false, true]}, "edr_threat_count": {"caption": "EDR Threat Count", "description": "The total number of threats detected by the Endpoint Detection and Response (EDR) product. For example, values may include '0' or '1'.\n", "group": "entity_specific", "type": "integer", "ui_visibility": true, "range_selection": true, "category": "Security and Compliance", "navigator_attribute_description": "The edr_threat_count represents the total number of threats detected by the EDR Product.", "navigator_deep_thinking_numerical": true}, "internal_contributor": {"caption": "Internal Contributor", "description": "The identifier for the internal contributor, which can represent various types of devices or instances within the system. Examples include 'Android 11 RegisteredDevice' and 'CN=Laptop-LC-07,CN=Computers,DC=prevalent,DC=com'.\n", "group": "entity_specific", "type": "integer", "ui_visibility": false}, "wiz_id": {"caption": "Wiz ID", "description": "Unique ID assigned by Wiz.It is an alphanumeric value.\nFor example '000b58bd-554d-5cc9-b114-b58bd518cf79'.", "group": "source_specific", "type": "string", "ui_visibility": true, "navigator_attribute_description": "Unique alphanumeric ID assigned by Wiz, such as '000b58bd-554d-5cc9-b114-b58bd518cf79'."}, "wifi_mac_address": {"caption": "Wifi MAC Address", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false}, "ethernet_mac_address": {"caption": "Ethernet MAC Address", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false}, "ad_sam_account_name": {"caption": "AD SAM Account Name", "description": "The SAM account name associated with the machine account in an Active Directory environment. This name is utilized for authentication and authorization when the machine accesses resources or services within the Active Directory domain. Example values include 'SOF-GABBGEO-MOY$' and 'DESKTOP12$'.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "description": "Denotes the type of SAM account within an Active Directory environment, indicating the nature of the account associated with a specific entity. Possible values are Normal User Account,Machine Account,Service Account,Group Account etc. For hosts, this field typically holds the value 'Machine Account'. \n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "account_enabled_status": {"caption": "Account Enabled Status", "description": "Indicates whether the account is currently active or inactive. Possible values are `true` (enabled) or `false` (disabled). \n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "earliest_ad_account_disabled_date": {"caption": "Earliest AD Account Disabled Date", "description": "The date when the earliest Active Directory (AD) user account was disabled. For example, '*************' corresponds to a specific timestamp.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "ad_account_disabled_date": {"caption": "AD Account Disabled Date", "description": "The date when the associated Active Directory (AD) account for the host was disabled.  For example, '*************' indicates a specific point in time when the account was deactivated.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The ad_account_disabled_date is the date when the Active Directory account for a host, which enables authentication and resource access, was disabled."}, "ad_distinguished_name": {"caption": "AD Distinguished Name", "description": "Refers to a unique identifier within the Active Directory environment that specifies the exact location of an object in the directory hierarchy. This identifier provides a hierarchical path that uniquely distinguishes the object's position within the domain. \nExamples: \n- CN=<PERSON>,OU=Directors,OU=Management,OU=PAI users,DC=prevalent,DC=com\n- CN=DESKTOP12,CN=Computers,DC=prevalent,DC=com'\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "A unique identifier in Active Directory that specifies an object's precise location, such as 'CN=802.1X (Wired) 301 Developers,OU=CorporateOld,OU=Groups,OU=Stage,OU=Locations,OU=_Corp,DC=corp,DC=safe,DC=com'."}, "ad_uac": {"caption": "AD User Account Control", "description": "Refers to a collection of flags that govern the properties of a user account within the Active Directory environment. These flags determine aspects such as login permissions, password requirements, and account status. Examples include 'PASSWD_NOTREQD' and 'WORKSTATION_TRUST_ACCOUNT'.\n", "group": "source_specific", "type": "string", "ui_visibility": true, "category": "General Information", "to_be_deprecated": true}, "ad_object_guid": {"caption": "AD ObjectGUID", "description": "The unique identifier assigned to each object in the directory. Example: '0a9a511d-7f2b-4e78-a346-a47d80c0e982'.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "The ad_object_guid is a unique identifier for directory objects, such as '0a9a511d-7f2b-4e78-a346-a47d80c0e982'."}, "aad_device_category": {"caption": "AAD Device Category", "description": "Indicates the category of the device, which helps classify its type or purpose within the system. Example values include 'Test E8'.\n", "group": "source_specific", "type": "string", "derived_field": true, "navigator_attribute_description": "The aad_device_category attribute classifies devices into categories such as \"Mobile\" or \"Desktop\" for Azure Active Directory.", "navigator_attribute_distinct_values": ["Test E8"]}, "aad_system_label": {"caption": "AAD System Label", "description": "The label assigned to the system for identification purposes. This field is typically used to categorize or describe the host in a meaningful way. Example: 'Production Server' or 'Test Environment'.\n", "group": "source_specific", "type": "string", "derived_field": true, "data_structure": "list", "navigator_attribute_description": "The aad_system_label attribute categorizes Azure Active Directory (AAD) resources, such as applications and users, for better organization and management."}, "ad_created_date": {"caption": "AD Created", "description": "Date indicating when the associated host object was created in the Active Directory environment. \nExample: '1530284080000', '1610384210000'\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date of creation for the associated host object in Active Directory (AD)."}, "ad_last_sync_date": {"caption": "AD Last Sync Date", "description": "The date and time when the host's object was last synchronized with the Active Directory domain controller. This field helps administrators monitor synchronization activities and troubleshoot any related issues. Example: '2023-10-01T14:30:00Z'.\n", "group": "source_specific", "type": "timestamp", "category": "General Information", "navigator_attribute_description": "The ad_last_sync_date indicates the most recent synchronization of the host's object in Active Directory with the domain controller, helping administrators track changes and troubleshoot issues."}, "ad_operational_status": {"caption": "AD Operational Status", "description": "Indicates the current operational status of the host's account within the Active Directory environment, reflecting whether the account is active or disabled. For example, values may include 'Active' or 'Disabled'.\n", "group": "source_specific", "type": "string", "category": "General Information", "ui_visibility": true, "to_be_deprecated": true}, "aad_id": {"caption": "AAD ID", "description": "The unique identifier assigned to the host in Azure Active Directory. For example, '000138b5-dc2b-4209-8098-7360801c7861'.\n", "ui_visibility": false, "group": "source_specific", "type": "string"}, "aad_device_id": {"caption": "AAD Device ID", "description": "Unique identifier assigned to a device within Azure Active Directory (AAD). This identifier enables the system to differentiate between devices and link them to specific user accounts or security policies. Example: '0a9a511d-7f2b-4e78-a346-a47d80c0e982'.\n", "group": "source_specific", "type": "string", "candidate_key": true, "data_structure": "list", "navigator_attribute_description": "The aad_device_id is a unique identifier for a device in Azure Active Directory, such as '0a9a511d-7f2b-4e78-a346-a47d80c0e982', used to associate devices with user accounts and policies."}, "aad_enrolled_date": {"caption": "AAD Enrolled", "description": "The date when a device was registered with Azure Active Directory (AAD) for management and access control. \nExamples: `*************`, `*************`.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when a device was enrolled in Azure Active Directory (AAD) for management and access control."}, "aad_created_date": {"caption": "AAD Created", "description": "Date indicating when a device was registered in Azure Active Directory (AAD). This timestamp is crucial for tracking the lifecycle of device management and access control within the Azure environment. \nExample: '*************', '*************'\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Device entry creation date in Azure Active Directory for management and access control."}, "aad_management_service": {"caption": "AAD Management Service", "description": "The management service or mechanism utilized for administering and controlling a host within Azure Active Directory (AAD). Examples include 'MDM' and 'MicrosoftSense'.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "The aad_management_service refers to the management tool used for administering Azure Active Directory hosts, such as Intune.", "navigator_attribute_distinct_values": ["MicrosoftSense", "ClientCertificateAuth", "ConfigMgr", "MDM", "SCEP"]}, "aad_deleted_date": {"caption": "AAD Deleted Date", "description": "The date when the host's object was removed from Azure Active Directory (AAD), indicating that the associated device or system is no longer under management within the Azure environment.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when the host's object was deleted from Azure Active Directory, indicating that the associated device or system is no longer managed in Azure."}, "aad_operational_status": {"caption": "AAD Operational Status", "description": "Indicates the current operational status of the host within Azure Active Directory (AAD). This field specifies whether the host is functioning as expected, with possible values such as 'Active' or 'Disabled'.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "azure_vm_power_state": {"caption": "Azure VM Power State", "description": "The current power state of a virtual machine (VM) in the Azure cloud platform. Possible values include 'Running' and 'Stopped.' For example, a VM may be in a 'Running' state when actively processing tasks.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "The Azure VM power state indicates whether a virtual machine is Running or Stopped.", "navigator_attribute_distinct_values": ["Running", "Stopped"]}, "azure_tags": {"caption": "Azure Tags", "description": "Metadata elements associated with Azure resources, formatted as key-value pairs. These tags are utilized for categorizing resources based on various attributes, such as project names, environment types, cost centers, and ownership details. \n", "group": "source_specific", "type": "string", "ui_visibility": true, "data_structure": "struct", "navigator_attribute_description": "Azure tags are key-value pairs used to categorize resources by attributes like project names, environment types, cost centers, and owners."}, "azure_vm_image_id": {"caption": "Azure VM Image ID", "description": "Specifies the unique identifier for the virtual machine image to be used, which can include platform images, marketplace images, or custom images from a gallery. The format follows the Azure resource ID structure.  \nExample: '/subscriptions/0f2e3e80-6e2c-47c6-977a-c83c9a9b005e/resourceGroups/MC_aks-poc_aks-argocd_centralindia/providers/Microsoft.Compute/galleries/cast.ai_aks.node.images.gallery_72c7fc97/images/castpool/versions/2024.02.08'.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "azure_vm_os_version": {"caption": "Azure VM OS Version", "description": "TThe version of the operating system installed on the virtual machine instance. Examples include '10.0.17763.5696' and '22.04'.\n", "group": "source_specific", "type": "string", "ui_visibility": true, "navigator_attribute_description": "The Azure VM OS version indicates the operating system of the virtual machine, such as Windows Server 2022 or Ubuntu 20.04."}, "azure_vm_os_name": {"caption": "Azure VM OS Name", "description": "The name of the operating system running on the virtual machine instance. Examples include 'Windows Server 2019 Datacenter' and 'ubuntu'.\n", "group": "source_specific", "type": "string", "ui_visibility": true, "navigator_attribute_description": "The operating system name of the virtual machine instance, such as Windows or Linux."}, "azure_vm_lifecycle": {"caption": "Azure VM Lifecycle", "description": "Indicates the priority level for deploying virtual machines to meet different workload demands. Possible values include 'Regular', 'Low', and 'Spot'. For example, 'Spot' indicates a lower-cost option for non-critical workloads.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "azure_region": {"caption": "Azure Region", "description": "Specifies the geographical region of Azure data centers, indicating where the associated resources are hosted. Examples include 'centralindia' and 'eastus'.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "aws_ecs_cluster_name": {"caption": "AWS ECS Cluster Name", "description": "Name assigned to the ECS Cluster.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "aws_eks_cluster_name": {"caption": "AWS EKS Cluster Name", "description": "Name assigned to the EKS Cluster.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "aws_region": {"caption": "AWS Region", "description": "Indicates the specific geographical region of AWS data centers where the resources are hosted. For example, 'ap-south-1' represents the Asia Pacific (Mumbai) region.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "description": "The date and time when a resource was created in the Amazon Web Services (AWS) environment. For example, '1678164783000' or '1680593152449'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when an AWS resource, such as an EC2 instance or S3 bucket, was created."}, "aws_instance_launch_date": {"caption": "AWS Instance Launch Date", "description": "The date and time when an EC2 instance was initially launched in the Amazon Web Services (AWS) environment. For example, '1696832558000' or '1700053130000'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The aws_instance_launch_date indicates when an EC2 instance was initially launched in AWS, based on the launchTime attribute."}, "aws_instance_attach_time": {"caption": "AWS Instance Attach Date", "description": "Denotes the timestamp when the Elastic Network Interface (ENI) was attached to the instance in the Amazon Web Services (AWS) environment. For example, '1686824884000' or '1709001124000'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Denotes the date the Elastic Network Interface (ENI) was attached to the AWS instance."}, "aws_operational_state": {"caption": "AWS Operational State", "description": "The last known status of a resource within its lifecycle in the Amazon Web Services (AWS) environment. Possible values include 'active', 'inactive', 'pending', 'running', and others. For example, 'active' indicates the resource is currently operational, while 'terminated' signifies it has been permanently removed.\n", "group": "source_specific", "type": "string", "ui_visibility": true, "navigator_attribute_description": "The AWS operational state indicates the last known status of a resource, with possible values like active, inactive, pending, and running.", "navigator_attribute_distinct_values": ["pending", "terminated", "running", "stopping", "shutting-down", "stopped"]}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "description": "The date and time when a resource was initially created in the Microsoft Azure cloud platform. For example, '1655447089038' or '1662814204274'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when a resource was initially created in Microsoft Azure."}, "azure_operational_state": {"caption": "Azure Operational State", "description": "Last known operational state of an entity in Azure AD.This attribute indicates whether the entity is active, inactive, disabled, or undergoing some other operational state.\nFor example active.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The azure_operational_state indicates the last known status of an entity in Azure AD, such as active, inactive, or disabled."}, "aws_instance_private_ip_address": {"caption": "AWS Instance Private IP Address", "description": "Refers to the private IP address assigned to an EC2 instance in the AWS environment. This address is used for internal communication within the same network. Example: '************'.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "The private IP address assigned to an AWS EC2 instance, e.g., '************'."}, "aws_instance_public_ip_address": {"caption": "AWS Instance Public IP Address", "description": "The public IP address assigned to an EC2 instance in the AWS environment, facilitating communication between the instance and the public internet. Example: '*************'.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "The public IP address, such as '**************', assigned to an EC2 instance in AWS enables communication with the public internet."}, "aws_instance_image_id": {"caption": "AWS Instance Image ID", "description": "Unique identifier for a specific Amazon Machine Image (AMI), which is a pre-configured virtual machine image that includes an operating system and often additional software. This identifier is used to create EC2 instances. Example: 'ami-00d2c340dbd26e13b'.\n", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "aws_instance_usage_update_time": {"caption": "AWS Instance Usage Update Date", "description": "Date indicating when the billing cycle for Amazon Machine Images begins, based on the instance usage operation. For example, '1686824884000' corresponds to a specific date and time.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The aws_instance_usage_update_time indicates the start date of the billing cycle for Amazon Machine Images based on instance usage."}, "aws_tags": {"caption": "AWS Tags", "description": "Metadata elements associated with resources, represented as key-value pairs.\nFor example 'Environment : integration,kubernetes.io/cluster/pai-sol-integration : owned,Name : pai-sol-integration-cast-deeac92d,cast:managed-by : cast.ai,Billing : sol-integration,cast:cluster-id : 5c5e1caf-a6a3-497b-834e-ce37814d4499,cast:node-id : deeac92d-9c15-4c0d-9668-4808c899ab87'.", "group": "source_specific", "type": "string", "ui_visibility": false, "data_structure": "struct"}, "win_event_id": {"caption": "Windows Event Code", "description": "Unique identifier for a specific event recorded in the Windows event log. Each event, whether related to system, application, or security activities, is assigned this ID to facilitate tracking and analysis. For example, 4624.\n", "group": "source_specific", "type": "string", "data_structure": "list", "ui_visibility": false, "to_be_deprecated": true}, "qualys_id": {"caption": "Qualys ID", "description": "Unique identifier assigned to each host by Qualys, used for tracking and managing host-related data. Example: '0a7e6969-e99d-430d-aecc-78d8749438aa'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Unique reference number for hosts in Qualys, e.g., '0a7e6969-e99d-430d-aecc-78d8749438aa'."}, "qualys_tags": {"caption": "Qualys Tags", "description": "A piece of information from qualys that gives more context to a host.Qualys tags are labels or identifiers assigned to assets, vulnerabilities, or other elements within the Qualys vulnerability management platform. They provide a flexible way to categorize and organize items based on various criteria such as location, department, asset type, severity level, or compliance status.\nFor example '{Internet Facing Assets,Unknown Business Unit,No Asset Group}'.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Qualys tags are flexible labels used to categorize assets and vulnerabilities based on criteria like location or compliance, such as '{Internet Facing Assets,Unknown Business Unit,No Asset Group}'."}, "qualys_groups": {"caption": "Qualys Groups", "description": "", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "qualys_asset_id": {"caption": "Qualys Asset ID", "description": "Unique identifier assigned to each scanned and managed asset within the Qualys Cloud Platform. This ID is used to track and manage information related to the asset, such as its configuration, vulnerabilities, and compliance status. Example: '46307729'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Unique identifier for each asset in the Qualys Cloud Platform, such as '46307729', used to track configuration, vulnerabilities, and compliance status."}, "qualys_detection_method": {"caption": "Qualys Detection Method", "description": "The method used to identify security vulnerabilities on an asset, categorizing the detection approach employed. For example, 'Qualys Cloud Agent'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Method of detection in Qualys categorizes how security issues are identified on assets, such as using the 'Qualys Cloud Agent'.", "navigator_attribute_distinct_values": [["Qualys Cloud Agent"], ["Qualys IP"]]}, "defender_id": {"caption": "Defender ID", "description": "ID used to uniquely identify and manage individual hosts within the system. This field is typically formatted as a hexadecimal string. Examples include '0022ba22e168587ef7b3ed582e92e3e198300ab5' and '007317fe64800f778900b7373ef03ef4b5729264'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Unique ID for managing hosts in Microsoft Defender, e.g., '0a7e6969-e99d-430d-aecc-78d8749438aa,134248746'."}, "defender_health_status": {"caption": "Defender Health Status", "description": "Indicates the current health status of a host as determined by Microsoft Defender. This status reflects the operational effectiveness and connectivity of Defender on the endpoint. Possible values include Active, NoSensorData, ImpairedCommunication, and Inactive.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Host status assigned by Microsoft Defender indicating effectiveness and functionality, with possible values including Active, NoSensorData, ImpairedCommunication, and Inactive.", "navigator_attribute_distinct_values": ["NoSensorDataImpairedCommunication", "Inactive", "Active"]}, "defender_detection_method": {"caption": "Defender Detection Method", "description": "Method used for detecting threats or vulnerabilities, indicating the approach taken by the security system. Examples include 'Defender Agent' and 'Network Scan'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Detection method used in defender, such as the Defender Agent.", "navigator_attribute_distinct_values": [["Network Scan"], ["Defender Agent"]]}, "defender_tags": {"caption": "Defender Tags", "description": "A list of tags assigned to a host by the Endpoint Detection and Response (EDR) system, providing additional context or categorization. Example: '[OU=CIT-USER,OU=Computers,OU=Locations,OU=_Corp,DC=corp,DC=com]'.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Defender tags provide contextual information about a host, such as '['OU=CIT-USER,OU=Computers,OU=Locations,OU=_Corp,DC=corp,DC=com']'."}, "defender_risk_score": {"caption": "Defender Risk Score", "description": "Risk score evaluated by Microsoft Defender for Endpoint, providing a quantitative measure of the overall security health and resilience of endpoints. Possible values include Low, Medium, and Informational. For example, a score of 'Informational' indicates a non-critical status.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Defender risk score, evaluated by Microsoft Defender for Endpoint, quantitatively measures endpoint security health with values of Low, Medium, or Informational.", "navigator_attribute_distinct_values": ["None", "Medium", "High", "Low", "Informational"]}, "defender_subscription_id": {"caption": "Defender Subscription ID", "description": "Unique identifier associated with the subscription plan or license for Microsoft Defender, Microsoft's comprehensive endpoint security solution. This identifier helps Microsoft and its customers keep track of the subscription status, entitlements, and usage details for the Defender service.\nFor example '2c53d3f7-5cd7-4110-accf-2a975a3cc6d9'.", "group": "source_specific", "type": "string", "ui_visibility": true}, "defender_exposure_level": {"caption": "Defender Exposure Level", "description": "Exposure level assessed by Microsoft Defender for Endpoint, indicating the potential security risks associated with the endpoint based on its configuration, threat landscape, and other relevant factors. Possible values include Low, Medium, and High.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Defender exposure level indicates potential security risks of an endpoint based on its configuration and threat landscape, with values of Low, Medium, or Informational.", "navigator_attribute_distinct_values": ["Medium", "None", "High", "Low"]}, "defender_threat_name": {"caption": "Defender Threat Name", "description": "Refers to the specific name or identifier assigned to a detected threat or security incident by Microsoft Defender. This field captures the nature of the threat, allowing for identification and categorization of security risks. Examples include 'TrojanDownloader:PowerShell/CobaltStrike.C!ibt' and 'Exploit:O97M/CVE-2017-11882.RV!MTB'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Defender threat name is the identifier for a detected threat, such as 'TrojanDownloader:PowerShell/CobaltStrike.C!ibt' or 'Exploit:O97M/CVE-2017-11882.RV!MTB'."}, "defender_onboarding_status": {"caption": "Defender Onboarding Status", "description": "Indicates the onboarding status of the host within the Defender security ecosystem. A value of 'Onboarded' signifies that the endpoint is actively protected and fully enrolled. \nExamples: Onboarded, Unsupported.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Defender onboarding status indicates if a host is actively protected, with \"Onboarded\" signifying full enrollment in the Defender security ecosystem.", "navigator_attribute_distinct_values": ["Onboarded", "InsufficientInfo", "Unsupported"]}, "defender_threat_count": {"caption": "Defender Infection Count", "description": "The total number of threats detected by Microsoft Defender on the host. For example, 1.\n", "group": "source_specific", "type": "integer", "range_selection": true, "navigator_attribute_description": "Number of threats detected by Microsoft Defender, e.g., 9."}, "defender_management_service": {"caption": "Defender Management Service", "description": "Service used for managing the host within the Defender ecosystem. Examples include 'Intune' and 'MicrosoftDefenderForEndpoint'.\n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Defender management service is used to manage hosts in Defender, such as with Intune.", "navigator_attribute_distinct_values": ["Unknown", "MicrosoftDefenderforEndpoint", "Intune", "MicrosoftDefenderForEndpoint"]}, "defender_action_type": {"caption": "Defender Action Type", "description": "Describes the type of action performed by the antivirus software, which is crucial for identifying and responding to potential threats. This field aids in the timely remediation of security issues, thereby protecting the integrity of the endpoint and its data. Possible values are 'AntivirusScanCompleted','AntivirusDetection','AntivirusScanCancelled','AntivirusDetectionActionType'.\n", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Defender action types refer to antivirus activities like 'AntivirusScanCompleted' and 'AntivirusDetection' that help remediate threats and protect endpoint integrity."}, "defender_onboarding_date": {"caption": "Defender Onboarding Date", "description": "The date when a host was first detected or registered in the Microsoft Defender security environment.  \nExamples: 1669202353632, 1670302370771\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when a host was first detected or registered in the Microsoft Defender security environment."}, "intune_ownership_status": {"caption": "Intune Ownership Status", "description": "Indicates the ownership category of the device enrolled in the Intune management system. Possible values include 'Company', 'Personal', and 'Unknown'. \n", "group": "source_specific", "type": "string", "navigator_attribute_description": "Ownership status of devices in Intune, indicating categories like personal.", "navigator_attribute_distinct_values": ["Company", "Unknown", "Personal"]}, "mdm_encryption_status": {"caption": "MDM Encryption Status", "description": "It likely indicates whether encryption is enabled on devices managed through a Mobile Device Management (MDM) solution.It is a boolean value indicator.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if encryption is enabled on MDM-managed devices, represented as a boolean value.", "navigator_attribute_distinct_values": [true, false]}, "intune_id": {"caption": "Intune ID", "description": "Unique identifier assigned to each enrolled host within the Intune management system.\nFor example '47c8298-24a9-4575-a414-eeb35f205e9d'.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Unique identifier for each enrolled host in Intune, such as '47c8298-24a9-4575-a414-eeb35f205e9d'."}, "intune_management_service": {"caption": "Intune Management Service", "description": "Service through which the host is managed in MS Intune.\nFor example 'mdm'.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Intune management service is used for host management in MS Intune, such as through MDM."}, "tenablesc_onboarding_status": {"caption": "Tenable.sc onboarding status", "description": "Indicates whether the host is covered by Tenable.sc.It is a boolean value indicator.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if a host is covered by Tenable.sc (boolean value).", "navigator_attribute_distinct_values": [true]}, "tenablesc_last_active_date": {"caption": "Tenable.sc Last Active", "description": "Date when a specific host was last active within the Tenable.sc system.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Last active date of a specific host in the Tenable.sc system."}, "tenablesc_repositories": {"caption": "Tenable.sc Repositories", "description": "Tenable.sc Repositories to which the host belongs.\nFor example '{'id': -1, 'name': 'Individual Scan', 'description': '', 'dataFormat': 'agent'}'.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Tenable.sc Repositories indicate the host's associations, such as '{'id': -1, 'name': 'Individual Scan', 'dataFormat': 'agent'}'."}, "tenablesc_assets": {"caption": "Tenable.sc Assets", "description": "Tenable.sc assets to which the host belongs.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Assets in Tenable.sc associated with the host."}, "tenablesc_asset_tags": {"caption": "Tenable.sc Asset Tags", "description": "Tenable.sc Asset Tags to which the host is tagged.\nFor example PCI Workstation, PCI ISO Workstation, PMI, Workstation.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Tenable.sc Asset Tags assigned to the host include examples like PCI Workstation, PCI ISO Workstation, PMI, and Workstation."}, "tenablesc_asset_name": {"caption": "Tenable.sc Asset Name", "description": "Tenable.sc assets to which the host belongs.Indicates the name or identifier assigned to a particular asset within the system.\nFor example ABC-Critical-servers.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenablesc_asset_groups": {"caption": "Tenable.sc Asset Groups", "description": "Tenable.sc Asset Groups to which the host belongs.\nFor example India Desktop Group.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "Tenable.sc Asset Groups that the host belongs to, such as India Desktop Group."}, "tenable_repo": {"caption": "Tenable.sc Repo", "description": "Tenable.sc Asset Groups to which the host belongs.", "group": "source_specific", "type": "string", "data_structure": "struct", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_modified_date": {"caption": "CrowdStrike Modified Date", "description": "Host modified time as per CrowdStrike.This field is automatically updated whenever changes are made to the associated object, such as policies, configurations, or detections.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The crowdstrike_modified_date reflects the automatic update of host modification time whenever changes occur to policies, configurations, or detections."}, "crowdstrike_last_report_date": {"caption": "CrowdStrike Last Report Date", "description": "Most recent date when security report was generated by CrowdStrike for a particular endpoint or system.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Most recent date of a security report generated by CrowdStrike for an endpoint or system."}, "crowdstrike_onboarding_status": {"caption": "CrowdStrike Onboarding Status", "description": "Indicates whether a system or endpoint has been successfully onboarded onto the CrowdStrike platform for endpoint protection. When the value is 'true,' it signifies that the system has been successfully onboarded, while 'false' indicates that it has not.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if a system is successfully onboarded to CrowdStrike for endpoint protection, with 'true' for onboarded and 'false' for not onboarded.", "navigator_attribute_distinct_values": [true]}, "crowdstrike_connection_ip": {"caption": "CrowdStrike Connection IP", "description": "CS AWS IP the sensor connects to.\nFor example *************.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_product_type_desc": {"caption": "CrowdStrike Product Type", "description": "Type of host a sensor is running on.\nPossible values are Workstation ,Domain Controller,Desktop,Unknown,Server", "group": "source_specific", "type": "string", "navigator_attribute_description": "Type of host for a sensor includes Workstation, Domain Controller, Desktop, Unknown, and Server.", "navigator_attribute_distinct_values": ["Server"]}, "crowdstrike_operational_state": {"caption": "CrowdStrike Operational State", "description": "The containment status of the machine within the CrowdStrike environment. Possible values are: normal,containment_pending,contained, and lift_containment_pending.", "group": "source_specific", "type": "string", "ui_visibility": true}, "crowdstrike_provision_state": {"caption": "CrowdStrike Provision State", "description": "Status of crowdstrike provisioning in a host.\nPossible values are Provisioned,NotProvisioned", "group": "source_specific", "type": "string", "navigator_attribute_description": "The crowdstrike provisioning status of a host can be either Provisioned or NotProvisioned.", "navigator_attribute_distinct_values": ["Provisioned"]}, "crowdstrike_agent_local_date": {"caption": "CrowdStrike Agent Local Date", "description": "The local time of the crowdstrike sensor. This date reflects the current time according to the time zone settings configured on the endpoint where the CrowdStrike agent is deployed.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The crowdstrike_agent_local_date indicates the local time of the CrowdStrike sensor based on the endpoint's time zone settings."}, "crowdstrike_device_id": {"caption": "CrowdStrike Device ID", "description": "Unique identifier for the host in CrowdStrike.This identifier serves as a permanent, globally unique reference for the device, facilitating its identification and tracking across the CrowdStrike environment.\nFor example '2511c9f8a20axxxa5a6ddf94cd8943'.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Unique identifier for a host in CrowdStrike, like '2511c9f8a20axxxa5a6ddf94cd8943', used for tracking the device."}, "crowdstrike_customer_id": {"caption": "CrowdStrike Customer ID", "description": "CrowdStrike customer identification.Unique identifier assigned to each customer or organization utilizing the services of the CrowdStrike.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Unique identifier for each customer using CrowdStrike services.", "navigator_attribute_distinct_values": ["1db15dc9a40c887771b5f5906d8f", "1db15dc9a40c556661b5f5906d8f"]}, "crowdstrike_onboarding_date": {"caption": "CrowdStrike Onboarding Date", "description": "Date on which a host was successfully onboarded onto the CrowdStrike platform for endpoint protection.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date a host was successfully onboarded to the CrowdStrike platform for endpoint protection."}, "crowdstrike_reboot_date": {"caption": "CrowdStrike Reboot Date", "description": "Date when a host managed by CrowdStrike was last rebooted.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date of the last reboot for a CrowdStrike-managed host."}, "crowdstrike_tags": {"caption": "CrowdStrike Tags", "description": "", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "CrowdStrike tags are metadata labels used to categorize and identify threats, such as \"ransomware\" or \"phishing.\""}, "crowdstrike_external_ip": {"caption": "CrowdStrike External IP", "description": "The external IP address of the device seen by crowdstrike.\nFor example ************.", "group": "source_specific", "type": "string", "navigator_attribute_description": "External IP address of the device as seen by CrowdStrike, e.g., ************."}, "crowdstrike_local_ip": {"caption": "CrowdStrike Local IP", "description": "IP of the local interface on the device which is used to connect to CrowdStrike servers.\nFor example *************.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Local IP address used for connecting to CrowdStrike servers, e.g., *************."}, "itop_pc_display_name": {"caption": "iTOP PC Display Name", "description": "Name of the host in iTOP PC.This field serves as a user-friendly identifier for the PC, allowing IT administrators and users to easily recognize and refer to the computer within the organization's IT infrastructure.\nFor example INDABCD001.", "group": "source_specific", "type": "string", "navigator_attribute_description": "User-friendly identifier for a PC in iTOP, such as INDABCD001, aiding in recognition by IT administrators and users."}, "itop_pc_move_to_production_date": {"caption": "iTOP PC Move To Production Date", "description": "Date when a personal computer (PC) was transitioned or deployed into a production environment for operational use within the iTOP", "group": "source_specific", "type": "timestamp", "ui_visibility": false, "to_be_deprecated": true}, "itop_pc_end_of_warranty_date": {"caption": "iTOP PC End of Warranty Date", "description": "Denotes the expiration date of the warranty period for a personal computer (PC) within the iTOP", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Indicates the warranty expiration date for a personal computer (PC) in iTOP."}, "itop_pc_status": {"caption": "iTOP PC Status", "description": "Host status as per iTOP.It provides insight into the current condition or availability of the PC asset, facilitating efficient management and monitoring of PC deployments within the organization's IT infrastructure.\nFor example implementation, obsolete, production, stock.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Host status in iTOP reflects the PC's condition or availability, aiding in management and monitoring, with examples like obsolete, production, and stock.", "navigator_attribute_distinct_values": ["Production", "decomissioned"]}, "itop_pc_org_id": {"caption": "iTOP PC Organization ID", "description": "Unique ID for the organization name.This identifier serves as a reference to the organizational unit or entity responsible for the PC asset within the broader organizational structure.\nFor example 2.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Unique identifier for the organization responsible for the PC asset, such as itop_pc_org_id."}, "itop_pc_org_unit": {"caption": "iTOP PC Organization Unit", "description": "Organizational unit or department to which a personal computer (PC) belongs within the iTOP.\nFor example PrevalentAI Dubai.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The organizational unit or department of a personal computer (PC) in iTOP, such as PrevalentAI Dubai.", "navigator_attribute_distinct_values": ["Finance", "Kiosk", "Sales", "Marketing", "Help Desk", "Admin", "HR"]}, "itop_pc_business_criticality": {"caption": "iTOP PC Business Criticality", "description": "Business criticality of a PC as per iTOP.Evaluates the level of importance or criticality of a personal computer (PC) to the organization's business operations within the iTOP (IT Operations Portal) system. This field helps in categorizing PC assets based on their significance to the organization's mission-critical processes, services, or activities.\nFor example low.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Business criticality of a PC in iTOP categorizes its importance to organizational operations, such as low criticality."}, "itop_pc_device_serial_number": {"caption": "iTOP PC Device Serial Number", "description": "Device serial number assigned to a personal computer (PC) within the iTOP.\nFor example 9THR203.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Device serial number for a personal computer in iTOP, e.g., 9THR203."}, "itop_pc_location": {"caption": "iTOP PC Location", "description": "Physical location of a personal computer (PC) within the iTOP.\nFor example Dubai.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Physical location of a personal computer (PC) within the iTOP, such as Dubai.", "navigator_attribute_distinct_values": ["Manchester", "Glasgow", "Belfast", "Cardiff"]}, "itop_pc_obsolete_status": {"caption": "ITOP PC Obsolete Status", "description": "Checks whether the PC has been obsoleted or not.It is a boolean value indicator.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if the PC is obsolete (true/false)."}, "itop_pc_obsolete_date": {"caption": "ITOP PC Obsolete Date", "description": "Date at with PC was obsoleted.Indicates the date when a personal computer (PC) was designated as obsolete or outdated within an IT infrastructure.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when a personal computer (PC) was marked as obsolete in IT infrastructure."}, "itop_pc_brand": {"caption": "iTOP PC Brand", "description": "Brand of the PC with iTOP.\nFor example Dell.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Brand of the PC with iTOP, such as Dell."}, "itop_pc_model": {"caption": "iTOP PC Model", "description": "The model or specific product identifier of a personal computer (PC) within iTOP.\nFor example Latitude 3410.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The itop_pc_model is the specific product identifier of a PC in iTOP, such as Latitude 3410."}, "itop_pc_cpu": {"caption": "iTOP PC CPU Name", "description": "CPU used in the iTOP device.\nFor example Intel Core i7.", "group": "source_specific", "type": "string"}, "itop_pc_ram": {"caption": "iTOP PC RAM Name", "description": "RAM used in the device.\nFor example 8GB DDR4.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "itop_pc_type": {"caption": "iTOP PC Device Type", "description": "Type of Device.\nFor example Laptop.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Device type, e.g., Laptop.", "navigator_attribute_distinct_values": ["Laptop", "Workstations"]}, "itop_pc_os_family": {"caption": "iTOP PC OS Family", "description": "Classifies the OS into its corresponding platforms such as Windows, Linux, macOS, Android, iOS.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Classifies operating systems into platforms like Windows, Linux, macOS, Android, and iOS."}, "itop_pc_os_version": {"caption": "iTOP PC OS Version", "description": "Version of the OS within iTOP PC.\nFor example Microsoft Windows 10 Professional Edition, 64-bit 10.0.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Version of the OS on iTOP PC, such as Microsoft Windows 10 Professional Edition, 64-bit 10.0."}, "itop_pc_os_build": {"caption": "iTOP PC OS Build", "description": "Operating System Build.\nFor example 19044.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Operating System Build, e.g., 19044."}, "itop_pc_asset_number": {"caption": "iTOP PC Asset Number", "description": "Asset number of the PC assigned by iTOP.\nFor example DELL-LT-56789.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Asset number assigned by iTOP for PCs, e.g., DELL-LT-56789."}, "itop_pc_purchase_date": {"caption": "iTOP PC Purchase Date", "description": "Purchase date of the device.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when the device was purchased."}, "itop_class": {"caption": "iTOP Class", "description": "Class name of the asset as per iTOP.\nFor example PC.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Class name of the asset in iTOP, such as PC.", "navigator_attribute_distinct_values": ["Server"]}, "itop_server_display_name": {"caption": "iTOP Server Display Name", "description": "Indicates name of the iTop server.\nFor example LSERVER-A0011D.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates the name of the iTop server, such as LSERVER-A0011D."}, "itop_server_organization": {"caption": "iTOP Server Organization", "description": "Organization name as per iTOP Server.\nFor example Prevalent AI India.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Organization name according to iTOP Server, e.g., Prevalent AI India."}, "itop_server_org_id": {"caption": "iTOP Server Organization ID", "description": "Unique ID for the organization name.\nFor example 2.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Unique ID for the organization, such as \"org123\"."}, "itop_server_status": {"caption": "iTOP Server Status", "description": "Server status as per iTOP.\nFor example production.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Server status in iTOP, such as production.", "navigator_attribute_distinct_values": ["production", "decomissioned"]}, "itop_server_business_criticality": {"caption": "iTOP Server Business Criticality", "description": "Business criticality of a server as per iTOP.\nFor example high.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Business criticality of a server in iTOP, such as high."}, "itop_server_location": {"caption": "iTOP Server Location", "description": "Location of server as per iTOP.\nFor example PAI DC Kochi.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Server location in iTOP, e.g., PAI DC Kochi."}, "itop_server_rack_name": {"caption": "iTOP Server Rack Name", "description": "Name of the physical rack where the iTop server devices kept in.\nFor example Rack-001.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "itop_server_enclosure_name": {"caption": "iTOP Server Enclosure Name", "description": "Name of the physical enclosure or cage where the iTop server devices kept in.\nFor example Virtual Enclosure-1", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "itop_server_obsolete_status": {"caption": "iTOP Server Obsolete Status", "description": "Name of the physical enclosure or cage where the iTop server devices kept in.It is boolean value indicator.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates whether the physical enclosure or cage for iTop server devices is obsolete (boolean value)."}, "itop_server_obsolete_date": {"caption": "iTOP Server Obsolete Date", "description": "Date when a server is deemed obsolete or no longer in use within an organization's infrastructure.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when a server, such as an older model, is considered obsolete in an organization's infrastructure."}, "itop_server_brand": {"caption": "iTOP Server Brand Name", "description": "Brand of the iTop server.\nFor example Dell", "group": "source_specific", "type": "string", "navigator_attribute_description": "Brand of the iTop server, such as Dell."}, "itop_server_model": {"caption": "iTOP Server Model Name", "description": "Model of the iTop server.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Model of the iTop server, such as iTop 2.6 or iTop 3.0."}, "itop_server_os_family": {"caption": "iTOP Server OS Family", "description": "OS Family of the iTop server.\nFor example Windows.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The OS family of the iTop server, such as Windows.", "navigator_attribute_distinct_values": ["Windows", "Linux"]}, "itop_server_os_version": {"caption": "iTOP Server OS Version", "description": "Version of the operating system (OS) installed on a server within the iTOP.\nFor example Microsoft Windows Server 2019.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Version of the operating system installed on a server in iTOP, such as Microsoft Windows Server 2019."}, "itop_server_management_ip": {"caption": "iTOP Server IP Address", "description": "Internal IP address designated for managing a server within the iTOP server.\nFor example ************.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Internal IP address for server management in iTOP, e.g., ************."}, "itop_server_os_license": {"caption": "iTOP Server OS License", "description": "OS License of the iTop server.Shows information about the licensing status and details of the operating system (OS) installed on a server within the iTOP\nFor example Windows Server Standard.", "group": "source_specific", "type": "string", "navigator_attribute_description": "OS license information for the iTop server, such as Windows Server Standard, detailing the licensing status and OS installed."}, "itop_server_cpu": {"caption": "iTOP Server CPU Name", "description": "CPU used in the iTop server.This field typically includes details such as the CPU model, architecture, clock speed, number of cores, and other relevant specifications.\nFor example Intel Xeon Gold 6140.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "itop_server_ram": {"caption": "iTOP Server RAM Name", "description": "RAM used in the iTop server.\nFor example 64GB.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "itop_server_serial_number": {"caption": "iTOP Server Serial Number", "description": "Device serial number associated with each server managed within the iTop.\nFor example DELL123456.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Device serial number for each server in iTop, e.g., DELL123456."}, "itop_server_asset_number": {"caption": "iTOP Server Asset Number", "description": "Unique identifier assigned to individual servers managed within the iTop.\nFor example TSI-SRV-001.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Unique identifier for individual servers in iTop, such as TSI-SRV-001."}, "itop_server_move_to_production_date": {"caption": "iTOP Server Move To Production Date", "description": "Date when a server managed within the iTop was transitioned to a production environment for operational use.", "group": "source_specific", "type": "timestamp", "ui_visibility": false, "to_be_deprecated": true}, "itop_server_purchase_date": {"caption": "iTOP Server Purchase Date", "description": "Purchase date when a server asset was purchased within the iTop.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The date when a server asset was purchased in iTop."}, "itop_server_end_of_warranty_date": {"caption": "iTOP Server End of Warranty Date", "description": "End of warranty date of iTOP server.This date signifies the expiration of the warranty coverage provided by the server.", "group": "source_specific", "type": "string", "navigator_attribute_description": "End of warranty date for iTOP server, marking the end of warranty coverage."}, "itop_server_primary_power_source": {"caption": "iTOP Server Primary Power Source", "description": "Primary power source used by the iTOP server.\nFor example Generator-3", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "itop_server_secondary_power_source": {"caption": "iTOP Server Secondary Power Source", "description": "Secondary power source used by the iTOP server.\nFor example Generator-4", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "wiz_onboarding_status": {"caption": "Wiz Onboarding Status", "description": "Indicates that the onboarding process for a particular cloud resource within the Wiz platform has been successfully completed.\nIt is true.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates successful completion of the onboarding process for a cloud resource in the Wiz platform.", "navigator_attribute_distinct_values": [true]}, "wiz_onboarding_date": {"caption": "Wiz Onboarding Date", "description": "Date when a particular cloud resource was initially onboarded or registered within the Wiz platform. This date marks the beginning of the resource's visibility and management within the Wiz.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The wiz_onboarding_date indicates when a cloud resource was first registered in the Wiz platform, marking the start of its visibility and management."}, "wiz_last_scan_date": {"caption": "Wiz Last Scan Date", "description": "Date when a particular cloud resource was last scanned within the Wiz platform.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date of the last scan of a cloud resource in the Wiz platform."}, "wiz_operational_state": {"caption": "Wiz Operational State", "description": "Reflects the operational state of a cloud resource within the Wiz platform. Represent the current status or condition of the resource, indicating whether it is Active or Inactive.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates the operational state of a cloud resource in the Wiz platform, which can be either Active or Inactive.", "navigator_attribute_distinct_values": ["Inactive", "Active"]}, "wiz_modified_date": {"caption": "Wiz Modified Date", "description": "Date when a particular cloud resource was last modified or updated within the Wiz platform.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Date when a cloud resource was last modified on the Wiz platform."}, "aws_eks_finish_date": {"caption": "AWS EKS Finish Date", "description": "Denotes the timestamp indicating when a task, process, or operation within an EKS cluster on AWS was completed. This field is essential for tracking the timing of actions related to EKS operations, facilitating effective deployment, management, and optimization. Example values include '1706667569000' and '1706683831000'.\n", "group": "source_specific", "type": "timestamp", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_sensor_file_deployment_status": {"caption": "Crowdstrike ZTA Sensor File Deployement Status", "description": "Whether or not the data.zta file is deployed on the host.\nPossible values are Confirmed,Pending,Not Deployed.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_os_score": {"caption": "Crowdstrike ZTA OS Score", "description": "A score calculated by Crowdstrike by evaluationg various parameters to evaluate the security posture of an OS within the Zero Trust framework.\nIt will be a value between 0 and 100.", "group": "source_specific", "type": "integer", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_overall_score": {"caption": "Crowdstrike ZTA Overall Score", "description": "It refers to the comprehensive evaluation of an organization's adherence to Zero Trust principles across these different categories.\nIt will be a value from 1 to 100.\nIt is a combination of os signal score and sensor config score.", "group": "source_specific", "type": "integer", "navigator_attribute_description": "The crowdstrike_zta_overall_score, ranging from 1 to 100, evaluates an organization's adherence to Zero Trust principles based on the os signal score and sensor config score."}, "crowdstrike_zta_sensor_score": {"caption": "Crowdstrike ZTA Sensor Score", "description": "Sensor signal score as a value from crowdstrike zta from 1 to 100.", "group": "source_specific", "type": "integer", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_score_version": {"caption": "Crowdstrike ZTA Score Version", "description": "ZTA score version of crowdstrike.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_os_signal_meet_criteria_yes": {"caption": "Crowdstrike ZTA OS Signal Met", "description": "The assessment items(OS Signals) that meet the required criteria.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_os_signal_meet_criteria_no": {"caption": "Crowdstrike ZTA OS Signal Not Met", "description": "The assessment items (OS Signals) that does not meet the required criteria.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_os_signal_meet_criteria_unknown": {"caption": "Crowdstrike ZTA OS Signal Unknown", "description": "The assessment items (OS Signals) that has not been assessed or meet Unknown criterias.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_sensor_signal_meet_criteria_yes": {"caption": "Crowdstrike ZTA Sensor Signal Met", "description": "The assessment items (Sensor Signals) that meet the required criteria.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_sensor_signal_meet_criteria_no": {"caption": "Crowdstrike ZTA Sensor Signal Not Met", "description": "The assessment items (Sensor Signals) that does not meet the required criteria.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "crowdstrike_zta_sensor_signal_meet_criteria_unknown": {"caption": "Crowdstrike ZTA Sensor Signal Unknown", "description": "The assessment items (Sensor Signals) that has not been assessed or meet Unknown criterias.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenable_io_id": {"caption": "Tenable.io ID", "description": "The UUID of the asset in Tenable.io.This value is the unique key for the asset.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The tenable_io_id is the unique UUID key for an asset in Tenable.io."}, "tenable_io_onboarding_date": {"caption": "Tenable.io Onboarding Date", "description": "Indicates the date when the asset was onboarded into the Tenable.io platform. Format: YYYY-MM-DD. Example: '2023-01-15'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "Onboarding date of the asset in Tenable.io."}, "tenable_io_asset_agent_status": {"caption": "Tenable.io Asset Agent Status", "description": "Specifies whether a Nessus agent scan identified the asset.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if a Nessus agent scan found the asset."}, "tenable_io_asset_plugin_status": {"caption": "Tenable.io Asset Plugin Status", "description": "Specifies whether the asset has plugin results associated with it.", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "tenable_io_asset_updated_at": {"caption": "Tenable.io <PERSON> Updated Date", "description": "The timestamp indicating the last update of the asset record. Example: '2023-10-01T12:34:56Z'.\n", "group": "source_specific", "type": "timestamp", "ui_visibility": false, "to_be_deprecated": true}, "tenable_io_last_scan_date": {"caption": "Tenable.io <PERSON> Last Scan Date", "description": "The date and time when the most recent scan was conducted on the asset. For example, '2023-10-01T14:30:00Z'. \n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The last scan date for the asset indicates when it was last evaluated for vulnerabilities."}, "tenable_io_last_authenticated_scan_date": {"caption": "Tenable.io Last Autheticated Scan Date", "description": "The timestamp indicating when the last credentialed scan was performed on the asset. Example: '2023-10-01T12:30:00Z'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The last credentialed scan on the asset was performed on a specific date and time."}, "tenable_io_asset_last_licensed_scan_date": {"caption": "Tenable.io Last Licensed Scan Date", "description": "The time and date of the last scan that identified the asset as licensed. Tenable Vulnerability Management categorizes an asset as licensed if a scan of that asset has returned results from a non-discovery plugin within the last 90 days.", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The last scan date that identified the asset as licensed occurs if a non-discovery plugin scan returns results within the last 90 days."}, "tenable_io_asset_aws_terminated_date": {"caption": "Tenable.io AWS Terminated Date", "description": "The date and time when the Amazon Web Services (AWS) virtual machine instance associated with the asset was terminated by a user. Example: '2023-10-01T14:30:00Z'.\n", "group": "source_specific", "type": "timestamp", "navigator_attribute_description": "The date and time when the AWS virtual machine instance of the asset was terminated."}, "tenable_io_asset_source_name": {"caption": "Tenable.io Asset Source Name", "description": "The name of the entity that reported the asset details. Sources can include sensors, connectors, and API imports.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "The name of the entity reporting asset details, such as sensors, connectors, or API imports."}, "tenable_io_asset_license_status": {"caption": "Tenable.io Asset License Status", "description": "Indicates whether the asset was licensed at the time of the identified scans.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Indicates if the asset was licensed during the identified scans."}, "tenable_io_ipv4_addresses": {"caption": "Tenable.io IPv4 Address", "description": "The IPv4 addresses that scans have associated with the asset record.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "The IPv4 addresses linked to the asset record from scans."}, "tenable_io_ipv6_addresses": {"caption": "Tenable.io IPv6 Address", "description": "The IPv6 addresses that scans have associated with the asset record.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "IPv6 addresses linked to the asset record from scans."}, "tenable_io_system_type": {"caption": "Tenable.io System Type", "description": "The system types as reported by Plugin ID 54615. Nessus Plugin ID 54615 is associated with the 'System Type Enumeration' plugin. This plugin is designed to determine the type of system based on various characteristics observed during a vulnerability scan.", "group": "source_specific", "type": "string", "data_structure": "list", "navigator_attribute_description": "System types reported by Plugin ID 54615, used for identifying system characteristics during vulnerability scans."}, "aws_vpc_id": {"caption": "AWS VPC ID", "description": "The unique identifier for the virtual public cloud that hosts the AWS virtual machine instance.", "group": "source_specific", "type": "string", "navigator_attribute_description": "The aws_vpc_id is the unique identifier for the virtual public cloud hosting the AWS VM instance."}, "tenable_io_onboarding_status": {"caption": "Tenable.io Onboarding Status", "description": "Tenable.io coverage of the asset.", "group": "source_specific", "type": "string", "navigator_attribute_description": "Tenable.io onboarding status indicates the coverage of the asset."}, "tenable_io_agent_uuid": {"caption": "Tenable.io Agent UUID", "description": "The UUID of the agent that performed the scan where the vulnerability was found", "group": "source_specific", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "device_type_score": {"caption": "Host Type Risk Score", "description": "Risk score assigned to a host based on its device type, reflecting the potential exposure to risk associated with different device categories such as workstations, servers, and mobile devices. Higher scores indicate greater risk due to varying levels of access to information and security controls.\n", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "domain_score": {"caption": "Host Domain Risk Score", "description": "Risk score assigned to the host, indicating the level of security based on its domain membership status. Domain-joined devices typically have enhanced security policies and controls, while non-domain joined devices may lack these protections, resulting in a higher risk score. \n", "group": "enrichment", "type": "double", "ui_visibility": false, "to_be_deprecated": true}, "sensitive_role_host_score": {"caption": "Host Role Risk Score", "description": "Risk score assigned to a host based on its designation as a sensitive role. This score reflects the potential risks associated with the availability and criticality of the host within sensitive infrastructure. \n", "group": "enrichment", "type": "double", "ui_visibility": false, "to_be_deprecated": true}, "av_score": {"caption": "Host AV Risk Score", "description": "Risk score indicating the status of antivirus signature updates for the host. A value of 'true' signifies that the signatures are up to date, while 'false' indicates they are not.\n", "group": "enrichment", "type": "string", "ui_visibility": false, "to_be_deprecated": true}, "posture_score": {"caption": "Host Security Posture Risk Score", "description": "Risk score calculated based on the security posture of the host. A lower score indicates a higher level of risk.\n", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "host_inherent_score": {"caption": "Host Inherent Risk Score", "description": "Risk Score derived for the Host based on multiple risk attributes as below.\nHost Type:The potential exposure to risk varies from an workstation, server and user mobile devices due to the availability and access to information and controls in place.\nHost Domain:Domain joined devices will have policies and controls in place, these might be lacked in a non-domain joined devices there by increasing the risk factor.\nHost Role:Risk Score derived for the Host based the assignment of sensitive role for the host. For eg: Domain Controller.\nHost Security Posture:Potential exposure to risk based on security posture of the host such as regular AV signature updates.Lower posture score indicates higher risk.\nFor example, a score of 1.2 suggests moderate risk, while a score of 5.4 indicates a significantly higher risk level.\n", "group": "enrichment", "type": "double", "range_selection": true, "ui_visibility": false, "to_be_deprecated": true}, "asset_compliance_scope": {"caption": "Asset Compliance Scope", "description": "Specifies the range of compliance requirements applicable to the asset. This field can be customized to include specific checks, such as IP address ranges or asset group tags, based on client needs. For example, \"PCI DSS\" or \"HIPAA\". If the Asset Compliance Scope is not available, this field is marked as No Data.", "group": "enrichment", "type": "string", "data_structure": "list", "category": "General Information", "navigator_attribute_description": "Defines the compliance requirements for an asset, such as PCI DSS, and can be customized to include checks like IP address ranges or asset tagging. If the Asset Compliance Scope is not available, this field is marked as No Data.", "navigator_attribute_distinct_values": [["PCI DSS"], ["SOX"], ["No Data"]]}, "asset_is_inventoried": {"caption": "Is Inventoried", "description": "Indicates whether the asset is recorded in the client's inventory systems, such as a Configuration Management Database (CMDB) or directory service. Possible values are 'true' (asset is inventoried) or 'false' (asset is not inventoried).\n", "type": "string", "group": "enrichment", "navigator_attribute_description": "Indicates if an asset is listed in client inventories like CMDB or directory services.", "navigator_attribute_distinct_values": [true, false]}, "asset_security_posture": {"caption": "Security Posture Score", "description": "Indicates the security posture of an asset, represented as a score between 0 and 1. This score reflects the effectiveness of security measures in place to protect against potential threats, based on the presence and status of key security controls, such as Endpoint Detection and Response (EDR) and Vulnerability Management (VM) tools. For example, a score of 0.0 indicates poor security posture, while a score of 1.0 signifies optimal protection.\n", "type": "double", "range_selection": true, "min": 0, "max": 1, "step_interval": 0.01, "group": "enrichment", "navigator_attribute_description": "The asset security posture is a score from 0 to 1 indicating how well a device is protected against security threats, based on the presence of security controls like EDR and VM tools.", "navigator_attribute_distinct_values": [0.0, 0.5, 1.0], "navigator_deep_thinking_numerical": true}, "host_meet_security_posture": {"caption": "Host Meet Security Posture", "description": "Indicates compliance with the organization's security standards based on the presence of an Endpoint Detection and Response (EDR) and Vulnerability Management (VM) agent. A value of 'true' signifies that the host meets the required security measures, while 'false' indicates non-compliance. Example values include 'true' and 'false'.\n", "type": "string", "group": "enrichment", "navigator_attribute_description": "Host security posture indicates compliance with organizational standards, with hosts having EDR and VM agents deemed compliant.", "navigator_attribute_distinct_values": [false, true]}, "infrastructure_type": {"caption": "Infrastructure Type", "description": "Indicates the deployment environment of a device, specifying whether it is hosted on a cloud platform (e.g., AWS, Azure, Google Cloud) or located on-premise within an organization's data center. Examples include 'Cloud' and 'On-Premise'.\n", "type": "string", "group": "enrichment", "navigator_attribute_description": "Indicates if a device is hosted on a cloud platform (e.g., AWS, Azure, Google Cloud) or on-premise in a local data center.", "navigator_attribute_distinct_values": ["Cloud", "On-Premise"]}, "threat_count": {"caption": "Threat Count", "description": "The number of threats detected on a device. For example, values may include '0' or '1'.\n", "group": "entity_specific", "type": "integer", "ui_visibility": true, "range_selection": true}, "encryption_status": {"caption": "Encryption Status", "description": "It likely indicates whether encryption is enabled on devices managed through a Mobile Device Management (MDM) solution.It is a boolean value indicator.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "compliance_state": {"caption": "Compliance State", "description": "Indicates the compliance status of a device within a Mobile Device Management (MDM) system, reflecting adherence to established policies and rules. Possible values include 'compliant', 'inGracePeriod', 'non-compliant', and 'Config Manager'.\n", "group": "entity_specific", "type": "string", "ui_visibility": true}, "archival_flag": {"caption": "Archival Flag", "description": "The Archival Flag indicates whether the host is set to be archived or not.", "group": "enrichment", "examples": "", "type": "string", "enable_hiding": true, "dashboard_identifier": {"EI": {}}}}, "dashboard_identifier": "EI"}