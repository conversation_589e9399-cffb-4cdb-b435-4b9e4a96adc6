{"caption": "Account", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "extends": "", "attributes": {"p_id": {"caption": "Entity ID", "description": "Unique identifier generated for each entity by the Entity Inventory.It is generated using the primary key, origin, class, and the attribute name of the primary key, as obtained from the data.", "examples": ["0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "112bfd47e969ccaad185b1f087dd9c8b6e23bf6f882ccb59343af3476159e8cc"], "group": "common", "enable_hiding": true, "type": "string", "category": "General Information"}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it.", "examples": ["<EMAIL>", "P-111"], "group": "common", "enable_hiding": false, "type": "string", "category": "General Information"}, "class": {"caption": "Class", "description": "The category of the entity, which corresponds to the entity type itself. Examples include Host, Person, Vulnerability etc.", "examples": ["Host", "Person", "Identity", "Vulnerability"], "group": "common", "enable_hiding": false, "type": "string"}, "type": {"caption": "Type", "description": "A classification that describes the nature or purpose of the account. e.g. Windows Account, AWS IAM User", "group": "common", "enable_hiding": false, "type": "string"}, "origin": {"caption": "Origin", "description": "Data source(s) in which the entity was present.\nEg:MS Active Directory,MS Azure AD and AWS", "examples": ["MS Intune", "Defender"], "group": "common", "enable_hiding": false, "type": "string", "category": "General Information", "data_structure": "list"}, "count_of_origin": {"caption": "Origin (Count)", "description": "Number of data sources from which the entity has been extracted.", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true}, "data_source_subset_name": {"caption": "Data Feed", "description": "The API feed from which the data is being ingested.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": false, "type": "string", "category": "General Information", "data_structure": "list"}, "first_found_date": {"caption": "First Found", "description": "Date at which the entity was first discovered in the ingested data.\nThis will be the minimum time at which the entity is observed within the scope of inventory run.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp"}, "first_seen_date": {"caption": "First Seen", "description": "Initial observation date of the entity as inferred from available data sources.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp"}, "last_updated_date": {"caption": "Last Updated", "description": "Most recent date on which any update happened on an entity as inferred from the data.\nDefaults to First Found.\nIf any of the relevant attribute changes within an entity then that date is considered as last updated date.", "examples": "*************", "group": "common", "type": "timestamp", "derived_field": true, "enable_hiding": false}, "associated_identity_count": {"caption": "Count of Identities having Accounts", "description": "Number of Identities of the Account.", "group": "enrichment", "enable_hiding": true, "type": "integer", "range_selection": true}, "last_found_date": {"caption": "Last Found", "description": "The date at which the entity was last observed in the ingested data.\nThis will be the maximum time at which the entity is observed within the scope of inventory run.", "group": "common", "examples": "*************", "enable_hiding": false, "type": "timestamp"}, "last_active_date": {"caption": "Last Active", "description": "Latest date on which an activity has been observed for the entity.", "examples": "*************", "group": "common", "type": "timestamp", "enable_hiding": false, "derived_field": true, "category": "General Information"}, "activity_status": {"caption": "Activity Status", "description": "Specifies the current status of the entity. \n\nIf the time since the last inventory update is less than the specified inactivity period, the entity is considered active; otherwise, it is inactive. \nFor Cloud data sources such as AWS, Azure, etc., the inactivity period is set to 2 days, while for other sources like VM, EDR, etc., it is set to 180 days.", "group": "common", "type": "string", "enable_hiding": false, "derived_field": true}, "lifetime": {"caption": "Lifetime", "description": "The duration of time the entity has been active, calculated as the difference between the Last Active Date and the First Seen Date.This represents the total span in days from the moment they were first detected or engaged with the system until their most recent activity.", "examples": "5", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true}, "recent_activity": {"caption": "Recent Activity", "description": "Number of days since the entity was last active as inferred from ingested data sources.Field is determined based on the difference between last inventory update date and the last active date inferred from the source.", "examples": "10", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true}, "observed_lifetime": {"caption": "Observed Lifetime", "description": "Number of days over which the entity was present in one or more ingested data sources.This field is calculated as the difference between the time the entity was first found in the ingested data and the time it was last found.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true}, "recency": {"caption": "Recency", "description": "Number of days since the entity was last discovered in the ingested data. This field is determined based on the difference between last inventory update date and the last found date in the ingested data.", "examples": "10", "group": "common", "enable_hiding": true, "type": "integer", "range_selection": true, "category": "General Information"}, "description": {"caption": "Description", "description": "Detailed explanation of the entity.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string"}, "business_unit": {"caption": "Business Unit", "description": "The business unit within an organization is often a department or team responsible for specific functions, products, or markets.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string"}, "location_country": {"caption": "Location Country", "description": "IDP provider configured country location of the account", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string"}, "location_city": {"caption": "Location City", "description": "Location City of the entity.", "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string"}, "department": {"caption": "Department", "description": "Name of the department within the business unit.", "group": "common", "examples": "", "ui_visibility": true, "enable_hiding": true, "type": "string"}, "fragments": {"caption": "Fragments", "description": "Count of partial records or pieces of evidence of an entity.\nIt is determined based on number of sources that gets resolved for each entity.", "group": "common", "enable_hiding": false, "type": "integer", "range_selection": true}, "last_updated_attrs": {"caption": "Last Updated Attributes", "description": "Key fields that are considering for updating the last updated date of an entity.", "examples": "{identity_provider,identity_type,account_name,account_type,status,ad_distinguished_name,ad_when_created_date,ad_account_expires_date,ad_last_password_change_date,ad_sam_account_name_with_domain,user_principal_name,iga_account_id,ad_last_login_date,ad_password_policy,ad_last_sync_date,azure_on_premises_sync_enabled_status,azure_enrollment_type,ad_security_identifier,ad_sam_account_type,ad_member_of,manager}", "group": "common", "type": "string", "ui_visibility": false, "enable_hiding": true, "data_structure": "struct"}, "inactivity_period": {"caption": "Inactivity Period", "description": "Indicates the number of days after which the entity become inactive.", "group": "common", "ui_visibility": false, "enable_hiding": true, "type": "integer"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "description": "Describes whether origin is unique or corroborated.", "group": "enrichment", "enable_hiding": true, "type": "string"}, "operational_status": {"caption": "Operational Status", "description": "Indicates the current functioning state of the entity derived from the source.It reflects whether entity is active or disabled.", "examples": ["Active", "Disabled"], "group": "entity_specific", "type": "string", "category": "Account Status"}, "account_name": {"caption": "Account Name", "description": "Name of the Account entity.", "examples": ["<EMAIL>"], "group": "entity_specific", "type": "string"}, "account_display_name": {"caption": "Account Full Name", "description": "Full name of the account including the identity name and Service.", "examples": ["<EMAIL>: Azure AD"], "group": "entity_specific", "type": "string", "category": "General Information"}, "source_of_identity": {"caption": "Source Of Identity", "description": "Origin which the Identity is created from.", "examples": ["Azure AD"], "group": "entity_specific", "type": "string", "category": "General Information"}, "login_last_date": {"caption": "Login Last Date", "description": "The last login date of the entity based on the signin activity.", "group": "entity_specific", "type": "timestamp", "category": "Login and Access History"}, "service": {"caption": "Service", "description": "Identity provider service or applications logged using the account.", "examples": ["Azure AD"], "group": "entity_specific", "type": "string", "category": "General Information"}, "internal_service": {"caption": "Internal Service", "description": "Internal service within the vendor for which account is used such as IAM Center, Cloudtrail.", "examples": ["IAM Users"], "group": "entity_specific", "type": "string", "data_structure": "list", "category": "General Information"}, "entitlement_id": {"caption": "Entitlement ID", "description": "ID's of the entitlement or roles assigned for a particular account.", "examples": ["[18d5622d-6459-4cb0-900f-7adbeb8476f0]"], "group": "entity_specific", "type": "string", "data_structure": "list"}, "entitlement_value": {"caption": "Entitlement Value", "description": "Role or permission group associated to the account.Eg:Infosec Team, Schema Admin,Remote Desktop User.", "examples": ["[License Administrator]"], "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Privileged Access Management"}, "entitlement_description": {"caption": "Entitlement Description", "description": "Descriptive information regarding the entitlement.", "examples": ["Can create and manage all aspects of app registrations and enterprise apps."], "group": "entity_specific", "type": "string", "data_structure": "string", "ui_visibility": false, "has_line_break": true}, "entitlement_details": {"caption": "Entitlement Details", "description": "Descriptive information regarding the entitlement along with the role name.", "examples": ["Can create and manage all aspects of app registrations and enterprise apps."], "group": "entity_specific", "type": "string", "data_structure": "string", "has_line_break": true}, "privilege_account": {"caption": "Privilege Account", "description": "Specifies whether the account is privileged or not based on the entitlements associated with the entity or the information available from the data source.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Privileged Access Management"}, "privilege_roles": {"caption": "Privilege Roles", "description": "Specifies privilege roles assigned for the account.\nEg:Gloabl Reader,Global Administrator,User Administrator", "examples": [""], "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Privileged Access Management"}, "privilege_roles_count": {"caption": "Privilege Count", "description": "Specifies the number of privilege roles assigned for the account.", "examples": [""], "group": "entity_specific", "type": "integer", "category": "Privileged Access Management"}, "is_mfa_enabled": {"caption": "Is MFA Enabled", "description": "Indicates whether the account has MFA registered or not.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Authentication Details"}, "ownership_of_identity": {"caption": "Ownership Of Associated Identity", "description": "Specifies the ownership category of the Identity associated to this account.Eg:Corp,External.\nIdentities owned and managed within the organization are classified as Corp and the rest are marked as external.", "examples": ["Corp", "External"], "group": "entity_specific", "type": "string", "category": "General Information"}, "account_never_expire": {"caption": "Account Never Expire", "description": "Boolean value indicating whether the account is configured to never expire.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Account Password Information"}, "password_not_required": {"caption": "Password Not Required", "description": "Boolean value indicating whether the account is configured to have blank password.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Account Password Information"}, "password_never_expire": {"caption": "Password Never Expire", "description": "Boolean value indicating whether password for the account is configured to never expire.", "examples": ["True"], "group": "entity_specific", "type": "string", "category": "Account Password Information"}, "last_password_change_date": {"caption": "Last Password Change", "description": "Latest date at which the password is updated.", "examples": ["*************", "*************"], "group": "entity_specific", "type": "timestamp"}, "password_last_used": {"caption": "Last Password Used Date", "description": "Latest date at which the password is used.", "examples": ["*************", "*************"], "group": "entity_specific", "type": "timestamp"}, "last_logged_in_location": {"caption": "Successful Login Location", "description": "Country from which latest successful login attempt was made.", "group": "entity_specific", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "Login and Access History"}, "failed_login_attempt_location": {"caption": "Failed Login Attempt Location", "description": "Country from which latest failed login attempt was made.", "group": "entity_specific", "ui_visibility": true, "enable_hiding": true, "type": "string", "category": "Login and Access History"}, "account_id": {"caption": "Account ID", "description": "Unique identifier associated with an account in a cloud provider.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "General Information"}, "failed_logon_count": {"caption": "Failed Logon Count", "description": "Count of failed logins for the latest day respective to entity.", "group": "entity_specific", "type": "string", "ui_visibility": false}, "last_lock_out_time": {"caption": "Last Lock Out Time", "description": "Time at which the account was last locked out from the IDP.", "group": "entity_specific", "type": "timestamp", "ui_visibility": true}, "last_lock_out_flag": {"caption": "Last Lock Out Flag", "description": "Boolean value indicating whether the account was locked out on the latest day.", "group": "entity_specific", "type": "string", "ui_visibility": true, "category": "Login and Access History"}, "bad_password_configured_time": {"caption": "Bad Password Configured Time", "description": "Indicates the threshold value for the lockout of an account respective to the IDP.This value differs based on the organization.", "group": "entity_specific", "type": "integer", "ui_visibility": true}, "bad_password_count_flag": {"caption": "Bad Password Flag", "description": "Indicates whether the number of bad password attempts have breached the threshold value of bad password configured time in the IDP.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "location_accessed_flag": {"caption": "Multiple Location Access Flag", "description": "Indicates whether the location of sign-in is from more than 1 country on the latest day.", "group": "entity_specific", "type": "string", "ui_visibility": true}, "last_signin_attempt": {"caption": "Last Signin Attempt", "description": "The lastest date of sign in attempt found in the sign in logs.", "examples": ["*************"], "group": "entity_specific", "type": "timestamp"}, "last_failed_signin_attempt": {"caption": "Last Failed Signin Attempt", "description": "The lastest date of failed sign in attempt found in the sign in logs.", "examples": ["*************"], "group": "entity_specific", "type": "timestamp"}, "is_sspr_registered": {"caption": "Is SSPR Registered", "description": "Indicates whether the user has completed the registration process for Self Service Password Reset.", "group": "entity_specific", "type": "string", "category": "Account Password Information"}, "auth_methods_registered": {"caption": "Authentication Methods Registered", "description": "Collection of authentication methods registered for the account.\nEg:mobilePhone,microsoftAuthenticatorPush", "group": "entity_specific", "type": "string", "data_structure": "list", "category": "Authentication Details"}, "default_mfa_method": {"caption": "Default MFA Method", "description": "The method the user selected as the default second-factor for performing multi-factor authentication.", "group": "entity_specific", "type": "string"}, "aad_created_date": {"caption": "AAD Created", "description": "Date when Azure AD object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp"}, "ad_created_date": {"caption": "AD Created", "description": "Date when Active Directory object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp"}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "description": "A SAM Account Type is a single valued indexed attribute that uniquely defines user objects in AD.", "examples": "*********", "group": "source_specific", "type": "string"}, "aws_account_created_date": {"caption": "AWS Created Date", "description": "Date when AWS object was created.", "examples": ["*************", "*************"], "group": "source_specific", "type": "timestamp"}, "authentication_factors": {"caption": "Authentication Factors", "description": "Lists the various authentication factors that are enabled for the Account.", "group": "enrichment", "type": "string", "data_structure": "list", "category": "Authentication and Security"}, "privilege_score": {"caption": "Account Privilege Risk Score", "description": "Risk Score derived for the account based on the presence of privileged permissions.", "group": "enrichment", "type": "double", "range_selection": true}, "idle_score": {"caption": "Account Idle Risk Score", "description": "Risk Score derived for the account based on the duration of idle status.\nHigher the duration, higher is the risk.", "group": "enrichment", "type": "double", "range_selection": true}, "activity_score": {"caption": "Account Activity Risk Score", "description": "Risk Score derived for the account based on the activity status.\nInactive accounts are considered to be of higher risk.", "group": "enrichment", "type": "double", "range_selection": true}, "account_inherent_score": {"caption": "Account Inherent Risk Score", "description": "Inherent Risk Score derived for the account based on below risk attributes.\nPrivilege Risk:Checks whether account has privileged permissions such as administrator or domain admins.\nIdle Risk:Checks on the duration of idle status.Higher the duration, higher is the risk.\nActivity Risk:Checks on the activity status.Inactive accounts are considered to be of higher risk.\nRole risk:Account is considered to be risky if the IDP has marked the assigned role as critical.", "group": "enrichment", "type": "double", "range_selection": true}}, "dashboard_identifier": "EI"}