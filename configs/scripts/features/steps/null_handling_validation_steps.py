import os
import sys
import json
import re
import requests
from behave import given, when, then
from jsonschema import validate, ValidationError
import sys
import os.path

# Import the schema from the local schemas directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from schemas.data_dictionary_schema import DATA_DICTIONARY_SCHEMA

class NullHandlingValidator:
    def __init__(self, project_root, entity_filter=None):
        self.project_root = project_root
        self.entity_filter = entity_filter
        self.data_dictionary = None
        self.string_attributes = {}  # {entity: [attribute_names]}
        self.attribute_feed_mapping = {}  # {entity: {attribute: [feedNames]}}
        self.validation_results = {}  # {entity: {attribute: {result: bool, message: str, reason: str}}}

        # Paths - project_root already includes the base path, so don't add 'configs' again
        self.inventory_models_path = os.path.join(project_root, 'spark_job_configs', 'source_models', 'inventory_models')
        self.global_entity_config_path = os.path.join(project_root, 'spark_job_configs', 'source_models', 'global_entity_config')

        print(f"Inventory models path: {self.inventory_models_path}")
        print(f"Global entity config path: {self.global_entity_config_path}")

        # Verify paths exist
        if not os.path.exists(self.inventory_models_path):
            print(f"Warning: Inventory models path does not exist: {self.inventory_models_path}")
        if not os.path.exists(self.global_entity_config_path):
            print(f"Warning: Global entity config path does not exist: {self.global_entity_config_path}")

    def fetch_data_dictionary(self, token):
        """Fetch the entity data dictionary from the API using the provided token"""
        url = "https://staging.demo.prevalent.ai/em-api/v1/general-configs/entity-data-dictionary"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            self.data_dictionary = response.json()

            # Validate against schema
            validate(instance=self.data_dictionary, schema=DATA_DICTIONARY_SCHEMA)

            return True
        except requests.exceptions.RequestException as e:
            print(f"Error fetching data from API: {e}")
            return False
        except ValidationError as e:
            print(f"Data dictionary does not conform to schema: {e}")
            return False

    def extract_string_attributes(self):
        """Extract attributes with type 'string' and ui_visibility not false for each entity"""
        if not self.data_dictionary:
            return False

        entities = self.data_dictionary.get('config', {})

        for entity_name, entity_data in entities.items():
            # Skip if entity filter is provided and doesn't match
            if self.entity_filter and self.entity_filter.lower() != entity_name.lower():
                continue

            attributes = entity_data.get('attributes', {})
            string_attrs = []

            for attr_name, attr_data in attributes.items():
                # Check if the attribute type is string and ui_visibility is not false
                if attr_data.get('type') == 'string' and attr_data.get('ui_visibility') is not False:
                    string_attrs.append(attr_name)

            if string_attrs:
                self.string_attributes[entity_name] = string_attrs

        return len(self.string_attributes) > 0

    def collect_feed_names(self):
        """Collect feedName values for string attributes from inventory models"""
        if not self.string_attributes:
            return False

        # List all directories in inventory_models_path for debugging
        print(f"\nAvailable directories in inventory_models_path:")
        if os.path.exists(self.inventory_models_path):
            for item in os.listdir(self.inventory_models_path):
                if os.path.isdir(os.path.join(self.inventory_models_path, item)):
                    print(f"  - {item}")
        else:
            print(f"  (inventory_models_path does not exist)")

        for entity_name, attributes in self.string_attributes.items():
            # Try different possible folder naming patterns
            possible_folders = [
                f"sds_ei__loader__{entity_name.lower().replace(' ', '_')}",
                f"sds_ei__loader__{entity_name.lower()}",
                entity_name.lower().replace(' ', '_')
            ]

            entity_path = None
            for folder in possible_folders:
                path = os.path.join(self.inventory_models_path, folder)
                if os.path.exists(path):
                    entity_path = path
                    print(f"Found entity folder for '{entity_name}': {folder}")
                    break

            # Check if the entity folder exists
            if not entity_path:
                print(f"Warning: Entity folder not found for '{entity_name}'. Tried: {possible_folders}")
                continue

            self.attribute_feed_mapping[entity_name] = {}

            # Process all JSON files in the entity folder
            for root, _, files in os.walk(entity_path):
                for file in files:
                    if not file.endswith('.json'):
                        continue

                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r') as f:
                            config = json.load(f)

                        # Get the feed name from the 'origin' key only
                        feed_name = None
                        has_origin = 'origin' in config

                        if has_origin:
                            # The origin is often in the format "'Feed Name'" with quotes
                            # Extract the actual feed name by removing quotes
                            origin_value = config['origin']
                            # Remove surrounding quotes if present
                            if origin_value.startswith("'") and origin_value.endswith("'"):
                                feed_name = origin_value[1:-1]
                            elif origin_value.startswith('"') and origin_value.endswith('"'):
                                feed_name = origin_value[1:-1]
                            else:
                                feed_name = origin_value

                        # Check sourceSpecificProperties for string attributes even if no origin
                        # This allows us to track attributes from files without origin
                        source_specific_props = config.get('sourceSpecificProperties', [])

                        # If no origin, mark attributes from this file but continue processing
                        if not has_origin or not feed_name:
                            for prop in source_specific_props:
                                col_name = prop.get('colName')
                                if col_name in attributes:
                                    if col_name not in self.attribute_feed_mapping[entity_name]:
                                        self.attribute_feed_mapping[entity_name][col_name] = []
                                    # Add a special marker to indicate missing origin
                                    self.attribute_feed_mapping[entity_name][col_name].append('__MISSING_ORIGIN__')
                            # Skip to next file
                            continue

                        # Check sourceSpecificProperties for string attributes
                        source_specific_props = config.get('sourceSpecificProperties', [])
                        for prop in source_specific_props:
                            col_name = prop.get('colName')
                            if col_name in attributes:
                                if col_name not in self.attribute_feed_mapping[entity_name]:
                                    self.attribute_feed_mapping[entity_name][col_name] = []
                                if feed_name not in self.attribute_feed_mapping[entity_name][col_name]:
                                    self.attribute_feed_mapping[entity_name][col_name].append(feed_name)
                    except Exception as e:
                        print(f"Error processing file {file_path}: {e}")

        return True

    def validate_null_handling(self):
        """Validate that each string attribute has proper null handling in global entity config"""
        if not self.attribute_feed_mapping:
            return False

        # Track additional attributes with proper structure but not in our list
        self.additional_attributes = {}

        for entity_name, attributes in self.attribute_feed_mapping.items():
            # Convert entity name to file name format
            entity_file_name = f"{entity_name.lower().replace(' ', '_')}_entity_config.json"
            entity_config_path = os.path.join(self.global_entity_config_path, entity_file_name)

            if not os.path.exists(entity_config_path):
                print(f"Warning: Global entity config not found: {entity_config_path}")
                continue

            try:
                with open(entity_config_path, 'r') as f:
                    global_config = json.load(f)

                # Initialize results for this entity
                self.validation_results[entity_name] = {}

                # Check if sourceSpecificProperties section exists
                if 'sourceSpecificProperties' not in global_config:
                    print(f"Warning: No sourceSpecificProperties section found in {entity_file_name}")
                    # Initialize sourceSpecificProperties if it doesn't exist
                    global_config['sourceSpecificProperties'] = []

                # Get source specific properties
                source_specific_props = global_config.get('sourceSpecificProperties', [])

                # Also check other property sections for comparison
                other_properties = []
                for prop_section in ['commonProperties', 'entitySpecificProperties']:
                    if prop_section in global_config:
                        other_properties.extend(global_config[prop_section])

                # Create a set of all attribute names we're checking
                expected_attributes = set(attributes.keys())

                # Validate each attribute
                for attr_name, feed_names in attributes.items():
                    # Check for special cases
                    if not feed_names:
                        self.validation_results[entity_name][attr_name] = {
                            'result': False,
                            'message': 'No feed names found for this attribute',
                            'reason': 'no_feed_names'
                        }
                        continue

                    # Check if the attribute has files with missing origin
                    if '__MISSING_ORIGIN__' in feed_names:
                        # Remove the special marker
                        feed_names.remove('__MISSING_ORIGIN__')

                        # If there are no other feed names, mark as missing origin
                        if not feed_names:
                            self.validation_results[entity_name][attr_name] = {
                                'result': False,
                                'message': 'Attribute found in files without origin key',
                                'reason': 'missing_origin'
                            }
                            continue
                        # Otherwise, continue with the remaining feed names but note the issue
                        else:
                            print(f"Warning: Attribute {attr_name} found in some files without origin key")

                    # First check if attribute exists in sourceSpecificProperties
                    found_in_source_specific = False
                    for prop in source_specific_props:
                        if prop.get('colName') == attr_name:
                            found_in_source_specific = True
                            expr = prop.get('colExpr', '')
                            fields_spec = prop.get('fieldsSpec', {})

                            # Check for required structure
                            has_fields_spec = 'fieldsSpec' in prop
                            has_computation_phase = fields_spec.get('computation_phase') == 'inter'

                            # Check for null handling pattern
                            # Expected: COALESCE({attribute}, CASE WHEN array_contains(data_source_subset_name, <feedName1>) OR ... THEN 'No Data' END)
                            has_coalesce = 'COALESCE(' in expr.upper()

                            # Check if first attribute in COALESCE matches the attribute name
                            first_attr_match = False
                            if has_coalesce:
                                # Extract the first parameter of COALESCE
                                # Use a more efficient regex pattern to avoid potential ReDoS vulnerability
                                # Limit the amount of whitespace with {0,10} and use a non-greedy quantifier for the parameter
                                coalesce_match = re.search(r'COALESCE\s{0,10}\(\s{0,10}([^,]+?)\s{0,10},', expr.upper())
                                if coalesce_match:
                                    first_param = coalesce_match.group(1).strip()
                                    # Check if it matches the attribute name
                                    first_attr_match = first_param.upper() == attr_name.upper()

                            has_case_when = 'CASE WHEN' in expr.upper()
                            has_data_source_subset = 'DATA_SOURCE_SUBSET_NAME' in expr.upper()
                            has_array_contains = 'ARRAY_CONTAINS(' in expr.upper()
                            has_no_data = "'NO DATA'" in expr.upper() or '"NO DATA"' in expr.upper()

                            # Check if all required elements are present
                            if has_coalesce and first_attr_match and has_case_when and has_data_source_subset and has_array_contains and has_no_data and has_fields_spec and has_computation_phase:
                                # Check if all feed names are included in array_contains expressions
                                all_feeds_included = True
                                missing_feeds = []

                                for feed in feed_names:
                                    # Look for array_contains(data_source_subset_name, '<feed>') pattern
                                    feed_pattern = f"ARRAY_CONTAINS\\s*\\(\\s*DATA_SOURCE_SUBSET_NAME\\s*,\\s*['\"]?{re.escape(feed)}['\"]?\\s*\\)"
                                    if not re.search(feed_pattern, expr.upper(), re.IGNORECASE):
                                        all_feeds_included = False
                                        missing_feeds.append(feed)

                                # For multiple feed names, check for OR operators between array_contains expressions
                                missing_or_operators = False
                                if len(feed_names) > 1:
                                    has_or_operators = expr.upper().count(' OR ') >= (len(feed_names) - 1)
                                    if not has_or_operators:
                                        all_feeds_included = False
                                        missing_or_operators = True

                                if all_feeds_included:
                                    self.validation_results[entity_name][attr_name] = {
                                        'result': True,
                                        'message': 'Proper null handling expression found in sourceSpecificProperties with correct structure',
                                        'reason': 'pass'
                                    }
                                else:
                                    error_message = 'Not all feed names included in array_contains expressions'
                                    if missing_feeds:
                                        error_message += f". Missing feeds: {', '.join(missing_feeds)}"
                                    if missing_or_operators:
                                        error_message += ". Missing OR operators between array_contains expressions"

                                    self.validation_results[entity_name][attr_name] = {
                                        'result': False,
                                        'message': error_message,
                                        'reason': 'missing_feeds_or_operators'
                                    }
                            else:
                                # Detailed error message based on what's missing
                                missing = []
                                if not has_coalesce: missing.append("COALESCE expression")
                                if has_coalesce and not first_attr_match: missing.append(f"first parameter in COALESCE matching attribute name '{attr_name}'")
                                if not has_case_when: missing.append("CASE WHEN clause")
                                if not has_data_source_subset: missing.append("data_source_subset_name reference")
                                if not has_array_contains: missing.append("array_contains function")
                                if not has_no_data: missing.append("'No Data' text")
                                if not has_fields_spec: missing.append("fieldsSpec section")
                                if not has_computation_phase: missing.append("computation_phase: 'inter'")

                                self.validation_results[entity_name][attr_name] = {
                                    'result': False,
                                    'message': f"Missing required elements: {', '.join(missing)}",
                                    'reason': 'missing_elements'
                                }
                            break

                    # If not found in sourceSpecificProperties, check if it exists elsewhere
                    if not found_in_source_specific:
                        found_elsewhere = False
                        for prop in other_properties:
                            if prop.get('colName') == attr_name:
                                found_elsewhere = True
                                self.validation_results[entity_name][attr_name] = {
                                    'result': False,
                                    'message': f"Attribute found in another section but should be in sourceSpecificProperties",
                                    'reason': 'wrong_section'
                                }
                                break

                        if not found_elsewhere:
                            self.validation_results[entity_name][attr_name] = {
                                'result': False,
                                'message': 'Attribute not found in global entity config',
                                'reason': 'not_found'
                            }

                # Now check for additional attributes in sourceSpecificProperties that aren't in our expected list
                if entity_name not in self.additional_attributes:
                    self.additional_attributes[entity_name] = []

                for prop in source_specific_props:
                    attr_name = prop.get('colName')
                    if attr_name and attr_name not in expected_attributes:
                        # Check if it has the expected structure
                        expr = prop.get('colExpr', '')
                        fields_spec = prop.get('fieldsSpec', {})

                        has_fields_spec = 'fieldsSpec' in prop
                        has_computation_phase = fields_spec.get('computation_phase') == 'inter'
                        has_coalesce = 'COALESCE(' in expr.upper()
                        has_case_when = 'CASE WHEN' in expr.upper()
                        has_data_source_subset = 'DATA_SOURCE_SUBSET_NAME' in expr.upper()
                        has_array_contains = 'ARRAY_CONTAINS(' in expr.upper()
                        has_no_data = "'NO DATA'" in expr.upper() or '"NO DATA"' in expr.upper()

                        # Check if first attribute in COALESCE matches the attribute name
                        first_attr_match = False
                        if has_coalesce:
                            # Use a more efficient regex pattern to avoid potential ReDoS vulnerability
                            # Limit the amount of whitespace with {0,10} and use a non-greedy quantifier for the parameter
                            coalesce_match = re.search(r'COALESCE\s{0,10}\(\s{0,10}([^,]+?)\s{0,10},', expr.upper())
                            if coalesce_match:
                                first_param = coalesce_match.group(1).strip()
                                first_attr_match = first_param.upper() == attr_name.upper()

                        if has_coalesce and first_attr_match and has_case_when and has_data_source_subset and has_array_contains and has_no_data and has_fields_spec and has_computation_phase:
                            self.additional_attributes[entity_name].append({
                                'name': attr_name,
                                'expression': expr
                            })
            except Exception as e:
                print(f"Error processing global entity config {entity_config_path}: {e}")

        return True

    def get_validation_summary(self):
        """Get a summary of validation results"""
        if not self.validation_results:
            return "No validation results available"

        summary = []
        total_passed = 0
        total_failed = 0
        total_attributes = 0

        # Group attributes by their status for each entity
        for entity_name, attributes in self.validation_results.items():
            entity_summary = f"Entity: {entity_name}\n"
            passed = 0
            failed = 0

            # First list all passing attributes
            passing_attrs = []
            for attr_name, result in attributes.items():
                if result['result']:
                    passing_attrs.append(f"  ✓ {attr_name}: {result['message']}")
                    passed += 1
                    total_passed += 1

            # Group failing attributes by reason
            failing_attrs_by_reason = {}
            for attr_name, result in attributes.items():
                if not result['result']:
                    reason = result.get('reason', 'unknown')
                    if reason not in failing_attrs_by_reason:
                        failing_attrs_by_reason[reason] = []
                    failing_attrs_by_reason[reason].append(f"  ✗ {attr_name}: {result['message']}")
                    failed += 1
                    total_failed += 1

            total_attributes += len(attributes)

            # Add passing attributes to summary
            if passing_attrs:
                entity_summary += f"  Passing Attributes ({passed}):\n"
                entity_summary += "\n".join(passing_attrs) + "\n"

            # Add failing attributes to summary, grouped by reason
            if failing_attrs_by_reason:
                entity_summary += f"  Failing Attributes ({failed}):\n"

                # Define the order of reasons for better readability
                reason_order = [
                    'not_found',
                    'wrong_section',
                    'missing_elements',
                    'missing_feeds_or_operators',
                    'no_feed_names',
                    'missing_origin',
                    'unknown'
                ]

                # Reason display names for better readability
                reason_display = {
                    'not_found': 'Attributes Not Found in Config',
                    'wrong_section': 'Attributes in Wrong Section',
                    'missing_elements': 'Attributes Missing Required Elements',
                    'missing_feeds_or_operators': 'Attributes Missing Feed Names or OR Operators',
                    'no_feed_names': 'Attributes with No Feed Names',
                    'missing_origin': 'Attributes Found in Files Without Origin Key',
                    'unknown': 'Attributes with Unknown Issues'
                }

                # Add failing attributes grouped by reason in the defined order
                for reason in reason_order:
                    if reason in failing_attrs_by_reason:
                        entity_summary += f"    {reason_display.get(reason, reason)} ({len(failing_attrs_by_reason[reason])}):\n"
                        entity_summary += "\n".join(failing_attrs_by_reason[reason]) + "\n"

            # Add entity summary
            entity_summary += f"  Entity Summary: {passed} passed, {failed} failed, {passed + failed} total\n"
            summary.append(entity_summary)

        # Add information about additional attributes
        additional_summary = []
        total_additional = 0

        for entity_name, attrs in self.additional_attributes.items():
            if attrs:  # Only include entities with additional attributes
                additional_summary.append(f"\nAdditional Attributes for {entity_name} ({len(attrs)}):\n")
                for attr in attrs:
                    additional_summary.append(f"  - {attr['name']}: Has proper null handling structure but not in expected string attributes list")
                    total_additional += 1

        # Add overall summary
        overall_summary = f"\nOverall Summary:\n"
        overall_summary += f"  Total Entities: {len(self.validation_results)}\n"
        overall_summary += f"  Total Attributes: {total_attributes}\n"
        overall_summary += f"  Total Passed: {total_passed} ({(total_passed / total_attributes * 100) if total_attributes > 0 else 0:.1f}%)\n"
        overall_summary += f"  Total Failed: {total_failed} ({(total_failed / total_attributes * 100) if total_attributes > 0 else 0:.1f}%)\n"

        if total_additional > 0:
            overall_summary += f"  Additional Attributes Found: {total_additional} (these have proper structure but weren't in expected list)\n"

        result = "\n".join(summary)
        if additional_summary:
            result += "\n" + "\n".join(additional_summary)

        return result + overall_summary


@given('I have access to the entity data dictionary using the provided token')
def step_impl(context):
    # Get the token from context.config.userdata
    # This allows passing arguments like: behave -D token=<token> -D entity=<entity>
    token = context.config.userdata.get('token')
    entity_filter = context.config.userdata.get('entity')

    if not token:
        raise ValueError("Bearer token must be provided with -D token=<token>")

    # Get the project root directory
    features_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    project_root = os.path.abspath(os.path.join(features_dir, '..', '..'))

    print(f"Features directory: {features_dir}")
    print(f"Project root: {project_root}")

    # Verify that we're in the correct directory structure
    configs_dir = os.path.join(project_root, 'configs')
    if os.path.exists(configs_dir):
        # If we found a 'configs' directory, we're one level too high
        project_root = configs_dir
        print(f"Adjusted project root: {project_root}")

    # Initialize the validator
    context.validator = NullHandlingValidator(project_root, entity_filter)

    # Fetch the data dictionary
    if not context.validator.fetch_data_dictionary(token):
        raise AssertionError("Failed to fetch or validate the entity data dictionary")

    print("Successfully fetched and validated the entity data dictionary")


@when('I extract string attributes for each entity')
def step_impl(context):
    if not context.validator.extract_string_attributes():
        raise AssertionError("Failed to extract string attributes from the data dictionary")

    print(f"Extracted string attributes for {len(context.validator.string_attributes)} entities")
    for entity, attrs in context.validator.string_attributes.items():
        print(f"  - {entity}: {len(attrs)} string attributes")


@when('I collect feedName values for string attributes from inventory models')
def step_impl(context):
    if not context.validator.collect_feed_names():
        raise AssertionError("Failed to collect feedName values from inventory models")

    print(f"Collected feedName values for attributes in {len(context.validator.attribute_feed_mapping)} entities")
    for entity, attrs in context.validator.attribute_feed_mapping.items():
        print(f"  - {entity}: {len(attrs)} attributes with feedName values")


@then('each string attribute should have a proper null handling expression in its global entity config')
def step_impl(context):
    if not context.validator.validate_null_handling():
        raise AssertionError("Failed to validate null handling expressions")

    # Print validation summary
    summary = context.validator.get_validation_summary()
    print("\nValidation Summary:\n" + summary)

    # Check if any validations failed
    failed = False
    failed_count = 0
    total_count = 0

    for entity, attributes in context.validator.validation_results.items():
        for attr, result in attributes.items():
            total_count += 1
            if not result['result']:
                failed = True
                failed_count += 1

    if failed:
        error_message = f"Validation failed: {failed_count} out of {total_count} attributes do not have proper null handling expressions"
        print(f"\n❌ {error_message}")
        raise AssertionError(error_message)

    print(f"\n✅ Success! All {total_count} string attributes have proper null handling expressions")
