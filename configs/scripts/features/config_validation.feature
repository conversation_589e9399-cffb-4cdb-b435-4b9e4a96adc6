Feature: Configuration File Validation
    Validate that all configuration files meet the required standards

    Scenario: All inventory model configs should have primaryKey attribute
        Given I have access to the inventory models config directory
        When I read all JSON files in the directory
        Then each JSON file should contain a "primaryKey" attribute

    Scenario: All inventory model configs should follow the standard schema
        Given I have access to the inventory models config directory
        When I read all JSON files in the directory
        Then each JSON file should conform to the inventory model schema
