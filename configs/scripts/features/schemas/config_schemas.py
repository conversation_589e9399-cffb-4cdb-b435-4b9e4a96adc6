# Base property schema used across different config types
PROPERTY_SCHEMA = {
    "type": "object",
    "required": ["colName"],  # Removed colExpr from required properties
    "properties": {
        "colName": {"type": "string"},
        "colExpr": {"type": "string"},
        "fieldsSpec": {
            "type": "object",
            "properties": {
                "isInventoryDerived": {"type": "boolean"},
                "aggregateFunction": {"type": "string"}
            }
        }
    }
}

# Individual schemas for different config types
INVENTORY_MODEL_SCHEMA = {
    "type": "object",
    "required": ["primaryKey", "origin", "dataSource", "outputTable"],
    "properties": {
        "primaryKey": {
            "type": "string"
        },
        "origin": {
            "type": "string"
        },
        "commonProperties": {
            "type": "array",
            "items": PROPERTY_SCHEMA
        },
        "entitySpecificProperties": {
            "type": "array",
            "items": PROPERTY_SCHEMA
        },
        "sourceSpecificProperties": {
            "type": "array",
            "items": PROPERTY_SCHEMA
        },
        "enrichments": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["lookupInfo", "joinCondition"],
                "properties": {
                    "lookupInfo": {
                        "type": "object",
                        "required": ["tableName", "enrichmentColumns"],
                        "properties": {
                            "tableName": {"type": "string"},
                            "enrichmentColumns": {
                                "type": "array",
                                "items": {"type": "string"}
                            }
                        }
                    },
                    "joinCondition": {"type": "string"},
                    "sourcePreTransform": {
                        "type": "array",
                        "items": PROPERTY_SCHEMA
                    }
                }
            }
        },
        "dataSource": {
            "type": "object",
            "required": ["name", "feedName", "srdm"],
            "properties": {
                "name": {"type": "string"},
                "feedName": {"type": "string"},
                "srdm": {"type": "string"}
            }
        },
        "outputTable": {
            "type": "string"
        }
    }
}

# Add other schemas as separate variables, for example:
# RELATIONSHIP_MODEL_SCHEMA = { ... }
# ENTITY_MODEL_SCHEMA = { ... }
