{"primaryKey": "object_guid", "filterBy": "LOWER(sam_account_type) LIKE '%machine_account%' AND object_guid IS NOT NULL AND TRIM(object_guid)!=''", "origin": "'MS Active Directory'", "commonProperties": [{"colName": "class", "colExpr": "'Host'"}, {"colName": "type", "colExpr": "CASE WHEN LOWER(distinguished_name) LIKE '%server%' OR LOWER(operating_system) LIKE '%server%' THEN 'Server' ELSE 'Endpoint' END"}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "test"}], "sourceSpecificProperties": [{"colName": "test", "colExpr": "test"}]}