{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__active_directory__object_guid", "name": "sds_ei__host__active_directory__object_guid"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_defender", "name": "sds_ei__host__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_intunes__device_id", "name": "sds_ei__host__ms_intunes"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys", "name": "sds_ei__host__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn", "name": "sds_ei__host__globalprotect_vpn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__winevents", "name": "sds_ei__host__winevents"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_resource_details__arn", "name": "sds_ei__host__aws_resource_details__arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad", "name": "sds_ei__host__ms_azure_ad"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "name": "sds_ei__host__ms_azure"}], "disambiguation": {"candidateKeys": ["fqdn"], "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_defender", "sds_ei__host__ms_intunes", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "type", "confidenceMatrix": ["sds_ei__host__active_directory__object_guid", "sds_ei__host__ms_intunes", "sds_ei__host__ms_defender", "sds_ei__host__qualys", "sds_ei__host__globalprotect_vpn", "sds_ei__host__winevents", "sds_ei__host__aws_resource_details__arn", "sds_ei__host__ms_azure_ad", "sds_ei__host__ms_azure"]}], "rollingUpFields": ["defender_threat_name", "defender_action_type", "qualys_id", "defender_id", "qualys_asset_id", "qualys_detection_method", "defender_detection_method", "win_event_id", "ip"], "aggregation": [{"field": "origin", "function": "max"}, {"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "mdm_enrolled_date", "function": "max"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "ad_last_sync_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "vm_status", "function": "max"}, {"field": "edr_status", "function": "max"}, {"field": "azure_created_date", "function": "max"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "av_status", "confidenceMatrix": ["false", "true"]}, {"field": "av_block_malicious_code_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_status", "confidenceMatrix": ["false", "true"]}, {"field": "fw_compliant_status", "confidenceMatrix": ["false", "true"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_inter_source_resolver"}}