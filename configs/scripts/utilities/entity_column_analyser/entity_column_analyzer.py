#!/usr/bin/env python3
import json
import csv
import sys
import re
from pathlib import Path
from collections import defaultdict

def extract_entity_and_loader(filename):
    """Extract entity name and loader name from the filename"""
    # The filename format is: sds_ei__<entity_name>__<loader_name>__job_config.json
    parts = filename.split('__')
    if len(parts) >= 4 and parts[0] == 'sds_ei':
        entity_name = parts[1]
        # Remove the entity name prefix and job_config.json suffix to get the loader name
        # Join all middle parts with __ in case loader name contains __
        loader_name = '__'.join(parts[2:-1])
        return entity_name, loader_name
    return None, None

def process_json_file(file_path):
    """Process a single JSON file and return column details"""
    column_data = []

    try:
        with open(file_path, 'r') as f:
            data = json.load(f)

            # Process each property category
            categories = {
                'temporaryProperties': 'temporaryProperties',
                'commonProperties': 'commonProperties',
                'entitySpecificProperties': 'entitySpecificProperties',
                'sourceSpecificProperties': 'sourceSpecificProperties'
            }

            for category_key, category_name in categories.items():
                properties = data.get(category_key, [])
                for prop in properties:
                    if 'colName' in prop and 'colExpr' in prop:
                        # Clean up the expression - replace newlines with spaces
                        expr = str(prop['colExpr'])
                        expr = re.sub(r'\s+', ' ', expr).strip()

                        column_data.append({
                            'column_name': prop['colName'],
                            'column_category': category_name,
                            'column_expr': expr
                        })
    except Exception as e:
        print(f"Error processing file {file_path}: {e}")

    return column_data

def main():
    # Define base directory using absolute path from project root
    project_root = Path(__file__).parent.parent.parent.parent.parent  # Navigate up to project root
    base_dir = project_root / 'configs/spark_job_configs/source_models/inventory_models'
    # Set output directory to be in a hidden folder within the same folder as this script
    output_dir = Path(__file__).parent / '.outputs'

    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)

    # Check if input entities are provided
    if len(sys.argv) > 1:
        # Split input by commas to support multiple entities
        input_entities = [entity.strip() for entity in sys.argv[1].split(',')]
        target_dirs = []
        single_output = len(input_entities) > 1  # If multiple entities, use single output

        # Get available directories
        available_dirs = [d for d in base_dir.iterdir() if d.is_dir()]
        available_dir_names = [d.name for d in available_dirs]

        # Process each entity
        for entity in input_entities:
            # Try different formats of the folder name
            folder_formats = [
                f"sds_ei__loader__{entity}",  # Full format
                entity  # As provided
            ]

            found = False
            for folder_format in folder_formats:
                if folder_format in available_dir_names:
                    target_dirs.append(base_dir / folder_format)
                    found = True
                    break

            if not found:
                print(f"\nError: Entity '{entity}' not found in {base_dir}")
                print("Available directories:")
                for d in available_dir_names:
                    print(f"- {d}")
                sys.exit(1)

        print(f"Processing files for entities: {', '.join(input_entities)}")
    else:
        target_dirs = [base_dir]  # Process all directories
        single_output = True
        print("No input entities specified. Processing all files in inventory_models...")
        print("Will generate a single consolidated output file.")

    # Dictionary to store all column data by entity
    entity_data = defaultdict(lambda: defaultdict(lambda: defaultdict(dict)))

    # Dictionary to keep track of all loaders for each entity
    entity_loaders = defaultdict(set)

    # Set to keep track of all loaders across all entities (for single output mode)
    all_loaders = set()

    # Dictionary to track which loaders are applicable to which entities
    entity_loader_map = defaultdict(set)

    # Find all JSON files in the target directories and their subdirectories
    json_files = []
    for target_dir in target_dirs:
        json_files.extend(list(target_dir.glob('**/*.json')))
    print(f"Found {len(json_files)} JSON files to process")

    # Process each JSON file
    for json_file in json_files:
        entity_name, loader_name = extract_entity_and_loader(json_file.name)

        if entity_name and loader_name:
            print(f"Processing {json_file.name} - Entity: {entity_name}, Loader: {loader_name}")
            column_data = process_json_file(json_file)

            # Store column data
            for column in column_data:
                entity_data[entity_name][column['column_name']][column['column_category']][loader_name] = column['column_expr']
                entity_loaders[entity_name].add(loader_name)
                all_loaders.add(loader_name)
                entity_loader_map[entity_name].add(loader_name)

    if single_output:
        # Generate a single consolidated output file
        output_file = output_dir / "all_entities_columns.csv"

        # Sort all loaders for consistent output
        all_loaders_sorted = sorted(all_loaders)

        # Prepare CSV header
        header = ['entity_name', 'column_name', 'column_category'] + all_loaders_sorted

        # Prepare rows for CSV
        all_rows = []
        for entity_name, columns in entity_data.items():
            for column_name, categories in columns.items():
                for category, loader_exprs in categories.items():
                    row = [entity_name, column_name, category]
                    # Add expressions for each loader
                    for loader in all_loaders_sorted:
                        # If the loader is applicable to this entity but has no value for this column, use empty string
                        # If the loader is not applicable to this entity, use "**NA**"
                        if loader in entity_loader_map[entity_name]:
                            row.append(loader_exprs.get(loader, ''))
                        else:
                            row.append("**NA**")
                    all_rows.append(row)

        # Sort rows by entity name and then column name for better readability
        all_rows.sort(key=lambda x: (x[0], x[1]))

        # Write to CSV
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, quoting=csv.QUOTE_ALL)
            writer.writerow(header)
            writer.writerows(all_rows)

        print(f"Output written to {output_file}")
    else:
        # Generate separate output files for each entity
        for entity_name, columns in entity_data.items():
            output_file = output_dir / f"{entity_name}_columns.csv"

            # Sort loaders for consistent output
            loaders = sorted(entity_loaders[entity_name])

            # Prepare CSV header
            header = ['entity_name', 'column_name', 'column_category'] + loaders

            # Prepare rows for CSV
            rows = []
            for column_name, categories in columns.items():
                for category, loader_exprs in categories.items():
                    row = [entity_name, column_name, category]
                    # Add expressions for each loader
                    for loader in loaders:
                        row.append(loader_exprs.get(loader, ''))
                    rows.append(row)

            # Sort rows by column name for better readability
            rows.sort(key=lambda x: x[1])

            # Write to CSV
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                writer.writerow(header)
                writer.writerows(rows)

            print(f"Output written to {output_file}")

if __name__ == "__main__":
    main()
