# Entity Column Analyzer

## Overview

The Entity Column Analyzer is a utility script that analyzes JSON configuration files for entities in the SDS Entity Inventory system. It extracts column information from these configuration files and generates a consolidated CSV report showing which columns are used by each entity and loader.

This tool is particularly useful for:
- Understanding the column structure of entities
- Identifying which loaders populate which columns
- Analyzing column categories across different entities
- Comparing column expressions across different loaders

## Prerequisites

- Python 3.6+
- Access to the SDS Entity Inventory configuration files

## Usage

```bash
# Process a single entity
python3 entity_column_analyzer.py <entity_name>

# Process multiple entities
python3 entity_column_analyzer.py <entity1>,<entity2>,<entity3>

# Process all entities
python3 entity_column_analyzer.py
```

### Input Parameters

The script accepts the following input parameters:

- **Entity Name(s)** (optional): One or more entity names, separated by commas.
  - You can specify just the entity name without the "sds_ei__loader__" prefix (e.g., "host", "cloud_compute")
  - You can also use the full folder name format (e.g., "sds_ei__loader__host")
  - If no entity is specified, the script processes all entities

### Examples

```bash
# Process the 'host' entity
python3 entity_column_analyzer.py host

# Process the 'cloud_compute' entity using the full folder name
python3 entity_column_analyzer.py sds_ei__loader__cloud_compute

# Process both 'host' and 'cloud_compute' entities
python3 entity_column_analyzer.py host,cloud_compute

# Process all entities
python3 entity_column_analyzer.py
```

## Output

The script generates CSV files in the `.outputs` directory (hidden folder) within the same folder as the script:

- When processing a single entity: `.outputs/<entity_name>_columns.csv`
- When processing multiple entities or all entities: `.outputs/all_entities_columns.csv`

### Output Format

The CSV file contains the following columns:

1. **entity_name**: The name of the entity
2. **column_name**: The name of the column
3. **column_category**: The category of the column (temporaryProperties, commonProperties, entitySpecificProperties, sourceSpecificProperties)
4. **[loader_name]**: One column for each loader, containing the column expression used by that loader

For the consolidated output (multiple entities), the loader columns represent a superset of all loaders across all entities. If a loader is not applicable to a particular entity, the cell will contain "**NA**".

### Column Categories

The script identifies four categories of columns:

- **temporaryProperties**: Temporary columns used during processing
- **commonProperties**: Common columns shared across entities
- **entitySpecificProperties**: Columns specific to an entity
- **sourceSpecificProperties**: Columns specific to a data source

## Notes

- The script analyzes JSON configuration files located in `configs/spark_job_configs/source_models/inventory_models`
- If a column appears in multiple categories across different loaders, it will be listed multiple times in the output
- The script handles multi-line expressions by replacing newlines with spaces
- When processing multiple entities, the script generates a single consolidated CSV file with all entities and their columns
