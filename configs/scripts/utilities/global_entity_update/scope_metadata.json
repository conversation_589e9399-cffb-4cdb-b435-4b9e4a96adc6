{"scopes": [{"scope_definition": "Vulnerability N/A Attributes - String", "field_group": "commonProperties", "entities": ["Vulnerability"], "attribute_list": ["location_city", "location_country", "business_unit"], "field_type": "string", "default_expression": "{not_applicable}", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Cloud Attributes - String", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["cloud_account_name", "cloud_resource_type", "cloud_instance_type", "cloud_instance_lifecycle"], "field_type": "string", "default_expression": "COALESCE({field},CASE WHEN cloud_provider={not_applicable} then {not_applicable} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Host Type - Network,Printer (String)", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["mdm_product", "mdm_compliance_state"], "field_type": "string", "default_expression": "COALESCE({field}, CASE WHEN type IN ('Network Device','Printer') then {not_applicable} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Source Specific - String", "field_group": "sourceSpecificProperties", "entities": ["Account", "Application", "Cloud Account", "Cloud Compute", "Cloud Container", "Cloud Storage", "Host", "Identity", "Person", "Vulnerability"], "attribute_list": [], "field_type": "string", "default_expression": "COALESCE({field}, CASE WHEN {origin_condition} THEN {no_data} ELSE {not_applicable} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Source Specific - Timestamp", "field_group": "sourceSpecificProperties", "entities": ["Account", "Application", "Cloud Account", "Cloud Compute", "Cloud Container", "Cloud Storage", "Host", "Identity", "Person", "Vulnerability"], "attribute_list": [], "field_type": "timestamp", "default_expression": "COALESCE({field}, CASE WHEN {origin_condition} THEN {no_data} ELSE 0 END)", "check_api_dd_only": true, "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Source Specific Properties - Boolean", "field_group": "sourceSpecificProperties", "entities": ["Account", "Application", "Cloud Account", "Cloud Compute", "Cloud Container", "Cloud Storage", "Host", "Identity", "Person", "Vulnerability"], "attribute_list": [], "field_type": "boolean", "default_expression": "COALESCE(cast({field} as string), CASE WHEN {origin_condition} THEN {no_data} ELSE {not_applicable} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Source Specific Properties - Bigint", "field_group": "sourceSpecificProperties", "entities": ["Account", "Application", "Cloud Account", "Cloud Compute", "Cloud Container", "Cloud Storage", "Host", "Identity", "Person", "Vulnerability"], "attribute_list": [], "field_type": "bigint", "default_expression": "COALESCE({field}, CASE WHEN {origin_condition} THEN {no_data} ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Volume Storage type - Bigint", "field_group": "entitySpecificProperties", "entities": ["Cloud Storage"], "attribute_list": [], "field_type": "bigint", "default_expression": "COALESCE({field}, CASE WHEN  CASE WHEN type != 'Volume' THEN {not_applicable} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Storage Account access - Boolean", "field_group": "entitySpecificProperties", "entities": ["Cloud Storage"], "attribute_list": ["allow_blob_public_access"], "field_type": "boolean", "default_expression": "COALESCE(cast({field} as string), CASE WHEN type!='Storage Account' then {not_applicable} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Mobile Host type - Boolean", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["is_ephemeral", "vm_onboarding_status"], "field_type": "boolean", "default_expression": "COALESCE(cast({field} as string), CASE WHEN Type ='Mobile' then {not_applicable} else {field} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Host Type - Network,Printer (Timestamp)", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["mdm_last_sync_date", "mdm_enrolled_date"], "field_type": "timestamp", "default_expression": "COALESCE({field}, CASE WHEN type in ('Network Device','Printer') THEN 0 END)", "check_api_dd_only": true, "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Mobile host type - Timestamp", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["vm_last_scan_date", "vulnerability_last_observed_date"], "field_type": "timestamp", "default_expression": "COALESCE({field}, CASE WHEN type='Mobile' THEN 0 END)", "check_api_dd_only": true, "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Source Specific Properties - Array", "field_group": "sourceSpecificProperties", "entities": ["Account", "Application", "Cloud Account", "Cloud Compute", "Cloud Container", "Cloud Storage", "Host", "Identity", "Person", "Vulnerability"], "attribute_list": [], "field_type": "array<string>", "default_expression": "CASE WHEN size({field}) > 0 THEN {field} WHEN {origin_condition} THEN NULL ELSE array({not_applicable}) END", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Cloud attributes - A<PERSON>y", "field_group": "entitySpecificProperties", "entities": ["Host"], "attribute_list": ["cloud_instance_id"], "field_type": "array<string>", "default_expression": "COALESCE({field},CASE WHEN cloud_provider={not_applicable} THEN array({not_applicable}) else {field} END)", "fieldsSpec": {"computationPhase": "inter"}}, {"scope_definition": "Mobile host type - Array", "field_group": "entitySpecificProperties", "entities": ["Host", "Cloud Compute"], "attribute_list": ["vm_product", "vm_tracking_method"], "field_type": "array<string>", "default_expression": "COALESCE({field},CASE WHEN type='Mobile' THEN array({not_applicable}) else {field} END)", "fieldsSpec": {"computationPhase": "inter"}}]}