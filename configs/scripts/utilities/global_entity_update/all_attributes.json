[
  {
    "field_name": "p_id",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "origin",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "internal_service",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "activity_status",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "privilege_account",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "fragments",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "entitlement_id",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "updated_at",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "bad_password_count_flag",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "password_never_expire",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "entitlement_value",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "kg_config",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "aad_id_with_service__resolved",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "account_never_expire",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_sam_account_type",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "aad_id_with_service",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "bad_password_configured_time",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "is_mfa_enabled",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "password_last_used",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "exposure_score",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "last_lock_out_time",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "aws_account_created_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Account",
    "data_type": "timestamp"
  },
  {
    "field_name": "account_id",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "last_failed_signin_attempt",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Account",
    "data_type": "struct<first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,account_display_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_logged_in_location:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,authentication_factors:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_lock_out_time:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,privilege_account:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_password_change_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,password_never_expire:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,password_not_required:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,account_never_expire:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,ownership_of_identity:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,is_mfa_enabled:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,privilege_roles_count:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,activity_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,operational_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,entitlement_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,entitlement_value:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,entitlement_details:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "password_not_required",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "last_password_change_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "recency",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "entitlement_description",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "company_email_domains",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "failed_logon_count",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "lifetime",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "last_lock_out_flag",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "failed_count_flag",
    "entity": "Account",
    "data_type": "boolean"
  },
  {
    "field_name": "class",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "aad_id",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "privilege_roles_count",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "privilege_roles",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "last_signin_attempt",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_created_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_created_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Account",
    "data_type": "int"
  },
  {
    "field_name": "account_display_name__resolved",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "account_display_name",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "login_last_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "last_active_date",
    "entity": "Account",
    "data_type": "bigint"
  },
  {
    "field_name": "account_name",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "source_of_identity",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "service",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "ownership_of_identity",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "operational_status",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "last_logged_in_location",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "failed_login_attempt_location",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "entitlement_details",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "default_mfa_method",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Account",
    "data_type": "string"
  },
  {
    "field_name": "authentication_factors",
    "entity": "Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "p_id",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "origin",
    "entity": "Application",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Application",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Application",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Application",
    "data_type": "array<string>"
  },
  {
    "field_name": "activity_status",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "fragments",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "lifetime",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "primary_key",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "kg_config",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "derived_criticality",
    "entity": "Application",
    "data_type": "boolean"
  },
  {
    "field_name": "exposure_score",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "app_first_seen",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Application",
    "data_type": "timestamp"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Application",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_active_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,app_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,category:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,risk_category:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,criticality:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,operational_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,lifecycle:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,derived_criticality:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,internet_facing:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,retired_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,sensitive_information:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,app_vendor:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,app_version:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "recency",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "class",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "sensitive_information",
    "entity": "Application",
    "data_type": "boolean"
  },
  {
    "field_name": "internet_facing",
    "entity": "Application",
    "data_type": "boolean"
  },
  {
    "field_name": "updated_at",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "criticality",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Application",
    "data_type": "int"
  },
  {
    "field_name": "app_name__resolved",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "app_name",
    "entity": "Application",
    "data_type": "array<string>"
  },
  {
    "field_name": "last_active_date",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "retired_date",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Application",
    "data_type": "bigint"
  },
  {
    "field_name": "app_version",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "app_vendor",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "lifecycle",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "operational_status",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "risk_category",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "category",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Application",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "origin",
    "entity": "Cloud Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Cloud Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Cloud Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Cloud Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_subscription_quota_id",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "fragments",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_account_status",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "updated_at",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "azure_subscription_tenantId",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "aws_account_email",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "azure_subscription_spending_limit",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "azure_subscription_tags",
    "entity": "Cloud Account",
    "data_type": "struct<Name:string>"
  },
  {
    "field_name": "azure_subscription_state",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "kg_config",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "azure_subscription_policies",
    "entity": "Cloud Account",
    "data_type": "struct<locationPlacementId:string,quotaId:string,spendingLimit:string>"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "last_active_date",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_account_joined_method",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "exposure_score",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_account_arn",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "azure_subscription_managed_by_tenants",
    "entity": "Cloud Account",
    "data_type": "array<struct<tenantId:string>>"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "id",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Cloud Account",
    "data_type": "timestamp"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "properties",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Cloud Account",
    "data_type": "struct<account_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,azure_subscription_state:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,aws_account_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "recency",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "lifetime",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "azure_subscription_location_placement_id",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "azure_subscription_authorization_source",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "class",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "aws_account_joined_timestamp",
    "entity": "Cloud Account",
    "data_type": "bigint"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Cloud Account",
    "data_type": "int"
  },
  {
    "field_name": "aws_account_name",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "account_id__resolved",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "account_id",
    "entity": "Cloud Account",
    "data_type": "array<string>"
  },
  {
    "field_name": "cloud_inactivity_period",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "account_status",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "account_name",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "cloud_provider",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Cloud Account",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "qualys_detection_method",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "private_ip",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_threat_name",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_detection_method",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_action_type",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "vm_product",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "vm_tracking_method",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "vm_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "edr_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "defender_exposure_level",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "av_block_malicious_code_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "av_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "fw_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "vm_onboarding_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "is_ephemeral",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aks_node_pool_maximum_vm_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "scaling_instance_type",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_region",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_eks_cluster_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vmss_vm_count",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_aks_azure_policy_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_vmss_vm_size",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_eks_cluster_key",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_aks_node_pool_profiles",
    "entity": "Cloud Compute",
    "data_type": "array<struct<availabilityZones:array<string>,count:bigint,currentOrchestratorVersion:string,enableAutoScaling:boolean,enableEncryptionAtHost:boolean,enableFIPS:boolean,enableNodePublicIP:boolean,enableUltraSSD:boolean,kubeletDiskType:string,maxCount:bigint,maxPods:bigint,minCount:bigint,mode:string,name:string,nodeImageVersion:string,nodeLabels:struct<Terraform:string,jobtype:string,kubernetes__azure__com__scalesetpriority:string,name:string,ou:string,instancetype:string>,nodeTaints:array<string>,orchestratorVersion:string,osDiskSizeGB:bigint,osDiskType:string,osSKU:string,osType:string,powerState:struct<code:string>,provisioningState:string,... 9 more fields>>"
  },
  {
    "field_name": "aws_ec2fleet_spot_target_capacity",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_ec2fleet_total_target_capacity",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_ec2fleet_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_model_primary_container_mode",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_support_plan",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "wiz_last_scan_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_emr_volume",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "os_family",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_private_fqdn",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_model_datasource_s3_uri",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_resource_last_modified_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_emr_master_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_availability_zone",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_lambda_last_modified_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_ec2fleet_default_target_capacity_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_monitoring_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vmss_key__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "defender_onboarding_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_applications",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_cluster_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "edr_onboarding_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "scaling_instance_ids",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_ec2fleet_terminate_instances",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_sagemaker_model_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_enable_rbac",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aci_restart_policy",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "kg_config",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_state_update_time",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_instance_network_interface_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_vm_power_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_tags",
    "entity": "Cloud Compute",
    "data_type": "struct<Billing:string,Environemnt:string,Environment:string,Name:string,OU:string,Project:string,Terraform:string,aks__managed__consolidated__additional__properties:string,aks__managed__createOperationID:string,aks__managed__creationSource:string,aks__managed__kubeletIdentityClientID:string,aks__managed__operationID:string,aks__managed__orchestrator:string,aks__managed__poolName:string,aks__managed__resourceNameSuffix:string,aks__managed__ssh__access:string,aksAPIServerIPAddress:string,name:string,ou:string,cast__cluster__id:string,cast__instance__template:string,cast__managed__by:string,cast__node__id:string,cast__pool__version:string,... 192 more fields>"
  },
  {
    "field_name": "azure_aks_node_pool_maximum_pod_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_container_node_key",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_id__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ec2fleet_provisioned_on_demand_capacity",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_lambda_code_size",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "azure_aks_power_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aci_finish_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_autoscaling_instance_min_size",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "fragments",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_autoscaling_instance_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "azure_plan",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_node_pool_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_autoscaling_group_key__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_public_network_access",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_lifecycle_config_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_dns_prefix",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecr_repository_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_managed_disk_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_computer_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_usage_update_time",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_sagemaker_default_code_repository",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_ecr_image_tag_mutability",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "wiz_is_cloud_managed",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "defender_threat_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "azure_aks_node_resource_group",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_eks_cluster_key__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_image_cleaner_enabled_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_sagemaker_configuration_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecs_assign_public_ip",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "exposure_score",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_instance_image_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_autoscaling_instance_types",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_sagemaker_root_access_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_node_pool_type",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "vulnerability_last_observed_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "wiz_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecs_service_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_private_ip_address",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_visible_to_users",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_lambda_runtime",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_availability_zone",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "open_to_all_internet",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_ec2fleet_valid_until",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_resource_configuration_change_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_emr_service_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ec2fleet_replace_unhealthy_instances",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_emr_instance_fleet_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "azure_aks_aad_profile_admin_group_object_ids",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_vm_network_interface_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_aks_node_pool_vm_sizes",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_lambda_memory_size",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_tag",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_attach_time",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_aks_oms_agent_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aks_monitor_enabled",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "edr_threat_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_instance_lifecycle",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_max_node_pools",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "wiz_onboarding_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_operational_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Cloud Compute",
    "data_type": "timestamp"
  },
  {
    "field_name": "account_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_volume_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "properties",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_storage_snapshot_controller_enabled_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aks_defender_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "wiz_onboarding_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Cloud Compute",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,business_unit:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,department:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,location_country:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,purchase_plan_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,os_family:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,billing_tag:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,environment:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,edr_onboarding_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,vm_onboarding_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,operational_state:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,accessibility:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,os:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cloud_provider:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,resource_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,resource_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_user:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,edr_last_scan_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,vm_last_scan_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,private_ip:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,public_ip:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cloud_instance_lifecycle:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,is_ephemeral:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "aws_ec2fleet_on_demand_target_capacity",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_instance_public_ip_address",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_auto_upgrade_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_instance_image_architecture",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "recency",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_autoscaling_override_mixed_instance_type",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "active_operational_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "has_admin_privileges",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_eks_endpoint_private_access",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aks_key_vault_secrets_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_sagemaker_execution_role_arn",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_subnet_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_model_cache_root",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_version",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_node_pool_node_image_version",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "lifetime",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "azure_system_data",
    "entity": "Cloud Compute",
    "data_type": "struct<createdAt:string,createdBy:string,createdByType:string,lastModifiedAt:string,lastModifiedBy:string,lastModifiedByType:string,managedByExtended:string>"
  },
  {
    "field_name": "azure_vm_os_version",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_eks_network_ip_family",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ec2fleet_provisioned_spot_capacity",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aci_container_services_active_containers",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_region",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_model_data_url",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecr_scan_on_push",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_emr_instance_collection_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_slave_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_network_isolation_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_emr_end_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_sagemaker_model_primary_container_image",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_network_security_group_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "class",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aci_instance_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "edr_product",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "scaling_instance_count",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_model_available_gpu",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vmss_key",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "has_sensitive_data",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_sagemaker_notebook_instance_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_tags",
    "entity": "Cloud Compute",
    "data_type": "array<struct<key:string,tag:string,value:string>>"
  },
  {
    "field_name": "azure_vm_lifecycle",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "wiz_modified_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_ec2fleet_valid_from",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "kubernetes_node_group_name",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_aks_azure_rbac_status",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aci_container_services_active_containers_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_eks_endpoint_public_access",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_vm_data_disk",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_sagemaker_model_environment_config",
    "entity": "Cloud Compute",
    "data_type": "struct<ENDPOINT_SERVER_TIMEOUT:string,HF_MODEL_ID:string,MAX_BATCH_PREFILL_TOKENS:string,MAX_INPUT_LENGTH:string,MAX_TOTAL_TOKENS:string,MODEL_CACHE_ROOT:string,SAGEMAKER_ENV:string,SAGEMAKER_MODEL_SERVER_WORKERS:string,SAGEMAKER_PROGRAM:string,SM_NUM_GPUS:string>"
  },
  {
    "field_name": "has_high_privileges",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "updated_at",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_autoscaling_instance_max_size",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_device_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_aks_node_pool_names",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_storage_account_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_auto_terminate",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_emr_cluster_arn",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecs_platform_version",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_network_interface_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_autoscaling_group_key",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_ecs_scheduling_strategy",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "kubernetes_node_pool_vm_count",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "aws_instance_vpc_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecr_encryption_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_direct_internet_access_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_role_arn",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "wiz_operational_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "primary_key",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "total_disk_size",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "cloud_inactivity_period",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "azure_vm_disable_password_authentication",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Cloud Compute",
    "data_type": "int"
  },
  {
    "field_name": "is_accessible_from_internet",
    "entity": "Cloud Compute",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_vm_image_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_os_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "aws_sagemaker_notebook_instance_storage_size",
    "entity": "Cloud Compute",
    "data_type": "double"
  },
  {
    "field_name": "resource_id__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "resource_name__resolved",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "resource_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "resource_name",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "cloud_instance_id",
    "entity": "Cloud Compute",
    "data_type": "array<string>"
  },
  {
    "field_name": "login_last_date",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "last_active_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "vm_last_scan_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "edr_last_scan_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "av_last_scan_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "av_signature_update_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_resource_created_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_resource_created_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_instance_launch_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "defender_onboarding_date",
    "entity": "Cloud Compute",
    "data_type": "bigint"
  },
  {
    "field_name": "tenable_io_last_authenticated_scan_date",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_last_scan_date",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_asset_updated_at",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_asset_aws_terminated_date",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_onboarding_date",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "instance_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "private_dns_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "public_dns_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "zone_availability",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "scaling_group_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "region",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "purchase_plan_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "public_ip",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "provisioning_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "project",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "os",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "organisational_unit",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "operational_state",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "native_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "login_last_user",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "kubernetes_version",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "kubernetes_cluster_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "instance_imdsv2_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "image",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "environment",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "emr_cluster_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "ecs_cluster_name",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "ec2fleet_id",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "cloud_provider",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_lifecycle",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "billing_tag",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "accessibility",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Cloud Compute",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "origin",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "fragments",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "aws_ecr_repository_name",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_region",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_container_runs_as_root",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_container_launch_type",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_container_privileged",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aci_cluster_id",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "vm_onboarding_status",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_acr_export_policy_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_zone_redundency",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_ad_auth_as_arm_policy",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "wiz_last_scan_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "resource_name",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_resource_last_modified_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_availability_zone",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_container_state",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_container_runtime_id",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_encryption_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_container_privileged",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "kg_config",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_tags",
    "entity": "Cloud Container",
    "data_type": "struct<Billing:string,Environemnt:string,Environment:string,Name:string,OU:string,POC:string,Project:string,Terraform:string,Terrrafrom:string,Utilization:string,aks__managed__cluster__name:string,aks__managed__cluster__rg:string,aks__managed__consolidated__additional__properties:string,aks__managed__createOperationID:string,aks__managed__creationSource:string,aks__managed__kubeletIdentityClientID:string,aks__managed__operationID:string,aks__managed__orchestrator:string,aks__managed__poolName:string,aks__managed__resourceNameSuffix:string,aks__managed__ssh__access:string,aks__managed__type:string,aksAPIServerIPAddress:string,application:string,... 192 more fields>"
  },
  {
    "field_name": "kubernetes_cluster_name",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "image",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_container_port",
    "entity": "Cloud Container",
    "data_type": "array<bigint>"
  },
  {
    "field_name": "azure_aci_finish_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "kubernetes_pod_name",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_container_image",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "aws_ecr_image_tag_mutability",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "exposure_score",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "vulnerability_last_observed_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "wiz_id",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_container_port",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_container_host_port",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_resource_configuration_change_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_availability_zone",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "open_to_all_internet",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "aws_ecs_cluster_arn",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_tag",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "wiz_onboarding_status",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Cloud Container",
    "data_type": "timestamp"
  },
  {
    "field_name": "account_id",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "properties",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_volume_name",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "is_container_root",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Cloud Container",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,business_unit:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,department:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,location_country:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,billing_tag:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,environment:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,operational_state:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cloud_provider:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,resource_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,resource_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,private_ip:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,encryption_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "azure_acr_admin_user_enabled",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "container_memory",
    "entity": "Cloud Container",
    "data_type": "double"
  },
  {
    "field_name": "azure_acr_anonymous_pull_enabled",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "kubernetes_namespace",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "recency",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "active_operational_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "has_admin_privileges",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "ecs_cluster_name",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "encryption_status",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "lifetime",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "aws_container_runs_as_root",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_aks_finish_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_system_data",
    "entity": "Cloud Container",
    "data_type": "struct<createdAt:string,createdBy:string,createdByType:string,lastModifiedAt:string,lastModifiedBy:string,lastModifiedByType:string,managedByExtended:string>"
  },
  {
    "field_name": "vm_product",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "is_container_privileged",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "private_ip",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_region",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_ecr_scan_on_push",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "is_container_tty_enabled",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_acr_network_rule_bypass_options",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "kubernetes_cluster_id",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_container_image",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "class",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "kuberenetes_namespace",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_soft_delete_policy",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_environment_variables",
    "entity": "Cloud Container",
    "data_type": "array<struct<name:string,value:string,valueFrom:struct<fieldRef:struct<apiVersion:string,fieldPath:string>,resourceFieldRef:struct<divisor:string,resource:string>,secretKeyRef:struct<key:string,name:string>>>>"
  },
  {
    "field_name": "aws_container_network_interface_attc_id",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_tags",
    "entity": "Cloud Container",
    "data_type": "array<struct<key:string,tag:string,value:string>>"
  },
  {
    "field_name": "azure_acr_quarantine_policy_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_container_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "wiz_modified_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "wiz_active_services",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_retention_policy_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aws_container_health_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "has_high_privileges",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "updated_at",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_container_host_port",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_port_protocol",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_eks_finish_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "resource_id__resolved",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_trust_policy_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "azure_acr_data_endpoint_enabled",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_ecr_encryption_type",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_standard_input_once",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "wiz_operational_state",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "primary_key",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "cloud_inactivity_period",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "is_accessible_from_internet",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "is_container_serverless",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "wiz_is_default_security_context",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "container_standard_input",
    "entity": "Cloud Container",
    "data_type": "boolean"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Cloud Container",
    "data_type": "int"
  },
  {
    "field_name": "vulnerability_first_observed",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "resource_id",
    "entity": "Cloud Container",
    "data_type": "array<string>"
  },
  {
    "field_name": "last_active_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_resource_created_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_resource_created_date",
    "entity": "Cloud Container",
    "data_type": "bigint"
  },
  {
    "field_name": "zone_availability",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "region",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "operational_state",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "native_type",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "kubernetes_flavor",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "environment",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_read_only_root_filesystem",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_privilege_escalation",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_hostname",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_ecs_task_arn",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_cpu",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "container_application",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "cloud_provider",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "billing_tag",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "aci_cluster_name",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Cloud Container",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "origin",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "volume_size",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_region",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_file_share_enabled_protocols",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_version_level_immutability_support",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_disk_image_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_bsc_public_access",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "encryption_in_transit",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_blob_encryption_enabled_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "allow_blob_public_access",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_disk_os_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_s3_block_public_policy",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_ebs_size",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "aws_efs_transition_to_ia",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_multi_attached_instances",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "azure_file_encryption_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_disk_unique_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "resource_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_resource_last_modified_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_availability_zone",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_vmss_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_s3_server_side_encryption",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_remaining_retention_days",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_storage_account_secondary_location",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_large_file_shares_state",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_disk_controller_types",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "kg_config",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_ebs_multi_attach_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "infrastructure_encryption_applied",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_s3_server_side_encryption_kms_master_key",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_tags",
    "entity": "Cloud Storage",
    "data_type": "struct<Billing:string,Environment:string,Name:string,OU:string,Project:string,Terraform:string,application:string,clustername:string,databricks__environment:string,kubernetes_namespace:string,k8s__azure__created__by:string,ms__resource__usage:string,Environemnt:string,POC:string,Terrrafrom:string,Utilization:string,aks__managed__cluster__name:string,aks__managed__cluster__rg:string,aks__managed__consolidated__additional__properties:string,aks__managed__createOperationID:string,aks__managed__creationSource:string,aks__managed__kubeletIdentityClientID:string,aks__managed__operationID:string,aks__managed__orchestrator:string,... 192 more fields>"
  },
  {
    "field_name": "azure_disk_create_option",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "is_sftp_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_is_hns_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_supports_http_traffic_only",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "fragments",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "azure_file_encryption_enabled_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_storage_account_allow_shared_key",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_network_acls_default_action",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "lease_status",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_s3_is_mfa_delete_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_lease_state",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_bsc_deny_encryption_scope_override",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_kms_key_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_disk_encryption_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_storage_account_primary_location",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_default_o_auth",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_resource_group",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_disk_state",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "azure_disk_tier",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_s3_block_public_acls",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_ebs_attach_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "exposure_score",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "aws_s3_owner_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_s3_restrict_public_buckets",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_availability_zone",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "aws_resource_configuration_change_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_s3_access_control_list",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_ebs_delete_on_termination",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_s3_bucket_versioning_status",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "azure_file_share_quota",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_storage_account_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_ebs_snapshot_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_dns_endpoint_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_tag",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_efs_throughput_mode",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_file_share_access_tier",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_geo_replication_last_sync",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_s3_bucket_policy_text",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Cloud Storage",
    "data_type": "timestamp"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_encryption_key_source",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "account_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_storage_account_key1_creation_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_file_encryption_key_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "properties",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_storage_account_key2_creation_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_blob_encryption_key_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Cloud Storage",
    "data_type": "struct<billing_tag:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,environment:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,public_network_access:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,operational_state:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "volume_throughput",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_ebs_attachment_state",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "recency",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "is_nfs_v3_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "active_operational_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "lifetime",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "azure_network_policy_access",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_network_acls_bypass",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_bsc_has_immutability_policy",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_s3_bucket_encryption_key_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_storage_account_kind",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_efs_performance_mode",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_ebs_state",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_region",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "supplementary_configuration",
    "entity": "Cloud Storage",
    "data_type": "struct<AccessControlList:string,Arn:string,BucketAccelerateConfiguration:struct<isRequesterCharged:boolean>,BucketLifecycleConfiguration:struct<rules:array<struct<expirationInDays:double,expiredObjectDeleteMarker:boolean,filter:struct<predicate:struct<type:string>>,id:string,noncurrentVersionExpiration:struct<days:double,newerNoncurrentVersions:double>,status:string,transitions:array<struct<days:double,storageClass:string>>>>>,BucketNotificationConfiguration:struct<configurations:struct<YmYxMzVhOWEtZTk1Ny00MzM2LTg4ZmQtMzE2MzJmOTRjZjYx:struct<events:array<string>,filter:struct<s3KeyFilter:struct<filterRules:array<struct<name:string,value:string>>>>,functionARN:string,type:string>>>,BucketPolicy:struct<policyText:string>,BucketTaggingConfiguration:struct<tagSets:array<struct<tags:struct<Billing:string,Environment:string,Name:string,Project:string,Terraform:string,dremio_managed:string,dremio_project_id:string,Application:string,Contact:string,ManagedBy:string,Region:string>>>>,BucketVersioningConfiguration:struct<isMfaDeleteEnabled:boolean,status:string>,Concurrency:struct<reservedConcurrentExecutions:double>,ContinuousBackupsDescription:struct<continuousBackupsStatus:string,pointInTimeRecoveryDescription:struct<pointInTimeRecoveryStatus:string>>,DBSnapshotAttributes:array<struct<attributeName:string>>,EnableTerminationProtection:boolean,IsRequesterPaysEnabled:boolean,KeyRotationStatus:boolean,LifeCycleHooks:array<struct<autoScalingGroupName:string,defaultResult:string,globalTimeout:double,heartbeatTimeout:double,lifecycleHookName:string,lifecycleTransition:string,notificationTargetARN:string>>,Policy:string,PublicAccessBlockConfiguration:struct<blockPublicAcls:boolean,blockPublicPolicy:boolean,ignorePublicAcls:boolean,restrictPublicBuckets:boolean>,ResourceCreationTime:string,ServerSideEncryptionConfiguration:struct<rules:array<struct<applyServerSideEncryptionByDefault:struct<kmsMasterKeyID:string,sseAlgorithm:string>,bucketKeyEnabled:boolean>>>,StackResourceSummaries:array<struct<deletionPolicy:string,driftInformation:struct<stackResourceDriftStatus:string>,lastUpdatedTimestamp:string,logicalResourceId:string,physicalResourceId:string,resourceStatus:string,resourceType:string,updateReplacePolicy:string>>,Tags:string,instanceStatus:string,unsupportedResources:array<struct<resourceId:string,resourceType:string>>,CreationTime:string,... 3 more fields>"
  },
  {
    "field_name": "azure_deleted_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "class",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "encryption_at_rest",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_minimum_tls_version",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_ebs_csi_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_s3_log_bucket_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_tags",
    "entity": "Cloud Storage",
    "data_type": "array<struct<key:string,tag:string,value:string>>"
  },
  {
    "field_name": "volume_iops",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_file_share_access_tier_change_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_ou",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "updated_at",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_disk_last_ownership_update",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_vm_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_s3_ignore_public_acls",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_blob_encryption_enabled",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_bsc_has_legal_hold",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_storage_account_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_allow_cross_tenant_replication",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_s3_requester_charged",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "primary_key",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_bsc_default_encryption_scope",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_identity_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "aws_efs_backup_policy_status",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Cloud Storage",
    "data_type": "int"
  },
  {
    "field_name": "azure_disk_supports_hibernation",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_instance_id",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "capacity",
    "entity": "Cloud Storage",
    "data_type": "bigint",
  },
  {
    "field_name": "azure_disk_security_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "azure_deleted",
    "entity": "Cloud Storage",
    "data_type": "boolean"
  },
  {
    "field_name": "resource_id__resolved",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "resource_id",
    "entity": "Cloud Storage",
    "data_type": "array<string>"
  },
  {
    "field_name": "last_active_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_resource_created_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_resource_created_date",
    "entity": "Cloud Storage",
    "data_type": "bigint"
  },
  {
    "field_name": "cloud_inactivity_period",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "volume_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "zone_availability",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "volume_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "volume_pvc_namespace",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "volume_pvc_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "volume_pv_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "storage_account_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "storage_account_access_tier",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "region",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "public_network_access",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "provisioning_state",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "project",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "operational_state",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "native_type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "kubernetes_cluster_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "file_system_service_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "environment",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "cloud_provider",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "bucket_name",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "billing_tag",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Cloud Storage",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "internal_service",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "identity_format",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "activity_status",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "fragments",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "ad_sam_account_name_with_domain",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "aws_cloudtrail_user_identity_arn",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "bad_password_count_flag",
    "entity": "Identity",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_cloudtrail_event_name",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "employee_id",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "password_never_expire",
    "entity": "Identity",
    "data_type": "boolean"
  },
  {
    "field_name": "kg_config",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "email_id",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "ad_sam_account_name",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "account_never_expire",
    "entity": "Identity",
    "data_type": "boolean"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_sam_account_type",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "bad_password_configured_time",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "identity_display_name",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "is_mfa_enabled",
    "entity": "Identity",
    "data_type": "boolean"
  },
  {
    "field_name": "password_last_used",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_cloudtrail_event_type",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "exposure_score",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "email_id__resolved",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "recent_activity",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "aws_cloudtrail_event_source",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "ad_domain",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "aws_account_created_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Identity",
    "data_type": "timestamp"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Identity",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,user_principal_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,identity_display_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,email_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,ownership:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_password_change_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_logged_in_location:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,password_never_expire:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,password_not_required:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,account_never_expire:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,operational_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,aad_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,is_mfa_enabled:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,identity_format:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,authentication_factors:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,location_country:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "password_not_required",
    "entity": "Identity",
    "data_type": "boolean"
  },
  {
    "field_name": "last_password_change_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "recency",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "company_email_domains",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "lifetime",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "class",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "aws_cloudtrail_user_identity_type",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "ad_distinguished_name",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "aad_id__resolved",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "updated_at",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_id",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "location_accessed_flag",
    "entity": "Identity",
    "data_type": "boolean"
  },
  {
    "field_name": "last_signin_attempt",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_created_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_created_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "aws_cloudtrail_region",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Identity",
    "data_type": "int"
  },
  {
    "field_name": "ad_last_sync_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "identity_primary_key__resolved",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "identity_primary_key",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "login_last_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "last_active_date",
    "entity": "Identity",
    "data_type": "bigint"
  },
  {
    "field_name": "user_principal_name",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "ownership",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "operational_status",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "last_logged_in_location",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "identity_provider",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "default_mfa_method",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Identity",
    "data_type": "string"
  },
  {
    "field_name": "authentication_factors",
    "entity": "Identity",
    "data_type": "array<string>"
  },
  {
    "field_name": "p_id",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "external_email_id",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "termination_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "updated_at",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "bamboo_emp_business_unit",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "aws_created_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "kg_config",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "project",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "fragments",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "bamboo_emp_competency",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "aad_deleted_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "bamboo_emp_project",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "password_last_used",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "exposure_score",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "exposure_severity",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "earliest_ad_account_disabled_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "recent_activity",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Person",
    "data_type": "timestamp"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Person",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,business_unit:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,department:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,description:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,location_country:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,ad_last_sync_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,manager:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,ad_last_password_change_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,full_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,email_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,manager_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,employee_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,company:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,external_email_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,recruit_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,contract_end_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,job_title:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,job_position_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,legal_entity:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,organisation_unit_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cost_center:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,job_function:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,... 8 more fields>"
  },
  {
    "field_name": "recency",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "lifetime",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "bamboo_emp_recruit_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "sf_employee_status",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "aad_operational_status",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "class",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "ad_operational_status",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "account_enabled_status",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "ad_account_disabled_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "aad_created_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Person",
    "data_type": "int"
  },
  {
    "field_name": "employee_id__resolved",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "aad_user_id__resolved",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "email_id__resolved",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "aws_iam_user_name__resolved",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "employee_id",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "aad_user_id",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "email_id",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_iam_user_name",
    "entity": "Person",
    "data_type": "array<string>"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "last_active_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "recruit_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "last_known_termination_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "contract_end_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "login_last_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_last_password_change_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_last_sync_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_created_date",
    "entity": "Person",
    "data_type": "bigint"
  },
  {
    "field_name": "full_name",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "employment_type",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "employee_level",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "employee_status",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "address",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "phone_number",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "job_function",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "cost_center",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "organisation_unit_id",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "legal_entity",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "job_position_id",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "job_title",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "company",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "manager_id",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "manager",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "middle_name",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "last_name",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "first_name",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Person",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "origin",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "vendor_id",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "v30_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "v31_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "cwe",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "patch_available",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "found_in_organisation",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "qualys_pci_flag",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "epss",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "exploit_available",
    "entity": "Vulnerability",
    "data_type": "boolean"
  },
  {
    "field_name": "cisa_exploit_add_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "ms_recommended_update_id",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "tenable_exploit_ease",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "vendor_id__resolved",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "has_cisa_kev_exploit",
    "entity": "Vulnerability",
    "data_type": "boolean"
  },
  {
    "field_name": "kg_config",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v30_impact_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "fragments",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "v2_exploitability",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "v31_exploitability",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "qualys_threat_intel",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "v30_exploitability",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "v2_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "v40_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "normalized_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "recommendation",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "v31_impact_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "tenablesc_recast_risk",
    "entity": "Vulnerability",
    "data_type": "boolean"
  },
  {
    "field_name": "epss_percentile",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "recent_activity",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Vulnerability",
    "data_type": "timestamp"
  },
  {
    "field_name": "published_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "temp",
    "entity": "Vulnerability",
    "data_type": "struct<p_id:string,primary_key:string,origin:string>"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Vulnerability",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,business_unit:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,department:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,description:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,title:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cve_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,temporal_cvss_score:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,v31_score:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,v31_severity:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,v31_impact_score:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,patch_available:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,exploit_available:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,recommendation:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,ms_recommended_update:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,last_modified_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cwe:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,normalized_severity:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,nvd_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,epss_percentile:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,v40_severity:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,v40_score:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,epss:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>>"
  },
  {
    "field_name": "qualys_category",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "recency",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "tenablesc_risk_factor",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "cisa_action_due_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "lifetime",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "ms_recommended_update",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "temporal_cvss_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "bugtraq_id",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "class",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "tenablesc_accept_risk",
    "entity": "Vulnerability",
    "data_type": "boolean"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "updated_at",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "v2_impact_score",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "cpe",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "qualys_consequence",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "nvd_status",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Vulnerability",
    "data_type": "int"
  },
  {
    "field_name": "tenable_vulnerability_mitigation_status",
    "entity": "Vulnerability",
    "data_type": "boolean"
  },
  {
    "field_name": "tenablesc_stig_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "cve_id__resolved",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "cve_id",
    "entity": "Vulnerability",
    "data_type": "array<string>"
  },
  {
    "field_name": "last_modified_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "last_active_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "vulnerability_first_observed_date",
    "entity": "Vulnerability",
    "data_type": "bigint"
  },
  {
    "field_name": "v40_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "cisa_required_action",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "software_list",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v31_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v31_vector",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v30_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v30_vector",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v2_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "v2_vector",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "title",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "vendor_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "vendor_severity_normalised",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "vulnerability_severity",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "cvss_normalised",
    "entity": "Vulnerability",
    "data_type": "double"
  },
  {
    "field_name": "exploitability",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Vulnerability",
    "data_type": "string"
  },
  {
    "field_name": "p_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "last_found_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "first_found_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "ip",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "mac_address",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "asset_role",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin_entity_enrich",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_threat_name",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "edr_product",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "origin",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "qualys_asset_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "vm_product",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "qualys_detection_method",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_detection_method",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_action_type",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "qualys_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "vm_tracking_method",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_subset_name",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "data_source_dev",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "defender_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "win_event_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "av_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "fw_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "av_block_malicious_code_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "vm_onboarding_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_region",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "mdm_mac_address",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "mdm_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "wiz_last_scan_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "origin_contribution_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_conditional_access_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "edr_onboarding_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "aad_device_id__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "kg_config",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenablesc_asset_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "azure_tags",
    "entity": "Host",
    "data_type": "struct<Billing:string,Environemnt:string,Environment:string,Name:string,OU:string,Project:string,Terraform:string,aks__managed__consolidated__additional__properties:string,aks__managed__createOperationID:string,aks__managed__creationSource:string,aks__managed__kubeletIdentityClientID:string,aks__managed__operationID:string,aks__managed__orchestrator:string,aks__managed__poolName:string,aks__managed__resourceNameSuffix:string,aks__managed__ssh__access:string,aksAPIServerIPAddress:string,name:string,ou:string,cast__cluster__id:string,cast__instance__template:string,cast__managed__by:string,cast__node__id:string,cast__pool__version:string,... 192 more fields>"
  },
  {
    "field_name": "ad_sam_account_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_modified_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "temp_precedence",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "fragments",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "crowdstrike_reboot_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "itop_pc_end_of_warranty_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_deleted_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "domain",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_move_to_production_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "tenablesc_repositories",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "analysis_period_start",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_sam_account_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenablesc_asset_groups",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "mdm_last_sync_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "tenablesc_onboarding_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "aws_instance_usage_update_time",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "observed_lifetime",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "defender_threat_count",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "aad_system_label",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "aws_instance_image_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_last_signin_attempt",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "last_updated_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_profile_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_obsolete_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_management_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "open_to_all_internet",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "host_name__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "earliest_ad_account_disabled_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "recent_activity",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "aws_instance_attach_time",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "crowdstrike_connection_ip",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_compliance_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "edr_threat_count",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "wiz_onboarding_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "tenablesc_assets",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "is_ephemeral",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "host_last_reboot_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "primary_key__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "updated_at_ts",
    "entity": "Host",
    "data_type": "timestamp"
  },
  {
    "field_name": "account_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "inactivity_period",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "crowdstrike_agent_local_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "tenable_repo",
    "entity": "Host",
    "data_type": "struct<dataFormat:string,description:string,id:string,name:string>"
  },
  {
    "field_name": "wiz_onboarding_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "itop_pc_move_to_production_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "itop_server_end_of_warranty_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "last_updated_attrs",
    "entity": "Host",
    "data_type": "struct<type:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,first_seen_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,business_unit:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,department:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,description:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,location_country:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,host_name:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,fqdn:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,ip:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,netbios:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,accessibility:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,os:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,cloud_provider:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,resource_id:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,hardware_model:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,hardware_serial_number:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,login_last_user:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,edr_onboarding_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,vm_onboarding_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,mdm_product:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,mdm_status:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,mdm_last_sync_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,edr_last_scan_date:struct<isChangedInCurrentRun:boolean,prev:struct<value:string,updated_at:bigint,last_found_date:bigint>,last_changed:struct<value:string,updated_at:bigint,last_found_date:bigint>>,... 11 more fields>"
  },
  {
    "field_name": "ethernet_mac_address",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "crowdstrike_device_id__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_onboarding_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "recency",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "active_operational_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "edr_discovered_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "lifetime",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "environment",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "encryption_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "itop_pc_purchase_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "tenablesc_last_active_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "aad_operational_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aws_emr_end_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "class",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "kg_content_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_enrolled_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "hardware_serial_number__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aws_tags",
    "entity": "Host",
    "data_type": "array<struct<key:string,tag:string,value:string>>"
  },
  {
    "field_name": "azure_vm_lifecycle",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "wiz_modified_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "mdm_encryption_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "wifi_mac_address",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "crowdstrike_device_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "updated_at",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "account_enabled_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "threat_count",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "aad_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "exclude__temp_precedence",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "qualys_groups",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_last_report_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_created_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "dns_name",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "primary_key",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_inactivity_period",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "itop_server_purchase_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "crowdstrike_onboarding_status",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "dns_name__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_created_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "count_of_origin",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "is_accessible_from_internet",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "azure_vm_image_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "is_container_host",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "itop_server_obsolete_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "fqdn__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "resource_id__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_id__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "netbios__resolved",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_device_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "host_name",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "fqdn",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "hardware_serial_number",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "resource_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "cloud_instance_id",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "netbios",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "login_last_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "last_active_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "first_seen_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "mdm_enrolled_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "vm_last_scan_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_last_sync_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "edr_last_scan_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "av_last_scan_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "av_signature_update_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "azure_resource_created_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "ad_account_disabled_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "defender_onboarding_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "vulnerability_last_observed_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_instance_launch_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "aws_resource_created_date",
    "entity": "Host",
    "data_type": "bigint"
  },
  {
    "field_name": "tenable_io_last_authenticated_scan_date",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_last_scan_date",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_asset_updated_at",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_asset_aws_terminated_date",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_onboarding_date",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenable_io_system_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "infrastructure_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_tags",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_os_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "qualys_tags",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "ad_distinguished_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "hardware_manufacturer",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_os_version",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_class",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "hardware_chassis_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_product_type_desc",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "os",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_device_category",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "native_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "internal_contributor",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "instance_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "wiz_operational_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "wiz_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "tenablesc_asset_tags",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_serial_number",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_os_version",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_os_family",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_location",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_server_display_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_os_version",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_os_family",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_os_build",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_org_unit",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_location",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_display_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "itop_pc_device_serial_number",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "intune_ownership_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "intune_management_service",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "intune_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "defender_tags",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "defender_risk_score",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "defender_onboarding_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "defender_management_service",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "defender_health_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "defender_exposure_level",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_provision_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_operational_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_local_ip",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_external_ip",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "crowdstrike_customer_id",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "azure_vm_power_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aws_operational_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_public_ip_address",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aws_instance_private_ip_address",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "ad_uac",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "ad_operational_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "ad_object_guid",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "aad_management_service",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "image",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "hardware_bios_version",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "hardware_bios_manufacturer",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "edr_mac_address",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_instance_lifecycle",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_account_name",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "compliance_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "mdm_compliance_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "mdm_product",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "login_last_user",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "hardware_imei",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "hardware_model",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "zone_availability",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "region",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "provisioning_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "operational_state",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_resource_type",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "cloud_provider",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "os_build",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "os_architecture",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "os_version",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "accessibility",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "os_family",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "location_city",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "location_country",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "description",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "department",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "business_unit",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "activity_status",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "display_label",
    "entity": "Host",
    "data_type": "string"
  },
  {
    "field_name": "asset_compliance_scope",
    "entity": "Host",
    "data_type": "array<string>"
  },
  {
    "field_name": "host_type_rating",
    "entity": "Host",
    "data_type": "decimal(12,2)"
  },
  {
    "field_name": "asset_compliance_scope_rating",
    "entity": "Host",
    "data_type": "decimal(12,2)"
  },
  {
    "field_name": "asset_is_inventoried",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "is_edr_present",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "is_vuln_software_present",
    "entity": "Host",
    "data_type": "int"
  },
  {
    "field_name": "asset_security_posture",
    "entity": "Host",
    "data_type": "double"
  },
  {
    "field_name": "host_meet_security_posture",
    "entity": "Host",
    "data_type": "boolean"
  },
  {
    "field_name": "archival_flag",
    "entity": "Host",
    "data_type": "boolean"
  }
]