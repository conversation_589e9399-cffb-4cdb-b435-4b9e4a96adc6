#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update entity configs based on a comprehensive metadata file.

This script:
1. Loads the comprehensive metadata file with multiple scopes
2. For each scope, processes all entities specified in the "entities" field
3. For each entity, loads the entity-specific data dictionary
4. Extracts properties from all sections (commonProperties, entitySpecificProperties, sourceSpecificProperties)
5. Filters them based on the entity-specific data dictionary
6. Updates the corresponding entity config file using the templates from the metadata
"""

import os
import json
import re
import argparse
import glob
from collections import defaultdict

# Global constants for default values
NO_DATA = "NULL"
NOT_APPLICABLE = "'  !'"

# Global variable to track which attributes have been processed by specific scopes
PROCESSED_ATTRIBUTES = {}

# Load all_attributes.json file
def load_all_attributes():
    """Load the all_attributes.json file."""
    all_attributes_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "all_attributes.json")
    with open(all_attributes_path, "r") as f:
        return json.load(f)

# Global variable to store all_attributes data
ALL_ATTRIBUTES = None

def normalize_entity_name(entity_name, format_type="display"):
    """
    Normalize entity name to a consistent format.

    Args:
        entity_name: The entity name to normalize
        format_type: The format type to normalize to:
            - "display": For display and matching with all_attributes.json (spaces, e.g., "Cloud Account")
            - "file": For file paths (lowercase with underscores, e.g., "cloud_account")
            - "code": For internal code processing (capitalized with underscores, e.g., "Cloud_account")

    Returns:
        str: The normalized entity name
    """
    if not entity_name:
        return entity_name

    # First, handle the case where entity_name comes from an inventory model path
    if entity_name.startswith("sds_ei__loader__"):
        entity_name = entity_name.replace("sds_ei__loader__", "")

    # Convert to lowercase and split by underscores or spaces
    parts = re.split(r'[_ ]', entity_name.lower())

    if format_type == "display":
        # For display format: capitalize each word and join with spaces
        return " ".join(part.capitalize() for part in parts)
    elif format_type == "file":
        # For file format: lowercase with underscores
        return "_".join(parts)
    elif format_type == "code":
        # For code format: capitalize first letter of each word and join with underscores
        return "_".join(part.capitalize() for part in parts)
    else:
        # Default: return as is
        return entity_name

def mark_attribute_as_processed(entity_name, attribute_name, source=""):
    """
    Mark an attribute as processed for an entity.

    Args:
        entity_name: The entity name
        attribute_name: The attribute name
        source: Optional source information (e.g., 'attribute_list')
    """
    global PROCESSED_ATTRIBUTES
    # Normalize entity name to ensure consistent keys in PROCESSED_ATTRIBUTES
    normalized_entity = normalize_entity_name(entity_name, format_type="display")

    if normalized_entity not in PROCESSED_ATTRIBUTES:
        PROCESSED_ATTRIBUTES[normalized_entity] = []

    if attribute_name not in PROCESSED_ATTRIBUTES[normalized_entity]:
        PROCESSED_ATTRIBUTES[normalized_entity].append(attribute_name)
        print(f"  Marked {attribute_name} as processed for {normalized_entity}" + (f" (from {source})" if source else ""))

def is_attribute_processed(entity_name, attribute_name):
    """
    Check if an attribute has been processed for an entity.

    Args:
        entity_name: The entity name
        attribute_name: The attribute name

    Returns:
        bool: True if the attribute has been processed, False otherwise
    """
    # Normalize entity name to ensure consistent keys in PROCESSED_ATTRIBUTES
    normalized_entity = normalize_entity_name(entity_name, format_type="display")
    return normalized_entity in PROCESSED_ATTRIBUTES and attribute_name in PROCESSED_ATTRIBUTES[normalized_entity]

def fix_nested_coalesce(expression):
    """
    Fix nested COALESCE expressions by detecting if the inner expression already has a COALESCE
    with the same default value as the outer COALESCE.

    Args:
        expression: The expression to fix

    Returns:
        The fixed expression
    """
    # Check if this is a nested COALESCE
    if "COALESCE(COALESCE(" in expression:
        print(f"Checking nested COALESCE: {expression}")

        # Match the pattern: COALESCE(COALESCE(...), '(No Data)')
        match = re.search(r"COALESCE\((COALESCE\(.+?\)), +'\(No Data\)'\)", expression, re.IGNORECASE)

        if match:
            inner_coalesce = match.group(1)

            # Check if the inner COALESCE already has a default value that handles nulls
            if ("'(No Data)'" in inner_coalesce) or \
               ("'(N/A)'" in inner_coalesce) or \
               ("CASE WHEN" in inner_coalesce and ("THEN '(No Data)'" in inner_coalesce or "ELSE '(No Data)'" in inner_coalesce or "ELSE '(N/A)'" in inner_coalesce)):
                # This is a redundant nested COALESCE - the inner expression already handles nulls
                print(f"  Fixing redundant nested COALESCE: {expression}")
                return inner_coalesce
            else:
                # The inner COALESCE doesn't have a default value that handles nulls
                # Keep the outer COALESCE to catch nulls from the inner expression
                print(f"  Not fixing nested COALESCE as inner expression doesn't handle nulls: {expression}")
                return expression
        else:
            # Complex nested COALESCE that doesn't match our pattern
            print(f"  Not fixing complex nested COALESCE: {expression}")
            return expression

    return expression

# Determine the project root directory
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# Check if temp_scope_metadata.json exists (for testing)
TEMP_METADATA_FILE = os.path.join(SCRIPT_DIR, "temp_scope_metadata.json")
if os.path.exists(TEMP_METADATA_FILE):
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(SCRIPT_DIR))))
    METADATA_FILE = TEMP_METADATA_FILE
elif 'configs/scripts/utilities/global_entity_update' in os.getcwd() or 'configs\\scripts\\utilities\\global_entity_update' in os.getcwd():
    # If we're in configs/scripts/utilities/global_entity_update
    PROJECT_ROOT = os.path.abspath(os.path.join(os.getcwd(), '../../../..'))
    METADATA_FILE = "scope_metadata.json"
elif os.path.basename(os.getcwd()) == 'global_entity_update' and os.path.basename(os.path.dirname(os.getcwd())) == 'utilities':
    # If we're in utilities/global_entity_update
    PROJECT_ROOT = os.path.dirname(os.path.dirname(os.getcwd()))
    METADATA_FILE = "scope_metadata.json"
else:
    # If we're in the project root or elsewhere
    PROJECT_ROOT = os.getcwd()
    METADATA_FILE = "configs/scripts/utilities/global_entity_update/scope_metadata.json"

# Define base paths relative to the project root
BASE_INVENTORY_DIR = os.path.join(PROJECT_ROOT, "configs/spark_job_configs/source_models/inventory_models")
BASE_DATA_DICTIONARY_DIR = os.path.join(PROJECT_ROOT, "configs/orchestration_shared_fs/sds-ei-configs/ui-configs/sds_data_dictionary")

# Check if we're running the test script
TEMP_CONFIG_DIR = os.path.join(SCRIPT_DIR, "temp_configs")
if os.path.exists(TEMP_CONFIG_DIR):
    BASE_ENTITY_CONFIG_DIR = TEMP_CONFIG_DIR
else:
    BASE_ENTITY_CONFIG_DIR = os.path.join(PROJECT_ROOT, "configs/spark_job_configs/source_models/global_entity_config")

# Look for api_dd_extracted.json in multiple locations
POSSIBLE_DD_PATHS = [
    os.path.join(SCRIPT_DIR, "api_dd_extracted.json"),
    os.path.join(SCRIPT_DIR, "api_dd.json"),
    os.path.join(PROJECT_ROOT, "configs/scripts/utilities/global_entity_update/api_dd_extracted.json"),
    os.path.join(PROJECT_ROOT, "configs/scripts/utilities/global_entity_update/api_dd.json")
]

# Use the first path that exists
EXTRACTED_DATA_DICTIONARY = next((path for path in POSSIBLE_DD_PATHS if os.path.exists(path)),
                               os.path.join(SCRIPT_DIR, "api_dd_extracted.json"))

# Function to get entity-specific paths
def get_entity_paths(entity):
    """Get entity-specific paths for inventory, data dictionary, and entity config.

    Args:
        entity: The entity name (e.g., 'Host', 'Cloud Account')

    Returns:
        tuple: (inventory_dir, entity_config_path)
    """
    # Normalize entity name to file format (lowercase with underscores)
    entity_file = normalize_entity_name(entity, format_type="file")

    inventory_dir = os.path.join(BASE_INVENTORY_DIR, f"sds_ei__loader__{entity_file}")
    entity_config_path = os.path.join(BASE_ENTITY_CONFIG_DIR, f"{entity_file}_entity_config.json")
    return inventory_dir, entity_config_path

def get_absolute_path(relative_path):
    """
    Get the absolute path for a relative path.
    Handles the case where the script is run from different directories.

    Args:
        relative_path: The relative path

    Returns:
        str: The absolute path
    """
    # If the path is already absolute, return it as is
    if os.path.isabs(relative_path):
        return relative_path

    # Otherwise, join it with the current working directory
    return os.path.join(os.getcwd(), relative_path)

def load_metadata():
    """
    Load the comprehensive metadata file.

    Returns:
        dict: The metadata with multiple scopes
    """
    print("Loading comprehensive metadata...")

    # Check if we're using the temporary metadata file for testing
    if os.path.exists(TEMP_METADATA_FILE):
        metadata_path = TEMP_METADATA_FILE
    # If we're in utilities/source_extraction, use the local path
    elif os.path.basename(os.getcwd()) == 'source_extraction' and os.path.basename(os.path.dirname(os.getcwd())) == 'utilities':
        metadata_path = METADATA_FILE
    else:
        metadata_path = os.path.join(PROJECT_ROOT, METADATA_FILE)

    print(f"Loading metadata from: {metadata_path}")

    with open(metadata_path, 'r', encoding='utf-8') as f:
        metadata = json.load(f)

    print(f"Loaded metadata with {len(metadata['scopes'])} scopes")
    return metadata

def load_data_dictionary(entity):
    """
    Load the entity-specific data dictionary from the extracted data dictionary file.
    Extract entity fields from the structure "config": { "{Entity}": { attributes }}.

    Args:
        entity: The entity name (e.g., 'Host', 'Application')

    Returns:
        dict: The data dictionary
    """
    # Load from the extracted data dictionary file
    extracted_dict_path = get_absolute_path(EXTRACTED_DATA_DICTIONARY)
    print(f"Loading data dictionary for {entity} from extracted file: {extracted_dict_path}")

    try:
        with open(extracted_dict_path, 'r', encoding='utf-8') as f:
            extracted_data = json.load(f)

        # Extract entity fields from the extracted data dictionary
        # Normalize entity name to display format (spaces between words)
        normalized_entity = normalize_entity_name(entity, format_type="display")

        # Define possible entity name transformations
        entity_transformations = [
            # Normalized entity name (display format)
            normalized_entity,
            # Original entity name
            entity,
            # With spaces instead of underscores
            entity.replace('_', ' '),
            # With proper capitalization and spaces
            ' '.join(entity.split('_')),
            # With lowercase and spaces
            entity.lower().replace('_', ' '),
            # With uppercase first letter and spaces
            ' '.join(word.capitalize() for word in entity.split('_')),
            # With all uppercase and spaces
            ' '.join(word.upper() for word in entity.split('_')),
        ]

        # Try each transformation
        for transformed_entity in entity_transformations:
            if "config" in extracted_data and transformed_entity in extracted_data["config"]:
                entity_attributes = extracted_data["config"][transformed_entity]["attributes"]
                print(f"Loaded data dictionary with {len(entity_attributes)} {entity} attributes (using '{transformed_entity}')")
                return {"attributes": entity_attributes}

        # If we get here, none of the transformations worked
        print(f"Could not find {entity} in the extracted data dictionary file. Skipping this entity.")
        # Return None to indicate that this entity should be skipped
        return None
    except Exception as e:
        print(f"Error loading data dictionary for {entity} from extracted file: {e}")
        return {"attributes": {}}

def is_valid_attribute(attr_name, attr_details, scope_metadata, entity_name=None):
    """
    Check if an attribute is valid for a given scope.

    Args:
        attr_name: The attribute name
        attr_details: The attribute details from the data dictionary
        scope_metadata: The metadata for the scope

    Returns:
        bool: True if the attribute is valid, False otherwise
    """
    # Check if this is the array scope
    is_array_scope = scope_metadata.get("scope_definition") == "Source Specific Properties - Array"

    # For all scopes, check common exclusion criteria
    # Skip attributes that are not visible in the UI
    # Handle both string "hidden" and boolean False values
    ui_visibility = attr_details.get("ui_visibility")
    if attr_name == "normalized_severity":
        print(f"DEBUG: normalized_severity ui_visibility type: {type(ui_visibility)}, value: {ui_visibility}")
    if ui_visibility == "hidden" or ui_visibility is False:
        if attr_name == "normalized_severity":
            print(f"DEBUG: Rejecting normalized_severity due to ui_visibility: {ui_visibility}")
        return False

    # If attribute_list is provided and not empty, check if the attribute is in the list
    if "attribute_list" in scope_metadata and scope_metadata["attribute_list"]:
        if attr_name not in scope_metadata["attribute_list"]:
            # If not in attribute_list, check if it matches any pattern in attribute_patterns
            if "attribute_patterns" in scope_metadata and scope_metadata["attribute_patterns"]:
                # Check each pattern
                for pattern in scope_metadata["attribute_patterns"]:
                    # Handle wildcard patterns like *_date
                    if pattern.startswith("*") and attr_name.endswith(pattern[1:]):
                        print(f"Attribute {attr_name} matches pattern {pattern}")
                        return True
                    # Handle prefix patterns like aws_*
                    elif pattern.endswith("*") and attr_name.startswith(pattern[:-1]):
                        print(f"Attribute {attr_name} matches pattern {pattern}")
                        return True
                    # Handle contains patterns like *instance*
                    elif pattern.startswith("*") and pattern.endswith("*") and pattern[1:-1] in attr_name:
                        print(f"Attribute {attr_name} matches pattern {pattern}")
                        return True
                    # Handle exact match
                    elif pattern == attr_name:
                        print(f"Attribute {attr_name} matches pattern {pattern}")
                        return True
            # If no patterns match, reject the attribute
            return False
    # If attribute_patterns is provided but attribute_list is empty, check patterns
    elif "attribute_patterns" in scope_metadata and scope_metadata["attribute_patterns"]:
        # Check each pattern
        for pattern in scope_metadata["attribute_patterns"]:
            # Handle wildcard patterns like *_date
            if pattern.startswith("*") and attr_name.endswith(pattern[1:]):
                print(f"Attribute {attr_name} matches pattern {pattern}")
                return True
            # Handle prefix patterns like aws_*
            elif pattern.endswith("*") and attr_name.startswith(pattern[:-1]):
                print(f"Attribute {attr_name} matches pattern {pattern}")
                return True
            # Handle contains patterns like *instance*
            elif pattern.startswith("*") and pattern.endswith("*") and pattern[1:-1] in attr_name:
                print(f"Attribute {attr_name} matches pattern {pattern}")
                return True
            # Handle exact match
            elif pattern == attr_name:
                print(f"Attribute {attr_name} matches pattern {pattern}")
                return True

    # If field_expressions is provided, include attributes in that list
    if "field_expressions" in scope_metadata and attr_name in scope_metadata["field_expressions"]:
        return True

    # Check if the attribute has the correct field type
    field_type = scope_metadata.get("field_type", "any")
    check_api_dd_only = scope_metadata.get("check_api_dd_only", False)

    # If field_type is "any", accept any type
    if field_type != "any":
        global ALL_ATTRIBUTES

        # Load all_attributes.json if not already loaded (unless check_api_dd_only is True)
        if ALL_ATTRIBUTES is None and not check_api_dd_only:
            ALL_ATTRIBUTES = load_all_attributes()

        # Use the provided entity_name or try to get it from the scope metadata
        if not entity_name:
            entity_name = scope_metadata.get("entity", "")
            if not entity_name and "entities" in scope_metadata and len(scope_metadata["entities"]) == 1:
                entity_name = scope_metadata["entities"][0]

            # If entity_name is still empty, try to get it from the attribute details
            if not entity_name:
                # Try to extract entity name from the scope name
                scope_name = scope_metadata.get("name", "")
                if "Host" in scope_name:
                    entity_name = "Host"
                elif "Cloud" in scope_name:
                    entity_name = "Cloud_compute"  # Default to Cloud_compute if specific cloud entity not specified

        # If check_api_dd_only is True, validate using only api_dd_extracted.json
        if check_api_dd_only:
            print(f"Using api_dd_only validation for {attr_name}")
            api_dd_type = attr_details.get("type")

            # For timestamp scope with api_dd_only, check if the type is "timestamp" in api_dd
            if field_type == "timestamp":
                if api_dd_type == "timestamp":
                    print(f"DEBUG: API_DD_ONLY - Found valid timestamp field {attr_name} with type={api_dd_type}")
                    return True
                else:
                    print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                    return False
            # For other field types with api_dd_only, check direct match
            elif field_type == "string" and api_dd_type != "string":
                print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                return False
            elif field_type == "list<string>" and api_dd_type != "list<string>":
                print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                return False
            elif field_type == "array<string>" and api_dd_type != "list<string>":
                print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                return False
            elif field_type == "boolean" and api_dd_type != "boolean":
                print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                return False
            elif field_type == "int" and api_dd_type not in ["int", "integer"]:
                print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                return False
            elif field_type == "bigint":
                # For bigint scope with api_dd_only, check that it's bigint but not timestamp
                if api_dd_type == "timestamp":
                    print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type} (excluding timestamp fields)")
                    return False
                elif api_dd_type not in ["int", "integer", "bigint"]:
                    print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type}")
                    return False

            return True

        # Original validation logic using all_attributes.json
        # Normalize entity name for lookup in all_attributes.json
        normalized_entity_name = normalize_entity_name(entity_name, format_type="display")

        # Find the attribute in all_attributes.json
        attr_in_all_attributes = next((attr for attr in ALL_ATTRIBUTES if attr["field_name"] == attr_name and attr["entity"] == normalized_entity_name), None)

        if attr_in_all_attributes:
            print(f"Found attribute {attr_name} in all_attributes.json with data_type: {attr_in_all_attributes['data_type']}")
            # Check data type based on all_attributes.json
            if field_type == "string" and attr_in_all_attributes["data_type"] != "string":
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False
            elif field_type == "list<string>" and attr_in_all_attributes["data_type"] != "array<string>":
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False
            elif field_type == "array<string>" and attr_in_all_attributes["data_type"] != "array<string>":
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False
            elif field_type == "boolean" and attr_in_all_attributes["data_type"] != "boolean":
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False
            elif field_type == "int" and attr_in_all_attributes["data_type"] != "int":
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False
            elif field_type == "bigint" and attr_in_all_attributes["data_type"] != "bigint":
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False
            elif field_type == "bigint" and attr_in_all_attributes["data_type"] == "bigint":
                # For bigint scope, also check that it's not a timestamp field in api_dd
                api_dd_type = attr_details.get("type")
                if api_dd_type == "timestamp":
                    print(f"  Rejecting {attr_name} because field_type={field_type} but api_dd type={api_dd_type} (excluding timestamp fields)")
                    return False
            elif field_type == "timestamp" and attr_in_all_attributes["data_type"] not in ["bigint"]:
                print(f"  Rejecting {attr_name} because field_type={field_type} but data_type={attr_in_all_attributes['data_type']}")
                return False

            # For array scope, if we found a match, print additional debug info
            if is_array_scope and attr_in_all_attributes["data_type"] == "array<string>":
                print(f"DEBUG: ARRAY SCOPE - Found valid array field {attr_name} for entity {entity_name}")
                # Check the api_dd type as well
                api_dd_type = attr_details.get("type")
                print(f"DEBUG: ARRAY SCOPE - API DD type for {attr_name}: {api_dd_type}")
                return True
        else:
            # If not found in all_attributes.json, we can't validate the data type
            # Just log a warning and continue - we'll only use api_dd for UI visibility checks
            print(f"WARNING: Attribute {attr_name} not found in all_attributes.json for entity {entity_name}. Cannot validate data type.")

            # For array scope, check if the api_dd type indicates it's an array
            if is_array_scope:
                api_dd_type = attr_details.get("type")
                data_structure = attr_details.get("data_structure")
                if api_dd_type == "list<string>" or data_structure == "list":
                    print(f"DEBUG: ARRAY SCOPE - Found potential array field {attr_name} based on api_dd (type={api_dd_type}, data_structure={data_structure})")
                    return True
            # For timestamp scope, check if the field name ends with _date
            elif field_type == "timestamp":
                # Check if the field name matches the pattern for date fields
                if attr_name.endswith("_date"):
                    print(f"DEBUG: TIMESTAMP SCOPE - Found potential timestamp field {attr_name} based on name pattern")
                    return True

            # For string type, exclude structs
            if field_type == "string":
                # Exclude structs
                if attr_details.get("data_structure") == "struct":
                    return False
                    # Note: We're not excluding fields with data_structure=list here anymore
                    # Some fields in the data dictionary have data_structure=list but are actually string fields

    # Check based on group
    # Check if the attribute has the correct group for the scope
    # Map field_group to expected group value
    group_mapping = {
        "sourceSpecificProperties": "source_specific",
        "entitySpecificProperties": "entity_specific",
        "commonProperties": "common"
    }

    # Get the expected group for this field_group
    expected_group = group_mapping.get(scope_metadata["field_group"])

    # If we have an expected group, check if the attribute has that group
    if expected_group and attr_details.get("group") != expected_group:
        return False

    return True

def get_valid_attributes(data_dict, scope_metadata, entity_name=None):
    """
    Get valid attributes for a given scope.

    Args:
        data_dict: The data dictionary
        scope_metadata: The metadata for the scope
        entity_name: The name of the entity

    Returns:
        set: A set of valid attribute names
    """
    valid_attrs = set()

    # Check if this is the array scope
    is_array_scope = scope_metadata.get("scope_definition") == "Source Specific Properties - Array"

    for attr, details in data_dict["attributes"].items():
        if is_valid_attribute(attr, details, scope_metadata, entity_name):
            valid_attrs.add(attr)

    print(f"Found {len(valid_attrs)} valid attributes for scope '{scope_metadata['field_group']}'")

    # For array scope, check all array fields from all_attributes.json
    if is_array_scope:
        global ALL_ATTRIBUTES
        if ALL_ATTRIBUTES is None:
            ALL_ATTRIBUTES = load_all_attributes()

        # Normalize entity name for lookup in all_attributes.json
        normalized_entity_name = normalize_entity_name(entity_name, format_type="display")

    return valid_attrs

def extract_info_from_config(file_path, field_group, valid_attrs):
    """
    Extract properties from a specific section of a config file.

    Args:
        file_path: Path to the config file
        field_group: The field group to extract (commonProperties, entitySpecificProperties, sourceSpecificProperties)
        valid_attrs: Set of valid attribute names

    Returns:
        dict: Dictionary containing extracted information
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Extract properties that are in valid_attrs
        properties = []
        if field_group in config:
            for prop in config[field_group]:
                if 'colName' in prop and prop['colName'] in valid_attrs:
                    properties.append(prop['colName'])

        # Extract origin field
        origin = None
        if 'origin' in config:
            # Remove quotes from the origin field if present
            origin = config['origin']
            # Remove single quotes that might be in the origin field
            if isinstance(origin, str):
                origin = re.sub(r"^'|'$", "", origin)

        return {
            'file_name': os.path.basename(file_path),
            'origin': origin,
            'properties': properties
        }
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return {
            'file_name': os.path.basename(file_path),
            'origin': None,
            'properties': []
        }

def extract_properties_from_configs(entity, field_group, valid_attrs):
    """
    Extract properties from all config files for a specific entity and scope.

    Args:
        entity: The entity name (e.g., 'Host', 'Application')
        field_group: The field group to extract (commonProperties, entitySpecificProperties, sourceSpecificProperties)
        valid_attrs: Set of valid attribute names

    Returns:
        list: List of dictionaries with extracted information
    """
    print(f"Extracting {field_group} for {entity} from config files...")

    inventory_dir, _ = get_entity_paths(entity)
    config_dir_abs = get_absolute_path(inventory_dir)
    print(f"Looking for config files in: {config_dir_abs}")

    # Find all JSON config files
    config_files = []
    for root, _, files in os.walk(config_dir_abs):
        for file in files:
            if file.endswith('.json'):
                config_files.append(os.path.join(root, file))

    # Check for any JSON parsing issues
    for config_file in config_files:
        with open(config_file, 'r') as f:
            try:
                json.load(f)
            except json.JSONDecodeError:
                print(f"WARNING: Could not parse {config_file} as JSON")

    print(f"Found {len(config_files)} config files")

    # Extract information from each config file
    results = []
    for file_path in config_files:
        info = extract_info_from_config(file_path, field_group, valid_attrs)
        # Only add to results if there are properties and an origin
        if info['properties'] and info['origin']:
            results.append(info)

    # Count total properties extracted
    total_props = sum(len(info['properties']) for info in results)
    print(f"Extracted {total_props} {field_group} from {len(results)} config files")

    return results

def organize_properties_by_name(extracted_info):
    """
    Organize the extracted properties by property name.

    Args:
        extracted_info: List of dictionaries with extracted information

    Returns:
        dict: A dictionary mapping property names to a list of origins
    """
    property_to_origins = defaultdict(list)

    for info in extracted_info:
        if info['origin']:
            for prop in info['properties']:
                if info['origin'] not in property_to_origins[prop]:
                    property_to_origins[prop].append(info['origin'])

    return property_to_origins

def get_field_type_from_data_dict(property_name, data_dict, field_group, entity_name=None, check_api_dd_only=False):
    """
    Get the field type and data structure from all_attributes.json first, then fall back to the data dictionary for UI visibility.
    If check_api_dd_only is True, only use api_dd_extracted.json for validation.

    Args:
        property_name: The property name
        data_dict: The data dictionary
        field_group: The field group (commonProperties, entitySpecificProperties, sourceSpecificProperties)
        entity_name: The entity name (optional)
        check_api_dd_only: If True, only use api_dd_extracted.json for validation

    Returns:
        tuple: (field_type, data_structure) or (None, None) if not found
    """
    # If check_api_dd_only is True, only use api_dd_extracted.json
    if check_api_dd_only:
        if not data_dict or "attributes" not in data_dict:
            return None, None

        if property_name in data_dict["attributes"]:
            attr_info = data_dict["attributes"][property_name]
            api_dd_type = attr_info.get("type")
            data_structure = attr_info.get("data_structure")

            # Check if the property should be excluded based on UI visibility
            ui_visibility = attr_info.get("ui_visibility")
            if ui_visibility is False:
                print(f"Excluding {property_name} due to ui_visibility: {ui_visibility}")
                return None, None

            # Map api_dd type to field_type
            field_type = None
            if api_dd_type == "string":
                field_type = "string"
            elif api_dd_type == "list<string>":
                field_type = "list<string>"
                data_structure = "list"
            elif api_dd_type == "timestamp":
                field_type = "timestamp"
            elif api_dd_type == "boolean":
                field_type = "boolean"
            elif api_dd_type == "int" or api_dd_type == "integer":
                field_type = "int"
            elif api_dd_type == "bigint":
                field_type = "bigint"

            if field_type:
                print(f"Found field type in api_dd for {property_name}: {field_type}, data_structure: {data_structure}")
                return field_type, data_structure

        return None, None

    # Original logic using all_attributes.json first
    global ALL_ATTRIBUTES

    # Load all_attributes.json if not already loaded
    if ALL_ATTRIBUTES is None:
        ALL_ATTRIBUTES = load_all_attributes()

    # Extract entity name from data_dict if not provided
    if not entity_name and data_dict and "caption" in data_dict:
        entity_name = data_dict["caption"]

    # Normalize entity name to display format for matching with all_attributes.json
    normalized_entity_name = normalize_entity_name(entity_name, format_type="display")

    # Find the attribute in all_attributes.json

    attr_in_all_attributes = next((attr for attr in ALL_ATTRIBUTES if attr["field_name"] == property_name and attr["entity"] == normalized_entity_name), None)

    # If not found, try with the original entity name as a fallback
    if attr_in_all_attributes is None and entity_name != normalized_entity_name:
        attr_in_all_attributes = next((attr for attr in ALL_ATTRIBUTES if attr["field_name"] == property_name and attr["entity"] == entity_name), None)

    if attr_in_all_attributes:
        # Map data_type from all_attributes.json to type in api_dd
        data_type = attr_in_all_attributes["data_type"]
        field_type = None
        data_structure = None

        # Debug output for aws_autoscaling_group_key
        if property_name == "aws_autoscaling_group_key":
            print(f"DEBUG: aws_autoscaling_group_key found in all_attributes.json with data_type={data_type}")

        # Map data_type to field_type and data_structure
        if data_type == "string":
            field_type = "string"
        elif data_type == "array<string>":
            field_type = "list<string>"
            data_structure = "list"
            # Debug output for aws_autoscaling_group_key
            if property_name == "aws_autoscaling_group_key":
                print(f"DEBUG: aws_autoscaling_group_key converted to field_type={field_type}, data_structure={data_structure}")
        elif data_type == "int" or data_type == "integer":
            field_type = "int"
        elif data_type == "bigint":
            field_type = "bigint"
            # For timestamp fields, we need to check if the field name ends with _date
            if property_name.endswith("_date"):
                field_type = "timestamp"
        elif data_type == "boolean":
            field_type = "boolean"
        elif data_type == "timestamp":
            field_type = "timestamp"
        elif data_type.startswith("struct"):
            field_type = "string"
            data_structure = "struct"

        if field_type:
            print(f"Found field type in all_attributes.json for {property_name}: {field_type}, data_structure: {data_structure}")
            return field_type, data_structure

    # If not found in all_attributes.json, we can't determine the field type
    # We'll only check the data dictionary for UI visibility
    if not data_dict or "attributes" not in data_dict:
        return None, None

    # Check if the property should be excluded based on UI visibility
    if property_name in data_dict["attributes"]:
        attr_info = data_dict["attributes"][property_name]
        ui_visibility = attr_info.get("ui_visibility")
        if ui_visibility is False:
            print(f"Excluding {property_name} due to ui_visibility: {ui_visibility}")
            return None, None

    # We couldn't determine the field type from all_attributes.json
    print(f"WARNING: Attribute {property_name} not found in all_attributes.json for entity {entity_name}. Cannot validate data type.")
    return None, None

# No hardcoded special expressions - rely on scope metadata and expression detection instead

def generate_field_mapping(property_name, origins, scope_metadata, existing_expr=None, data_dict=None, field_group=None):
    """
    Generate a field mapping for a property with the specified origins using the scope metadata template.

    Args:
        property_name: The property name
        origins: List of origins where the property appears
        scope_metadata: The metadata for the scope
        existing_expr: Optional existing expression for the property
        data_dict: Optional data dictionary for the entity
        field_group: Optional field group (commonProperties, entitySpecificProperties, sourceSpecificProperties)

    Returns:
        dict: The field mapping
    """
    # Get the scope definition and check which type of scope we're dealing with
    scope_definition = scope_metadata.get("scope_definition", "")

    # Check if this field is in the attribute list for this scope
    attribute_list = scope_metadata.get("attribute_list", [])
    is_in_attribute_list = property_name in attribute_list

    # Check if the field has an existing expression that contains complex logic
    has_complex_logic = existing_expr and (
        "case when" in existing_expr.lower() or
        "when" in existing_expr.lower() or
        "then" in existing_expr.lower() or
        "else" in existing_expr.lower() or
        "end" in existing_expr.lower() or
        "regexp_extract" in existing_expr.lower() or
        "regexp_like" in existing_expr.lower() or
        "rlike" in existing_expr.lower() or
        "concat" in existing_expr.lower() or
        "array_contains" in existing_expr.lower() or
        "upper(" in existing_expr.lower() or
        "lower(" in existing_expr.lower()
    )

    # If this field is in the attribute list and the scope has a default_expression,
    # use that expression directly instead of using templates
    if is_in_attribute_list and "default_expression" in scope_metadata:
        print(f"Field {property_name} is in attribute list for scope {scope_definition}, using scope's default_expression")
        expression_template = scope_metadata["default_expression"]

        # If the field has complex logic and the scope has a real_logic_case template, use it
        if has_complex_logic and "default_expression_template" in scope_metadata and "real_logic_case" in scope_metadata["default_expression_template"]:
            print(f"  Using real_logic_case template for {property_name} as it has complex logic")
            expression_template = scope_metadata["default_expression_template"]["real_logic_case"]
            expression = expression_template.replace("{logic}", existing_expr)
            expression = expression.replace("{no_data}", NO_DATA)
        else:
            # Replace placeholders in the template
            expression = expression_template.replace("{field}", property_name)
            expression = expression.replace("{no_data}", NO_DATA)
            expression = expression.replace("{not_applicable}", NOT_APPLICABLE)

        # Return the field mapping with the scope's fieldsSpec
        return {
            "colName": property_name,
            "colExpr": expression,
            "fieldsSpec": scope_metadata["fieldsSpec"]
        }

    # Get the scope definition and field type
    scope_field_type = scope_metadata.get("field_type", "any")

    # Check if this scope has a default_expression_template
    has_default_expression_template = "default_expression_template" in scope_metadata

    # Check if this scope has a default_expression
    has_default_expression = "default_expression" in scope_metadata

    # Check if this scope has an attribute_list
    has_attribute_list = "attribute_list" in scope_metadata and scope_metadata["attribute_list"]
    attribute_list = scope_metadata.get("attribute_list", [])

    # Check if this scope has attribute_patterns
    has_attribute_patterns = "attribute_patterns" in scope_metadata and scope_metadata["attribute_patterns"]
    attribute_patterns = scope_metadata.get("attribute_patterns", [])

    # Check if this is a timestamp field
    is_timestamp_field = scope_field_type == "timestamp" and property_name.endswith("_date")

    # Special handling for os_family field
    is_os_family = property_name == "os_family"

    # If this is the os_family field and it has an existing expression, always use the real_logic_case template
    if is_os_family and existing_expr and "default_expression_template" in scope_metadata and "real_logic_case" in scope_metadata["default_expression_template"]:
        print(f"Special handling for os_family field: using real_logic_case template")
        expression_template = scope_metadata["default_expression_template"]["real_logic_case"]
        expression = expression_template.replace("{logic}", existing_expr)
        expression = expression.replace("{no_data}", NO_DATA)

        # Return the field mapping with the scope's fieldsSpec
        return {
            "colName": property_name,
            "colExpr": expression,
            "fieldsSpec": scope_metadata["fieldsSpec"]
        }

    # Get the field type and data structure from the data dictionary if available
    field_type, data_structure = None, None
    if data_dict and field_group:
        check_api_dd_only = scope_metadata.get("check_api_dd_only", False)
        field_type, data_structure = get_field_type_from_data_dict(property_name, data_dict, field_group, entity_name, check_api_dd_only)

    # Check if the field type matches the scope's field_type
    scope_field_type = scope_metadata.get("field_type", "any")

    # Skip processing if the field type doesn't match the scope's requirements
    if field_type:
        # For string scope, skip if:
        # 1. Field type is not exactly "string", or
        # 2. Field type is "string" but data structure is "struct"
        if scope_field_type == "string":
            if field_type != "string" or data_structure == "struct":
                print(f"Skipping {property_name} because it's not a simple string field (type: {field_type}, data_structure: {data_structure})")
                return None

        # For list<string> scope, skip if field type is not exactly "list<string>"
        elif scope_field_type == "list<string>":
            if field_type != "list<string>":
                print(f"Skipping {property_name} because it's not a list<string> field (type: {field_type})")
                return None

        # For boolean scope, skip if field type is not exactly "boolean"
        elif scope_field_type == "boolean":
            if field_type != "boolean":
                print(f"Skipping {property_name} because it's not a boolean field (type: {field_type})")
                return None

        # For bigint scope, skip if field type is not exactly "bigint"
        elif scope_field_type == "bigint":
            if field_type != "bigint":
                print(f"Skipping {property_name} because it's not a bigint field (type: {field_type})")
                return None

        # For any other scope type, check for exact match
        elif scope_field_type != "any":
            if field_type != scope_field_type:
                print(f"Skipping {property_name} because its type ({field_type}) doesn't match the scope's field_type ({scope_field_type})")
                return None

    # If field_type is not found in data dictionary, use the scope's field_type
    if not field_type and "field_type" in scope_metadata:
        field_type = scope_metadata["field_type"]

    # Check if there's a field-specific expression for this property
    if "field_expressions" in scope_metadata and property_name in scope_metadata["field_expressions"]:
        # Use the field-specific expression
        expression_template = scope_metadata["field_expressions"][property_name]
        print(f"Using field-specific expression for {property_name}")

        # Always replace {field} with property_name first to ensure all instances are replaced
        expression_template = expression_template.replace("{field}", property_name)

        # Replace placeholders for default values
        expression_template = expression_template.replace("{no_data}", NO_DATA)
        expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

        # Check if the expression uses the {origin_condition} placeholder
        if "{origin_condition}" in expression_template:
            # Create the origin condition for the case expression
            if len(origins) == 1:
                origin_condition = f"array_contains(data_source_subset_name,'{origins[0]}')"
            else:
                origin_conditions = [f"array_contains(data_source_subset_name,'{origin}')" for origin in origins]
                origin_condition = " or ".join(origin_conditions)

            # Replace the origin_condition placeholder
            expression = expression_template.replace("{origin_condition}", origin_condition)
        else:
            # If no special placeholders, we've already replaced {field}
            expression = expression_template
    else:
        # Handle different scope types
        if field_group == "commonProperties" and "default_expression_template" in scope_metadata:
            # Common Properties with default_expression_template
            templates = scope_metadata["default_expression_template"]
            expression_replacement_condition = scope_metadata.get("expression_replacement_condition", [])

            # No hardcoded list of fields to preserve - rely on scope metadata instead

            # Determine which template to use based on the existing expression
            if existing_expr is None:
                # No expression case - new field
                print(f"Using no_expression_case template for new field {property_name}")
                expression_template = templates["no_expression_case"]
            elif existing_expr and any(cond.lower() == existing_expr.strip().lower() for cond in expression_replacement_condition):
                # Simple null expression that should be replaced
                print(f"Using placeholder_case template for {property_name} as it has a null expression: {existing_expr}")
                expression_template = templates["placeholder_case"]
            elif existing_expr:
                # Any other existing expression is considered real logic and should be preserved
                # This is a more conservative approach that preserves any non-null expression
                print(f"Using real_logic_case template for {property_name} as it has an existing expression: {existing_expr}")
                expression_template = templates["real_logic_case"]
            else:
                # Default to placeholder case for simple expressions
                print(f"Using placeholder_case template for {property_name} with simple expression {existing_expr}")
                expression_template = templates["placeholder_case"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # Handle the {logic} placeholder for real_logic_case
            if "{logic}" in expression_template and existing_expr:
                # Check if the existing expression already has multiple nested COALESCE functions
                if existing_expr.lower().count("coalesce(coalesce") > 0:
                    # Try to extract the innermost expression - everything between the first COALESCE( and the last )
                    match = re.search(r"COALESCE\((.+)\)$", existing_expr, re.IGNORECASE)
                    if match:
                        # Extract the content and remove any trailing default values
                        inner_content = match.group(1)
                        # If there's a comma followed by a default value, remove it
                        if "," in inner_content:
                            inner_expr = inner_content.split(",")[0]
                        else:
                            inner_expr = inner_content
                        print(f"Fixing nested COALESCE in {property_name}: {existing_expr} -> {inner_expr}")
                        expression = expression_template.replace("{logic}", inner_expr)
                    else:
                        # If we can't extract the innermost expression, use the existing expression as is
                        print(f"Could not extract inner expression from {existing_expr}, using as is")
                        expression = expression_template.replace("{logic}", existing_expr)
                else:
                    # Use the existing expression as is
                    expression = expression_template.replace("{logic}", existing_expr)
            else:
                expression = expression_template

        elif field_group == "entitySpecificProperties" and "default_expression_template" in scope_metadata:
            # Entity Specific Properties with default_expression_template
            templates = scope_metadata["default_expression_template"]
            expression_replacement_condition = scope_metadata.get("expression_replacement_condition", [])

            # Check if this is a placeholder expression (cast(null as string))
            is_placeholder = existing_expr and any(expr in existing_expr.lower() for expr in ["cast(null", "null"])
            if is_placeholder:
                # For placeholder expressions, we should use the appropriate template without nesting
                print(f"Using appropriate template for placeholder expression: {existing_expr}")
                # Use the no_expression_case template for placeholder expressions
                expression_template = templates["no_expression_case"]
                expression_template = expression_template.replace("{field}", property_name)
                expression_template = expression_template.replace("{no_data}", NO_DATA)
                expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)
                expression = expression_template
                return {
                    "colName": property_name,
                    "colExpr": expression,
                    "fieldsSpec": scope_metadata["fieldsSpec"]
                }

            # Fix for double COALESCE issue - check if the existing expression already has a COALESCE
            if existing_expr and "COALESCE(COALESCE(" in existing_expr:
                print(f"Fixing double COALESCE in {property_name}")
                # Extract the inner expression
                inner_expr = re.search(r"COALESCE\(COALESCE\(([^,]+),", existing_expr)
                if inner_expr:
                    existing_expr = f"COALESCE({inner_expr.group(1)}, {NO_DATA})"
                    print(f"  Fixed to: {existing_expr}")

            # Determine which template to use based on the existing expression
            if existing_expr is None:
                # No expression case - new field
                print(f"Using no_expression_case template for new field {property_name}")
                expression_template = templates["no_expression_case"]
            elif existing_expr and any(cond.lower() == existing_expr.strip().lower() for cond in expression_replacement_condition):
                # Explicitly handle expressions that exactly match the replacement conditions
                print(f"Using placeholder_case template for {property_name} with replacement condition match: {existing_expr}")
                expression_template = templates["placeholder_case"]
            elif existing_expr and (
                # Check if the expression contains complex logic (functions, CASE statements, etc.)
                "case" in existing_expr.lower() or
                "when" in existing_expr.lower() or
                "coalesce" in existing_expr.lower() or
                "concat" in existing_expr.lower() or
                "regexp_extract" in existing_expr.lower() or
                "array_contains" in existing_expr.lower() or
                "upper(" in existing_expr.lower() or
                "lower(" in existing_expr.lower() or
                "if(" in existing_expr.lower() or
                "then" in existing_expr.lower() or
                "else" in existing_expr.lower() or
                "end" in existing_expr.lower() or
                ("cast(" in existing_expr.lower() and not "cast(null as" in existing_expr.lower())
            ):
                # Real logic case - expression contains complex logic that should be preserved
                print(f"Using real_logic_case template for {property_name} with complex expression {existing_expr}")
                expression_template = templates["real_logic_case"]
            else:
                # Default to placeholder case for simple expressions
                print(f"Using placeholder_case template for {property_name} with simple expression {existing_expr}")
                expression_template = templates["placeholder_case"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # Handle the {logic} placeholder for real_logic_case
            if "{logic}" in expression_template and existing_expr:
                # Check if the existing expression already has multiple nested COALESCE functions
                if existing_expr.lower().count("coalesce(coalesce") > 0:
                    # Try to extract the innermost expression - everything between the first COALESCE( and the last )
                    match = re.search(r"COALESCE\((.+)\)$", existing_expr, re.IGNORECASE)
                    if match:
                        # Extract the content and remove any trailing default values
                        inner_content = match.group(1)
                        # If there's a comma followed by a default value, remove it
                        if "," in inner_content:
                            inner_expr = inner_content.split(",")[0]
                        else:
                            inner_expr = inner_content
                        print(f"Fixing nested COALESCE in {property_name}: {existing_expr} -> {inner_expr}")
                        expression = expression_template.replace("{logic}", inner_expr)
                    else:
                        # If we can't extract the innermost expression, use the existing expression as is
                        print(f"Could not extract inner expression from {existing_expr}, using as is")
                        expression = expression_template.replace("{logic}", existing_expr)
                else:
                    # Use the existing expression as is
                    expression = expression_template.replace("{logic}", existing_expr)
            else:
                expression = expression_template

        elif field_group == "sourceSpecificProperties" and "default_expression" in scope_metadata:
            # Source Specific Properties with origin-based conditional logic
            expression_template = scope_metadata["default_expression"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # Create the origin condition for the case expression
            if len(origins) == 1:
                origin_condition = f"array_contains(data_source_subset_name,'{origins[0]}')"
            else:
                origin_conditions = [f"array_contains(data_source_subset_name,'{origin}')" for origin in origins]
                origin_condition = " or ".join(origin_conditions)

            # Replace the origin_condition placeholder
            expression = expression_template.replace("{origin_condition}", origin_condition)

        elif field_group == "entitySpecificProperties" and "Cloud Attributes" in scope_definition and "default_expression" in scope_metadata:
            # Cloud Attributes with infrastructure_type-based conditional logic
            expression_template = scope_metadata["default_expression"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # For cloud attributes, we should always use the specific expression from the scope metadata
            # This is especially important for fields like cloud_resource_type
            print(f"Using cloud-specific expression for {property_name}: {expression_template}")
            expression = expression_template

            # If the expression already has a COALESCE, remove any outer COALESCE wrapping
            if "COALESCE(COALESCE(" in expression:
                print(f"Fixing double COALESCE in {property_name}")
                expression = expression.replace("COALESCE(COALESCE(", "COALESCE(")
                expression = expression.replace("), '(No Data)')", ")")

        elif field_group == "entitySpecificProperties" and "Host Type - Mobile" in scope_definition and "default_expression" in scope_metadata:
            # Host Type - Mobile with type-based conditional logic
            expression_template = scope_metadata["default_expression"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # No need to replace any other placeholders for this scope
            expression = expression_template

        elif field_group == "entitySpecificProperties" and "Host Type - Network,Printer" in scope_definition and "default_expression" in scope_metadata:
            # Host Type - Network,Printer with type-based conditional logic
            expression_template = scope_metadata["default_expression"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # No need to replace any other placeholders for this scope
            expression = expression_template

        elif "default_expression" in scope_metadata:
            # Generic handling for any other scope with default_expression
            expression_template = scope_metadata["default_expression"]

            # Replace placeholders in the template
            expression_template = expression_template.replace("{field}", property_name)
            expression_template = expression_template.replace("{no_data}", NO_DATA)
            expression_template = expression_template.replace("{not_applicable}", NOT_APPLICABLE)

            # Check if the expression uses the {origin_condition} placeholder
            if "{origin_condition}" in expression_template:
                # Create the origin condition for the case expression
                if len(origins) == 1:
                    origin_condition = f"array_contains(data_source_subset_name,'{origins[0]}')"
                else:
                    origin_conditions = [f"array_contains(data_source_subset_name,'{origin}')" for origin in origins]
                    origin_condition = " or ".join(origin_conditions)

                # Replace the origin_condition placeholder
                expression = expression_template.replace("{origin_condition}", origin_condition)
            elif "{logic}" in expression_template and existing_expr:
                # Handle {logic} placeholder if present
                expression = expression_template.replace("{logic}", existing_expr)
            else:
                # No special placeholders to replace
                expression = expression_template

        else:
            # Default to a simple COALESCE expression if no templates are defined
            expression = f"COALESCE({property_name}, {NO_DATA})"
            print(f"No expression template found for {property_name}, using default COALESCE")

    # Create the field mapping with the scope's fieldsSpec

    # Fix missing closing parenthesis in expressions
    if expression.count('(') > expression.count(')'):
        print(f"Fixing missing closing parenthesis in expression for {property_name}")
        expression = expression + ')'
        print(f"  Fixed to: {expression}")

    # Final check for any remaining {origin_condition} placeholders
    if "{origin_condition}" in expression:
        print(f"Replacing remaining {origin_condition} placeholder in expression for {property_name}")
        # Create the origin condition for the case expression
        if len(origins) == 1:
            origin_condition = f"array_contains(data_source_subset_name,'{origins[0]}')"
        else:
            origin_conditions = [f"array_contains(data_source_subset_name,'{origin}')" for origin in origins]
            origin_condition = " or ".join(origin_conditions)

        # Replace the origin_condition placeholder
        expression = expression.replace("{origin_condition}", origin_condition)
        print(f"  Fixed to: {expression}")

    field_mapping = {
        "colName": property_name,
        "colExpr": expression,
        "fieldsSpec": scope_metadata["fieldsSpec"]
    }

    return field_mapping

def update_host_entity_config_for_scope(property_to_origins, scope_metadata, host_config, data_dict=None, entity_name=None):
    """
    Update a specific section of the host entity config.
    If attribute_list is provided, override existing mappings for fields in the list.
    If attribute_list is empty, handle according to the scope rules:
    - For first scope (commonProperties):
      - If field has predefined logic, don't change it
      - If field has cast(null as string), convert to COALESCE({field},'No Data')
      - Add new fields using COALESCE({field},'No Data')
    - For other scopes: override mappings for fields with basic null expressions
    - For all scopes: Check field type in data dictionary and use appropriate expression

    Args:
        property_to_origins: A dictionary mapping property names to a list of origins
        scope_metadata: The metadata for the scope
        host_config: The host entity config to update
        data_dict: Optional data dictionary for the entity

    Returns:
        tuple: (preserved_count, new_count, updated_count) - The number of preserved, new, and updated mappings
    """
    field_group = scope_metadata["field_group"]
    print(f"Updating {field_group} in host entity config...")

    # Check if the section exists
    if field_group not in host_config:
        host_config[field_group] = []

    # Get existing property names and their mappings
    existing_props = {}
    for prop in host_config[field_group]:
        if 'colName' in prop:
            existing_props[prop['colName']] = prop

    # Start with an empty list of mappings
    final_mappings = []
    preserved_count = 0
    new_count = 0
    updated_count = 0

    # Check if we have field_expressions or a non-empty attribute_list
    has_field_expressions = "field_expressions" in scope_metadata
    field_expressions_list = list(scope_metadata.get("field_expressions", {}).keys())

    has_attribute_list = "attribute_list" in scope_metadata and scope_metadata["attribute_list"]
    attribute_list = scope_metadata.get("attribute_list", [])

    # Get the field type from scope metadata
    field_type = scope_metadata.get("field_type", "any")

    # Define patterns for basic null expressions based on field type
    null_expr_patterns = []
    if field_type == "string":
        null_expr_patterns = [
            r"cast\(null as string\)",
            r"'No Data'"
        ]
    elif field_type == "integer":
        null_expr_patterns = [
            r"cast\(null as (int|integer)\)"
        ]
    elif field_type == "timestamp":
        null_expr_patterns = [
            r"cast\(null as (bigint|timestamp)\)"
        ]
    elif field_type == "array":
        null_expr_patterns = [
            r"from_json\(null, 'ARRAY<STRING>'\)"
        ]
    elif field_type == "any":
        null_expr_patterns = [
            r"cast\(null as (string|int|bigint|float|double|boolean)\)",
            r"'No Data'",
            r"from_json\(null, 'ARRAY<STRING>'\)"
        ]

    # Get the scope definition and field type
    scope_definition = scope_metadata.get("scope_definition", "")
    scope_field_type = scope_metadata.get("field_type", "any")

    # Check if this scope has a default_expression_template
    has_default_expression_template = "default_expression_template" in scope_metadata

    # Check if this scope has a default_expression
    has_default_expression = "default_expression" in scope_metadata

    # Check if this scope has an attribute_list
    has_attribute_list = "attribute_list" in scope_metadata and scope_metadata["attribute_list"]
    attribute_list = scope_metadata.get("attribute_list", [])

    # Process all existing properties
    for prop in host_config[field_group]:
        if 'colName' in prop:
            prop_name = prop['colName']
            prop_expr = prop.get('colExpr', '')

            # Skip if this property has already been processed by another scope
            if is_attribute_processed(entity_name, prop_name):
                print(f"Skipping {prop_name} as it has been processed by a specific scope already")
                final_mappings.append(prop)
                preserved_count += 1
                continue

            # Special handling for scopes with default_expression_template
            if has_default_expression_template:
                # Check if the field type matches the scope's field_type
                # Get the field type and data structure
                field_type, data_structure = None, None
                if data_dict:
                    check_api_dd_only = scope_metadata.get("check_api_dd_only", False)
                    field_type, data_structure = get_field_type_from_data_dict(prop_name, data_dict, field_group, entity_name, check_api_dd_only)

                # Check if the field type matches the scope's field_type
                scope_field_type = scope_metadata.get("field_type", "any")

                # Only proceed if the field type matches the scope's requirements
                if field_type and (scope_field_type == "any" or
                                  (scope_field_type == "string" and field_type == "string" and data_structure != "struct") or
                                  (scope_field_type == "list<string>" and field_type == "list<string>") or
                                  (scope_field_type == "boolean" and field_type == "boolean") or
                                  (scope_field_type == "bigint" and field_type == "bigint") or
                                  (scope_field_type == "timestamp" and field_type == "bigint") or
                                  (scope_field_type == "timestamp" and prop_name.endswith("_date")) or
                                  (scope_field_type not in ["string", "list<string>", "boolean", "bigint", "timestamp"] and field_type == scope_field_type)):
                    # Let the generate_field_mapping function handle the different logic for each scope
                    # It will apply the correct template based on the scope
                    origins = property_to_origins.get(prop_name, [])
                    field_mapping = generate_field_mapping(prop_name, origins, scope_metadata, prop_expr, data_dict, field_group)

                    # Check if we should preserve the original fieldsSpec
                    should_preserve_fieldsSpec = False

                    # Check if the expression contains complex logic
                    is_custom_logic = prop_expr and (
                        "case when" in prop_expr.lower() or
                        "when" in prop_expr.lower() or
                        "then" in prop_expr.lower() or
                        "else" in prop_expr.lower() or
                        "end" in prop_expr.lower() or
                        "coalesce" in prop_expr.lower() or
                        "concat" in prop_expr.lower() or
                        "regexp_extract" in prop_expr.lower() or
                        "regexp_like" in prop_expr.lower() or
                        "rlike" in prop_expr.lower() or
                        "array_contains" in prop_expr.lower() or
                        "upper(" in prop_expr.lower() or
                        "lower(" in prop_expr.lower() or
                        "if(" in prop_expr.lower() or
                        ("cast(" in prop_expr.lower() and not "cast(null as" in prop_expr.lower())
                    )

                    # Check if we're using a template that preserves logic
                    has_real_logic_case = "default_expression_template" in scope_metadata and "real_logic_case" in scope_metadata["default_expression_template"]

                    # Preserve fieldsSpec if we're preserving logic
                    if is_custom_logic and "fieldsSpec" in prop:
                        # Keep the original fieldsSpec when preserving logic
                        field_mapping["fieldsSpec"] = prop["fieldsSpec"]
                        should_preserve_fieldsSpec = True
                        print(f"Preserving fieldsSpec for {prop_name} while updating expression")

                    final_mappings.append(field_mapping)
                    updated_count += 1
                    print(f"Updating expression for {prop_name}" +
                          (" (preserving fieldsSpec)" if should_preserve_fieldsSpec else ""))
                else:
                    # Preserve the existing mapping if the field type doesn't match
                    final_mappings.append(prop)
                    preserved_count += 1
                    print(f"Preserving existing mapping for {prop_name} due to type mismatch")
            else:
                # Standard handling for other scopes
                should_update = False

                # If this is a scope with specific attributes, only update those attributes
                if has_attribute_list and prop_name in attribute_list:
                    # Even if it's in the attribute list, check if it's already been processed
                    if is_attribute_processed(entity_name, prop_name):
                        should_update = False
                        print(f"Skipping {prop_name} as it has been processed by a specific scope already")
                        # Add this property to the final mappings as-is to preserve it
                        final_mappings.append(prop)
                        preserved_count += 1
                        # Continue to the next property
                        continue
                    else:
                        # If this field is in the attribute list, we should update it with the scope's default_expression
                        should_update = True
                        print(f"Field {prop_name} is in attribute list for scope {scope_metadata.get('scope_definition')}, will update with scope's default_expression")

                        # If this scope has a default_expression, use it directly
                        if "default_expression" in scope_metadata:
                            # Generate a field mapping with the scope's default_expression
                            expression_template = scope_metadata["default_expression"]
                            expression = expression_template.replace("{field}", prop_name)
                            expression = expression.replace("{no_data}", NO_DATA)
                            expression = expression.replace("{not_applicable}", NOT_APPLICABLE)

                            # Fix double COALESCE issue
                            if "COALESCE(COALESCE(" in expression:
                                print(f"Fixing double COALESCE in {prop_name}")
                                expression = expression.replace("COALESCE(COALESCE(", "COALESCE(")
                                expression = expression.replace("), '(No Data)')", ")")

                            # Check if the existing expression already matches the required pattern
                            # This is to avoid unnecessary updates if the expression is already correct
                            if prop_expr:
                                # Generate the correct expression for this field
                                generated_expr = expression_template.replace("{field}", prop_name)
                                generated_expr = generated_expr.replace("{no_data}", NO_DATA)
                                generated_expr = generated_expr.replace("{not_applicable}", NOT_APPLICABLE)

                                # If there are origin conditions, replace them
                                origins = property_to_origins.get(prop_name, [])
                                if origins and "{origin_condition}" in generated_expr:
                                    origin_condition = " OR ".join([f"origin = '{origin}'" for origin in origins])
                                    generated_expr = generated_expr.replace("{origin_condition}", origin_condition)

                                # Normalize expressions for comparison (remove extra spaces, etc.)
                                normalized_existing = re.sub(r'\s+', ' ', prop_expr.strip())
                                normalized_generated = re.sub(r'\s+', ' ', generated_expr.strip())

                                # Compare the normalized expressions
                                if normalized_existing == normalized_generated:
                                    print(f"Skipping {prop_name} as it already has the correct expression: {prop_expr}")
                                    final_mappings.append(prop)
                                    preserved_count += 1
                                    continue

                                # Check if the existing expression is already a simple COALESCE with the field and a placeholder
                                # This handles the common case of COALESCE(field_name, '{No Data}')
                                simple_coalesce_pattern = re.compile(r"COALESCE\s*\(\s*([a-zA-Z0-9_]+)\s*,\s*'\{[^}]*\}'\s*\)", re.IGNORECASE)
                                if simple_coalesce_pattern.match(prop_expr):
                                    field_in_expr = simple_coalesce_pattern.match(prop_expr).group(1)
                                    if field_in_expr == prop_name:
                                        print(f"Skipping {prop_name} as it already has a simple COALESCE expression: {prop_expr}")
                                        final_mappings.append(prop)
                                        preserved_count += 1
                                        continue

                                # Create a pattern to match the expression with variable field names
                                pattern_template = expression_template
                                pattern_template = pattern_template.replace("{field}", r"[a-zA-Z0-9_]+")
                                pattern_template = pattern_template.replace("{no_data}", re.escape(NO_DATA))
                                pattern_template = pattern_template.replace("{not_applicable}", re.escape(NOT_APPLICABLE))
                                pattern_template = pattern_template.replace("{origin_condition}", r".*?")

                                # Create a regex pattern from the template
                                pattern = re.compile(pattern_template, re.IGNORECASE)

                                # Check if the existing expression matches the pattern
                                if pattern.match(prop_expr):
                                    print(f"Skipping {prop_name} as it already has the correct expression pattern: {prop_expr}")
                                    final_mappings.append(prop)
                                    preserved_count += 1
                                    continue

                            # Create the field mapping
                            field_mapping = {
                                "colName": prop_name,
                                "colExpr": expression,
                                "fieldsSpec": scope_metadata["fieldsSpec"]
                            }

                            # Add the field mapping to the final mappings
                            final_mappings.append(field_mapping)
                            updated_count += 1
                            print(f"Updated {prop_name} with scope's default_expression: {expression}")

                            # Mark this field as processed now that it's been added to the entity config
                            mark_attribute_as_processed(entity_name, prop_name, "attribute_list")

                            # Skip the rest of the processing for this property
                            continue
                # If this property has been processed by a specific scope already, skip it
                elif is_attribute_processed(entity_name, prop_name):
                    should_update = False
                    print(f"Skipping {prop_name} as it has been processed by a specific scope already")
                    # Add this property to the final mappings as-is to preserve it
                    final_mappings.append(prop)
                    preserved_count += 1
                    # Continue to the next property
                    continue
                # If attribute_list is empty, check if the property has a basic null expression matching the field type
                elif not has_attribute_list:
                    for pattern in null_expr_patterns:
                        if re.match(pattern, prop_expr, re.IGNORECASE):
                            should_update = True
                            break

                if should_update:
                    # Create new field mapping with updated expression
                    origins = property_to_origins.get(prop_name, [])

                    # Check if the existing expression already matches the required pattern
                    # This is to avoid unnecessary updates if the expression is already correct
                    if "default_expression" in scope_metadata:
                        # Generate the correct expression for this field
                        template = scope_metadata["default_expression"]
                        generated_expr = template.replace("{field}", prop_name)
                        generated_expr = generated_expr.replace("{no_data}", NO_DATA)
                        generated_expr = generated_expr.replace("{not_applicable}", NOT_APPLICABLE)

                        # If there are origin conditions, replace them
                        origins = property_to_origins.get(prop_name, [])
                        if origins and "{origin_condition}" in generated_expr:
                            origin_condition = " OR ".join([f"origin = '{origin}'" for origin in origins])
                            generated_expr = generated_expr.replace("{origin_condition}", origin_condition)

                        # Normalize expressions for comparison (remove extra spaces, etc.)
                        normalized_existing = re.sub(r'\s+', ' ', prop_expr.strip())
                        normalized_generated = re.sub(r'\s+', ' ', generated_expr.strip())

                        # Compare the normalized expressions
                        if normalized_existing == normalized_generated:
                            print(f"Skipping {prop_name} as it already has the correct expression: {prop_expr}")
                            final_mappings.append(prop)
                            preserved_count += 1
                            continue

                        # Check if the existing expression is already a simple COALESCE with the field and a placeholder
                        # This handles the common case of COALESCE(field_name, '{No Data}')
                        simple_coalesce_pattern = re.compile(r"COALESCE\s*\(\s*([a-zA-Z0-9_]+)\s*,\s*'\{[^}]*\}'\s*\)", re.IGNORECASE)
                        if simple_coalesce_pattern.match(prop_expr):
                            field_in_expr = simple_coalesce_pattern.match(prop_expr).group(1)
                            if field_in_expr == prop_name:
                                print(f"Skipping {prop_name} as it already has a simple COALESCE expression: {prop_expr}")
                                final_mappings.append(prop)
                                preserved_count += 1
                                continue

                        # Get the default expression template
                        template = scope_metadata["default_expression"]

                        # Create a pattern to match the expression with variable field names
                        # Replace {field} with a regex pattern that matches any field name
                        # Replace {no_data} and {not_applicable} with their actual values
                        pattern_template = template
                        pattern_template = pattern_template.replace("{field}", r"[a-zA-Z0-9_]+")
                        pattern_template = pattern_template.replace("{no_data}", re.escape(NO_DATA))
                        pattern_template = pattern_template.replace("{not_applicable}", re.escape(NOT_APPLICABLE))
                        pattern_template = pattern_template.replace("{origin_condition}", r".*?")

                        # Create a regex pattern from the template
                        pattern = re.compile(pattern_template, re.IGNORECASE)

                        # Check if the existing expression matches the pattern
                        if pattern.match(prop_expr):
                            print(f"Skipping {prop_name} as it already has the correct expression pattern: {prop_expr}")
                            final_mappings.append(prop)
                            preserved_count += 1
                            continue

                    # Check if we're using a CASE statement that preserves the original logic
                    is_preserving_logic = False
                    if "default_expression" in scope_metadata:
                        default_expr = scope_metadata["default_expression"]
                        # Check if the expression contains a CASE statement that might preserve logic
                        if "CASE WHEN" in default_expr and "THEN {logic}" in default_expr:
                            is_preserving_logic = True

                    # Check if the existing expression is a nested COALESCE
                    is_nested_coalesce = False
                    if prop_expr and "COALESCE(COALESCE(" in prop_expr:
                        # Extract the inner field name
                        inner_field_match = re.search(r"COALESCE\(COALESCE\(([^,]+),", prop_expr)
                        if inner_field_match and inner_field_match.group(1).strip() == prop_name:
                            # This is a nested COALESCE for the same field, we should fix it
                            is_nested_coalesce = True
                            print(f"Fixing nested COALESCE for {prop_name}")
                            prop_expr = f"COALESCE({prop_name}, '{NO_DATA}')"
                            # Update the property with the fixed expression
                            prop["colExpr"] = prop_expr
                        # Also check if it wraps the logic in COALESCE
                        elif "COALESCE({logic}" in default_expr:
                            is_preserving_logic = True

                    # Generate the field mapping with the existing expression and data dictionary
                    field_mapping = generate_field_mapping(prop_name, origins, scope_metadata, prop_expr, data_dict, field_group)

                    # If field_mapping is None, it means the field type doesn't match the scope's field_type
                    # In this case, preserve the original mapping
                    if field_mapping is None:
                        final_mappings.append(prop)
                        preserved_count += 1
                        print(f"Preserving existing mapping for {prop_name} due to type mismatch")
                    else:
                        # Determine if we should preserve the original fieldsSpec
                        should_preserve_fieldsSpec = False

                        # Check if this is a scope that preserves logic
                        scope_definition = scope_metadata.get("scope_definition", "")
                        is_common_properties = field_group == "commonProperties" and "Common Properties" in scope_definition
                        is_entity_specific = field_group == "entitySpecificProperties" and "Entity Specific Properties" in scope_definition

                        # Check if we're using a template that preserves logic
                        has_real_logic_case = "default_expression_template" in scope_metadata and "real_logic_case" in scope_metadata["default_expression_template"]

                        # Check if the expression is not a basic null or 'No Data' expression
                        is_custom_logic = prop_expr and prop_expr.lower() != "cast(null as string)" and prop_expr.strip() != "'No Data'"

                        # Preserve fieldsSpec if we're preserving logic
                        if (is_common_properties or is_entity_specific) and has_real_logic_case and is_custom_logic and "fieldsSpec" in prop:
                            # Keep the original fieldsSpec when preserving logic
                            field_mapping["fieldsSpec"] = prop["fieldsSpec"]
                            should_preserve_fieldsSpec = True
                            print(f"Preserving fieldsSpec for {prop_name} while updating expression")
                        # Also preserve fieldsSpec for other scopes with custom logic
                        elif is_custom_logic and "fieldsSpec" in prop:
                            field_mapping["fieldsSpec"] = prop["fieldsSpec"]
                            should_preserve_fieldsSpec = True
                            print(f"Preserving fieldsSpec for {prop_name} with custom logic")

                        final_mappings.append(field_mapping)
                        updated_count += 1
                        print(f"Overriding existing mapping for {prop_name} with new expression" +
                              (" (preserving fieldsSpec)" if should_preserve_fieldsSpec else ""))
                else:
                    # Preserve the existing mapping
                    final_mappings.append(prop)
                    preserved_count += 1

    # Add new mappings for properties that don't already exist
    for prop, origins in sorted(property_to_origins.items()):
        # Skip if the property has already been processed by another scope
        if is_attribute_processed(entity_name, prop):
            print(f"Skipping new mapping for {prop} as it has been processed by a specific scope already")
            continue

        if prop not in existing_props:
            # Check if this property is from the attribute_list
            is_from_attribute_list = "attribute_list" in origins

            # If it's from the attribute_list, we should add it regardless of field type
            if is_from_attribute_list:
                # Check if this property already exists in the final mappings
                prop_already_exists = False
                for mapping in final_mappings:
                    if mapping.get("colName") == prop:
                        prop_already_exists = True
                        break

                if prop_already_exists:
                    print(f"Skipping duplicate mapping for {prop}")
                    continue

                # Get the field type from the data dictionary
                check_api_dd_only = scope_metadata.get("check_api_dd_only", False)
                field_type, _ = get_field_type_from_data_dict(prop, data_dict, field_group, entity_name, check_api_dd_only)

                # If this scope has a default_expression, use that instead of a generic template
                if "default_expression" in scope_metadata:
                    print(f"Using scope's default_expression for {prop} from attribute_list")
                    expression_template = scope_metadata["default_expression"]
                    expression = expression_template.replace("{field}", prop)
                    expression = expression.replace("{no_data}", NO_DATA)
                    expression = expression.replace("{not_applicable}", NOT_APPLICABLE)

                    # Check if this property already exists in any of the entity configs with the correct expression
                    # This is to avoid unnecessary updates if the expression is already correct
                    for entity_config_file in glob.glob(os.path.join(BASE_ENTITY_CONFIG_DIR, f"*_entity_config.json")):
                        try:
                            with open(entity_config_file, 'r') as f:
                                entity_config = json.load(f)

                                # Check all property groups
                                for prop_group in ["commonProperties", "entitySpecificProperties", "sourceSpecificProperties"]:
                                    if prop_group in entity_config:
                                        for mapping in entity_config[prop_group]:
                                            if mapping.get("colName") == prop:
                                                existing_expr = mapping.get("colExpr")
                                                if existing_expr:
                                                    # Generate the correct expression for this field
                                                    generated_expr = expression_template.replace("{field}", prop)
                                                    generated_expr = generated_expr.replace("{no_data}", NO_DATA)
                                                    generated_expr = generated_expr.replace("{not_applicable}", NOT_APPLICABLE)

                                                    # If there are origin conditions, replace them
                                                    origins = []
                                                    for config_file in glob.glob(os.path.join(INVENTORY_MODELS_DIR, f"sds_ei__loader__{entity_name.lower()}", "*.json")):
                                                        try:
                                                            with open(config_file, 'r') as f:
                                                                config = json.load(f)
                                                                if "origin" in config and config["origin"]:
                                                                    origins.append(config["origin"])
                                                        except Exception as e:
                                                            print(f"Error reading config file {config_file}: {e}")

                                                    if origins and "{origin_condition}" in generated_expr:
                                                        origin_condition = " OR ".join([f"origin = '{origin}'" for origin in origins])
                                                        generated_expr = generated_expr.replace("{origin_condition}", origin_condition)

                                                    # Normalize expressions for comparison (remove extra spaces, etc.)
                                                    normalized_existing = re.sub(r'\s+', ' ', existing_expr.strip())
                                                    normalized_generated = re.sub(r'\s+', ' ', generated_expr.strip())

                                                    # Compare the normalized expressions
                                                    if normalized_existing == normalized_generated:
                                                        print(f"Found existing expression for {prop} in {entity_config_file} that matches the generated expression: {existing_expr}")
                                                        expression = existing_expr
                                                        break

                                                    # Check if the existing expression is already a simple COALESCE with the field and a placeholder
                                                    # This handles the common case of COALESCE(field_name, '{No Data}')
                                                    simple_coalesce_pattern = re.compile(r"COALESCE\s*\(\s*([a-zA-Z0-9_]+)\s*,\s*'\{[^}]*\}'\s*\)", re.IGNORECASE)
                                                    if simple_coalesce_pattern.match(existing_expr):
                                                        field_in_expr = simple_coalesce_pattern.match(existing_expr).group(1)
                                                        if field_in_expr == prop:
                                                            print(f"Found existing simple COALESCE expression for {prop} in {entity_config_file}: {existing_expr}")
                                                            expression = existing_expr
                                                            break

                                                    # Create a pattern to match the expression with variable field names
                                                    pattern_template = expression_template
                                                    pattern_template = pattern_template.replace("{field}", r"[a-zA-Z0-9_]+")
                                                    pattern_template = pattern_template.replace("{no_data}", re.escape(NO_DATA))
                                                    pattern_template = pattern_template.replace("{not_applicable}", re.escape(NOT_APPLICABLE))
                                                    pattern_template = pattern_template.replace("{origin_condition}", r".*?")

                                                    # Create a regex pattern from the template
                                                    pattern = re.compile(pattern_template, re.IGNORECASE)

                                                    # Check if the existing expression matches the pattern
                                                    if pattern.match(existing_expr):
                                                        print(f"Found existing expression for {prop} in {entity_config_file} that matches the required pattern: {existing_expr}")
                                                        expression = existing_expr
                                                        break
                        except Exception as e:
                            print(f"Error checking existing expressions in {entity_config_file}: {e}")

                    # Fix double COALESCE issue
                    if "COALESCE(COALESCE(" in expression:
                        print(f"Fixing double COALESCE in {prop}")
                        expression = expression.replace("COALESCE(COALESCE(", "COALESCE(")
                        expression = expression.replace("), '(No Data)')", ")")

                    # Mark this field as processed now that it's been added to the entity config
                    if entity_name:
                        mark_attribute_as_processed(entity_name, prop, "attribute_list")
                else:
                    # Create a default expression based on the field type
                    if field_type == "array":
                        expression = f"COALESCE({prop}, from_json(null, 'ARRAY<STRING>'))"
                    else:
                        # Default to string type
                        expression = f"COALESCE({prop}, {NO_DATA})"

                field_mapping = {
                    "colName": prop,
                    "colExpr": expression,
                    "fieldsSpec": scope_metadata["fieldsSpec"]
                }

                final_mappings.append(field_mapping)
                new_count += 1
                print(f"Adding new mapping for {prop} from attribute_list with expression: {field_mapping['colExpr']}")
                continue
            # For scopes with default_expression, use that expression
            if has_default_expression:
                # Get the field type and data structure
                field_type, data_structure = None, None
                if data_dict:
                    check_api_dd_only = scope_metadata.get("check_api_dd_only", False)
                    field_type, data_structure = get_field_type_from_data_dict(prop, data_dict, field_group, entity_name, check_api_dd_only)

                # Check if the field type matches the scope's field_type
                scope_field_type = scope_metadata.get("field_type", "any")

                # Only add if the field type matches the scope's requirements
                if field_type and (scope_field_type == "any" or
                                  (scope_field_type == "string" and field_type == "string" and data_structure != "struct") or
                                  (scope_field_type == "list<string>" and field_type == "list<string>") or
                                  (scope_field_type == "array<string>" and field_type == "list<string>") or
                                  (scope_field_type == "boolean" and field_type == "boolean") or
                                  (scope_field_type == "bigint" and field_type == "bigint") or
                                  (scope_field_type not in ["string", "list<string>", "array<string>", "boolean", "bigint"] and field_type == scope_field_type)):
                    # Process the field based on its type
                    # If this scope has a default_expression, use that instead of a template
                    if "default_expression" in scope_metadata:
                        print(f"Using scope's default_expression for new field {prop}")
                        expression_template = scope_metadata["default_expression"]
                        expression = expression_template.replace("{field}", prop)
                        expression = expression.replace("{no_data}", NO_DATA)
                        expression = expression.replace("{not_applicable}", NOT_APPLICABLE)

                        # Replace the origin_condition placeholder if present
                        if "{origin_condition}" in expression:
                            # Get the origins for this property
                            origins = property_to_origins.get(prop, [])

                            # Create the origin condition for the case expression
                            if len(origins) == 1:
                                origin_condition = f"array_contains(data_source_subset_name,'{origins[0]}')"
                            else:
                                origin_conditions = [f"array_contains(data_source_subset_name,'{origin}')" for origin in origins]
                                origin_condition = " or ".join(origin_conditions)

                            # Replace the origin_condition placeholder
                            expression = expression.replace("{origin_condition}", origin_condition)
                            print(f"  Replaced origin_condition in expression for {prop}: {expression}")
                        field_mapping = {
                            "colName": prop,
                            "colExpr": expression,
                            "fieldsSpec": scope_metadata["fieldsSpec"]
                        }
                    # Use the default_expression_template if available
                    elif "default_expression_template" in scope_metadata:
                        # Use the no_expression_case template for new fields
                        expression_template = scope_metadata["default_expression_template"]["no_expression_case"]
                        expression = expression_template.replace("{field}", prop).replace("{no_data}", NO_DATA)
                        field_mapping = {
                            "colName": prop,
                            "colExpr": expression,
                            "fieldsSpec": scope_metadata["fieldsSpec"]
                        }
                    else:
                        # Fall back to simple COALESCE if no template is available
                        field_mapping = {
                            "colName": prop,
                            "colExpr": f"COALESCE({prop},'No Data')",
                            "fieldsSpec": scope_metadata["fieldsSpec"]
                        }
                    final_mappings.append(field_mapping)
                    new_count += 1
                    print(f"Adding new mapping for {prop} with expression: {field_mapping['colExpr']}")
                else:
                    print(f"Skipping new mapping for {prop} due to type mismatch")
            else:
                # For other scopes, use the standard template with data dictionary
                field_mapping = generate_field_mapping(prop, origins, scope_metadata, None, data_dict, field_group)

                # If field_mapping is None, it means the field type doesn't match the scope's field_type
                # In this case, skip adding this field
                if field_mapping is None:
                    print(f"Skipping new mapping for {prop} due to type mismatch")
                else:
                    final_mappings.append(field_mapping)
                    new_count += 1
                    print(f"Adding new mapping for {prop}")

    # Update the section with all mappings (existing + new)
    host_config[field_group] = final_mappings

    print(f"Updated {field_group}: {preserved_count} existing mappings preserved, {updated_count} existing mappings updated, {new_count} new mappings added")
    return preserved_count, new_count, updated_count

def process_scope(scope_metadata, entities_to_process=None, force=False):
    """
    Process a single scope from the metadata for multiple entities.

    Args:
        scope_metadata: The metadata for the scope
        entities_to_process: Optional list of entities to process. If None, process all entities in the scope.
        force: Whether to force update all fields, even if they already have expressions

    Returns:
        dict: A dictionary mapping entity names to (preserved_count, new_count, updated_count) tuples
    """
    field_group = scope_metadata["field_group"]
    scope_definition = scope_metadata.get("scope_definition", "")
    print(f"\nProcessing scope: {field_group} - {scope_definition}")

    # Add debug output for the Source Specific Properties - Array scope
    if scope_definition == "Source Specific Properties - Array":
        print("DEBUG: Processing Source Specific Properties - Array scope")
        print(f"DEBUG: Array scope field_type={scope_metadata.get('field_type')}")
        print(f"DEBUG: Array scope entities={scope_metadata.get('entities')}")

    # Get the list of entities for this scope
    scope_entities = scope_metadata.get("entities", [])

    # Get the attribute list for this scope
    attribute_list = scope_metadata.get("attribute_list", [])
    if attribute_list:
        print(f"Scope has specific attributes: {', '.join(attribute_list)}")

    # Get the attribute patterns for this scope
    attribute_patterns = scope_metadata.get("attribute_patterns", [])
    if attribute_patterns:
        print(f"Scope has attribute patterns: {', '.join(attribute_patterns)}")

    # Get the field type for this scope
    field_type = scope_metadata.get("field_type", "any")
    print(f"Scope field type: {field_type}")

    # If entities list is empty, process all available entities
    if not scope_entities:
        # Get all entity config files to determine available entities
        entity_config_files = []
        for file in os.listdir(BASE_ENTITY_CONFIG_DIR):
            if file.endswith("_entity_config.json"):
                entity_name = file.replace("_entity_config.json", "")
                # Normalize entity name to display format (spaces between words)
                normalized_entity = normalize_entity_name(entity_name, format_type="display")
                entity_config_files.append(normalized_entity)
        scope_entities = entity_config_files
        print(f"Empty entities list in scope {field_group}. Processing all available entities: {', '.join(scope_entities)}")

    # If entities_to_process is provided, filter the scope entities
    if entities_to_process:
        filtered_entities = [entity for entity in scope_entities if entity in entities_to_process]
        if not filtered_entities and scope_entities:
            print(f"None of the requested entities {', '.join(entities_to_process)} match the scope entities {', '.join(scope_entities)}. Skipping.")
            return {}
        scope_entities = filtered_entities

    if not scope_entities:
        print(f"No entities to process for scope {field_group}. Skipping.")
        return {}

    # For scopes with attribute lists or patterns, we need to ensure the fields are processed with the scope's default expression
    # We'll mark them as processed only after they've been added to the entity config
    global PROCESSED_ATTRIBUTES
    if attribute_list:
        print(f"Processing attributes from specific scope: {', '.join(attribute_list)}")

        # Check if all attributes in the list have already been processed
        all_processed = True
        for attr in attribute_list:
            for entity in scope_entities:
                if not is_attribute_processed(entity, attr):
                    all_processed = False
                    break
            if not all_processed:
                break

        if all_processed and not attribute_patterns:
            print(f"All attributes in the attribute list have already been processed and no patterns defined. Skipping scope.")
            return {}
    elif attribute_patterns:
        print(f"Processing attributes matching patterns: {', '.join(attribute_patterns)}")
        # We'll mark them as processed after they've been added to the entity config in update_host_entity_config_for_scope

    results = {}

    # Process each entity for this scope
    for entity in scope_entities:
        print(f"\nProcessing entity: {entity} for scope: {field_group}")

        # Load entity-specific data dictionary
        data_dict = load_data_dictionary(entity)

        # Skip this entity if it's not found in the data dictionary
        if data_dict is None:
            print(f"Skipping {entity} as it's not found in the data dictionary.")
            continue

        # Load entity config
        _, entity_config_path = get_entity_paths(entity)
        entity_config_path_abs = get_absolute_path(entity_config_path)

        try:
            with open(entity_config_path_abs, 'r', encoding='utf-8') as f:
                entity_config = json.load(f)
        except Exception as e:
            print(f"Error loading entity config for {entity}: {e}. Skipping.")
            continue

        # Get valid attributes for this scope and entity
        # Normalize entity name for consistent processing
        normalized_entity = normalize_entity_name(entity, format_type="display")
        valid_attrs = get_valid_attributes(data_dict, scope_metadata, normalized_entity)

        # Extract properties from config files
        extracted_info = extract_properties_from_configs(entity, field_group, valid_attrs)

        # Organize properties by name
        property_to_origins = organize_properties_by_name(extracted_info)

        # If field_expressions is provided and no properties were found in config files,
        # add the attributes from field_expressions with empty origins
        if "field_expressions" in scope_metadata and not property_to_origins:
            print(f"No {field_group} found in config files for {entity}. Adding attributes from field_expressions.")
            for attr in scope_metadata["field_expressions"]:
                # Use an empty list for origins since these attributes weren't found in config files
                property_to_origins[attr] = []

        # If attribute_list is provided (and not empty), add the attributes from the attribute_list
        # regardless of whether properties were found in config files
        if "attribute_list" in scope_metadata and scope_metadata["attribute_list"]:
            print(f"Adding attributes from attribute_list: {', '.join(scope_metadata['attribute_list'])}")
            for attr in scope_metadata["attribute_list"]:
                # Skip if this attribute has already been processed
                if is_attribute_processed(entity, attr):
                    print(f"  Skipping {attr} as it has already been processed")
                    continue

                # Check if the attribute is in the data dictionary or is a special field
                is_special_field = attr in ["kubernetes_cluster_name", "ecs_cluster_name", "image", "cloud_resource_type", "cloud_provider", "cloud_account_name", "cloud_instance_type", "cloud_instance_lifecycle"]
                if is_special_field or (data_dict and "attributes" in data_dict and attr in data_dict["attributes"]):
                    # Add the attribute to property_to_origins, overriding any existing entry
                    property_to_origins[attr] = ["attribute_list"]
                    print(f"  Added {attr} from attribute_list")
                    # Do NOT mark the attribute as processed immediately
                    # It will be marked as processed after the expression is updated
                else:
                    print(f"  Skipping {attr} as it's not found in the data dictionary")

        # Update entity config for this scope
        preserved_count, new_count, updated_count = update_host_entity_config_for_scope(property_to_origins, scope_metadata, entity_config, data_dict, entity)

        # After updating, mark all processed properties to avoid duplicate processing in other scopes
        # Mark all properties that were processed in this scope, including those from attribute_list
        for prop in property_to_origins.keys():
            if not is_attribute_processed(entity, prop):
                mark_attribute_as_processed(entity, prop, "scope_processing")

        # Fix any nested COALESCE expressions and replace {origin_condition} placeholders before saving
        for section in entity_config.values():
            if isinstance(section, list):
                for mapping in section:
                    if isinstance(mapping, dict) and "colExpr" in mapping:
                        # Fix nested COALESCE expressions
                        if "COALESCE(COALESCE(" in mapping["colExpr"]:
                            print(f"Checking nested COALESCE: {mapping['colExpr']}")
                            mapping["colExpr"] = fix_nested_coalesce(mapping["colExpr"])

                        # Replace {origin_condition} placeholders
                        if "{origin_condition}" in mapping["colExpr"]:
                            prop = mapping["colName"]
                            print(f"Replacing {origin_condition} placeholder in expression for {prop}")
                            # Get the origins for this property
                            origins = property_to_origins.get(prop, [])

                            # Create the origin condition for the case expression
                            if len(origins) == 1:
                                origin_condition = f"array_contains(data_source_subset_name,'{origins[0]}')"
                            else:
                                origin_conditions = [f"array_contains(data_source_subset_name,'{origin}')" for origin in origins]
                                origin_condition = " or ".join(origin_conditions)

                            # Replace the origin_condition placeholder
                            mapping["colExpr"] = mapping["colExpr"].replace("{origin_condition}", origin_condition)
                            print(f"  Fixed to: {mapping['colExpr']}")

        # Save the updated entity config
        with open(entity_config_path_abs, 'w', encoding='utf-8') as f:
            json.dump(entity_config, f, indent=4)

        # Store the results
        results[entity] = (preserved_count, new_count, updated_count)

    return results

def main():
    """
    Main function to update entity configs based on the comprehensive metadata.
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Update entity configs based on metadata.')
    parser.add_argument('--entities', nargs='+', help='Specific entities to process (e.g., Host Application). If not provided, all entities in the metadata will be processed.')
    parser.add_argument('--force', action='store_true', help='Force update all fields, even if they already have expressions')
    parser.add_argument('--scope', help='Process only a specific scope by name (e.g., "Source Specific Properties - Array")')
    args = parser.parse_args()

    print(f"Current working directory: {os.getcwd()}")
    print("Starting comprehensive update process")
    print("Existing fields in the entity configs will be preserved")

    # Load comprehensive metadata
    metadata = load_metadata()

    # Sort scopes so that those with attribute_list or attribute_patterns are processed first
    sorted_scopes = sorted(metadata["scopes"],
                          key=lambda scope: 0 if scope.get("attribute_list") or scope.get("attribute_patterns") else 1)

    print(f"Sorted scopes to process specific scopes first:")
    for i, scope in enumerate(sorted_scopes):
        has_attrs = "Yes" if scope.get("attribute_list") else "No"
        has_patterns = "Yes" if scope.get("attribute_patterns") else "No"
        print(f"  {i+1}. {scope.get('scope_definition')} - Has attribute list: {has_attrs}, Has attribute patterns: {has_patterns}")

    # Reset the PROCESSED_ATTRIBUTES global variable
    global PROCESSED_ATTRIBUTES
    PROCESSED_ATTRIBUTES = {}
    print("Reset processed attributes tracking")

    # Process each scope
    all_results = {}

    # If a specific scope is requested, filter the scopes
    if args.scope:
        print(f"Filtering to only process scope: {args.scope}")
        sorted_scopes = [scope for scope in sorted_scopes if scope.get("scope_definition") == args.scope]
        if not sorted_scopes:
            print(f"No scope found with name: {args.scope}")
            return
        print(f"Found {len(sorted_scopes)} matching scopes")

    for scope_metadata in sorted_scopes:
        # Print the current state of PROCESSED_ATTRIBUTES before processing the scope
        print(f"\nBefore processing scope {scope_metadata.get('scope_definition')}, PROCESSED_ATTRIBUTES state:")
        for entity, attrs in PROCESSED_ATTRIBUTES.items():
            print(f"  {entity}: {', '.join(attrs) if attrs else 'None'}")

        # Process the scope for the specified entities (or all entities if not specified)
        scope_results = process_scope(scope_metadata, args.entities, args.force)

        # Print the updated state of PROCESSED_ATTRIBUTES after processing the scope
        print(f"\nAfter processing scope {scope_metadata.get('scope_definition')}, PROCESSED_ATTRIBUTES state:")
        for entity, attrs in PROCESSED_ATTRIBUTES.items():
            print(f"  {entity}: {', '.join(attrs) if attrs else 'None'}")

        # Merge the results
        for entity, (preserved, new, updated) in scope_results.items():
            if entity not in all_results:
                all_results[entity] = (0, 0, 0)

            # Add the results for this scope to the total for this entity
            prev_preserved, prev_new, prev_updated = all_results[entity]
            all_results[entity] = (prev_preserved + preserved, prev_new + new, prev_updated + updated)

    # Print the summary
    print("\nEntity config update summary:")

    total_preserved = 0
    total_new = 0
    total_updated = 0

    for entity, (preserved, new, updated) in sorted(all_results.items()):
        print(f"\n{entity}:")
        print(f"  Preserved mappings: {preserved}")
        print(f"  Updated mappings: {updated}")
        print(f"  New mappings added: {new}")

        total_preserved += preserved
        total_new += new
        total_updated += updated

    print("\nOverall summary:")
    print(f"Total preserved mappings: {total_preserved}")
    print(f"Total updated mappings: {total_updated}")
    print(f"Total new mappings added: {total_new}")
    print("Entity configs updated successfully!")

if __name__ == "__main__":
    main()