{"id": "entity-data-dictionary", "title": "Entity Data Dictionary", "description": "Entity Data Dictionary", "config": {"Cloud Compute": {"caption": "Cloud Compute", "description": "Cloud Compute entity encompasses both compute resources and its services within the broader cloud computing paradigm.", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "ui_visibility": true, "group": "common"}, "class": {"caption": "Class", "type": "string", "ui_visibility": true, "group": "common"}, "type": {"caption": "Type", "type": "string", "ui_visibility": true, "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "ui_visibility": true, "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "ui_visibility": true, "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "ui_visibility": true, "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recency": {"caption": "Recency", "type": "int", "ui_visibility": true, "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "ui_visibility": true, "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "type": "string", "ui_visibility": false, "group": "common"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "associated_host_count": {"caption": "Count of Corresponding Host", "type": "int", "group": "enrichment"}, "cloud_provider": {"caption": "Cloud Provider", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "account_id": {"caption": "Cloud Account ID", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "region": {"caption": "Cloud Region", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "resource_id": {"caption": "Cloud Resource ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "resource_name": {"caption": "Cloud Resource Name", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "native_type": {"caption": "Cloud Native Type", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "operational_state": {"caption": "Cloud Last Known Operational State", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "environment": {"caption": "Environment", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "provisioning_state": {"caption": "Provisioning State", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "zone_availability": {"caption": "Cloud Zone Availability", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "accessibility": {"caption": "Accessibility", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "os_family": {"caption": "OS Family", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "os": {"caption": "OS", "type": "string", "group": "entity_specific"}, "kubernetes_cluster_name": {"caption": "Kubernetes Cluster Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "kubernetes_version": {"caption": "Kubernetes Version", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "emr_cluster_id": {"caption": "EMR Cluster ID", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "kubernetes_node_group_name": {"caption": "Kubernetes Node Group Name", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "kubernetes_node_pool_vm_count": {"caption": "Kubernetes Node Group VM Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "ecs_cluster_name": {"caption": "ECS Cluster Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "scaling_instance_count": {"caption": "Scaling Instance Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "scaling_instance_type": {"caption": "Scaling Instance Type", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "scaling_instance_ids": {"caption": "Scaling Instance IDs", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "scaling_group_name": {"caption": "Scaling Group Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "ec2fleet_id": {"caption": "EC2 Fleet ID", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "is_ephemeral": {"caption": "Is Ephemeral", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "edr_threat_count": {"caption": "EDR Threat Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "cloud_instance_id": {"caption": "Cloud Instance ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "instance_name": {"caption": "Cloud Instance Name", "type": "string", "group": "entity_specific"}, "cloud_instance_type": {"caption": "Cloud Instance Type", "type": "string", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "cloud_instance_lifecycle": {"caption": "Cloud Instance Lifecycle", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "cloud_instance_security_group_id": {"caption": "Cloud Instance Security Group ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "cloud_instance_network_interface_count": {"caption": "Cloud Instance Network Interface Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "total_disk_size": {"caption": "Total Disk Size", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "aci_container_services_active_containers": {"caption": "ACI Active Containers", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "image": {"caption": "Image", "type": "string", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "private_ip": {"caption": "Private IP", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "public_ip": {"caption": "Public IP", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "private_dns_name": {"caption": "Private DNS Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "public_dns_name": {"caption": "Public DNS Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "active_operational_date": {"caption": "Active Operational Date", "type": "timestamp", "group": "entity_specific"}, "login_last_date": {"caption": "Last Login", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "login_last_user": {"caption": "Login Last User", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "edr_product": {"caption": "EDR Product", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "edr_onboarding_status": {"caption": "EDR Onboarding Status", "type": "boolean", "group": "entity_specific"}, "edr_last_scan_date": {"caption": "EDR <PERSON>", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "av_status": {"caption": "AV Status", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "av_last_scan_date": {"caption": "AV Last Scan Date", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "av_signature_update_date": {"caption": "AV Signature Update Date", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "av_block_malicious_code_status": {"caption": "AV Block Malicious Code Status", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "fw_status": {"caption": "FW Status", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "vm_product": {"caption": "VM Product", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "vm_onboarding_status": {"caption": "VM Onboarding Status", "type": "boolean", "group": "entity_specific"}, "vm_tracking_method": {"caption": "VM Tracking Method", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "vm_last_scan_date": {"caption": "VM <PERSON>", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "purchase_plan_name": {"caption": "Plan Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "properties": {"caption": "Properties", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "entity_specific"}, "billing_tag": {"caption": "Billing Tag", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "is_accessible_from_internet": {"caption": "Internet Exposure", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "has_high_privileges": {"caption": "Has High Privileges", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "has_admin_privileges": {"caption": "<PERSON> <PERSON><PERSON> Privileges", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "open_to_all_internet": {"caption": "Open to All Internet", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "has_sensitive_data": {"caption": "Has Sensitive Info", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "organisational_unit": {"caption": "Organizational Unit", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "project": {"caption": "Project", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "instance_imdsv2_status": {"caption": "Instance Metadata Version", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "wiz_id": {"caption": "Wiz ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_storage_account_type": {"caption": "Azure VM Storage Account Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_disable_password_authentication": {"caption": "Azure VM Password Auth Disabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_vmss_vm_count": {"caption": "Azure VMSS VM Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_vmss_vm_size": {"caption": "Azure VMSS VM Size", "type": "list<string>", "ui_visibility": true, "group": "source_specific"}, "azure_system_data": {"caption": "Azure System Data", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "azure_vm_lifecycle": {"caption": "Azure VM Lifecycle", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_data_disk": {"caption": "Azure VM Data Disk Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_vm_image_id": {"caption": "Azure VM Image ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_managed_disk_id": {"caption": "Azure VM Managed Disk ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_power_state": {"caption": "Azure VM Power State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_tags": {"caption": "Azure Tags", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_vm_network_interface_id": {"caption": "Azure VM Network Interface ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_vm_network_security_group_id": {"caption": "Azure VM Network Security Group ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_public_network_access": {"caption": "Azure Public Network Access", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_power_state": {"caption": "Azure AKS Power State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_dns_prefix": {"caption": "Azure AKS DNS Prefix", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_private_fqdn": {"caption": "Azure AKS Private FQDN", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_pool_profiles": {"caption": "Azure AKS Node Pool Profiles", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "azure_aks_node_pool_vm_sizes": {"caption": "Azure AKS Node Pool VM Sizes", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_pool_type": {"caption": "Azure AKS Node Pool Type", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_pool_node_image_version": {"caption": "Azure AKS Node Pool Node Image Version", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_resource_group": {"caption": "Azure AKS Node Resource Group", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_enable_rbac": {"caption": "Azure AKS Enable RBAC", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_max_node_pools": {"caption": "Azure AKS Max Node Pools", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_pool_count": {"caption": "Azure AKS Node Pool Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_pool_maximum_vm_count": {"caption": "Azure AKS Node Pool max VM Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_aks_node_pool_maximum_pod_count": {"caption": "Azure AKS Node Pool Max Pod Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_aks_image_cleaner_enabled_status": {"caption": "Azure AKS Image Cleaner Enabled Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_auto_upgrade_type": {"caption": "Azure AKS Auto Upgrade Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_storage_snapshot_controller_enabled_status": {"caption": "Azure AKS Snapshot Controller Enabled Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_defender_status": {"caption": "Azure AKS Defender Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_support_plan": {"caption": "Azure AKS Support Plan", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aks_aad_profile_admin_group_object_ids": {"caption": "Azure AKS AAD Profile Admin Group Object ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_aks_azure_rbac_status": {"caption": "Azure AKS Azure RBAC Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_key_vault_secrets_status": {"caption": "Azure AKS Key Vault Secrets Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_azure_policy_status": {"caption": "Azure AKS Azure Policy Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_oms_agent_status": {"caption": "Azure AKS OMS Agent Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_monitor_enabled": {"caption": "Azure AKS Monitor Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aci_instance_state": {"caption": "Azure ACI Instance State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aci_restart_policy": {"caption": "Azure ACI Restart Policy", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_aci_container_services_active_containers_count": {"caption": "Azure ACI Active Containers Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "azure_resource_last_modified_date": {"caption": "Azure Resource Modified", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_plan": {"caption": "Azure Plan", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_region": {"caption": "Azure Region", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_availability_zone": {"caption": "Azure Availability Zone", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_aci_finish_date": {"caption": "Azure ACI Finish Date", "type": "int", "ui_visibility": false, "group": "source_specific"}, "azure_vm_computer_name": {"caption": "Azure VM Computer Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_os_version": {"caption": "Azure VM OS Version", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_os_name": {"caption": "Azure VM OS Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_operational_state": {"caption": "AWS Operational State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_tags": {"caption": "AWS Tags", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "aws_tag": {"caption": "AWS Tag", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_resource_configuration_change_date": {"caption": "AWS Resource Configuration Change Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_instance_image_id": {"caption": "AWS Instance Image ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_private_ip_address": {"caption": "AWS Instance Private IP Address", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_public_ip_address": {"caption": "AWS Instance Public IP Address", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_monitoring_state": {"caption": "AWS Instance Monitoring State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_launch_date": {"caption": "AWS Instance Launch Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_instance_usage_update_time": {"caption": "AWS Instance Usage Update Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_instance_state_update_time": {"caption": "AWS Instance State Update Time", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_lambda_memory_size": {"caption": "AWS Lambda Memory Size", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_lambda_runtime": {"caption": "AWS Lambda Runtime", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_lambda_last_modified_date": {"caption": "AWS Lambda Last Modified", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_lambda_code_size": {"caption": "AWS Lambda Code Size", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_instance_vpc_id": {"caption": "AWS Instance VPC ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_network_interface_id": {"caption": "AWS Instance Network Interface ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_instance_volume_id": {"caption": "AWS Instance Volume ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_instance_subnet_id": {"caption": "AWS Instance Subnet ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_security_group_id": {"caption": "AWS Instance Security Group ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_instance_image_architecture": {"caption": "AWS Instance Image Architecture", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_lifecycle": {"caption": "AWS Instance Lifecycle", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_attach_time": {"caption": "AWS Instance Attach Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_eks_endpoint_private_access": {"caption": "AWS EKS Endpoint Private Access", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_eks_endpoint_public_access": {"caption": "AWS EKS Endpoint Public Access", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_eks_network_ip_family": {"caption": "AWS EKS Network IP Family", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_eks_cluster_security_group_id": {"caption": "AWS EKS Cluster Security Group ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ecs_service_name": {"caption": "AWS ECS Service Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ecs_platform_version": {"caption": "AWS ECS Platform Version", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ecs_scheduling_strategy": {"caption": "AWS ECS Scheduling Strategy", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ecs_assign_public_ip": {"caption": "AWS ECS Assign Public IP", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_autoscaling_instance_max_size": {"caption": "AWS Autoscaling Instance Max Si<PERSON>", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_autoscaling_instance_types": {"caption": "AWS Autoscaling Instance Types", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_autoscaling_instance_count": {"caption": "AWS Autoscaling Instance Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_autoscaling_instance_min_size": {"caption": "AWS Autoscaling Instance Min size", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_valid_from": {"caption": "AWS EC2 Fleet Valid From", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "aws_ec2fleet_valid_until": {"caption": "AWS EC2 Fleet Valid Until", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "aws_ec2fleet_terminate_instances": {"caption": "AWS EC2 Fleet Terminate Instances", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_replace_unhealthy_instances": {"caption": "AWS EC2 Fleet Replace Unhealthy Instances", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_total_target_capacity": {"caption": "AWS EC2 Fleet Total Target Capacity", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_on_demand_target_capacity": {"caption": "AWS EC2 Fleet On Demand Target Capacity", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_spot_target_capacity": {"caption": "AWS EC2 Fleet Spot Target Capacity", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_default_target_capacity_type": {"caption": "AWS EC2 Fleet Default Target Capacity type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_type": {"caption": "AWS EC2 Fleet Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_provisioned_on_demand_capacity": {"caption": "AWS EC2 Fleet Provisioned On-Demand Capacity", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_ec2fleet_provisioned_spot_capacity": {"caption": "AWS EC2 Fleet Provisioned Spot Capacity", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_autoscaling_override_mixed_instance_type": {"caption": "AWS Autoscaling Override Mixed Instance Type", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_region": {"caption": "AWS Region", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_availability_zone": {"caption": "AWS Availability Zone", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_master_security_group_id": {"caption": "AWS EMR Master Security Group", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_slave_security_group_id": {"caption": "AWS EMR Slave Security Group", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_service_security_group_id": {"caption": "AWS EMR Service Security Group", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_instance_collection_type": {"caption": "AWS EMR Instance Collection Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_version": {"caption": "AWS EMR Version", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_auto_terminate": {"caption": "AWS EMR Auto Terminate", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_emr_visible_to_users": {"caption": "AWS EMR Visible to All Users", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_emr_applications": {"caption": "AWS EMR Cluster Applications", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_volume": {"caption": "AWS EMR Cluster Volume", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_emr_cluster_name": {"caption": "AWS EMR Cluster Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_end_date": {"caption": "AWS EMR End Date", "type": "int", "ui_visibility": false, "group": "source_specific"}, "aws_emr_cluster_arn": {"caption": "AWS EMR Cluster ARN", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_emr_instance_fleet_type": {"caption": "AWS EMR Instance Fleet Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "defender_onboarding_date": {"caption": "Defender Onboarding Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "defender_onboarding_status": {"caption": "Defender Onboarding Status", "type": "string", "group": "source_specific"}, "defender_id": {"caption": "Defender ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "defender_detection_method": {"caption": "Defender Detection Method", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aad_device_id": {"caption": "AAD Device ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "defender_action_type": {"caption": "Defender Action Type", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "defender_threat_name": {"caption": "Defender Threat Name", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "defender_threat_count": {"caption": "Defender Infection Count", "type": "int", "ui_visibility": true, "group": "source_specific"}, "defender_exposure_level": {"caption": "Defender Exposure level", "type": "string", "ui_visibility": true, "group": "source_specific"}, "qualys_detection_method": {"caption": "Qualys Detection Method", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_configuration_id": {"caption": "AWS Sagemaker Configuration ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_notebook_instance_name": {"caption": "AWS Sagemaker Notebook Instance Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_notebook_instance_storage_size": {"caption": "AWS Sagemaker Notebook Instance Storage Size", "type": "float", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_default_code_repository": {"caption": "AWS Sagemaker Default Code Repository", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_direct_internet_access_status": {"caption": "AWS Sagemaker Direct Internet Access Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_role_arn": {"caption": "AWS Sagemaker Role ARN", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_root_access_status": {"caption": "AWS Sagemaker Root Access Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_lifecycle_config_name": {"caption": "AWS Sagemaker Lifecycle Configuration Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_name": {"caption": "AWS Sagemaker Model Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_network_isolation_status": {"caption": "AWS Sagemaker Network Isolation Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_execution_role_arn": {"caption": "AWS Sagemaker Execution Role ARN", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_primary_container_image": {"caption": "AWS Sagemaker Model Primary Container Image", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_primary_container_mode": {"caption": "AWS Sagemaker Model Mode", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_data_url": {"caption": "AWS Sagemaker Model Data URL", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_environment_config": {"caption": "AWS Sagemaker Model Environment Configuration", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_available_gpu": {"caption": "AWS Sagemaker Model Available GPU", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_cache_root": {"caption": "AWS Sagemaker Model Cache Directory Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_sagemaker_model_datasource_s3_uri": {"caption": "AWS Sagemaker Model Datasource S3 URI", "type": "string", "ui_visibility": true, "group": "source_specific"}, "wiz_operational_state": {"caption": "Wiz Operational State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "wiz_onboarding_date": {"caption": "Wiz Onboarding Date", "type": "timestamp", "group": "source_specific"}, "wiz_is_cloud_managed": {"caption": "Is Managed by Cloud As Per Wiz", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "wiz_last_scan_date": {"caption": "Wiz Last Scan Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "wiz_modified_date": {"caption": "Wiz Modified Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "wiz_onboarding_status": {"caption": "Wiz Onboarding Status", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "type": "int", "group": "enrichment"}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "tenable_io_onboarding_date": {"caption": "Tenable.io Onboarding Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_asset_updated_at": {"caption": "Tenable.io <PERSON> Updated Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_last_scan_date": {"caption": "Tenable.io <PERSON> Last Scan Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_last_authenticated_scan_date": {"caption": "Tenable.io Last Autheticated Scan Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_asset_aws_terminated_date": {"caption": "Tenable.io AWS Terminated Date", "type": "timestamp", "group": "source_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Cloud Compute": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "cloud_compute"}, "Cloud Storage": {"caption": "Cloud Storage", "description": "Cloud storage is a service where data is stored and managed remotely on internet servers, rather than on local hardware.", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "ui_visibility": true, "group": "common"}, "class": {"caption": "Class", "type": "string", "ui_visibility": true, "group": "common"}, "type": {"caption": "Type", "type": "string", "ui_visibility": true, "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "ui_visibility": true, "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "ui_visibility": true, "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "ui_visibility": true, "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recency": {"caption": "Recency", "type": "int", "ui_visibility": true, "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "ui_visibility": true, "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "type": "string", "ui_visibility": false, "group": "common"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "cloud_provider": {"caption": "Cloud Provider", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "account_id": {"caption": "Cloud Account ID", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "region": {"caption": "Cloud Region", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "kubernetes_cluster_name": {"caption": "Kubernetes Cluster Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "cloud_instance_id": {"caption": "Cloud Instance ID", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "resource_id": {"caption": "Cloud Resource Id", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "resource_name": {"caption": "Cloud Resource Name", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "native_type": {"caption": "Cloud Native Type", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "operational_state": {"caption": "Cloud Last Known Operational State", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "environment": {"caption": "Environment", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "provisioning_state": {"caption": "Provisioning State", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "encryption_at_rest": {"caption": "Encryption at Rest", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "encryption_in_transit": {"caption": "Encryption in Transit", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "zone_availability": {"caption": "Cloud Zone Availability", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "active_operational_date": {"caption": "Active Operational Date", "type": "timestamp", "group": "entity_specific"}, "billing_tag": {"caption": "Billing Tag", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "volume_name": {"caption": "Volume Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "bucket_name": {"caption": "Bucket Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "file_system_service_name": {"caption": "File System Service Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "storage_account_name": {"caption": "Storage Account Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "storage_account_access_tier": {"caption": "Storage Account Access Tier", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "is_sftp_enabled": {"caption": "Is SFTP Enabled", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "is_nfs_v3_enabled": {"caption": "Is NFS V3 Enabled", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "infrastructure_encryption_applied": {"caption": "Infrastructure Encryption Applied", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "public_network_access": {"caption": "Public Network Access", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "volume_pvc_namespace": {"caption": "Volume PVC Namespace", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "volume_pvc_name": {"caption": "Volume PVC Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "volume_pv_name": {"caption": "Volume PV Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "volume_size": {"caption": "Volume Size", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "volume_iops": {"caption": "Volume IOPS", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "volume_type": {"caption": "Volume Type", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "volume_throughput": {"caption": "Volume Throughput", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "allow_blob_public_access": {"caption": "Allow Blob Public Access", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "capacity": {"caption": "Capacity", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "lease_status": {"caption": "Lease Status", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "properties": {"caption": "Properties", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "project": {"caption": "Project", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_tags": {"caption": "AWS Tags", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_attach_date": {"caption": "AWS EBS Attach Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_size": {"caption": "AWS EBS Size", "type": "int", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_attachment_state": {"caption": "AWS EBS Attachment State", "type": "list<string>", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_state": {"caption": "AWS EBS State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_kms_key_id": {"caption": "AWS KMS Key ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_snapshot_id": {"caption": "AWS EBS Snapshot ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_instance_id": {"caption": "AWS Instance ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_multi_attached_instances": {"caption": "AWS Multi Attached Instances", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_multi_attach_enabled": {"caption": "AWS EBS Multi Attach Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_delete_on_termination": {"caption": "AWS EBS Delete On Termination", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_availability_zone": {"caption": "AWS Availability Zone", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_region": {"caption": "AWS Region", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ebs_csi_name": {"caption": "AWS EBS Container Storage Interface", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_efs_throughput_mode": {"caption": "AWS EFS Throughput Mode", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_efs_performance_mode": {"caption": "AWS EFS Performance Mode", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_efs_transition_to_ia": {"caption": "AWS EFS Transition To IA", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_efs_backup_policy_status": {"caption": "AWS EFS Backup Policy Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_s3_owner_id": {"caption": "AWS S3 Owner ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_s3_access_control_list": {"caption": "AWS S3 Access Control List", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "aws_s3_block_public_acls": {"caption": "AWS S3 Block Public ACLs", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_s3_ignore_public_acls": {"caption": "AWS S3 Ignore Public ACLs", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_s3_block_public_policy": {"caption": "AWS S3 Block Public Policy", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_s3_restrict_public_buckets": {"caption": "AWS S3 Restrict Public Buckets", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_s3_log_bucket_name": {"caption": "AWS S3 Log Bucket Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_s3_bucket_policy_text": {"caption": "AWS S3 Bucket Policy Text", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "aws_s3_is_mfa_delete_enabled": {"caption": "AWS S3 Is MFA Delete Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_s3_bucket_versioning_status": {"caption": "AWS S3 Bucket Versioning Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_s3_server_side_encryption": {"caption": "AWS S3 Encryption Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_s3_server_side_encryption_kms_master_key": {"caption": "AWS S3 SSE KMS Master Key", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_s3_bucket_encryption_key_enabled": {"caption": "AWS S3 SSE Key Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_s3_requester_charged": {"caption": "AWS S3 Requester Pays", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_resource_configuration_change_date": {"caption": "AWS Resource Configuration Change Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_availability_zone": {"caption": "Azure Availability Zone", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_resource_last_modified_date": {"caption": "Azure Resource Modified Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_region": {"caption": "Azure Region", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_resource_group": {"caption": "Azure Resource Group", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_ou": {"caption": "Azure OU", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_tags": {"caption": "Azure Tags", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_network_policy_access": {"caption": "Azure Network Policy Access", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_encryption_type": {"caption": "Azure Disk Encryption Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_image_id": {"caption": "Azure Disk Image ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_create_option": {"caption": "Azure Disk Create Option", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_state": {"caption": "Azure Disk State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_last_ownership_update": {"caption": "Azure Disk Last Ownership Change", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_disk_os_type": {"caption": "Azure Disk OS Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_unique_id": {"caption": "Azure Disk Unique ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_tier": {"caption": "Azure Disk Tier", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_security_type": {"caption": "Azure Disk Security Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_disk_supports_hibernation": {"caption": "Azure Disk Hibernation Support", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_disk_controller_types": {"caption": "Azure Disk Controller Types", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_id": {"caption": "Azure Virtual Machine ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vmss_name": {"caption": "Azure Virtual Machine Scaleset Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_name": {"caption": "Azure Virtual Machine Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_remaining_retention_days": {"caption": "Azure Remaining Retention Days", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_version_level_immutability_support": {"caption": "Azure Version Level Immutability Support", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_bsc_default_encryption_scope": {"caption": "Azure Blob Default Encryption Scope", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_bsc_deny_encryption_scope_override": {"caption": "Azure Blob Deny Encryption Scope Override", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_bsc_public_access": {"caption": "Azure Blob Public Access", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_lease_state": {"caption": "Azure Lease State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_bsc_has_immutability_policy": {"caption": "Azure Blob Has Immutability Policy", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_bsc_has_legal_hold": {"caption": "Azure Blob Has Legal Hold", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_deleted": {"caption": "Azure Is Deleted", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_deleted_date": {"caption": "Azure Deleted Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_id": {"caption": "Azure Storage Account ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_file_share_access_tier": {"caption": "Azure File Share Access Tier", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_file_share_access_tier_change_date": {"caption": "Azure File Share Access Tier Change Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_file_share_quota": {"caption": "Azure File Share Quota", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_file_share_enabled_protocols": {"caption": "Azure File Share Enabled Protocols", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_encryption_key_source": {"caption": "Azure Encryption Key Source", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_blob_encryption_enabled": {"caption": "Azure Blob Encryption Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_blob_encryption_enabled_date": {"caption": "Azure Blob Encryption Enabled Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_blob_encryption_key_type": {"caption": "Azure Blob Encryption Key Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_file_encryption_enabled": {"caption": "Azure File Encryption Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_file_encryption_enabled_date": {"caption": "Azure File Encryption Enabled Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_file_encryption_key_type": {"caption": "Azure File Encryption Key Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_supports_http_traffic_only": {"caption": "Azure Supports HTTP Traffic Only", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_minimum_tls_version": {"caption": "Azure Minimum TLS Version", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_primary_location": {"caption": "Azure Storage Account Primary Location", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_secondary_location": {"caption": "Azure Storage Account Secondary Location", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_key1_creation_date": {"caption": "Azure Storage Account Key1 Creation", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_key2_creation_date": {"caption": "Azure Storage Account Key2 Creation", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_allow_cross_tenant_replication": {"caption": "Azure Allow Cross Tenant Replication", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_default_o_auth": {"caption": "Azure De<PERSON>ult <PERSON>", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_allow_shared_key": {"caption": "Azure Storage Account Allow Shared Key", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_is_hns_enabled": {"caption": "Azure Is HNS Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_large_file_shares_state": {"caption": "Azure Large File Shares State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_identity_type": {"caption": "Azure Identity Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_type": {"caption": "Azure Storage Account Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_storage_account_kind": {"caption": "Azure Storage Account Kind", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_geo_replication_last_sync": {"caption": "Azure Geo Replication Last Sync", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_network_acls_bypass": {"caption": "Azure Network ACLs Bypass", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_dns_endpoint_type": {"caption": "Azure DNS Endpoint Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Cloud Storage": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "cloud_storage"}, "Person": {"caption": "Person", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "navigator_enabled": true, "navigator_description": "\n# Person Table Summary\n\n## Overview\nThe Person table provides a comprehensive repository of all individual personnel associated with the organization, tracking employees, contractors, and other human resources across their entire lifecycle. It integrates data from HR systems, identity platforms, and access management solutions to create a unified view of personnel, their attributes, relationships, and inherent risk factors.\n\n## Data Categories\n\n### Personnel Identification\n- **Personal Details**: Full names, email addresses, phone numbers, physical addresses\n- **Employee Information**: Employee IDs, job titles, job functions, position IDs\n- **Organizational Attribution**: Company affiliation, legal entity, cost center\n- **Contact Information**: Corporate and external email addresses, phone numbers\n\n### Employment Context\n- **Employment Classification**: Types (Permanent, Contractor, Intern, Fixed Term)\n- **Employment Status**: Active or terminated state\n- **Hierarchical Position**: Employee levels (L1-L8) and organizational structure\n- **Job Details**: Titles, functions, positions, and organizational units\n\n### Professional Relationships\n- **Management Structure**: Direct manager relationships\n- **System Relationships**: Hosts owned, identities associated\n- **Organizational Placement**: Department, business unit, cost center\n\n### Lifecycle Information\n- **Employment Timeline**: Recruitment dates, contract end dates, termination dates\n- **Activity Tracking**: First seen, last active, last login dates\n- **Status Assessment**: Current activity status, employment status\n- **Duration Metrics**: Lifetime and observed lifetime calculations\n\n### Geographic Context\n- **Location Information**: Country and city placement\n- **Regional Association**: Physical or assigned location\n\n### Risk Assessment\n- **Employee Type Risk**: Risk evaluation based on employment classification\n- **Employee Level Risk**: Risk stratification based on organizational level\n- **Status-Based Risk**: Evaluation based on active vs. terminated status\n- **Identity Complexity Risk**: Assessment based on number of associated identities\n- **Composite Risk Scoring**: Consolidated person_inherent_score based on multiple factors\n\n### Authentication & Access\n- **Login Information**: Last login dates, password usage\n- **Identity Associations**: Linked identity counts and relationships\n- **System Access**: Related systems and platforms\n\n### Data Provenance\n- **Source Systems**: Original data sources (BambooHR, SuccessFactors, Azure AD, etc.)\n- **Data Integration**: Fragment counts, source attribution\n- **Origin Classification**: Unique vs. corroborated data points\n- **Data Verification**: Data source subset names and API feeds\n\n## Key Features\n\n1. **Personnel Lifecycle Management**: Complete tracking from recruitment through termination\n\n2. **Multi-Source Integration**: Consolidation of data from HR, identity, and access management systems\n\n3. **Organizational Context**: Detailed mapping of departmental and hierarchical relationships\n\n4. **Risk-Based Personnel Assessment**: Multi-dimensional risk scoring for personnel prioritization\n\n5. **Relationship Mapping**: Connections to hosts, identities, and systems\n\n6. **Employment Classification Insights**: Detailed categorization by employment type and status\n\n7. **Temporal Tracking**: Historical visibility of personnel activity and status changes\n\nThis Person table serves as a critical foundation for personnel risk assessment, access governance, and identifying potential insider threats. It enables security teams to prioritize attention based on employee levels, employment status, identity complexity, and other risk factors.\n", "navigator_graph_node_description": "The Person entity represents an individual within an organization, focusing on their employment details, role, organizational relationships, and asset ownership. It is essential for HR, compliance, security, and asset management.Key attributes include personal identification (Employee ID, Name, Email), job details (title, department, business unit), employment status (hire date, termination date), and reporting structure (manager details).It tracks ownership of hosts and assets, system interactions, and risk factors based on role sensitivity. Data comes from HR systems, directory services, and asset management tools. This entity ensures personnel tracking without overlapping with identity authentication.", "navigator_entity_description": "A Person in cybersecurity represents an individual within an organization, focusing on their employment, role, organizational relationships, and asset ownership. The Person entity is primarily relevant to HR, compliance, organizational security, and asset management, rather than technical authentication or system-level access control.\\n\\n    The Person entity tracks an individual's employment-related attributes, including their job function, department, and interactions with enterprise assets, ensuring comprehensive oversight of personnel within the organization.\\n    \\n    A Person entity does NOT encompass system authentication, access permissions, or authorization processes.\\n    \\n    Key Attributes Defining a Person:\\n    Personal Identification:\\n    A Person is uniquely identified through HR and administrative attributes that distinguish them within the organization.\\n    \\n    Examples: employee_id, full_name, email, workday_id, payroll_number, contact_number.\\n    Not Included: System-based access identifiers such as aad_object_id, sam_account_name, oauth_client_id.\\n    Employment & Organizational Context:\\n    Attributes related to an individual's role and position within the company, such as:\\n    \\n    Job-related details: job_title, employment_type (permanent/contract), department, business_unit, cost_center.\\n    Hierarchy: Reporting relationships (e.g., manager details), organizational affiliations, and cost center allocations.\\n    Not Included: Login credentials, multi-factor authentication (MFA) settings, or sign-in activity.\\n    Lifecycle Events:\\n    Tracks employment status and key dates:\\n    \\n    Relevant attributes: hire_date, employment_status (active/terminated), termination_date, first_seen_date.\\n    Not Included: Authentication timestamps, last login activity, or system-based expiration policies.\\n    Asset Ownership & Interaction:\\n    A Person is responsible for assets such as:\\n    \\n    Assigned devices, workstations, and company-issued resources.\\n    Direct linkage to organizational resources and ownership accountability.\\n    Not Included: Access roles or permissions to digital resources.\\n    System Interaction (Non-Technical):\\n    Tracks personnel interactions from an organizational perspective, including:\\n    \\n    Engagement with company systems at a high level (e.g., policy compliance, mandatory training).\\n    Not Included: Authentication logs, session history, or security event monitoring.\\n    Risk Profiling:\\n    Risk is assessed based on:\\n    \\n    Job role sensitivity, access to confidential business data, and potential insider threats.\\n    Not Included: Security posture, identity risk scores, or anomalous login behavior.\\n    Data Sources:\\n    A Personâ€™s information is sourced from HR and administrative systems such as:\\n    \\n    HRIS (Workday, SAP), directory services (Azure AD profile, LDAP), and asset management systems.\\n    Not Included: IAM platforms like Azure AD or AWS IAM focusing on authentication and authorization.\\n    Common Ways a Person Is Referred To:\\n    By laypeople: \\'employee,\\' \\'staff member,\\' \\'team member,\\' \\'users.\\'\\n    By HR teams: \\'employee profile,\\' \\'personnel record.\\'\\n    By IT teams: \\'user account,\\' \\'personnel record.\\'\\n    By security teams: \\'user entity,\\' \\'individual identity profile.\\'\\n    Key Differentiation from Identity:\\n    A Person focuses on employment, role, and organizational context, while Identity focuses solely on system access, authentication, and security aspects. Queries related to the following topics should be classified under Person, NOT Identity:\\n    \\n    Employment-related queries: Job title, department, employment status, and historical employment records.\\n    Asset ownership: Devices assigned to individuals, their responsibility over resources.\\n    Organizational structure: Reporting lines, department affiliation, and business units.\\n    Work history: Tracking former employees and their past interactions.\\n    Compliance and HR tracking: Employee onboarding/offboarding, adherence to security policies.\\n    \\n\\n    To prevent confusion with Identity, the following aspects are NOT part of the Person entity:\\n    \\n    Authentication and authorization: MFA setup, login credentials, access roles, permissions.\\n    Sign-in behavior: Last login timestamps, account lockout events, anomalous sign-in detection.\\n    IAM-related queries: Azure AD, AWS IAM, and related access management platforms.\\n    Device vulnerabilities and host-based security assessments.\\n    IMPORTANT:Active users in a directory service mean Person entity\\n\\n    Additionally,Person table contains below details for each Person/Employee/Owner/Indivitual\\n    - **owns_host_count (without filter,becase there is so many categories this only give sum of all)**: The count of hosts/machines/devices associated/owned by a Person/Employee/Owner/Indivitual,this field will give how many hosts/machines/devices are associated with a Person/Employee/Owner/Indivitual\\n    - **has_identity_count (without filter,becase there is so many categories like active,inavtive this only give sum of all)**: The number of identities associated with a Person/Employee/Owner/Indivitual, this field will give how many identities are associated with a Person/Employee/Owner/Indivitual\\n    - if user asking about max/high/largest min/least/lower or filter like less than greater than of above (host count/identity count of person) return Person only, dont take above additional attribute for get total count of owners/identities because redundancy\\n        - When a user queries for maximum, minimum, highest, lowest, or filtered values (e.g., greater than or less than) related to the above quantitative attributes, results should focus only on the Person entity without including other entities for that specific part of the query. However, for other aspects of the query, additional entities may be included as needed. \\n        - The above quantitative attributes should not be used when calculating the total count of hosts or identities to avoid redundancy and we dont have *sum* operation support", "navigator_examples": ["User Query: 'Show all data analysts' Output: 'person'", "User Query: 'Find all terminated users' Output: 'person'"], "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "has_identity_count": {"caption": "Count of Identities", "type": "int", "group": "enrichment"}, "owns_host_count": {"caption": "Count of Owns Host", "type": "int", "group": "enrichment"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "full_name": {"caption": "Full Name", "type": "string", "group": "entity_specific"}, "first_name": {"caption": "First Name", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "middle_name": {"caption": "Middle Name", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "last_name": {"caption": "Last Name", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "company": {"caption": "Company", "type": "string", "group": "entity_specific"}, "employee_id": {"caption": "Employee ID", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "employee_status": {"caption": "Employee Status", "type": "string", "group": "entity_specific"}, "employee_level": {"caption": "Employee Level", "type": "string", "group": "entity_specific"}, "employment_type": {"caption": "Employment Type", "type": "string", "group": "entity_specific"}, "email_id": {"caption": "Email ID", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "manager": {"caption": "Manager", "type": "string", "group": "entity_specific"}, "manager_id": {"caption": "Manager ID", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "recruit_date": {"caption": "Recruited On", "type": "timestamp", "group": "entity_specific"}, "last_known_termination_date": {"caption": "Last Known Termination Date", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "contract_end_date": {"caption": "Contract End", "type": "timestamp", "group": "entity_specific"}, "job_title": {"caption": "Job Title", "type": "string", "group": "entity_specific"}, "job_position_id": {"caption": "Job Position ID", "type": "string", "group": "entity_specific"}, "job_function": {"caption": "Job Function", "type": "string", "group": "entity_specific"}, "legal_entity": {"caption": "Legal Entity", "type": "string", "group": "entity_specific"}, "organisation_unit_id": {"caption": "Organisation Unit ID", "type": "string", "group": "entity_specific"}, "cost_center": {"caption": "Cost Center", "type": "string", "group": "entity_specific"}, "login_last_date": {"caption": "Last Login", "type": "timestamp", "group": "entity_specific"}, "termination_date": {"caption": "Terminated On", "type": "timestamp", "group": "entity_specific"}, "external_email_id": {"caption": "External Email ID", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "phone_number": {"caption": "Phone Number", "type": "string", "group": "entity_specific"}, "address": {"caption": "Address", "type": "string", "group": "entity_specific"}, "password_last_used": {"caption": "Last Password Used Date", "type": "timestamp", "group": "entity_specific"}, "ad_operational_status": {"caption": "AD Operational Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "ad_created_date": {"caption": "AD Created", "type": "timestamp", "group": "source_specific"}, "ad_last_sync_date": {"caption": "AD Last Sync date", "type": "timestamp", "group": "source_specific"}, "ad_last_password_change_date": {"caption": "AD Last Password Change Date", "type": "timestamp", "group": "source_specific"}, "earliest_ad_account_disabled_date": {"caption": "Earliest AD Account Disabled Date", "type": "int", "ui_visibility": false, "group": "source_specific"}, "ad_account_disabled_date": {"caption": "AD Account Disabled Date", "type": "timestamp", "group": "source_specific"}, "account_enabled_status": {"caption": "Account Enabled Status", "type": "string", "ui_visibility": false, "group": "source_specific"}, "aad_operational_status": {"caption": "AAD Operational Status", "type": "string", "ui_visibility": false, "group": "source_specific"}, "aad_created_date": {"caption": "AAD Created", "type": "timestamp", "group": "source_specific"}, "aad_deleted_date": {"caption": "AAD Deleted Date", "type": "timestamp", "group": "source_specific"}, "aad_user_id": {"caption": "AAD User ID", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "aws_created_date": {"caption": "AWS Created Date", "type": "timestamp", "group": "source_specific"}, "aws_iam_center_user_id": {"caption": "AWS IAM Center User Id", "type": "string", "ui_visibility": false, "group": "source_specific"}, "sf_employee_status": {"caption": "SF Employee Status", "type": "string", "group": "source_specific"}, "project": {"caption": "Project", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Person": {}, "IDRA Risk Index": {}, "CCM": {}, "IDRA": {}}, "is_available_in_datalake": true, "graph_reference_name": "person"}, "Application": {"caption": "Application", "description": "Software program designed to perform specific tasks or provide certain functionalities on electronic devices such as computers, smartphones, or tablets. It serves as an intermediary between users and computer systems, facilitating interaction, data processing, and problem-solving.", "navigator_enabled": true, "navigator_description": "\n# Application Table Summary\n\n## Overview\nThe Application table serves as a central repository for tracking and managing all software applications across an organization's environment. It provides a unified view of systems, tools, software, databases, and applications with comprehensive details about their configuration, operational status, and security posture.\n\n## Data Categories\n\n### Identity & Classification\n- **Application Names & Identifiers**: Various name formats and unique identifiers (p_id, display_label)\n- **Type Classification**: Different classifications based on functionality (Software, Tool, Application, System, Database)\n- **Category**: Classifications based on origin, development approach, and licensing model (Opensource, Off The Shelf, Off The Shelf Customised)\n\n### Vendor & Version Information\n- **Vendor Details**: Information about the application providers and vendors\n- **Version Information**: Version numbers and release details\n- **Lifecycle Status**: Application lifecycle stages (Mainstream, Retired)\n\n### Operational Context\n- **Internet Exposure**: Whether the application is internet-facing\n- **Deployment Scope**: Internal vs. external accessibility\n- **Operational Status**: Current state of the application (Active, Disabled)\n\n### Security Posture\n- **Risk Categorization**: Internal classification of risk (CAT 1-4)\n- **Criticality Assessment**: Derived criticality indicators\n- **Sensitivity Status**: Whether the application contains sensitive information\n- **Vulnerability Metrics**: Count of vulnerabilities (total and open) associated with the application\n\n### Activity & Lifecycle\n- **Temporal Information**: First seen, last found, and last active dates\n- **Activity Status**: Current operational state (Active/Inactive)\n- **Retirement Details**: When applicable, retirement dates for decommissioned applications\n- **Lifecycle Metrics**: Lifetime duration and observed timeline\n\n### Relationship Mapping\n- **Host Relationships**: Number of hosts running the application\n- **Vulnerability Context**: Associated vulnerability findings (total and open)\n\n### Data Provenance\n- **Source Attribution**: Origin systems contributing data (MS Defender, Mega)\n- **Corroboration Information**: Whether data points are unique or corroborated across sources\n- **Source Count**: Number of data sources from which the entity has been extracted\n\n### Business Context\n- **Organizational Placement**: Business unit and department assignments\n\n## Key Features\n\n1. **Comprehensive Application Catalog**: Consolidates data about all software applications regardless of type or purpose\n\n2. **Security Risk Assessment**: Provides categorization of applications based on risk levels and criticality\n\n3. **Operational Status Tracking**: Monitors the active status and lifecycle stage of applications\n\n4. **Vulnerability Context**: Tracks vulnerability findings associated with applications\n\n5. **Deployment Scope Identification**: Distinguishes between internal and internet-facing applications\n\n6. **Temporal Tracking**: Maintains historical timeline of application lifecycle and activity\n\n7. **Source Verification**: Tracks data origin and corroboration across multiple sources\n\n8. **Vendor and Version Management**: Catalogs vendor information and version details for software asset management\n\nThe Application table functions as a critical component of the security analytics platform, providing essential software context needed for vulnerability management, security risk assessment, and application lifecycle management.\nIMPORTANT: Query: Show applications with critical vulnerability, filter for host will be '' because there is no filter related to application in the query.\nIMPORTANT:If the query asks for anyting related to any software,that filter should go to Application.For example:query:Show all openssl softwares,filter for application will be 'openssl'.", "navigator_graph_node_description": "The Application entity  represents software systems within an organization's technology landscape. It captures essential attributes including unique identifiers (p_id), display labels, classification types (Software, Tool, Application, System, Database), and data source origins.The schema tracks temporal aspects through timestamps (first_seen_date, last_found_date), activity status, and lifecycle stages (Mainstream, Retired). Application-specific attributes include app_name, vendor information, version details, and categorization (Opensource, Off The Shelf).Security aspects are covered through risk categorization (CAT1-4), internet exposure flags, criticality indicators, and sensitive information markers. Enrichment attributes establish relationships with hosts and vulnerability findings, providing context for security assessments.This schema enables comprehensive application inventory management, risk evaluation, and security monitoring across the organization's software ecosystem.", "navigator_entity_description": "\nThe Application Entity represents software systems, tools, databases, and applications within an organization's technology landscape. It serves as a comprehensive inventory of all software assets, providing visibility into the application ecosystem to support security posture, risk assessment, compliance tracking, and lifecycle management.\n\n## Key Attributes and Characteristics\n\n### Identification and Classification\n- **Unique identifiers** (p_id) and display labels for each application entity\n- **Type classification** categories including System, Tool, Application, Software, and Database\n- **Origin information** tracking data sources from which the application was discovered (e.g., MS Defender, Mega)\n- **Application name and vendor details** providing specific identification information\n\n### Lifecycle Management\n- **Temporal tracking** through timestamps for first seen, first found, last found, and last active dates\n- **Activity status indicators** showing whether applications are Active or Inactive\n- **Lifecycle stage classification** (Mainstream, Retired) with retirement dates when applicable\n- **Observed lifetime** measuring the duration an application has been present in data sources\n\n### Risk and Security Profiling\n- **Risk categorization** (CAT 1-4) indicating different risk levels associated with applications\n- **Internet exposure status** flagging whether applications are internet-facing\n- **Derived criticality markers** highlighting applications of heightened importance\n- **Sensitive information indicators** noting applications that process or store sensitive data\n- **Vulnerability association** tracking the number of vulnerabilities and open findings\n\n### Categorization and Context\n- **Application categories** classifying by origin, development approach, and licensing model:\n  - Opensource\n  - Off The Shelf\n  - Off The Shelf Customized\n- **Version information** tracking specific software versions and updates\n- **Department association** linking applications to business functions and organizational structure\n\n### Relationship Management\n- **Host relationships** tracking the number of hosts running each application\n- **Vulnerability exposure** monitoring vulnerability findings associated with applications\n- **Origin contribution type** distinguishing between unique and corroborated data sources\n\n## Purpose and Importance\n\nThe Application Entity serves multiple critical functions within an organization:\n\n1. **Asset Inventory Management**: Provides a comprehensive catalog of all software assets for visibility and governance\n2. **Security Risk Assessment**: Enables evaluation of application security posture through vulnerability tracking and risk categorization\n3. **Compliance Monitoring**: Supports regulatory adherence by identifying applications handling sensitive information\n4. **Lifecycle Oversight**: Facilitates software lifecycle management from deployment to retirement\n5. **Vulnerability Management**: Links applications to security vulnerabilities for targeted remediation planning\n6. **Technology Portfolio Analysis**: Offers insights into application diversity, redundancy, and deployment patterns\n7. **Software Supply Chain Visibility**: Tracks vendor relationships and software origins for supply chain security\n8. **Resource Optimization**: Identifies inactive applications for potential decommissioning to reduce overhead\n\nThe Application Entity forms a cornerstone of technology asset management, enabling organizations to maintain awareness of their software landscape, manage associated risks effectively, and make informed decisions about application deployment, security, and retirement strategies.\nIMPORTANT:ANY SOFTWARE NAME IN A QUERY REFERS TO THE APPLICATION ENTITY/TABLE", "navigator_examples": ["User Query: 'Show vulnerable applications' Output: 'application'"], "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "running_on_host_count": {"caption": "Count of Host Hosting Application", "type": "int", "group": "enrichment"}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "type": "int", "group": "enrichment"}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "app_name": {"caption": "Application Name", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "app_vendor": {"caption": "Application Vendor", "type": "string", "group": "entity_specific"}, "app_version": {"caption": "Application Version", "type": "string", "group": "entity_specific"}, "category": {"caption": "License", "type": "string", "group": "entity_specific"}, "risk_category": {"caption": "Risk Category", "type": "string", "group": "entity_specific"}, "criticality": {"caption": "Mega Application Criticality", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "operational_status": {"caption": "Application Operational Status", "type": "string", "group": "entity_specific"}, "lifecycle": {"caption": "Application Lifecycle", "type": "string", "group": "entity_specific"}, "derived_criticality": {"caption": "Application Criticality", "type": "string", "group": "entity_specific"}, "internet_facing": {"caption": "Internet Facing", "type": "boolean", "group": "entity_specific"}, "retired_date": {"caption": "Retired Date", "type": "timestamp", "group": "entity_specific"}, "sensitive_information": {"caption": "Sensitive Information", "type": "boolean", "group": "entity_specific"}, "app_first_seen": {"caption": "App First Seen", "type": "int", "ui_visibility": false, "group": "source_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Application": {}}, "is_available_in_datalake": true, "graph_reference_name": "application"}, "Cloud Account": {"caption": "Cloud Account", "description": "A Cloud Account is a user's gateway to accessing and managing cloud computing services or any resources offered by a cloud provider.", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "ui_visibility": true, "group": "common"}, "class": {"caption": "Class", "type": "string", "ui_visibility": true, "group": "common"}, "type": {"caption": "Type", "type": "string", "ui_visibility": true, "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "ui_visibility": true, "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "ui_visibility": true, "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "ui_visibility": true, "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recency": {"caption": "Recency", "type": "int", "ui_visibility": true, "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "ui_visibility": true, "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "type": "string", "ui_visibility": false, "group": "common"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "account_id": {"caption": "Cloud Account ID", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "account_name": {"caption": "Account Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "cloud_provider": {"caption": "Cloud Provider", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "account_status": {"caption": "Account Status", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "aws_account_arn": {"caption": "AWS Arn", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_account_email": {"caption": "AWS Account Email", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_account_status": {"caption": "AWS Account Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_account_joined_method": {"caption": "AWS Account Joined Method", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_account_joined_timestamp": {"caption": "AWS Account Joined Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_authorization_source": {"caption": "AWS Account Authorization Source", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_managed_by_tenants": {"caption": "Azure Managed By Tenants", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_state": {"caption": "Azure Subscription State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_policies": {"caption": "Azure Subscription Policies", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_location_placement_id": {"caption": "Azure Subscription Location Placement ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_quota_id": {"caption": "Azure Subscription Quota ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_spending_limit": {"caption": "Azure Subscription Spending Limit", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_tags": {"caption": "Azure Subscription Tags", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_subscription_tenantId": {"caption": "Azure Tenant ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Cloud Account": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "cloud_account"}, "Finding": {"caption": "Finding", "description": "A finding is a specific piece of information that indicates a potential security issue or anomaly within an organization's IT infrastructure, systems, applications, processes or operations.", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "ui_visibility": true, "group": "common"}, "finding_description": {"caption": "Finding Description", "type": "list<string>", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "assessment_id": {"caption": "Assessment ID", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "assessment_title": {"caption": "Assessment Title", "type": "string", "group": "common"}, "finding_title": {"caption": "Finding Title", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "exposure_weightage": {"caption": "Exposure Weightage", "type": "float", "ui_visibility": true, "group": "entity_specific"}, "exposure_severity": {"caption": "Exposure Severity", "type": "string", "ui_visibility": true, "group": "enrichment"}, "affected_asset_origin": {"caption": "Affected Asset Origin", "type": "list<string>", "ui_visibility": false, "group": "entity_specific"}, "affected_asset": {"caption": "Associated Entities", "type": "string", "ui_visibility": false, "group": "enrichment"}, "affected_asset_display_label": {"caption": "Associated Entities Display Label", "type": "list<string>", "ui_visibility": true, "group": "enrichment"}, "status": {"caption": "Finding Status", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "failure_reason": {"caption": "Finding Failure Reason", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "reopened_date": {"caption": "Finding Reopened Date", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "criticality": {"caption": "Likelihood", "type": "string", "ui_visibility": true, "group": "enrichment"}, "criticality_score": {"caption": "Likelihood Score", "type": "int", "ui_visibility": true, "group": "enrichment"}, "exposure_score": {"caption": "Exposure Score", "type": "int", "ui_visibility": true, "group": "enrichment"}, "contributing_module": {"caption": "Contributing <PERSON><PERSON><PERSON>", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "assessment_severity": {"caption": "Assessment Severity", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "vulnerability_severity": {"caption": "Vulnerability Severity", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "exposure_category": {"caption": "Exposure Category", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "scope": {"caption": "<PERSON><PERSON>", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "ui_visibility": true, "group": "common"}, "count_of_origin": {"caption": "Count of Origin", "type": "int", "group": "common"}}, "dashboard_identifier": {"EM": {}, "Finding": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "finding"}, "Account": {"caption": "Account", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used.", "extends": "", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "associated_identity_count": {"caption": "Count of Identities having Accounts", "type": "int", "group": "enrichment"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "operational_status": {"caption": "Operational Status", "type": "string", "group": "entity_specific"}, "account_name": {"caption": "Account Name", "type": "string", "group": "entity_specific"}, "account_display_name": {"caption": "Account Full Name", "type": "list<string>", "group": "entity_specific"}, "source_of_identity": {"caption": "Source Of Identity", "type": "string", "group": "entity_specific"}, "login_last_date": {"caption": "Login Last Date", "type": "timestamp", "group": "entity_specific"}, "service": {"caption": "Service", "type": "string", "group": "entity_specific"}, "internal_service": {"caption": "Internal Service", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "entitlement_id": {"caption": "Entitlement ID", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "entitlement_value": {"caption": "Entitlement Value", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "entitlement_description": {"caption": "Entitlement Description", "type": "string", "data_structure": "string", "ui_visibility": false, "group": "entity_specific"}, "entitlement_details": {"caption": "Entitlement Details", "type": "string", "data_structure": "string", "group": "entity_specific"}, "privilege_account": {"caption": "Privilege Account", "type": "boolean", "group": "entity_specific"}, "privilege_roles": {"caption": "Privilege Roles", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "privilege_roles_count": {"caption": "Privilege Count", "type": "int", "group": "entity_specific"}, "is_mfa_enabled": {"caption": "Is MFA Enabled", "type": "boolean", "group": "entity_specific"}, "ownership_of_identity": {"caption": "Ownership Of Associated Identity", "type": "string", "group": "entity_specific"}, "account_never_expire": {"caption": "Account Never Expire", "type": "boolean", "group": "entity_specific"}, "password_not_required": {"caption": "Password Not Required", "type": "boolean", "group": "entity_specific"}, "password_never_expire": {"caption": "Password Never Expire", "type": "boolean", "group": "entity_specific"}, "last_password_change_date": {"caption": "Last Password Change", "type": "timestamp", "group": "entity_specific"}, "password_last_used": {"caption": "Last Password Used Date", "type": "timestamp", "group": "entity_specific"}, "last_logged_in_location": {"caption": "Successful Login Location", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "failed_login_attempt_location": {"caption": "Failed Login Attempt Location", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "account_id": {"caption": "Account ID", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "failed_logon_count": {"caption": "Failed Logon Count", "type": "int", "ui_visibility": false, "group": "entity_specific"}, "last_lock_out_time": {"caption": "Last Lock Out Time", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "last_lock_out_flag": {"caption": "Last Lock Out Flag", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "bad_password_configured_time": {"caption": "Bad Password Configured Time", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "bad_password_count_flag": {"caption": "Bad Password Flag", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "last_signin_attempt": {"caption": "Last Signin Attempt", "type": "timestamp", "group": "entity_specific"}, "last_failed_signin_attempt": {"caption": "Last Failed Signin Attempt", "type": "timestamp", "group": "entity_specific"}, "is_sspr_registered": {"caption": "Is SSPR Registered", "type": "boolean", "group": "entity_specific"}, "auth_methods_registered": {"caption": "Authentication Methods Registered", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "default_mfa_method": {"caption": "Default MFA Method", "type": "string", "group": "entity_specific"}, "aad_created_date": {"caption": "AAD Created", "type": "timestamp", "group": "source_specific"}, "ad_created_date": {"caption": "AD Created", "type": "timestamp", "group": "source_specific"}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "type": "string", "group": "source_specific"}, "aws_account_created_date": {"caption": "AWS Created Date", "type": "timestamp", "group": "source_specific"}, "authentication_factors": {"caption": "Authentication Factors", "type": "list<string>", "data_structure": "list", "group": "enrichment"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Account": {}, "IDRA": {}}, "is_available_in_datalake": true, "graph_reference_name": "account"}, "Host": {"caption": "Host", "description": "An independent compute instance where we have visibility or management of the operating system.", "navigator_enabled": true, "navigator_constraints": "Don't include any 'type' filters when user asks just about hosts/devices/machines like show all devices/machines/hosts,only include them if specifically mentioned in user query like workstation/Mobile/Servers.\\n If user asks about exposed devices/hosts, provide hosts/devices with high exposure_score.\\n If user asks about EOL or End of Life devices, provide devices with end of life, older, or outdated operating systems from the list of available operating systems. \\n If the property is about domain joined hosts/devices, filter MS Active Directory as origin  \\n If the property is about rougue devices filter hosts which are not inventoried", "navigator_description": "\n# Host Table Summary\n\n## Overview\nThe Host table serves as a central repository for tracking and managing all compute devices across an organization's environment. It provides a unified view of servers, workstations, mobile devices, network appliances, and cloud-based virtual machines with comprehensive details about their configuration, security posture, and operational status.\n\n## Data Categories\n\n### Identity & Classification\n- **Hostnames & Identifiers**: Various name formats (FQDN, NetBIOS, DNS names) and unique identifiers\n- **IP & MAC Addresses**: Network identifiers and hardware addressing\n- **Resource IDs**: Cloud-specific instance and resource identifiers\n- **Hardware Details**: Serial numbers, IMEI, models, manufacturers, and chassis information\n- **Classification**: Host types (Server, Workstation, Mobile, Network, etc.)\n\n### Infrastructure Context\n- **Location Information**: Physical and logical placement (country, city, region)\n- **Network Context**: Domain membership, accessibility (internal/external)\n- **Infrastructure Type**: On-premise vs. cloud-based deployments\n- **Cloud-Specific Attributes**: Provider, account details, instance types, and resource information\n- **Virtualization Status**: Container hosting capabilities and relationships\n\n### Operating System Information\n- **OS Details**: Full OS name, family, version, architecture, and build information\n- **System Configuration**: BIOS details, operational parameters\n- **Boot Information**: Last reboot timestamps and operational history\n\n### Security Posture\n- **Security Agents**: Coverage status for EDR, antivirus, and vulnerability management tools\n- **Security Tool Status**: Last scan dates, signature updates, and operational status\n- **Compliance State**: MDM compliance status and security configurations\n- **Firewall & Protection**: Status of security controls and defensive capabilities\n- **Internet Exposure**: Accessibility from internet and exposure metrics\n- **Exposure Score**: Numerical value (0-1000) quantifying a host's accessibility to potential threats, with higher scores indicating greater risk. Tracks both active and inactive hosts, maintaining scores even after deactivation to identify hosts requiring remediation despite inactive status.\n\n### Activity & Lifecycle\n- **Temporal Information**: First seen, last found, and last active dates\n- **Activity Status**: Current operational state (active/inactive)\n- **Login Information**: Last login user and timestamp\n- **Lifecycle Metrics**: Lifetime duration and observed timeline\n- **Provisioning Status**: Deployment and operational state information\n\n### Management Coverage\n- **MDM Information**: Mobile device management enrollment, sync status\n- **Vulnerability Management**: Scanning methods and coverage details\n- **Security Product Coverage**: Types and statuses of security tools deployed\n\n### Security Posture Assessment\n- **Risk Scoring**: Host inherent risk scoring across multiple dimensions\n- **Exposure Metrics**: Measures of the host's exposure to potential threats\n- **Domain & Device Scoring**: Risk evaluations based on domain status and device type\n\n### Relationship Mapping\n- **Ownership Information**: Person-to-device relationships\n- **Identity Associations**: Linked user identities count\n- **Vulnerability Context**: Associated vulnerability findings (total and open)\n- **Application Relationships**: Hosted application counts and relationships\n- **Cloud Resource Connections**: Associated cloud infrastructure elements\n\n### Data Provenance\n- **Source Attribution**: Origin systems contributing data (MS Defender, CrowdStrike, Qualys, etc.)\n- **Data Source Details**: Specific API endpoints and data feeds\n- **Corroboration Information**: Whether data points are unique or corroborated across sources\n- **Fragment Counts**: Number of partial records resolved for an entity\n\n### Business Context\n- **Organizational Placement**: Business unit and department assignments\n- **Asset Purpose**: Functional role within the organization\n- **Asset Compliance Scope**: Regulatory frameworks (PCI DSS, SOX, etc.)\n- **Inventory Status**: Confirmation of proper asset recording\n\n## Key Features\n\n1. **Comprehensive Device Catalog**: Consolidates data about all compute devices regardless of type or location\n\n2. **Multi-Dimensional Security Assessment**: Combines security agent status, vulnerability data, compliance information, and exposure scoring\n\n3. **Cloud and On-Premise Integration**: Unifies data across traditional and cloud infrastructure\n\n4. **Risk-Based Prioritization**: Provides multiple scoring dimensions for risk-based security approaches, including exposure scores that persist for inactive devices\n\n5. **Relationship Context**: Maps connections between hosts and other entities (users, applications, vulnerabilities)\n\n6. **Temporal Tracking**: Maintains historical timeline of device lifecycle and activity\n\n7. **Security Posture Evaluation**: Assesses implementation of security controls and protective measures\n\n8. **Enriched Context**: Adds business relevance through criticality scores and compliance scope\n\nThis Host table functions as the foundation of the security analytics platform, providing the essential device context needed for vulnerability management, threat detection, compliance reporting, and overall security risk assessment.\n\nIMPORTANT: Query: Show devices with critical vulnerability, filter for host will be '' because there is no filter related to host in the query. End user host is a filter.\n", "navigator_graph_node_description": "The Host entity represents any computing device within an organization's IT infrastructure, including servers, workstations, virtual machines, network appliances, and cloud instances. It captures key attributes such as hostname, IP addresses, OS details, hardware specifications, unique identifiers (serial numbers, cloud instance IDs), and lifecycle timestamps (first seen, last active).Security attributes include installed security software, vulnerability status, compliance tracking, and integrations with tools like Defender, CrowdStrike, and Tenable. For cloud hosts, it records provider details (AWS, Azure), regions, and resource types. This entity helps assess security posture, risk, and compliance while enabling device monitoring and management.", "navigator_entity_description": "\\nA Host is a key entity in cybersecurity, representing an independent compute instance where visibility or management of the operating system is available. This includes a variety of devices such as servers, workstations, mobile devices, network appliances, and cloud-based virtual machines.  \\n\\nThe Host entity contains essential details about the deviceâ€™s identity, configuration, security posture, and operational status. It includes attributes such as hostname, IP addresses, operating system information, hardware specifications, and unique identifiers like serial numbers or cloud instance IDs. It also tracks timestamps related to the hostâ€™s lifecycle, such as when it was first discovered, last seen, or last active.  \\n\\nSecurity-related attributes provide insight into the deviceâ€™s protection status. These include details on installed security software like antivirus or EDR agents, vulnerability management data, and compliance with security policies. The entity aggregates information from various security tools and platforms such as Microsoft Defender, CrowdStrike, Qualys, and Tenable, offering a comprehensive security view.  \\n\\nFor cloud-based hosts, additional attributes include cloud provider-specific details like AWS or Azure account IDs, regions, and resource types, ensuring effective cloud security management.  \\n\\nThe Host entity serves as a central point for consolidating and correlating data from multiple sources, offering a holistic perspective on the IT infrastructure.  \\n\\nIn addition, the Host entity includes specific quantitative attributes:  \\n\\n- Number/Count of Owners: The total count of individuals associated with ownership or usage of the host, including all categories such as active, inactive, or permanent.  \\n- Number/Count of Identities: The total number of identities linked to the host, covering all states like active or inactive.  \\n- Number/Count of Applications Running on the Host: The total number of installed or actively running applications.  \\n- Number/Count of Cloud Resources Linked to the Host: The number of cloud-based assets associated with the host.  \\n- Number/Count of Vulnerability Findings: The total number of security vulnerabilities identified on the host.  \\n- Number/Count of Open Vulnerability Findings: The number of unresolved security vulnerabilities.  \\n    - When a user queries for maximum, minimum, highest, lowest, or filtered values (e.g., greater than or less than) related to the above quantitative attributes, results should focus only on the Host entity without including other entities for that specific part of the query. However, for other aspects of the query, additional entities may be included as needed. \\n    - The above quantitative attributes should not be used when calculating the total count of owners, identities, applications, or vulnerabilities to avoid redundancy and we dont have *sum* operation support.\\n", "navigator_examples": ["User Query: 'Show windows devices' Output: 'host'", "User Query: 'Show linux servers' Output: 'host'"], "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "has_identity_count": {"caption": "Count of Identities", "type": "int", "group": "enrichment"}, "owned_person_count": {"caption": "Count of Owned Persons", "type": "int", "group": "enrichment"}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_cloud_resource_count": {"caption": "Count of Corresponding Cloud Resources", "type": "int", "group": "enrichment"}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "hosting_application_count": {"caption": "Count of Hosting Applications", "type": "int", "group": "enrichment"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "type": "string", "ui_visibility": false, "group": "common"}, "host_name": {"caption": "Hostname", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "fqdn": {"caption": "FQDN", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "domain": {"caption": "Domain", "type": "string", "group": "entity_specific"}, "asset_role": {"caption": "Asset Role", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "ip": {"caption": "IP", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "dns_name": {"caption": "DNS Name", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "netbios": {"caption": "NetBIOS", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "accessibility": {"caption": "Accessibility", "type": "string", "group": "entity_specific"}, "mac_address": {"caption": "MAC Address", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "mdm_mac_address": {"caption": "MDM MAC Address", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "login_last_date": {"caption": "Last Login", "type": "timestamp", "group": "entity_specific"}, "login_last_user": {"caption": "Last Logged in User", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "type": "timestamp", "group": "entity_specific"}, "os": {"caption": "OS", "type": "string", "group": "entity_specific"}, "os_family": {"caption": "OS Family", "type": "string", "group": "entity_specific"}, "os_version": {"caption": "OS Version", "type": "string", "group": "entity_specific"}, "os_architecture": {"caption": "OS Architecture", "type": "string", "group": "entity_specific"}, "os_build": {"caption": "OS Build", "type": "string", "group": "entity_specific"}, "cloud_provider": {"caption": "Cloud Provider", "type": "string", "group": "entity_specific"}, "account_id": {"caption": "Cloud Account ID", "type": "list<string>", "group": "entity_specific"}, "cloud_account_name": {"caption": "Cloud Account Name", "type": "string", "group": "entity_specific"}, "region": {"caption": "Cloud Region", "type": "string", "group": "entity_specific"}, "zone_availability": {"caption": "Cloud Zone Availability", "type": "string", "group": "entity_specific"}, "resource_id": {"caption": "Cloud Resource ID", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "cloud_resource_type": {"caption": "Cloud Resource Type", "type": "string", "group": "entity_specific"}, "native_type": {"caption": "Cloud Native Type", "type": "string", "group": "entity_specific"}, "operational_state": {"caption": "Cloud Last Known Operational State", "type": "string", "group": "entity_specific"}, "active_operational_date": {"caption": "Active Operational Date", "type": "timestamp", "group": "entity_specific"}, "provisioning_state": {"caption": "Provisioning State", "type": "string", "group": "entity_specific"}, "cloud_instance_id": {"caption": "Cloud Instance ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "cloud_instance_type": {"caption": "Cloud Instance Type", "type": "string", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "image": {"caption": "Cloud Instance Image ID", "type": "string", "group": "entity_specific"}, "cloud_instance_lifecycle": {"caption": "Cloud Instance Lifecycle", "type": "string", "group": "entity_specific"}, "instance_name": {"caption": "Cloud Instance Name", "type": "string", "group": "entity_specific"}, "is_ephemeral": {"caption": "Is Ephemeral", "type": "boolean", "group": "entity_specific"}, "is_container_host": {"caption": "Is Container Host", "type": "boolean", "group": "entity_specific"}, "edr_onboarding_status": {"caption": "EDR Onboarding Status", "type": "boolean", "group": "entity_specific"}, "edr_product": {"caption": "EDR Product", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "edr_last_scan_date": {"caption": "EDR Last Scan Date", "type": "timestamp", "group": "entity_specific"}, "edr_mac_address": {"caption": "EDR Mac <PERSON>dress", "type": "string", "group": "entity_specific"}, "av_status": {"caption": "Anti Virus Scan Completed", "type": "boolean", "group": "entity_specific"}, "av_last_scan_date": {"caption": "AV Last Scan Date", "type": "timestamp", "group": "entity_specific"}, "av_signature_update_date": {"caption": "AV Signature Update Date", "type": "timestamp", "group": "entity_specific"}, "av_block_malicious_code_status": {"caption": "AV Block Malicious Code Setting Enabled", "type": "boolean", "group": "entity_specific"}, "fw_status": {"caption": "Firewall Enabled", "type": "boolean", "group": "entity_specific"}, "vm_product": {"caption": "VM Product", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "vm_onboarding_status": {"caption": "VM Onboarding Status", "type": "boolean", "group": "entity_specific"}, "vm_tracking_method": {"caption": "VM Tracking Method", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "vm_last_scan_date": {"caption": "VM Last Scan Date", "type": "timestamp", "group": "entity_specific"}, "host_last_reboot_date": {"caption": "Host Last Reboot Date", "type": "timestamp", "group": "entity_specific"}, "mdm_product": {"caption": "MDM Product", "type": "string", "group": "entity_specific"}, "mdm_status": {"caption": "MDM Registration Status", "type": "string", "group": "entity_specific"}, "mdm_compliance_state": {"caption": "MDM Compliance State", "type": "string", "group": "entity_specific"}, "mdm_enrolled_date": {"caption": "MDM Enrolled", "type": "timestamp", "group": "entity_specific"}, "mdm_last_sync_date": {"caption": "MDM Last Sync", "type": "timestamp", "group": "entity_specific"}, "hardware_manufacturer": {"caption": "Hardware Manufacturer", "type": "string", "group": "entity_specific"}, "hardware_model": {"caption": "Hardware Model", "type": "string", "group": "entity_specific"}, "hardware_serial_number": {"caption": "Hardware Serial Number", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "hardware_imei": {"caption": "Hardware IMEI", "type": "string", "group": "entity_specific"}, "hardware_bios_manufacturer": {"caption": "Hardware BIOS Manufacturer", "type": "string", "group": "entity_specific"}, "hardware_bios_version": {"caption": "Hardware BIOS Version", "type": "string", "group": "entity_specific"}, "hardware_chassis_type": {"caption": "Hardware Chassis Type", "type": "string", "group": "entity_specific"}, "is_accessible_from_internet": {"caption": "Internet Exposure", "type": "boolean", "group": "entity_specific"}, "open_to_all_internet": {"caption": "Open To All Internet", "type": "boolean", "group": "entity_specific"}, "edr_threat_count": {"caption": "EDR Threat Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "internal_contributor": {"caption": "Internal Contributor", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "wiz_id": {"caption": "Wiz ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "wifi_mac_address": {"caption": "Wifi MAC Address", "type": "list<string>", "ui_visibility": false, "group": "source_specific"}, "ethernet_mac_address": {"caption": "Ethernet MAC Address", "type": "list<string>", "ui_visibility": false, "group": "source_specific"}, "ad_sam_account_name": {"caption": "AD SAM Account Name", "type": "string", "ui_visibility": false, "group": "source_specific"}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "type": "string", "ui_visibility": false, "group": "source_specific"}, "account_enabled_status": {"caption": "Account Enabled Status", "type": "string", "ui_visibility": false, "group": "source_specific"}, "earliest_ad_account_disabled_date": {"caption": "Earliest AD Account Disabled Date", "type": "int", "ui_visibility": false, "group": "source_specific"}, "ad_account_disabled_date": {"caption": "AD Account Disabled Date", "type": "timestamp", "group": "source_specific"}, "ad_distinguished_name": {"caption": "AD Distinguished Name", "type": "string", "group": "source_specific"}, "ad_uac": {"caption": "AD User Account Control", "type": "string", "ui_visibility": true, "group": "source_specific"}, "ad_object_guid": {"caption": "AD ObjectGUID", "type": "string", "group": "source_specific"}, "aad_device_category": {"caption": "AAD Device Category", "type": "string", "group": "source_specific"}, "aad_system_label": {"caption": "AAD System Label", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "ad_created_date": {"caption": "AD Created", "type": "timestamp", "group": "source_specific"}, "ad_last_sync_date": {"caption": "AD Last Sync Date", "type": "timestamp", "group": "source_specific"}, "ad_operational_status": {"caption": "AD Operational Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aad_id": {"caption": "AAD ID", "type": "list<string>", "ui_visibility": false, "group": "source_specific"}, "aad_device_id": {"caption": "AAD Device ID", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "aad_enrolled_date": {"caption": "AAD Enrolled", "type": "timestamp", "group": "source_specific"}, "aad_created_date": {"caption": "AAD Created", "type": "timestamp", "group": "source_specific"}, "aad_management_service": {"caption": "AAD Management Service", "type": "string", "group": "source_specific"}, "aad_deleted_date": {"caption": "AAD Deleted Date", "type": "timestamp", "group": "source_specific"}, "aad_operational_status": {"caption": "AAD Operational Status", "type": "string", "ui_visibility": false, "group": "source_specific"}, "azure_vm_power_state": {"caption": "Azure VM Power State", "type": "string", "group": "source_specific"}, "azure_tags": {"caption": "Azure Tags", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_vm_image_id": {"caption": "Azure VM Image ID", "type": "string", "ui_visibility": false, "group": "source_specific"}, "azure_vm_os_version": {"caption": "Azure VM OS Version", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_os_name": {"caption": "Azure VM OS Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_vm_lifecycle": {"caption": "Azure VM Lifecycle", "type": "string", "ui_visibility": false, "group": "source_specific"}, "aws_region": {"caption": "AWS Region", "type": "string", "ui_visibility": false, "group": "source_specific"}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "type": "timestamp", "group": "source_specific"}, "aws_instance_launch_date": {"caption": "AWS Instance Launch Date", "type": "timestamp", "group": "source_specific"}, "aws_instance_attach_time": {"caption": "AWS Instance Attach Date", "type": "timestamp", "group": "source_specific"}, "aws_operational_state": {"caption": "AWS Operational State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "type": "timestamp", "group": "source_specific"}, "aws_instance_private_ip_address": {"caption": "AWS Instance Private IP Address", "type": "string", "group": "source_specific"}, "aws_instance_public_ip_address": {"caption": "AWS Instance Public IP Address", "type": "string", "group": "source_specific"}, "aws_instance_image_id": {"caption": "AWS Instance Image ID", "type": "string", "ui_visibility": false, "group": "source_specific"}, "aws_instance_usage_update_time": {"caption": "AWS Instance Usage Update Date", "type": "timestamp", "group": "source_specific"}, "aws_tags": {"caption": "AWS Tags", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "win_event_id": {"caption": "Windows Event Code", "type": "list<string>", "data_structure": "list", "ui_visibility": false, "group": "source_specific"}, "qualys_id": {"caption": "Qualys ID", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "qualys_tags": {"caption": "Qualys Tags", "type": "string", "group": "source_specific"}, "qualys_groups": {"caption": "Qualys Groups", "type": "string", "ui_visibility": false, "group": "source_specific"}, "qualys_asset_id": {"caption": "Qualys Asset ID", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "qualys_detection_method": {"caption": "Qualys Detection Method", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "defender_id": {"caption": "Defender ID", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "defender_health_status": {"caption": "Defender Health Status", "type": "string", "group": "source_specific"}, "defender_detection_method": {"caption": "Defender Detection Method", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "defender_tags": {"caption": "Defender Tags", "type": "string", "group": "source_specific"}, "defender_risk_score": {"caption": "Defender Risk Score", "type": "string", "group": "source_specific"}, "defender_exposure_level": {"caption": "Defender Exposure Level", "type": "string", "group": "source_specific"}, "defender_threat_name": {"caption": "Defender Threat Name", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "defender_onboarding_status": {"caption": "Defender Onboarding Status", "type": "string", "group": "source_specific"}, "defender_threat_count": {"caption": "Defender Infection Count", "type": "int", "group": "source_specific"}, "defender_management_service": {"caption": "Defender Management Service", "type": "string", "group": "source_specific"}, "defender_action_type": {"caption": "Defender Action Type", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "defender_onboarding_date": {"caption": "Defender Onboarding Date", "type": "timestamp", "group": "source_specific"}, "intune_ownership_status": {"caption": "Intune Ownership Status", "type": "string", "group": "source_specific"}, "mdm_encryption_status": {"caption": "MDM Encryption Status", "type": "boolean", "group": "source_specific"}, "intune_id": {"caption": "Intune ID", "type": "string", "group": "source_specific"}, "intune_management_service": {"caption": "Intune Management Service", "type": "string", "group": "source_specific"}, "tenablesc_onboarding_status": {"caption": "Tenable.sc onboarding status", "type": "boolean", "group": "source_specific"}, "tenablesc_last_active_date": {"caption": "Tenable.sc Last Active", "type": "timestamp", "group": "source_specific"}, "tenablesc_repositories": {"caption": "Tenable.sc Repositories", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "tenablesc_assets": {"caption": "Tenable.sc Assets", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "tenablesc_asset_tags": {"caption": "Tenable.sc Asset Tags", "type": "string", "group": "source_specific"}, "tenablesc_asset_name": {"caption": "Tenable.sc Asset Name", "type": "string", "ui_visibility": false, "group": "source_specific"}, "tenablesc_asset_groups": {"caption": "Tenable.sc Asset Groups", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "tenable_repo": {"caption": "Tenable.sc Repo", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "crowdstrike_modified_date": {"caption": "CrowdStrike Modified Date", "type": "timestamp", "group": "source_specific"}, "crowdstrike_last_report_date": {"caption": "CrowdStrike Last Report Date", "type": "timestamp", "group": "source_specific"}, "crowdstrike_onboarding_status": {"caption": "CrowdStrike Onboarding Status", "type": "boolean", "group": "source_specific"}, "crowdstrike_connection_ip": {"caption": "CrowdStrike Connection IP", "type": "string", "ui_visibility": false, "group": "source_specific"}, "crowdstrike_product_type_desc": {"caption": "CrowdStrike Product Type", "type": "string", "group": "source_specific"}, "crowdstrike_operational_state": {"caption": "CrowdStrike Operational State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "crowdstrike_provision_state": {"caption": "CrowdStrike Provision State", "type": "string", "group": "source_specific"}, "crowdstrike_agent_local_date": {"caption": "CrowdStrike Agent Local Date", "type": "timestamp", "group": "source_specific"}, "crowdstrike_device_id": {"caption": "CrowdStrike Device ID", "type": "list<string>", "group": "source_specific"}, "crowdstrike_customer_id": {"caption": "CrowdStrike Customer ID", "type": "string", "group": "source_specific"}, "crowdstrike_onboarding_date": {"caption": "CrowdStrike Onboarding Date", "type": "timestamp", "group": "source_specific"}, "crowdstrike_reboot_date": {"caption": "CrowdStrike Reboot Date", "type": "timestamp", "group": "source_specific"}, "crowdstrike_tags": {"caption": "CrowdStrike Tags", "type": "string", "data_structure": "list", "group": "source_specific"}, "crowdstrike_external_ip": {"caption": "CrowdStrike External IP", "type": "string", "group": "source_specific"}, "crowdstrike_local_ip": {"caption": "CrowdStrike Local IP", "type": "string", "group": "source_specific"}, "itop_pc_display_name": {"caption": "ServiceNow PC Display Name", "type": "string", "group": "source_specific"}, "itop_pc_move_to_production_date": {"caption": "ServiceNow PC Move To Production Date", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "itop_pc_end_of_warranty_date": {"caption": "ServiceNow PC End of Warranty Date", "type": "timestamp", "group": "source_specific"}, "itop_pc_status": {"caption": "ServiceNow PC Status", "type": "string", "group": "source_specific"}, "itop_pc_org_unit": {"caption": "ServiceNow PC Organization Unit", "type": "string", "group": "source_specific"}, "itop_pc_device_serial_number": {"caption": "ServiceNow PC Device Serial Number", "type": "string", "group": "source_specific"}, "itop_pc_location": {"caption": "ServiceNow PC Location", "type": "string", "group": "source_specific"}, "itop_pc_obsolete_date": {"caption": "ServiceNow PC Obsolete Date", "type": "timestamp", "group": "source_specific"}, "itop_pc_type": {"caption": "ServiceNow PC Device Type", "type": "string", "group": "source_specific"}, "itop_pc_os_family": {"caption": "ServiceNow PC OS Family", "type": "string", "group": "source_specific"}, "itop_pc_os_version": {"caption": "ServiceNow PC OS Version", "type": "string", "group": "source_specific"}, "itop_pc_os_build": {"caption": "ServiceNow PC OS Build", "type": "string", "group": "source_specific"}, "itop_pc_purchase_date": {"caption": "ServiceNow PC Purchase Date", "type": "timestamp", "group": "source_specific"}, "itop_class": {"caption": "ServiceNow Class", "type": "string", "group": "source_specific"}, "itop_server_display_name": {"caption": "ServiceNow Server Display Name", "type": "string", "group": "source_specific"}, "itop_server_status": {"caption": "ServiceNow Server Status", "type": "string", "group": "source_specific"}, "itop_server_location": {"caption": "ServiceNow Server Location", "type": "string", "group": "source_specific"}, "itop_server_obsolete_date": {"caption": "ServiceNow Server Obsolete Date", "type": "timestamp", "group": "source_specific"}, "itop_server_os_family": {"caption": "ServiceNow Server OS Family", "type": "string", "group": "source_specific"}, "itop_server_os_version": {"caption": "ServiceNow Server OS Version", "type": "string", "group": "source_specific"}, "itop_server_serial_number": {"caption": "ServiceNow Server Serial Number", "type": "string", "group": "source_specific"}, "itop_server_move_to_production_date": {"caption": "ServiceNow Server Move To Production Date", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "itop_server_purchase_date": {"caption": "ServiceNow Server Purchase Date", "type": "timestamp", "group": "source_specific"}, "itop_server_end_of_warranty_date": {"caption": "ServiceNow Server End of Warranty Date", "type": "timestamp", "group": "source_specific"}, "wiz_onboarding_status": {"caption": "Wiz Onboarding Status", "type": "boolean", "group": "source_specific"}, "wiz_onboarding_date": {"caption": "Wiz Onboarding Date", "type": "timestamp", "group": "source_specific"}, "wiz_last_scan_date": {"caption": "Wiz Last Scan Date", "type": "timestamp", "group": "source_specific"}, "wiz_operational_state": {"caption": "Wiz Operational State", "type": "string", "group": "source_specific"}, "wiz_modified_date": {"caption": "Wiz Modified Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_onboarding_date": {"caption": "Tenable.io Onboarding Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_asset_updated_at": {"caption": "Tenable.io <PERSON> Updated Date", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "tenable_io_last_scan_date": {"caption": "Tenable.io <PERSON> Last Scan Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_last_authenticated_scan_date": {"caption": "Tenable.io Last Autheticated Scan Date", "type": "timestamp", "group": "source_specific"}, "tenable_io_asset_aws_terminated_date": {"caption": "Tenable.io AWS Terminated Date", "type": "timestamp", "group": "source_specific"}, "asset_compliance_scope": {"caption": "Asset Compliance Scope", "type": "list<string>", "data_structure": "list", "group": "enrichment"}, "asset_is_inventoried": {"caption": "Is Inventoried", "type": "boolean", "group": "enrichment"}, "asset_security_posture": {"caption": "Security Posture Score", "type": "float", "group": "enrichment"}, "host_meet_security_posture": {"caption": "Host Meet Security Posture", "type": "boolean", "group": "enrichment"}, "infrastructure_type": {"caption": "Infrastructure Type", "type": "string", "group": "enrichment"}, "threat_count": {"caption": "Threat Count", "type": "int", "ui_visibility": true, "group": "entity_specific"}, "encryption_status": {"caption": "Encryption Status", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "compliance_state": {"caption": "Compliance State", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "archival_flag": {"caption": "Archival Flag", "type": "boolean", "group": "enrichment"}, "asset_exposure": {"caption": "Asset Exposure", "type": "float", "ui_visibility": true, "group": "enrichment"}, "rollup_consequence": {"caption": "Rollup Consequence", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "enrichment"}, "criticality": {"caption": "Asset Criticality", "type": "string", "ui_visibility": true, "group": "enrichment"}, "criticality_score": {"caption": "Asset Criticality Score", "type": "int", "ui_visibility": true, "group": "enrichment"}, "exposure_score": {"caption": "Exposure Score", "type": "int", "ui_visibility": true, "group": "enrichment"}, "exposure_severity": {"caption": "Exposure Severity", "type": "string", "ui_visibility": true, "group": "enrichment"}, "environment": {"caption": "Environment", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Host": {}, "VRA": {}, "EM": {}, "IDRA Risk Index": {}, "CCM": {}, "VRA Risk Index": {}, "IDRA": {}}, "is_available_in_datalake": true, "graph_reference_name": "host"}, "Identity": {"caption": "Identity", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used", "navigator_enabled": true, "navigator_description": "\n# Identity Table Summary\n\n## Overview\nThe Identity table serves as the central repository for all authentication entities within the cybersecurity environment, tracking both human and non-human identities across various systems. It provides comprehensive visibility into authentication methods, access patterns, security configurations, and risk factors associated with identities that access organizational resources.\n\n## Data Categories\n\n### Identity Classification & Attribution\n- **Identity Types**: Categorization as Human or Non-Human identities\n- **Identity Formats**: Various formats including User Principal Name, SAM Account Name, AAD Device ID, External Email\n- **Provider Details**: Identity source systems (Azure AD, Active Directory, AWS, Others)\n- **Internal Services**: Associated services like IAM Center and Cloudtrail\n- **Ownership Classification**: Corporate vs. External identity categorization\n\n### Authentication Configuration\n- **MFA Status**: Multi-factor authentication enablement and registration\n- **Authentication Methods**: Registered authentication methods (mobile phone, authenticator app, Windows Hello)\n- **Default MFA Method**: Primary second-factor authentication type\n- **Password Settings**: Password expiration configuration, requirements, last change date\n- **SSPR Status**: Self-service password reset registration\n\n### Cross-Geography Access\n- **location_accessed_flag**: Tracking of logins from multiple countries within one day\n\n### Access Patterns & Behavior\n- **Login Information**: Last login dates, password usage, and sign-in attempts\n- **Location Data**: Last logged-in location and multiple-location access patterns\n\n- **Operational Status**: Current functioning state (Active, Disabled)\n- **Activity Timeline**: First seen, last found, last active, and lifetime metrics\n\n### Identifiers & Reference Data\n- **Primary Identifiers**: AAD IDs, Employee IDs, User Principal Names\n- **Email Addresses**: Associated email identifiers\n- **Display Labels**: Human-readable identity references\n- **Identity Primary Keys**: Combined identity and provider references\n- **Unique Identifiers**: System-generated p_ids for internal reference\n\n### Risk Assessment\n- **Inherent Risk Scoring**: Composite risk score based on multiple security factors\n- **MFA Risk Evaluation**: Risk assessment based on multi-factor authentication status\n- **Machine Identity Risk**: Special scoring for non-human identities\n- **Multi-Login Risk**: Assessment of risks from geographic access patterns\n- **Password Configuration Risk**: Evaluation of password policy implementation\n- **Ownership Risk**: Risk scores based on identity governance and control\n\n### Relationship Mapping\n- **Account Associations**: Count of linked accounts\n- **Service Connections**: Associated internal services\n- **Data Source Integration**: Original systems contributing identity data\n\n### Organizational Context\n- **Location Information**: Country and city data\n- **Business Unit**: Organizational placement\n- **Department**: Functional grouping within the organization\n\n### Data Provenance\n- **Origin Systems**: Source platforms (Azure AD, Active Directory, AWS)\n- **Data Source Details**: Specific API endpoints and data feeds\n- **Origin Contribution Type**: Whether data is unique or corroborated\n- **Fragment Counts**: Number of partial records consolidated for each identity\n\n## Key Features\n\n1. **Comprehensive Identity Catalog**: Unified repository of all authentication entities regardless of type or source\n\n2. **Authentication Security Assessment**: Detailed tracking of MFA enablement, methods, and password policies\n\n3. **Behavioral Analysis**: Monitoring of access patterns including geographic anomalies and multi-location access\n\n4. **Risk-Based Evaluation**: Multi-dimensional risk scoring across various security factors\n\n5. **Temporal Tracking**: Complete timeline from creation through latest activity\n\n6. **Cross-Platform Integration**: Consolidation of identity data from cloud, on-premise, and third-party systems\n\n7. **Identity Governance Insights**: Visibility into corporate vs. external identities and their security configurations\n\n8. **Relationship Context**: Mapping to accounts and services within the environment\n\nThis Identity table forms a critical component of the security analytics platform, providing the authentication context needed for comprehensive security risk assessment, access governance, and threat detection focusing on identity-based attacks.\n", "navigator_graph_node_description": "The Identity entity represents a digital persona within an organization's IT infrastructure, focusing on authentication, authorization, and access management. It includes both human and non-human identities (e.g., service accounts, applications, devices).Key attributes include unique digital identifiers (User Principal Name, AAD Object ID), authentication details (MFA status, sign-in activity), and lifecycle tracking (account creation, last active timestamp).It integrates with identity providers like Azure AD, Active Directory, and AWS IAM to enable risk assessment and security monitoring. Identity management is distinct from personnel tracking, focusing solely on system access and security controls.", "navigator_entity_description": "\\nAn Identity in cybersecurity represents a digital entity that interacts with an organization's IT infrastructure, focusing exclusively on system-level access, authentication, and authorization. \\nIt applies to both human and non-human entities (e.g., users, service accounts, applications, and devices) and plays a critical role in access control and security monitoring.\\n\\nIdentity strictly pertains to system authentication and authorization processes, and DOES NOT include employment details, organizational affiliations, or asset ownership.\\n\\nKey Attributes Defining an Identity:\\n1. Digital Identification:\\nAn Identity is uniquely recognized in digital systems by authentication-related attributes, such as:\\n\\nExamples: aad_object_id, sam_account_name, oauth_client_id, User Principal Name (UPN), AAD Device ID.\\nNot Included: HR identifiers such as employee ID, job title, department, or business unit.\\n2. Classification:\\nIdentities are categorized based on system-level properties, such as:\\n\\nType: Human vs. Non-Human (e.g., service accounts, applications, IoT devices).\\nFormat: Cloud-based, on-premises, federated identity, machine accounts.\\nNot Included: Employment status, organizational roles, or reporting structures.\\n3. Authentication & Authorization:\\nAttributes related to verifying and controlling system access, including:\\n\\nAuthentication: Multi-factor authentication (MFA) status, sign-in activity (e.g., successful/failed logins).\\nAuthorization: Access roles, privileges, sign-in frequency, access across locations.\\nNot Included: Employee job roles, HR-related access policies, or employment history.\\n4. Lifecycle Tracking:\\nTracking an identityâ€™s presence within systems, such as:\\n\\nIncluded: Account creation date, last active timestamp, operational status, expiration policies.\\nNot Included: Hire/termination dates, job transfers, or HR system records.\\n5. Risk Assessment & Security Monitoring:\\nSecurity aspects related to identity usage and authentication, such as:\\n\\nIncluded: Risk scores (e.g., MFA risk score), anomaly detection, exposure levels, unauthorized access attempts.\\nNot Included: Department-based risk profiling, employment risk analysis, or business unit compliance.\\n6. Integration with Identity Providers:\\nIdentity spans across various authentication platforms, including:\\n\\nExamples: Azure AD (AAD ID), Active Directory (Distinguished Name), AWS IAM (Role ARN).\\nNot Included: HR systems, personnel management platforms, or employee data repositories.\\nCommon Ways Identity Is Referred To:\\nBy non-technical users: \\'login,\\' \\'username,\\' \\'computer account.\\'\\nBy IT staff: \\'IAM entity,\\' \\'machine identity,\\' \\'service account.\\'\\nBy cybersecurity professionals: \\'authentication principal,\\' \\'privileged account.\\'\\nPlatform-specific terms: \\'Azure AD user,\\' \\'AWS IAM role.\\'\\nKey Differentiation from the Person Entity:\\nIdentity focuses solely on system access and authentication, while Person relates to employment details, organizational roles, and asset ownership.\\n\\nQueries relevant to Identity must center around access control and authentication, NOT employment or organizational affiliations.\\n\\nQueries Relevant to Identity (Examples):\\n\\'Disabled identities in MS Active Directory with recent activity.\\'\\n\\'Identities without MFA enrollment.\\'\\n\\'List of privileged accounts in Azure AD.\\'\\n\\'Service accounts that have accessed multiple systems recently.\\'\\n\\nKey Exclusions from Identity Entity:\\nTo ensure accurate classification, the following aspects should NOT be considered part of the Identity entity:\\n\\nEmployment details: Employee ID, job title, department, reporting manager.\\nOrganizational hierarchy: Business units, cost centers, reporting structures.\\nAsset ownership: Devices or resources assigned to individuals.\\nLocation or company-specific attributes: Physical location, company affiliation.\\nUser status in HR systems: Employment termination, contract types, department membership.\\n\\nAdditionally the table contains\\n**has_account_count**:Number of accounts linked to the identity\\n", "navigator_examples": ["User Query: 'Show active accounts in active directory' Output: 'identity'", "User Query: 'Find all active identities' Output: 'identity'"], "extends": "", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "has_account_count": {"caption": "Count of Accounts", "type": "int", "group": "enrichment"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "identity_format": {"caption": "Identity Format", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "ownership": {"caption": "Ownership", "type": "string", "group": "entity_specific"}, "identity_provider": {"caption": "Identity Provider", "type": "string", "group": "entity_specific"}, "is_mfa_enabled": {"caption": "Is MFA Enabled", "type": "boolean", "group": "entity_specific"}, "aad_id": {"caption": "AAD ID", "type": "list<string>", "group": "entity_specific"}, "email_id": {"caption": "Email ID", "type": "list<string>", "group": "entity_specific"}, "employee_id": {"caption": "Employee ID", "type": "list<string>", "group": "entity_specific"}, "identity_display_name": {"caption": "Identity Display Name", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "identity_primary_key": {"caption": "Identity with IDP", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "company_email_domains": {"caption": "Organization Email Domains", "type": "list<string>", "data_structure": "list", "ui_visibility": false, "group": "entity_specific"}, "user_principal_name": {"caption": "User Principal Name", "type": "string", "group": "entity_specific"}, "login_last_date": {"caption": "Last Login", "type": "timestamp", "group": "entity_specific"}, "last_password_change_date": {"caption": "Last Password Change", "type": "timestamp", "group": "entity_specific"}, "operational_status": {"caption": "Operational Status", "type": "string", "group": "entity_specific"}, "account_never_expire": {"caption": "Account Never Expire", "type": "boolean", "group": "entity_specific"}, "password_not_required": {"caption": "Password Not Required", "type": "boolean", "group": "entity_specific"}, "password_never_expire": {"caption": "Password Never Expire", "type": "boolean", "group": "entity_specific"}, "password_last_used": {"caption": "Last Password Used Date", "type": "timestamp", "group": "entity_specific"}, "last_logged_in_location": {"caption": "Successful Login Location", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "internal_service": {"caption": "Internal Service", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "last_signin_attempt": {"caption": "Last Signin Attempt", "type": "timestamp", "group": "entity_specific"}, "location_accessed_flag": {"caption": "Multiple Location Access Flag", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "location_accessed_in_one_day": {"caption": "Locations Accessed In One Day", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "is_sspr_registered": {"caption": "Is SSPR Registered", "type": "boolean", "group": "entity_specific"}, "auth_methods_registered": {"caption": "Authentication Methods Registered", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "default_mfa_method": {"caption": "Default MFA Method", "type": "string", "group": "entity_specific"}, "ad_last_sync_date": {"caption": "AD Last Sync Date", "type": "timestamp", "group": "source_specific"}, "aad_created_date": {"caption": "AAD Created", "type": "timestamp", "group": "source_specific"}, "ad_distinguished_name": {"caption": "AD Distinguished Name", "type": "string", "group": "source_specific"}, "ad_domain": {"caption": "AD Domain", "type": "string", "group": "source_specific"}, "ad_sam_account_name_with_domain": {"caption": "AD SAM Account Name With Domain", "type": "string", "group": "source_specific"}, "ad_sam_account_type": {"caption": "AD SAM Account Type", "type": "string", "group": "source_specific"}, "ad_created_date": {"caption": "AD Created", "type": "timestamp", "group": "source_specific"}, "aws_account_created_date": {"caption": "AWS Created Date", "type": "timestamp", "group": "source_specific"}, "authentication_factors": {"caption": "Authentication Factors", "type": "list<string>", "data_structure": "list", "group": "enrichment"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Identity": {}}, "is_available_in_datalake": true, "graph_reference_name": "identity"}, "Assessment": {"caption": "Assessment", "description": "Control assessment involves evaluating the effectiveness and implementation of security controls within an organization.", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Assessment Description", "type": "string", "ui_visibility": true, "group": "common"}, "exposure_category": {"caption": "Exposure Category", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "exposure_weightage": {"caption": "Exposure Weightage", "type": "float", "ui_visibility": true, "group": "entity_specific"}, "contributing_module": {"caption": "Contributing <PERSON><PERSON><PERSON>", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "failure_reason": {"caption": "Failure Reason", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "assessment_severity": {"caption": "Assessment Severity", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "assessment_category": {"caption": "Assessment Category", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "scope": {"caption": "<PERSON><PERSON>", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "ui_visibility": true, "group": "common"}, "count_of_origin": {"caption": "Count of Origin", "type": "int", "group": "common"}}, "dashboard_identifier": {"EM": {}, "Assessment": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "assessment"}, "Vulnerability": {"caption": "Vulnerability", "description": "The Vulnerability Dictionary defines attributes defined for Vulnerability entity derived from multiple sources.", "navigator_enabled": true, "navigator_constraints": "Severity normalized should always be used for filtering based on severity unless attribute is specifically mentioned in query.These are the list of 10 exploited vulnerability name and their related CVE. When asked about any of these, please pickup the relevant CVE. ZeroLogon (cve-2020-1472),Log4Shell (cve-2021-44228),ICMAD (cve-2022-22536),ProxyLogon (cve-2021-26855),Spring4Shell (cve-2022-22965),Atlassian Confluence RCE (cve-2022-26134),VMware vSphere (cve-2021-21972),Google Chrome Zero-Day (cve-2022-0609),<PERSON><PERSON><PERSON> (cve-2022-30190),PetitPotam (cve-2021-36942), ingress (cve-2021-25742), also you can filter on 'title' or 'description' attributes to find informations about vulnerabilities.\\n analize query based as cyber/exposure managment expert and provide answers accordingly.", "navigator_description": "\n# Vulnerability Table Summary\n\n## Overview\nThe vulnerability table provides comprehensive vulnerability intelligence data sourced from multiple security platforms and standards. It tracks security vulnerabilities through their entire lifecycle with detailed metrics for severity, exploitability, and impact across different scoring frameworks.\n\n## Data Categories\n\n### Identification & Classification\n- **CVE IDs**: Standardized vulnerability identifiers (e.g., CVE-2024-12345)\n- **CWE References**: Common Weakness Enumeration mappings for vulnerability classification\n- **Title & Description**: Detailed vulnerability information with affected products\n- **Type Classification**: Categorized as Vulnerability, Weakness, or Informational\n\n### Severity Metrics\n- **Multi-Framework Scoring**:\n  - CVSS v2.0, v3.0, v3.1, and v4.0 base scores\n  - Corresponding severity ratings (Critical, High, Medium, Low).\n  - Impact and exploitability sub-scores for deeper analysis\n  - Vector strings capturing detailed vulnerability characteristics\n  - Normalized severity and CVSS scores for simplified analysis\n\n### Exploitability Assessment\n- **Exploit Information**:\n  - Exploit availability status (true/false)\n  - EPSS scores and percentiles measuring likelihood of exploitation\n  - Exploitability categorization (Exploitable, Likely Exploitable, etc.)\n  - Patch availability status\n\n### Temporal Information\n- **Date Tracking**:\n  - Published date (initial disclosure)\n  - Last modified date (updates to vulnerability information)\n  - First observed date (when first detected on systems)\n  - CISA exploit add date (addition to known exploit database)\n  - CISA action due dates (required remediation timelines)\n\n### Remediation Guidance\n- **Mitigation Paths**:\n  - Vendor recommendations\n  - CISA required actions for critical vulnerabilities\n  - Microsoft recommended updates and update IDs\n  - General remediation recommendations\n\n### Impact Analysis\n- **Consequence Details**:\n  - Potential impacts (e.g., Remote Code Execution, Privilege Escalation)\n  - Vendor-assigned severity ratings\n\n### Entity Relationships\n- **Associated Asset Counts**:\n  - Hosts with findings (total and open)\n  - Applications with findings (total and open)\n  - Cloud compute instances with findings (total and open)\n  - Cloud containers with findings (total and open)\n\n### Source Attribution\n- **Data Provenance**:\n  - Origin systems (NVD, Qualys, EPSS, Tenable.sc, MS Defender, Wiz, CISA)\n  - Origin contribution type (Unique or Corroborated)\n  - Fragment counts and data source subsets\n  - Count of originating sources\n\n### Entity Management\n- **Identity & Activity**:\n  - Unique identifiers and display labels\n  - Activity status (Active/Inactive)\n  - First seen, last found, and last active dates\n  - Lifetime and observed lifetime metrics\n  - Recency (days since last discovery)\n\n## Key Features\n\n1. **Comprehensive Vulnerability Intelligence**: Integrates data from all major vulnerability databases and security tools\n\n2. **Multi-Dimensional Severity Assessment**: Incorporates multiple CVSS frameworks (v2.0 through v4.0) with normalized metrics\n\n3. **Temporal Tracking**: Captures vulnerability lifecycle from publication through detection and remediation\n\n4. **Actionable Remediation Guidance**: Includes vendor-specific update recommendations and CISA-mandated actions\n\n5. **Exploitability Context**: Combines theoretical exploit potential with real-world exploitation data\n\n6. **Impact Assessment**: Provides consequence categorization and impact scoring\n\n7. **Relationship Mapping**: Tracks affected assets across infrastructure types (hosts, applications, cloud resources)\n\n8. **Data Lineage**: Maintains source attribution and provenance information\n\nThis vulnerability table serves as a central repository for security teams to identify, prioritize, and remediate vulnerabilities across their environment, supporting risk-based security operations with context-rich vulnerability intelligence.\nIMPORTANT:If the query asks for anyting related to any software,that filter for vulnerability will be ''.For example:query:Show all vulnerabilities due to openssl softwares,filter for vulnerability will be ''", "navigator_graph_node_description": "The Vulnerability entity represents weaknesses in software, hardware, or systems that could be exploited by attackers. It includes key attributes such as CVE IDs, severity ratings (CVSS scores), lifecycle timestamps (first observed, disclosure date), affected assets (CPE, CWE classifications), exploitability status (PoC availability, KEV listings), and remediation details (patches, mitigations).This entity enables risk assessment, threat intelligence, and compliance tracking by linking vulnerabilities to impacted hosts, identities, and organizations. It helps prioritize remediation based on exploit likelihood and business impact, ensuring proactive cybersecurity measures and regulatory adherence.", "navigator_entity_description": "\\n### **Vulnerability Table Overview**  \\n\\nThe **Vulnerability Table** is a structured repository for tracking cybersecurity weaknesses in software, hardware, or systems. It helps security teams manage vulnerabilities, assess risk, prioritize remediation efforts, and ensure compliance with security standards. Each record in the table represents a specific vulnerability, providing critical details to support cybersecurity decision-making.  \\n\\n### **Key Attributes**  \\n\\n1. **Identification**  \\n   - Each vulnerability has a unique identifier, typically a **CVE ID** (e.g., CVE-2021-44228) or vendor-specific reference.  \\n\\n2. **Severity and Risk Assessment**  \\n   - Vulnerabilities are scored using the **CVSS (Common Vulnerability Scoring System)** across different versions (2.0, 3.0, 3.1, 4.0).  \\n   - Severity levels are categorized as **Critical, High, Medium, or Low** based on risk assessment.  \\n   - Exploitability vector strings define attack characteristics.  \\n\\n3. **Lifecycle and Timeline**  \\n   - Tracking key dates such as **first observed, public disclosure, and last modification** helps in monitoring vulnerability status.  \\n\\n4. **Technical Details**  \\n   - The table includes affected **software, hardware, versions (CPE identifiers), and CWE (Common Weakness Enumeration) classifications**.  \\n   - Attack vectors and exploitation conditions describe how an attacker could exploit the weakness.  \\n\\n5. **Exploitability**  \\n   - The availability of **proof-of-concept (PoC) exploits** and whether a vulnerability is listed in **CISAâ€™s Known Exploited Vulnerabilities (KEV)** database help assess the real-world threat level.  \\n\\n6. **Mitigation and Remediation**  \\n   - Includes available **patches, security updates, configuration changes, and compensating controls** to reduce risk.  \\n\\n7. **Threat Intelligence & Risk Prioritization**  \\n   - Incorporates **EPSS (Exploit Prediction Scoring System) ratings**, vendor advisories, and indicators of active exploitation.  \\n\\n8. **Compliance & Regulatory Considerations**  \\n   - Flags for **PCI DSS compliance, STIG severity ratings**, and other security frameworks.  \\n\\n### **Purpose and Importance**  \\n\\nThe Vulnerability Table helps organizations **identify, assess, and remediate** security weaknesses efficiently. By maintaining structured data on vulnerabilities, security teams can:  \\n\\n- **Prioritize threats** based on severity and exploitability.  \\n- **Track mitigation efforts** to ensure timely risk reduction.  \\n- **Align with compliance** frameworks to meet regulatory standards.  \\n- **Gain threat intelligence** on actively exploited vulnerabilities.  \\n\\nThis table is an essential component of cybersecurity risk management, providing a **centralized view of vulnerabilities** to enhance an organizationâ€™s security posture.\\n\\nAdditionally,Vulenrability table contains below details for each Vulnerability\\n- **associated_hosts_with_findings_count (without filter,becase there is so many categories like this only give sum of all)**: The count of hosts/machines/devices affected by vulenrability(open/closed),this field will give how many hosts/machines/devices are affected by that vulenrability(open/closed)\\n- **associated_hosts_with_open_findings_count (without filter,becase there is so many categories like this only give sum of all)**: The number of hosts/machines/devices which have that vulnerability currently open/unresolved, this field will give in how many hosts that vulnerability remains open/unresolved\\n- **associated_application_with_findings_count**: The count of applications/softwares which have that vulenrability(open/closed)\\n    - if user asking about max/high/largest min/least/lower or filter like less than greater than of above return Vulenrability only, dont take above additional attribute for get total count of hosts/applications because redundancy and we dont have *sum* support\\n", "navigator_examples": ["User Query: 'Show all critical vulnerabilities' Output: 'vulnerability'", "User Query: 'Find me denial of service vulnerability' Output: 'vulnerability'"], "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "group": "common"}, "class": {"caption": "Class", "type": "string", "group": "common"}, "type": {"caption": "Type", "type": "string", "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "group": "common"}, "recency": {"caption": "Recency", "type": "int", "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "associated_hosts_with_open_findings_count": {"caption": "Count of Hosts with Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_hosts_with_findings_count": {"caption": "Count of Hosts with Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_cloud_compute_with_open_findings_count": {"caption": "Count of Cloud Compute with Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_cloud_compute_with_findings_count": {"caption": "Count of Cloud Compute with Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_cloud_container_with_open_findings_count": {"caption": "Count of Cloud Container with Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_cloud_container_with_findings_count": {"caption": "Count of Cloud Container with Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_application_with_findings_count": {"caption": "Count of Application with Vulnerability Findings", "type": "int", "group": "enrichment"}, "associated_application_with_open_findings_count": {"caption": "Count of Application with Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "vulnerability_severity": {"caption": "Vulnerability Severity", "type": "string", "group": "enrichment"}, "cvss_normalised": {"caption": "CVSS Normalised", "type": "float", "group": "enrichment"}, "exploitability": {"caption": "Exploitability", "type": "string", "group": "enrichment"}, "title": {"caption": "Title", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "cve_id": {"caption": "CVE ID", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "v40_score": {"caption": "CVSSv4.0 Score", "type": "float", "group": "entity_specific"}, "v40_severity": {"caption": "CVSSv4.0 Severity", "type": "string", "group": "entity_specific"}, "v31_score": {"caption": "CVSSv3.1 Score", "type": "float", "group": "entity_specific"}, "v31_vector": {"caption": "CVSSv3.1 Vector", "type": "string", "group": "entity_specific"}, "v31_severity": {"caption": "CVSSv3.1 Severity", "type": "string", "group": "entity_specific"}, "v31_exploitability": {"caption": "CVSSv3.1 Exploitability", "type": "float", "group": "entity_specific"}, "v31_impact_score": {"caption": "CVSSv3.1 Impact Score", "type": "float", "group": "entity_specific"}, "v30_score": {"caption": "CVSSv3.0 Score", "type": "float", "group": "entity_specific"}, "temporal_cvss_score": {"caption": "CVSSv3.0 Temporal Score", "type": "float", "group": "entity_specific"}, "v30_vector": {"caption": "CVSSv3.0 Vector", "type": "string", "group": "entity_specific"}, "v30_severity": {"caption": "CVSSv3.0 Severity", "type": "string", "group": "entity_specific"}, "v30_exploitability": {"caption": "CVSSv3.0 Exploitability", "type": "float", "group": "entity_specific"}, "v30_impact_score": {"caption": "CVSSv3.0 Impact Score", "type": "float", "group": "entity_specific"}, "v2_score": {"caption": "CVSSv2 Score", "type": "float", "group": "entity_specific"}, "v2_vector": {"caption": "CVSSv2 Vector", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "v2_severity": {"caption": "CVSSv2 Severity", "type": "string", "group": "entity_specific"}, "v2_exploitability": {"caption": "CVSSv2 Exploitability", "type": "float", "group": "entity_specific"}, "v2_impact_score": {"caption": "CVSSv2 Impact Score", "type": "float", "group": "entity_specific"}, "software_list": {"caption": "Software List", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "vulnerability_first_observed_date": {"caption": "Vulnerability First Observed", "type": "timestamp", "group": "entity_specific"}, "last_modified_date": {"caption": "Last Modified", "type": "timestamp", "group": "entity_specific"}, "published_date": {"caption": "Published On", "type": "timestamp", "group": "entity_specific"}, "vendor_severity": {"caption": "<PERSON><PERSON><PERSON>", "type": "string", "group": "entity_specific"}, "patch_available": {"caption": "Patch Available", "type": "string", "group": "entity_specific"}, "exploit_available": {"caption": "Exploit Available", "type": "boolean", "group": "entity_specific"}, "recommendation": {"caption": "Recommendation", "type": "list<string>", "group": "entity_specific"}, "ms_recommended_update": {"caption": "Microsoft Recommended Update", "type": "list<string>", "group": "entity_specific"}, "ms_recommended_update_id": {"caption": "Microsoft Recommended Update ID", "type": "list<string>", "group": "entity_specific"}, "cisa_exploit_add_date": {"caption": "CISA Exploit Addition", "type": "timestamp", "group": "entity_specific"}, "cisa_action_due_date": {"caption": "CISA Action Due", "type": "timestamp", "group": "entity_specific"}, "cisa_required_action": {"caption": "CISA Required Action", "type": "string", "group": "entity_specific"}, "epss": {"caption": "EPSS", "type": "float", "ui_visibility": false, "group": "entity_specific"}, "epss_percentile": {"caption": "EPSS Percentile", "type": "float", "group": "entity_specific"}, "cwe": {"caption": "CWE", "type": "list<string>", "data_structure": "list", "group": "entity_specific"}, "cpe": {"caption": "CPE", "type": "list<string>", "data_structure": "list", "ui_visibility": false, "group": "entity_specific"}, "normalized_severity": {"caption": "Severity", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "vendor_id": {"caption": "Vendor ID", "type": "list<string>", "data_structure": "list", "group": "source_specific"}, "found_in_organisation": {"caption": "Found In Organisation", "type": "string", "ui_visibility": false, "group": "source_specific"}, "qualys_pci_flag": {"caption": "Qualys PCI Flag", "type": "string", "ui_visibility": false, "group": "source_specific"}, "qualys_consequence": {"caption": "Qualys Consequence", "type": "string", "ui_visibility": false, "group": "source_specific"}, "qualys_category": {"caption": "Qualys Category", "type": "string", "ui_visibility": false, "group": "source_specific"}, "qualys_threat_intel": {"caption": "<PERSON><PERSON>ys Threat <PERSON>", "type": "string", "data_structure": "list", "ui_visibility": false, "group": "source_specific"}, "bugtraq_id": {"caption": "Bugtraq ID", "type": "list<string>", "data_structure": "list", "ui_visibility": false, "group": "source_specific"}, "nvd_status": {"caption": "NVD Status", "type": "string", "group": "source_specific"}, "tenable_vulnerability_mitigation_status": {"caption": "Tenable Vulnerability Mitigation Status", "type": "boolean", "ui_visibility": false, "group": "source_specific"}, "tenablesc_accept_risk": {"caption": "Tenable Risk Accepted", "type": "boolean", "ui_visibility": false, "group": "source_specific"}, "tenablesc_recast_risk": {"caption": "Tenable Risk Recast", "type": "boolean", "ui_visibility": false, "group": "source_specific"}, "tenablesc_stig_severity": {"caption": "Tenable STIG Severity", "type": "string", "ui_visibility": false, "group": "source_specific"}, "tenable_exploit_ease": {"caption": "Tenable Exploit Ease", "type": "string", "ui_visibility": false, "group": "source_specific"}, "tenablesc_risk_factor": {"caption": "Tenable Risk Factor", "type": "string", "ui_visibility": false, "group": "source_specific"}, "has_cisa_kev_exploit": {"caption": "CISA Known Exploited Vulnerabilities", "type": "boolean", "ui_visibility": false, "group": "source_specific"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Vulnerability": {}, "VRA": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "vulnerability"}, "Cloud Container": {"caption": "Cloud Container", "description": "Cloud Container entity is a portable unit of software that packages up code and all its dependencies which helps the application runs quickly and reliably from one computing environment to another.", "attributes": {"p_id": {"caption": "Entity ID", "type": "string", "ui_visibility": true, "group": "common"}, "display_label": {"caption": "Display Label", "type": "string", "ui_visibility": true, "group": "common"}, "class": {"caption": "Class", "type": "string", "ui_visibility": true, "group": "common"}, "type": {"caption": "Type", "type": "string", "ui_visibility": true, "group": "common"}, "origin": {"caption": "Origin", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "count_of_origin": {"caption": "Origin (Count)", "type": "int", "group": "common"}, "data_source_subset_name": {"caption": "Data Feed", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "common"}, "first_found_date": {"caption": "First Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "first_seen_date": {"caption": "First Seen", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_updated_date": {"caption": "Last Updated", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_found_date": {"caption": "Last Found", "type": "timestamp", "ui_visibility": true, "group": "common"}, "last_active_date": {"caption": "Last Active", "type": "timestamp", "ui_visibility": true, "group": "common"}, "activity_status": {"caption": "Activity Status", "type": "string", "ui_visibility": true, "group": "common"}, "lifetime": {"caption": "Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recent_activity": {"caption": "Recent Activity", "type": "int", "ui_visibility": true, "group": "common"}, "observed_lifetime": {"caption": "Observed Lifetime", "type": "int", "ui_visibility": true, "group": "common"}, "recency": {"caption": "Recency", "type": "int", "ui_visibility": true, "group": "common"}, "description": {"caption": "Description", "type": "string", "ui_visibility": true, "group": "common"}, "business_unit": {"caption": "Business Unit", "type": "string", "ui_visibility": true, "group": "common"}, "location_country": {"caption": "Location Country", "type": "string", "ui_visibility": true, "group": "common"}, "location_city": {"caption": "Location City", "type": "string", "ui_visibility": true, "group": "common"}, "department": {"caption": "Department", "type": "string", "ui_visibility": true, "group": "common"}, "fragments": {"caption": "Fragments", "type": "int", "ui_visibility": true, "group": "common"}, "last_updated_attrs": {"caption": "Last Updated Attributes", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "common"}, "inactivity_period": {"caption": "Inactivity Period", "type": "int", "ui_visibility": false, "group": "common"}, "cloud_inactivity_period": {"caption": "Cloud Inactivity Period", "type": "string", "ui_visibility": false, "group": "common"}, "origin_contribution_type": {"caption": "Origin Contribution Type", "type": "string", "group": "enrichment"}, "cloud_provider": {"caption": "Cloud Provider", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "account_id": {"caption": "Cloud Account ID", "type": "list<string>", "ui_visibility": true, "group": "entity_specific"}, "region": {"caption": "Cloud Region", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "resource_id": {"caption": "Cloud Resource ID", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "resource_name": {"caption": "Cloud Resource Name", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "native_type": {"caption": "Cloud Native Type", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "operational_state": {"caption": "Cloud Last Known Operational State", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "environment": {"caption": "Environment", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "zone_availability": {"caption": "Cloud Zone Availability", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "encryption_status": {"caption": "Encryption Status", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "private_ip": {"caption": "Private IP", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "properties": {"caption": "Properties", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "entity_specific"}, "billing_tag": {"caption": "Billing Tag", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "active_operational_date": {"caption": "Active Operational Date", "type": "timestamp", "group": "entity_specific"}, "is_accessible_from_internet": {"caption": "Internet Exposure", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "has_high_privileges": {"caption": "Has High Privileges", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "has_admin_privileges": {"caption": "<PERSON> <PERSON><PERSON> Privileges", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "open_to_all_internet": {"caption": "Open to All Internet", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "vulnerability_last_observed_date": {"caption": "Vulnerability Last Observed", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "aci_cluster_name": {"caption": "ACI Cluster Name", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "container_hostname": {"caption": "Container Hostname", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "container_cpu": {"caption": "Container CPU", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "container_ecs_task_arn": {"caption": "Container ECS Task ARN", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "is_container_serverless": {"caption": "Is Container Serverless", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "is_container_privileged": {"caption": "Is Container Privileged", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "is_container_tty_enabled": {"caption": "Is Container TTY Enabled", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "is_container_root": {"caption": "Container Runs as Root", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "container_memory": {"caption": "Container Memory", "type": "float", "ui_visibility": true, "group": "entity_specific"}, "container_application": {"caption": "Container Application", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "container_volume_name": {"caption": "Container Volume Name", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "container_port_protocol": {"caption": "Container Port Protocol", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "entity_specific"}, "container_environment_variables": {"caption": "Container Environment Variables", "type": "string", "ui_visibility": false, "group": "entity_specific"}, "container_standard_input": {"caption": "Container Standard Input", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "container_standard_input_once": {"caption": "Container Standard Input Once", "type": "boolean", "ui_visibility": true, "group": "entity_specific"}, "container_privilege_escalation": {"caption": "Container Privilege Escalation", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "kubernetes_flavor": {"caption": "Kubernetes Flavor", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "container_read_only_root_filesystem": {"caption": "Container Read Only Root FS", "type": "string", "ui_visibility": true, "group": "entity_specific"}, "azure_resource_created_date": {"caption": "Azure Resource Created Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_system_data": {"caption": "Azure System Data", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "azure_tags": {"caption": "Azure Tags", "type": "string", "data_structure": "struct", "ui_visibility": true, "group": "source_specific"}, "azure_resource_last_modified_date": {"caption": "Azure Resource Modified", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "azure_region": {"caption": "Azure Region", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_availability_zone": {"caption": "Azure Availability Zone", "type": "int", "ui_visibility": true, "group": "source_specific"}, "azure_acr_admin_user_enabled": {"caption": "Azure ACR Admin User Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_acr_quarantine_policy_status": {"caption": "Azure ACR Quarantine Policy Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_trust_policy_status": {"caption": "Azure ACR Trust Policy Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_retention_policy_status": {"caption": "Azure ACR Retention Policy Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_export_policy_status": {"caption": "Azure ACR Export Policy Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_ad_auth_as_arm_policy": {"caption": "Azure ACR ARM Token Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_soft_delete_policy": {"caption": "Azure ACR Soft Delete Policy", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_encryption_status": {"caption": "Azure ACR Encryption Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_data_endpoint_enabled": {"caption": "Azure ACR Data Endpoint Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_acr_network_rule_bypass_options": {"caption": "Azure ACR Network Rule Bypass Options", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_zone_redundency": {"caption": "Azure ACR Zone Redundancy", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_acr_anonymous_pull_enabled": {"caption": "Azure ACR Anonymous Pull Enabled", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_container_image": {"caption": "Azure Container Image", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_container_port": {"caption": "Azure Container Port", "type": "list<int>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "azure_container_state": {"caption": "Azure Container State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_container_host_port": {"caption": "Azure Container Host Port", "type": "string", "ui_visibility": true, "group": "source_specific"}, "azure_container_runs_as_root": {"caption": "Azure Container Runs as Root", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_container_privileged": {"caption": "Azure Container is Privileged", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "azure_aks_finish_date": {"caption": "Azure AKS Finish Date", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "azure_aci_finish_date": {"caption": "Azure ACI Finish Date", "type": "int", "ui_visibility": false, "group": "source_specific"}, "aws_tags": {"caption": "AWS Tags", "type": "string", "data_structure": "struct", "ui_visibility": false, "group": "source_specific"}, "aws_tag": {"caption": "AWS Tag", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_resource_created_date": {"caption": "AWS Resource Created Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_resource_configuration_change_date": {"caption": "AWS Resource Configuration Change Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "aws_region": {"caption": "AWS Region", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_availability_zone": {"caption": "AWS Availability Zone", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_eks_finish_date": {"caption": "AWS EKS Finish Date", "type": "timestamp", "ui_visibility": false, "group": "source_specific"}, "aws_ecr_encryption_type": {"caption": "AWS ECR Encryption Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ecr_scan_on_push": {"caption": "AWS ECR Scan On Push", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_ecr_repository_name": {"caption": "AWS ECR Repository Name", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_ecr_image_tag_mutability": {"caption": "AWS ECR Image Tag Immutability", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_image": {"caption": "AWS Container Image", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_status": {"caption": "AWS Container Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_health_status": {"caption": "AWS Container Health Status", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_runtime_id": {"caption": "AWS Container Runtime ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_port": {"caption": "AWS Container Port", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "aws_container_host_port": {"caption": "AWS Container Host Port", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_privileged": {"caption": "AWS Container is Privileged", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "aws_container_network_interface_attc_id": {"caption": "AWS Container Network Interface Attachment ID", "type": "list<string>", "ui_visibility": true, "group": "source_specific"}, "aws_container_launch_type": {"caption": "AWS Container Launch Type", "type": "string", "ui_visibility": true, "group": "source_specific"}, "aws_container_runs_as_root": {"caption": "AWS Container Runs as Root", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "vulnerability_first_observed": {"caption": "Vulnerability First Observed", "type": "timestamp", "ui_visibility": true, "group": "entity_specific"}, "wiz_id": {"caption": "Wiz ID", "type": "string", "ui_visibility": true, "group": "source_specific"}, "wiz_operational_state": {"caption": "Wiz Operational State", "type": "string", "ui_visibility": true, "group": "source_specific"}, "wiz_modified_date": {"caption": "Wiz Modified Date", "type": "timestamp", "ui_visibility": true, "group": "source_specific"}, "wiz_is_default_security_context": {"caption": "Is Default Security Context As Per Wiz", "type": "boolean", "ui_visibility": true, "group": "source_specific"}, "wiz_active_services": {"caption": "Wiz Active Services", "type": "string", "data_structure": "list", "ui_visibility": true, "group": "source_specific"}, "has_vulnerability_finding_count": {"caption": "Count of Vulnerability Findings", "type": "int", "group": "enrichment"}, "has_open_vulnerability_finding_count": {"caption": "Count of Open Vulnerability Findings", "type": "int", "group": "enrichment"}, "asset_exposure": {"caption": "Asset Exposure", "type": "float", "ui_visibility": true, "group": "enrichment"}, "rollup_consequence": {"caption": "Rollup Consequence", "type": "list<string>", "data_structure": "list", "ui_visibility": true, "group": "enrichment"}, "data_source_dev": {"caption": "Source of the Data", "type": "list<string>", "data_structure": "list", "group": "common"}}, "dashboard_identifier": {"EI": {}, "Cloud Container": {}, "VRA": {}, "VRA Risk Index": {}, "CCM": {}}, "is_available_in_datalake": true, "graph_reference_name": "cloud_container"}}}