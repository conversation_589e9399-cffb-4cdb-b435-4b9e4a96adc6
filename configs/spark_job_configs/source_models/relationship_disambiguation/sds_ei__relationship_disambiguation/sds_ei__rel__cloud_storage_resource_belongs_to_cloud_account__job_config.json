{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_resource_details__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_resource_details__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_blob_storage__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_blob_storage__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_file_storage__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_file_storage__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_queue_storage__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_queue_storage__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_table_storage__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__azure_table_storage__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__aws_sh_findings__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_assessment__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_assessment__cloud_storage_resource_belongs_to_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__cloud_storage_resource_belongs_to_cloud_account", "name": "sds_ei__rel__ms_azure_security_center_alerts__cloud_storage_resource_belongs_to_cloud_account"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__cloud_storage_resource_belongs_to_cloud_account"}, "entity": {"name": "Cloud Storage Resource Belongs To Cloud Account"}}