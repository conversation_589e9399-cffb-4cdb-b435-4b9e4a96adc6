{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_ec2_instance__host_corresponds_to_cloud", "name": "sds_ei__rel__aws_ec2_instance__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__host_corresponds_to_cloud", "name": "sds_ei__rel__aws_resource_details__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_emr_ec2_instance__host_corresponds_to_cloud", "name": "sds_ei__rel__aws_emr_ec2_instance__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__host_corresponds_to_cloud", "name": "sds_ei__rel__aws_sh_findings__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_resource_details__host_corresponds_to_cloud", "name": "sds_ei__rel__azure_resource_details__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_virtual_machine__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_azure_virtual_machine__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_security_assessments__host_corresponds_to_cloud", "name": "sds_ei__rel__azure_security_assessments__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_vdi__host_corresponds_to_cloud", "name": "sds_ei__rel__azure_vdi__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_security_center_alerts__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_azure_security_center_alerts__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender_device_list__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_defender_device_list__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender_device_software_inventory__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_defender_device_software_inventory__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender_events__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_defender_events__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender_tvm__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_defender_tvm__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender_tvm_software_vulnerabilities_delta__host_corresponds_to_cloud", "name": "sds_ei__rel__ms_defender_tvm_software_vulnerabilities_delta__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_list__host_corresponds_to_cloud", "name": "sds_ei__rel__qualys_host_list__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_summary__host_corresponds_to_cloud", "name": "sds_ei__rel__qualys_host_summary__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__qualys_host_vulnerability__host_corresponds_to_cloud", "name": "sds_ei__rel__qualys_host_vulnerability__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz_cloud_resource__host_corresponds_to_cloud", "name": "sds_ei__rel__wiz_cloud_resource__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz_vulnerability_finding__host_corresponds_to_cloud", "name": "sds_ei__rel__wiz_vulnerability_finding__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__tenable_io_assets__host_corresponds_to_cloud", "name": "sds_ei__rel__tenable_io_assets__host_corresponds_to_cloud"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__tenable_io_vulnerabilities__host_corresponds_to_cloud", "name": "sds_ei__rel__tenable_io_vulnerabilities__host_corresponds_to_cloud"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__host_corresponds_to_cloud"}, "entity": {"name": "Host Corresponds To Cloud"}}