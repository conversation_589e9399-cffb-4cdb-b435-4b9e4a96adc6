{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender__vulnerability_finding_on_application", "name": "sds_ei__rel__ms_defender__vulnerability_finding_on_application"}, {"tableName": "<%EI_SCHEMA_NAME%>.dummy", "name": "dummy"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_name", "software_vendor", "software_version"], "statusField": "current_status"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin", "vendor_status", "ms_recommended_update", "ms_recommended_update_id", "path_details"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_application"}, "entity": {"name": "Vulnerability Finding On Application"}}