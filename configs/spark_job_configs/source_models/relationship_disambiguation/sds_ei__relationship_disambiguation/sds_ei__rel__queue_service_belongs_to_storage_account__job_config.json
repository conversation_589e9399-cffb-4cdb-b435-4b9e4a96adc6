{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_queue_storage__queue_service_belongs_to_storage_account", "name": "sds_ei__rel__azure_queue_storage__queue_service_belongs_to_storage_account"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__queue_service_belongs_to_storage_account"}, "entity": {"name": "Queue Service Belongs To Storage Account"}}