{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_ad_users__identity_has_account", "name": "sds_ei__rel__ms_azure_ad_users__identity_has_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_ad_sign_in__identity_has_account", "name": "sds_ei__rel__ms_azure_ad_sign_in__identity_has_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws__cloudtrail__identity_has_account", "name": "sds_ei__rel__aws__cloudtrail__identity_has_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__active_directory__identity_has_account", "name": "sds_ei__rel__active_directory__identity_has_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws__iam_security_center_permission_set_assignment_identity_has_account", "name": "sds_ei__rel__aws__iam_security_center_permission_set_assignment_identity_has_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws__iam_list_users__identity_has_account", "name": "sds_ei__rel__aws__iam_list_users__identity_has_account"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__identity_has_account"}, "entity": {"name": "Identity Has Account"}}