{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_aci_container__container_belongs_to_container_group", "name": "sds_ei__rel__azure_aci_container__container_belongs_to_container_group"}], "disambiguation": {"disambiguationGrouping": {"type": "Union"}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__container_belongs_to_container_group"}, "entity": {"name": "Container Belongs To Container Group"}}