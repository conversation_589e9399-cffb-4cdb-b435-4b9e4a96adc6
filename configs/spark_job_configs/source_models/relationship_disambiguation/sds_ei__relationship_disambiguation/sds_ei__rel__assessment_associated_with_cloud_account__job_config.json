{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_regulatory_compliance_assessments__assessment_associated_with_cloud_account", "name": "sds_ei__rel__azure_regulatory_compliance_assessments__assessment_associated_with_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__assessment_associated_with_cloud_account", "name": "sds_ei__rel__aws_sh_findings__assessment_associated_with_cloud_account"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_security_assessments__assessment_associated_with_cloud_account", "name": "sds_ei__rel__azure_security_assessments__assessment_associated_with_cloud_account"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__assessment_associated_with_cloud_account"}, "entity": {"name": "Assessment Associated With Cloud Account"}}