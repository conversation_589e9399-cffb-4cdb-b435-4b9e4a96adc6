{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure", "name": "sds_ei__cloud_compute__ms_azure"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender", "name": "sds_ei__cloud_compute__ms_defender"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__qualys", "name": "sds_ei__cloud_compute__qualys"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws", "name": "sds_ei__cloud_compute__aws"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__wiz", "name": "sds_ei__cloud_compute__wiz"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__tenable_io", "name": "sds_ei__cloud_compute__tenable_io"}], "disambiguation": {"candidateKeys": ["resource_id", {"name": "resource_name", "exceptionFilter": "type = 'Virtual Machine Extensions' OR type='EKS Addon' OR type='Container' OR type='MapReduce Cluster' OR type='Compute Instance Group'"}, "cloud_instance_id"], "confidenceMatrix": ["sds_ei__cloud_compute__ms_azure", "sds_ei__cloud_compute__aws", "sds_ei__cloud_compute__wiz", "sds_ei__cloud_compute__ms_defender", "sds_ei__cloud_compute__qualys", "sds_ei__cloud_compute__tenable_io"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "vm_status", "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "edr_status", "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "defender_exposure_level", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_block_malicious_code_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "fw_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "rollingUpFields": ["origin", "defender_threat_name", "defender_action_type", "qualys_detection_method", "defender_detection_method", "private_ip", "defender_id", "vm_tracking_method", "vm_product"], "aggregation": [{"field": "login_last_date", "function": "max"}, {"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "azure_resource_created_date", "function": "min"}, {"field": "aws_resource_created_date", "function": "min"}, {"field": "aws_instance_launch_date", "function": "min"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "tenable_io_last_authenticated_scan_date", "function": "max"}, {"field": "tenable_io_last_scan_date", "function": "max"}, {"field": "tenable_io_asset_updated_at", "function": "max"}, {"field": "tenable_io_asset_aws_terminated_date", "function": "max"}, {"field": "tenable_io_onboarding_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "vm_onboarding_status", "confidenceMatrix": ["true", "false"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "is_ephemeral", "confidenceMatrix": ["true", "false"]}]}}, "derivedProperties": [{"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)>=0 THEN size(defender_threat_name) END"}, {"colName": "edr_threat_count", "colExpr": "defender_threat_count"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute"}, "entity": {"name": "Cloud Compute"}}