{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__application__ms_defender_device_tvm_software_vulnerabilities_delta__software_name", "name": "sds_ei__application__ms_defender_device_tvm_software_vulnerabilities_delta__software_name"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__application__ms_defender_device_software_inventory__software_name", "name": "sds_ei__application__ms_defender_device_software_inventory__software_name"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__application__ms_defender_device_software_inventory__software_name", "sds_ei__application__ms_defender_device_tvm_software_vulnerabilities_delta__software_name"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}], "rollingUpFields": [], "valueConfidence": []}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__application__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Defender"}, "entity": {"name": "Application"}}