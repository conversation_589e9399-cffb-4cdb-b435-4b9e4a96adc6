{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_vdi__session_host_azure_vm_id", "name": "sds_ei__host__ms_azure_vdi__session_host_azure_vm_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_resource_list__resource_id", "name": "sds_ei__host__ms_azure_resource_list__resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_resource_details__id", "name": "sds_ei__host__ms_azure_resource_details__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_assessments__cloud_resource_id", "name": "sds_ei__host__ms_azure_assessments__cloud_resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_security_alerts__cloud_resource_id", "name": "sds_ei__host__ms_azure_security_alerts__cloud_resource_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_virtual_machine__resource_id", "name": "sds_ei__host__ms_azure_virtual_machine__resource_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__host__ms_azure_resource_details__id", "sds_ei__host__ms_azure_virtual_machine__resource_id", "sds_ei__host__ms_azure_resource_list__resource_id", "sds_ei__host__ms_azure_vdi__session_host_azure_vm_id", "sds_ei__host__ms_azure_assessments__cloud_resource_id", "sds_ei__host__ms_azure_security_alerts__cloud_resource_id"], "excludeValues": ["Unknown", "Other", "-", "No Data"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "azure_resource_created_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "operational_state", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Azure'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "MS Azure"}, "entity": {"name": "Host"}}