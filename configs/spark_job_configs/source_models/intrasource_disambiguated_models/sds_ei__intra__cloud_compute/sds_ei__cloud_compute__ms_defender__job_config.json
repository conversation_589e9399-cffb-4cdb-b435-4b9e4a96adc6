{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_list__id", "name": "sds_ei__cloud_compute__ms_defender_device_list__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_software_inventory__device_id", "name": "sds_ei__cloud_compute__ms_defender_device_software_inventory__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_tvm__device_id", "name": "sds_ei__cloud_compute__ms_defender_tvm__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_events__device_id", "name": "sds_ei__cloud_compute__ms_defender_device_events__device_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_tvm_software_vulnerabilities_delta__device_id", "name": "sds_ei__cloud_compute__ms_defender_device_tvm_software_vulnerabilities_delta__device_id"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__cloud_compute__ms_defender_device_list__id", "sds_ei__cloud_compute__ms_defender_device_software_inventory__device_id", "sds_ei__cloud_compute__ms_defender_tvm__device_id", "sds_ei__cloud_compute__ms_defender_device_events__device_id", "sds_ei__cloud_compute__ms_defender_device_tvm_software_vulnerabilities_delta__device_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "defender_exposure_level", "confidenceMatrix": ["last_active_date"]}, {"field": "av_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "av_block_malicious_code_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}, {"field": "fw_status", "temporalConfidenceMatrix": ["last_active_date"], "confidenceMatrix": [], "restrictToConfidenceMatrix": true}], "aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}, {"field": "edr_last_scan_date", "function": "max"}, {"field": "av_last_scan_date", "function": "max"}, {"field": "av_signature_update_date", "function": "max"}, {"field": "defender_onboarding_date", "function": "min"}, {"field": "vm_last_scan_date", "function": "max"}], "rollingUpFields": ["defender_threat_name", "defender_action_type"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "defender_detection_method", "confidenceMatrix": ["Defender Agent", "Network Scan"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'MS Defender'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "filter": "resource_id IS NOT NULL"}, "dataSource": {"name": "MS Defender"}, "entity": {"name": "Cloud Compute"}}