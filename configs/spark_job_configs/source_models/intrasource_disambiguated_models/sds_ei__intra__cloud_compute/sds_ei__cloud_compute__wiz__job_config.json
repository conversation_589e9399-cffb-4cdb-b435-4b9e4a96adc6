{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__wiz_cloud_resource__id", "name": "sds_ei__cloud_compute__wiz_cloud_resource__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__wiz_vulnerability_findings__vulnerableassetid", "name": "sds_ei__cloud_compute__wiz_vulnerability_findings__vulnerableassetid"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__cloud_compute__wiz_cloud_resource__id", "sds_ei__cloud_compute__wiz_vulnerability_findings__vulnerableassetid"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'Wiz'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__wiz", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "Wiz"}, "entity": {"name": "Cloud Compute"}}