{"inventoryModelInput": [{"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_organizations__id", "name": "sds_ei__cloud_account__aws_organizations__id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_resource_details__account_id", "name": "sds_ei__cloud_account__aws_resource_details__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_ec2_instance__owner_id", "name": "sds_ei__cloud_account__aws_ec2_instance__owner_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_ecs_task_container__account_id", "name": "sds_ei__cloud_account__aws_ecs_task_container__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_eks_container__account_id", "name": "sds_ei__cloud_account__aws_eks_container__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_emr_cluster__account_id", "name": "sds_ei__cloud_account__aws_emr_cluster__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_emr_ec2_fleet__account_id", "name": "sds_ei__cloud_account__aws_emr_ec2_fleet__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_emr_ec2_instance__account_id", "name": "sds_ei__cloud_account__aws_emr_ec2_instance__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_sh_finding__account_id", "name": "sds_ei__cloud_account__aws_sh_finding__account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_sh_finding__resource_account_id", "name": "sds_ei__cloud_account__aws_sh_finding__resource_account_id"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_describe_standards_controls__standards_control_arn", "name": "sds_ei__cloud_account__aws_describe_standards_controls__standards_control_arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_get_enabled_standards__standards_subscription_arn", "name": "sds_ei__cloud_account__aws_get_enabled_standards__standards_subscription_arn"}, {"path": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_list_standards_control_associations__security_control_arn", "name": "sds_ei__cloud_account__aws_list_standards_control_associations__security_control_arn"}], "disambiguation": {"candidateKeys": ["primary_key"], "confidenceMatrix": ["sds_ei__cloud_account__aws_organizations__id", "sds_ei__cloud_account__aws_resource_details__account_id", "sds_ei__cloud_account__aws_ec2_instance__owner_id", "sds_ei__cloud_account__aws_ecs_task_container__account_id", "sds_ei__cloud_account__aws_eks_container__account_id", "sds_ei__cloud_account__aws_emr_cluster__account_id", "sds_ei__cloud_account__aws_emr_ec2_fleet__account_id", "sds_ei__cloud_account__aws_emr_ec2_instance__account_id", "sds_ei__cloud_account__aws_sh_finding__account_id", "sds_ei__cloud_account__aws_describe_standards_controls__standards_control_arn", "sds_ei__cloud_account__aws_get_enabled_standards__standards_subscription_arn", "sds_ei__cloud_account__aws_list_standards_control_associations__security_control_arn", "sds_ei__cloud_account__aws_sh_finding__resource_account_id"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"aggregation": [{"field": "last_active_date", "function": "max"}, {"field": "first_seen_date", "function": "min"}], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}, {"field": "account_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "derivedProperties": [{"colName": "origin", "colExpr": "'AWS'"}, {"colName": "primary_key", "colExpr": "primary_key__resolved"}, {"colName": "properties", "colExpr": "cast(null as string)"}], "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver"}, "dataSource": {"name": "AWS"}, "entity": {"name": "Cloud Account"}}