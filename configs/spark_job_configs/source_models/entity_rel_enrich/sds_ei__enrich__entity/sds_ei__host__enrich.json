{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__host", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__host__enrich"}, "countEnriches": [{"colName": "has_vulnerability_finding_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host"}, {"colName": "has_open_vulnerability_finding_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "filter": "current_status='Open'"}, {"colName": "has_identity_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__host_has_identity"}, {"colName": "owned_person_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__person_owns_host"}, {"colName": "hosting_application_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__application_running_on_host"}, {"colName": "associated_cloud_resource_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__host_corresponds_to_cloud"}], "entity": {"name": "Host"}}