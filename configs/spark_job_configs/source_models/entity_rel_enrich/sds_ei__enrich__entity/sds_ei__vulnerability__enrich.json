{"entityTableName": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability", "output": {"outputTableName": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__enrich"}, "countEnriches": [{"colName": "associated_hosts_with_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host"}, {"colName": "associated_hosts_with_open_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_host", "filter": "current_status='Open'"}, {"colName": "associated_cloud_compute_with_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_compute"}, {"colName": "associated_cloud_compute_with_open_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_compute", "filter": "current_status='Open'"}, {"colName": "associated_cloud_container_with_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_container"}, {"colName": "associated_cloud_container_with_open_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_cloud_container", "filter": "current_status='Open'"}, {"colName": "associated_application_with_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_application"}, {"colName": "associated_application_with_open_findings_count", "relationshipTableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__vulnerability_finding_on_application", "filter": "current_status='Open'"}], "derivedProperties": [{"colName": "activity_status", "colExpr": "CASE WHEN class='Vulnerability' THEN CASE WHEN greatest(associated_hosts_with_open_findings_count,associated_cloud_compute_with_open_findings_count,associated_cloud_container_with_open_findings_count,associated_application_with_open_findings_count,0) >= 1 THEN 'Active' ELSE 'Inactive' END ELSE activity_status END"}], "entity": {"name": "Vulnerability"}}