{"name": "Vulnerability Finding On Application", "origin": "MS Defender", "inverseRelationshipName": "Application Has Vulnerability Finding", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta", "origin": "MS Defender", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__vulnerability__ms_defender_device_tvm_software_vulnerabilities_delta__cve_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__application__ms_defender_device_tvm_software_vulnerabilities_delta__software_name"]}], "optionalAttributes": [{"name": "ms_recommended_update", "exp": "recommendedSecurityUpdate", "occurrence": "LAST"}, {"name": "ms_recommended_update_id", "exp": "recommendedSecurityUpdateId", "occurrence": "LAST"}, {"name": "current_status", "exp": "CASE WHEN lower(status)='fixed' OR (datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))/1000))) > 180  )  THEN 'Closed' ELSE 'Open' END", "occurrence": "LAST"}, {"name": "initial_status", "exp": "'Open'", "occurrence": "LAST"}, {"name": "vendor_status", "exp": "status", "occurrence": "LAST"}, {"name": "inactivity_period", "exp": "180", "occurrence": "LAST"}, {"name": "software_name", "exp": "softwareName", "occurrence": "LAST"}, {"name": "software_vendor", "exp": "softwareVendor", "occurrence": "LAST"}, {"name": "software_version", "exp": "softwareVersion", "occurrence": "LAST"}, {"name": "relationship_first_seen_date", "exp": "first_seen_timestamp_epoch", "occurrence": "FIRST"}, {"name": "relationship_last_seen_date", "exp": "UNIX_MILLIS(TIMESTAMP(to_timestamp(lastSeenTimestamp)))", "occurrence": "LAST"}, {"name": "path_details", "exp": "diskPaths", "occurrence": "LAST"}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id", "softwareName", "softwareVendor", "softwareVersion", "first_seen_timestamp_epoch"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender__vulnerability_finding_on_application", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_defender__vulnerability_finding_on_application"}, "relationship": {"rel_name": "Vulnerability Finding On Application", "name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability"}}