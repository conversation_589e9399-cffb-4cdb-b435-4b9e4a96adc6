{"name": "Host Corresponds To Cloud", "origin": "MS Defender", "inverseRelationshipName": "Cloud Corresponds To Host", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_software", "origin": "MS Defender", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__ms_defender_device_software_inventory__device_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__ms_defender_device_software_inventory__device_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_defender_device_software_inventory__host_corresponds_to_cloud", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_defender_device_software_inventory__host_corresponds_to_cloud"}, "relationship": {"rel_name": "Host Corresponds To Cloud", "name": "Microsoft Defender For Endpoint", "feedName": "Device Software Inventory"}}