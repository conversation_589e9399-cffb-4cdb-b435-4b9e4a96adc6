{"name": "Host Corresponds To Cloud", "origin": "Wiz", "inverseRelationshipName": "Cloud Corresponds To Host", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.wiz__cloud_resources", "origin": "Wiz", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__wiz_cloud_resource__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__wiz_cloud_resource__id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz_cloud_resource__host_corresponds_to_cloud", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__wiz_cloud_resource__host_corresponds_to_cloud"}, "relationship": {"rel_name": "Host Corresponds To Cloud", "name": "Wiz", "feedName": "Cloud Resource"}}