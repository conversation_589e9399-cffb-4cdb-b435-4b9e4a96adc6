{"name": "Host Corresponds To Cloud", "origin": "Tenable.io", "inverseRelationshipName": "Cloud Corresponds To Host", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.tenable_io_asset", "origin": "Tenable.io", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__tenable_io_assets__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__tenable_io_assets__id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__tenable_io_assets__host_corresponds_to_cloud", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__tenable_io_assets__host_corresponds_to_cloud"}, "relationship": {"rel_name": "Host Corresponds To Cloud", "name": "Tenable.io", "feedName": "Assets"}}