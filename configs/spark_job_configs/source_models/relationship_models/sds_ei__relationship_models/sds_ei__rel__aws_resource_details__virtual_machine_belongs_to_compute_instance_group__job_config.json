{"name": "Virtual Machine Belongs To Compute Instance Group", "origin": "AWS", "inverseRelationshipName": "Compute Instance Group Has Virtual Machine", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__resource_details", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__aws_resource_details__arn"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__aws_resource_details__aws_autoscaling_group_key"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_resource_details__virtual_machine_belongs_to_compute_instance_group", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_resource_details__virtual_machine_belongs_to_compute_instance_group"}, "relationship": {"rel_name": "Virtual Machine Belongs To Compute Instance Group", "name": "AWS", "feedName": "Resource Details"}}