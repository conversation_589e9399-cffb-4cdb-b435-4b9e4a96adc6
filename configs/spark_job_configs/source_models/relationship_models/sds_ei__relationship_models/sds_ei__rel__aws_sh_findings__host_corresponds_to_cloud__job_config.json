{"name": "Host Corresponds To Cloud", "origin": "AWS", "inverseRelationshipName": "Cloud Corresponds To Host", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings", "origin": "AWS", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__host__aws_sh_findings__cloud_instance_id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__aws_sh_findings__resource_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__host_corresponds_to_cloud", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__aws_sh_findings__host_corresponds_to_cloud"}, "relationship": {"rel_name": "Host Corresponds To Cloud", "name": "AWS", "feedName": "SH Findings"}}