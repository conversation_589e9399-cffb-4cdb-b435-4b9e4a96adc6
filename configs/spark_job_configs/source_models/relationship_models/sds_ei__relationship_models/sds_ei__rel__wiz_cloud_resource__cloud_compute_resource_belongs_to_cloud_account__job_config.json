{"name": "Cloud Compute Resource Belongs To Cloud Account", "origin": "Wiz", "inverseRelationshipName": "Cloud Account Has Cloud Compute Resource", "intraSourcePath": "<%EI_SCHEMA_NAME%>.sds_ei_intra_source_resolver", "inputSourceInfo": [{"sdmPath": "<%SRDM_SCHEMA_NAME%>.wiz__cloud_resources", "origin": "Wiz", "sourceMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_compute__wiz_cloud_resource__id"], "targetMappingInfo": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__cloud_account__wiz_cloud_resource__account_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__wiz_cloud_resource__cloud_compute_resource_belongs_to_cloud_account", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__wiz_cloud_resource__cloud_compute_resource_belongs_to_cloud_account"}, "relationship": {"rel_name": "Cloud Compute Resource Belongs To Cloud Account", "name": "Wiz", "feedName": "Cloud Resource"}}