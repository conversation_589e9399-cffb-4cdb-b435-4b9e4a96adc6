{"primaryKey": "lower(name)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(type) IN ('microsoft.security/regulatorycompliancestandards')", "temporaryProperties": [{"colName": "temp_statuses", "colExpr": "collect_set(properties.state) over (partition by lower(name), DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000)) ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_id", "colExpr": "upper(name)"}], "commonProperties": [{"colName": "type", "colExpr": "'MS Azure Regulatory Compliance Standard'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "regexp_replace(temp_id, '[/\\\\-]', ' ')"}, {"colName": "status", "colExpr": "CASE WHEN array_contains(temp_statuses, 'Failed') OR array_contains(temp_statuses, 'Passed') THEN 'Enabled' ELSE 'Disabled' END"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__azure_regulatory_compliance_standards__name"}