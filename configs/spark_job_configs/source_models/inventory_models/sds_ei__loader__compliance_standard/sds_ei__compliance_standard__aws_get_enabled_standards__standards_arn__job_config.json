{"primaryKey": "lower(REGEXP_EXTRACT(StandardsArn, '\\/(.*)$'))", "origin": "'AWS Get Enabled Standards'", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(REGEXP_EXTRACT(StandardsArn, '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Standard'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "regexp_replace(temp_id, '[/\\\\-]', ' ')"}, {"colName": "status", "colExpr": "CASE WHEN LOWER(StandardsStatus) IN ('ready', 'pending') THEN 'Enabled' ELSE 'Disabled' END"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Get Enabled Standards", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_get_enabled_standards"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__aws_get_enabled_standards__standards_arn"}