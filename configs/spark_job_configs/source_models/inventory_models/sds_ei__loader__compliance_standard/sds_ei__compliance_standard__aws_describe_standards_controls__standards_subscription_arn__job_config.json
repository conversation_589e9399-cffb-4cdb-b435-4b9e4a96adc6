{"primaryKey": "lower(REGEXP_EXTRACT(StandardsSubscriptionArn, '\\/(.*)$'))", "origin": "'AWS Describe Standards Controls'", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(REGEXP_EXTRACT(StandardsSubscriptionArn, '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Standard'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "regexp_replace(temp_id, '[/\\\\-]', ' ')"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Describe Standards Controls", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_describe_standards_controls"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__aws_describe_standards_controls__standards_subscription_arn"}