{"primaryKey": "lower(REGEXP_EXTRACT(id,'regulatoryComplianceStandards/([^/]+)/'))", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols')", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(REGEXP_EXTRACT(id,'regulatoryComplianceStandards/([^/]+)/'))"}], "commonProperties": [{"colName": "type", "colExpr": "'MS Azure Regulatory Compliance Standard'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "regexp_replace(temp_id, '[/\\\\-]', ' ')"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__azure_regulatory_compliance_controls__standard_id"}