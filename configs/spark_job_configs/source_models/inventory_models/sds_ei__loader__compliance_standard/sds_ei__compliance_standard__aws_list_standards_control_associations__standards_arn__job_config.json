{"primaryKey": "lower(REGEXP_EXTRACT(StandardsArn, '\\/(.*)$'))", "origin": "'AWS List Standards Control Associations'", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(REGEXP_EXTRACT(StandardsArn, '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Standard'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "regexp_replace(temp_id, '[/\\\\-]', ' ')"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "List Standards Control Associations", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_list_standards_control_associations"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__compliance_standard__aws_list_standards_control_associations__standards_arn"}