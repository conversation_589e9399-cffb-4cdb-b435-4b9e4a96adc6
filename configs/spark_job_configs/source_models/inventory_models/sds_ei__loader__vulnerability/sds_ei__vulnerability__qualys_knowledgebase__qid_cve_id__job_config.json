{"primaryKey": "temp_primary_key", "origin": "'Qualys <PERSON>'", "temporaryProperties": [{"colName": "temp_cve_id", "colExpr": "explode_outer(from_json(CVE_LIST.CVE, 'array<struct<ID:string, URL:string>>').ID)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_primary_key", "colExpr": "concat_ws('||', CAST(QID AS STRING), temp_cve_id)"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN LOWER(VULN_TYPE) LIKE '%potential vulnerability%' THEN 'Weakness' WHEN LOWER(VULN_TYPE) LIKE '%vulnerability%'  THEN 'Vulnerability' WHEN LOWER(VULN_TYPE) LIKE '%information gathered%' THEN 'Informational' END"}, {"colName": "description", "colExpr": "regexp_replace(DIAGNOSIS, '<[^>]++>', ' ')"}, {"colName": "first_seen_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "title", "colExpr": "TITLE"}, {"colName": "cve_id", "colExpr": "temp_cve_id"}, {"colName": "v30_score", "colExpr": "CVSS_V3.BASE"}, {"colName": "temporal_cvss_score", "colExpr": "CVSS_V3.TEMPORAL"}, {"colName": "v30_vector", "colExpr": "CVSS_V3.VECTOR_STRING"}, {"colName": "v30_severity", "colExpr": "CASE WHEN v30_score>9 THEN 'Critical' WHEN v30_score>7 THEN 'High' WHEN v30_score>4 THEN 'Medium' WHEN v30_score>0 THEN 'Low' WHEN v30_score=0 THEN 'None' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "software_list", "colExpr": "CAST(SOFTWARE_LIST AS STRING)"}, {"colName": "patch_available", "colExpr": "CASE WHEN (CAST(PATCHABLE AS STRING))==1 THEN true WHEN (CAST(PATCHABLE AS STRING))==0 THEN false ELSE null END"}, {"colName": "exploit_available", "colExpr": "case when lower(DISCOVERY.ADDITIONAL_INFO) like '%exploit%available%' then true else false end"}, {"colName": "recommendation", "colExpr": "CAST(regexp_replace(SOLUTION, '<[^>]++>', ' ') AS STRING)"}, {"colName": "published_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(PUBLISHED_DATETIME)))"}, {"colName": "last_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LAST_SERVICE_MODIFICATION_DATETIME)))"}, {"colName": "vendor_severity", "colExpr": "INITCAP(CAST(SEVERITY_LEVEL AS INTEGER))"}], "sourceSpecificProperties": [{"colName": "vendor_id", "colExpr": "CAST(QID AS STRING)"}, {"colName": "found_in_organisation", "colExpr": "false", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "qualys_pci_flag", "colExpr": "CAST(PCI_FLAG AS STRING)"}, {"colName": "qualys_consequence", "colExpr": "CONSEQUENCE"}, {"colName": "qualys_category", "colExpr": "CATEGORY"}, {"colName": "bugtraq_id", "colExpr": "REGEXP_EXTRACT_ALL(CAST(BUGTRAQ_LIST AS STRING), '\"ID\"\\\\s*:\\\\s*([0-9]+)')"}, {"colName": "qualys_threat_intel", "colExpr": "TO_JSON(TRANSFORM(from_json(CAST(THREAT_INTELLIGENCE.THREAT_INTEL AS STRING),'ARRAY<STRUCT<id:STRING,value:String>>'), s -> CASE  WHEN s.id = 1 THEN 'Zero Day' WHEN s.id = 2 THEN 'Exploit_Public' WHEN s.id = 3 THEN 'Active_Attacks' WHEN s.id = 4 THEN 'High_Lateral_Movement' WHEN s.id = 5 THEN 'Easy_Exploit'  WHEN s.id = 6 THEN 'High_Data_Loss' WHEN s.id = 7 THEN 'Denial_of_Service' WHEN s.id = 8 THEN 'No_Patch' WHEN s.id = 9 THEN 'Malware' WHEN s.id = 10 THEN 'Exploit_Kit' WHEN s.id = 11 THEN 'Wormable'  WHEN s.id = 12 THEN 'Predicted_High_Risk' WHEN s.id = 13 THEN 'Privilege_Escalation' WHEN s.id = 14 THEN 'Unauthenticated_Exploitation' WHEN s.id = 15 THEN 'Remote_Code_Execution' WHEN s.id = 16 THEN 'Ransomware'  WHEN s.id = 17 THEN 'Solorigate_Sunburst' WHEN s.id = 18 THEN 'Cisa_Known_Exploited_Vulns' END))"}], "dataSource": {"name": "Qualys", "feedName": "KnowledgeBase", "srdm": "<%SRDM_SCHEMA_NAME%>.qualys__knowledge_base"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__qualys_knowledgebase__qid_cve_id"}