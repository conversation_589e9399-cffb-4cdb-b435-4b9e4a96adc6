{"origin": "'CISA Vulnrichment'", "primaryKey": "cveMetadata.cveId ", "commonProperties": [{"colExpr": "filter(containers.cna.descriptions,v->v.lang in ('en-US','en'))[0].value", "colName": "description"}], "entitySpecificProperties": [{"colExpr": "INITCAP(containers.cna.metrics.cvssV4_0.baseSeverity)", "colName": "v40_severity"}, {"colExpr": "containers.cna.metrics.cvssV4_0.baseScore", "colName": "v40_score"}, {"colExpr": "COALESCE (CASE WHEN (flatten(containers.cna.affected.cpes) = Array() or flatten(containers.cna.affected.cpes)=Array(null)) THEN NULL ELSE flatten(containers.cna.affected.cpes) END, CASE WHEN (flatten( transform ( FILTER (containers.adp, x -> x.affected.cpes IS NOT NULL ),x -> flatten(x.affected.cpes)))=Array() or flatten( transform ( FILTER (containers.adp, x -> x.affected.cpes IS NOT NULL ),x -> flatten(x.affected.cpes))) = Array(null)) then NULL else flatten( transform ( FILTER (containers.adp, x -> x.affected.cpes IS NOT NULL ),x -> flatten(x.affected.cpes))) END)", "colName": "cpe"}, {"colExpr": "COALESCE(CASE WHEN flatten(transform ( FILTER (containers.cna.problemTypes, x -> x.descriptions.cweId IS NOT NULL and x.descriptions.cweId!=Array(null)),x -> x.descriptions.cweId)) = Array() THEN NULL ELSE flatten(transform ( FILTER (containers.cna.problemTypes, x -> x.descriptions.cweId IS NOT NULL and x.descriptions.cweId!=Array(null)),x -> x.descriptions.cweId)) END,CASE WHEN flatten(transform ( FILTER (adp_problem_types, x -> x.descriptions.cweId IS NOT NULL and x.descriptions.cweId!=Array(null)),x -> x.descriptions.cweId)) = Array() THEN NULL ELSE flatten(transform ( FILTER (adp_problem_types, x -> x.descriptions.cweId IS NOT NULL and x.descriptions.cweId!=Array(null)),x -> x.descriptions.cweId)) END)", "colName": "cwe"}, {"colExpr": "containers.cna.title", "colName": "title"}, {"colExpr": "cveMetadata.cveId", "colName": "cve_id"}, {"colExpr": "containers.cna.metrics.cvssV2_0.baseScore", "colName": "v2_score"}, {"colExpr": "containers.cna.metrics.cvssV2_0.vectorString", "colName": "v2_vector"}, {"colExpr": "containers.cna.metrics.cvssV3_0.baseScore", "colName": "v30_score"}, {"colExpr": "coalesce(containers.cna.metrics.cvssV3_1.baseScore,transform ( FILTER (containers.adp, x -> x.metrics.cvssV3_1.baseScore IS NOT NULL ),x -> x.metrics.cvssV3_1.baseScore)[0])", "colName": "v31_score"}, {"colExpr": "containers.cna.metrics.cvssV3_0.vectorString", "colName": "v30_vector"}, {"colExpr": "coalesce(containers.cna.metrics.cvssV3_1.vectorString,transform ( FILTER (containers.adp, x -> x.metrics.cvssV3_1.vectorString IS NOT NULL ),x -> x.metrics.cvssV3_1.vectorString)[0])", "colName": "v31_vector"}, {"colExpr": "INITCAP(containers.cna.metrics.cvssV3_0.baseSeverity)", "colName": "v30_severity"}, {"colExpr": "INITCAP(coalesce(containers.cna.metrics.cvssV3_1.baseSeverity,transform ( FILTER (containers.adp, x -> x.metrics.cvssV3_1.baseSeverity IS NOT NULL ),x -> x.metrics.cvssV3_1.baseSeverity)[0]))", "colName": "v31_severity"}, {"colExpr": "CASE WHEN size(containers.adp.title)>1 THEN coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE to_json(transform(containers.cna.affected, x->(x.product, x.vendor,x.versions.version))) END, to_json(transform(filter(containers.adp,x->x.title in ('CISA ADP Vulnrichment'))[0].affected, x->(x.product, x.vendor,x.versions.version)))) ELSE coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE to_json(transform(containers.cna.affected, x->(x.product, x.vendor,x.versions.version))) END, to_json(transform(containers.adp[0].affected, x->(x.product, x.vendor,x.versions.version)))) END", "colName": "software_list"}, {"colExpr": " UNIX_TIMESTAMP(to_timestamp(cvemetadata.datePublished))*1000", "colName": "published_date"}, {"colExpr": " UNIX_TIMESTAMP(to_timestamp(cveMetadata.dateUpdated))*1000", "colName": "last_modified_date"}, {"colExpr": "filter(containers.cna.solutions,v->v.lang in ('en-US','en'))[0].value", "colName": "recommendation"}, {"colExpr": "containers.cna.exploits[0].supportingMedia[0]['base64']", "colName": "exploit_available"}, {"colExpr": "containers.cna.metrics.cvssV3_1.temporalScore", "colName": "temporal_cvss_score"}], "sourceSpecificProperties": [], "temporaryProperties": [{"colExpr": "transform ( FILTER (containers.adp, x -> x.problemTypes IS NOT NULL and x.problemTypes!=Array(null)),x -> x.problemTypes)[0]", "colName": "adp_problem_types"}], "dataSource": {"name": "CISA", "srdm": "<%SRDM_SCHEMA_NAME%>.cisa__vulnrichment", "feedName": "Vulnrichment"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__cisa_vulnrichment__cve_id"}