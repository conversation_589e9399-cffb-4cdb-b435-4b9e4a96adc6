{"primaryKey": "cve", "origin": "'EPSS'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cve_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "epss", "colExpr": "CAST(epss AS FLOAT)"}, {"colName": "epss_percentile", "colExpr": "CAST(percentile AS FLOAT)"}], "sourceSpecificProperties": [{"colName": "found_in_organisation", "colExpr": "false", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "EPSS", "feedName": "Score API", "srdm": "<%SRDM_SCHEMA_NAME%>.open_data__epss_data"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__vulnerability__open_data_epss_data"}