{"primaryKey": "LOWER(id)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(type) IN ('microsoft.security/assessments')", "commonProperties": [{"colName": "type", "colExpr": "'Cloud'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "trim(regexp_replace(regexp_replace(properties.metadata.description, '<[^>]++>', ' '), '\\s{2,}', ' '))", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "finding_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_title", "colExpr": "LTRIM(case when regexp_like(properties.displayName,'\\\\[.*\\\\](.*)') then REGEXP_EXTRACT(properties.displayName, '\\\\[.*\\\\](.*)',1) else properties.displayName end)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties", "colExpr": "properties"}, {"colName": "finding_type", "colExpr": "'MS Azure Security Assessment'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_sub_type", "colExpr": "regexp_replace(properties.metadata.securityIssue, '(?<=[a-z])([A-Z])', ' $1')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_status", "colExpr": "case when LOWER(vendor_status) in ('healthy','not applicable','offbypolicy') then 'Closed' when LOWER(vendor_status) not in ('healthy','not applicable','offbypolicy') and activity_status='Inactive' then 'Closed' else 'Open' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_severity", "colExpr": "case when lower(properties.metadata.severity) in ('informational','low','medium') then INITCAP(properties.metadata.severity) when lower(properties.metadata.severity) = 'high' then 'Critical' else 'Other' end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_detection_source", "colExpr": "properties.metadata.managementProvider", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "affected_resource_id", "colExpr": "CASE WHEN REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'/providers/[^/]+/(.+)') = '' THEN COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id) ELSE REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'/providers/[^/]+/(.+)') END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "affected_resource_name", "colExpr": "case when affected_resource_id is not null then regexp_extract(affected_resource_id,'[\\/:]+([^\\/:]+)$') else account_id  end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "compliance_status", "colExpr": "case when lower(properties.status.code) = 'unhealthy' then 'Failed' when lower(properties.status.code) = 'healthy' then 'Passed' when lower(properties.status.code) = 'notapplicable' then 'Not Applicable' else INITCAP(properties.status.code) end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(id, '/([a-zA-Z0-9-]+-+[a-zA-Z0-9-]+)/')"}, {"colName": "workflow_status", "colExpr": "case when lower(properties.status.code)= 'notapplicable' and (lower(properties.status.cause)='exempt' OR lower(properties.status.cause)= 'offbypolicy') then 'Suppressed' when lower(properties.status.code)='unhealthy' then 'Unresolved' else 'Resolved' end"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "techniques", "colExpr": "properties.metadata.techniques", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "threats", "colExpr": "properties.metadata.threats", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tactics", "colExpr": "properties.metadata.tactics", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "categories", "colExpr": "properties.metadata.categories", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_assessment", "colExpr": "name"}], "sourceSpecificProperties": [{"colName": "vendor_status", "colExpr": "case when LOWER(properties.status.code) = 'notapplicable' then 'Not Applicable' else INITCAP(properties.status.code) END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_assessment_cause", "colExpr": "properties.status.cause", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vendor_severity", "colExpr": "properties.metadata.severity", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "affected_resource_type", "colExpr": "case when properties.resourceDetails.ResourceType IS NOT NULL THEN REGEXP_REPLACE(REGEXP_EXTRACT(properties.resourceDetails.ResourceType, '([^/]+)$'), '[.]', '') WHEN REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'\\\\/providers\\\\/[^\\\\/]+\\\\/([^\\\\/]+)\\\\/') = '' THEN REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'\\\\/([^\\\\/]+)\\\\/') ELSE REGEXP_EXTRACT(COALESCE(properties.resourceDetails.ResourceId,properties.resourceDetails.Id),'\\\\/providers\\\\/[^\\\\/]+\\\\/([^\\\\/]+)\\\\/') END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_policy_initiative_name", "colExpr": "properties.statusPerInitiative.policyInitiativeName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_policy_definition_id", "colExpr": "properties.metadata.policyDefinitionId", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_assessment_type", "colExpr": "properties.metadata.assessmentType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_user_impact", "colExpr": "properties.metadata.userImpact", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_implementation_effort", "colExpr": "properties.metadata.implementationEffort", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__finding__ms_azure_security_assessment__id"}