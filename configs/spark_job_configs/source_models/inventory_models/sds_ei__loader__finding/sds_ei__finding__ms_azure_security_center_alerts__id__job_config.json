{"primaryKey": "LOWER(id)", "origin": "'MS Azure Security Center Alerts'", "commonProperties": [{"colName": "type", "colExpr": "'Cloud'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "finding_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_title", "colExpr": "properties.alertDisplayName"}, {"colName": "finding_type", "colExpr": "'MS Azure Security Center Alerts'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_sub_type", "colExpr": "regexp_replace(properties.alertType, '(?<=[a-z])([A-Z])', ' $1')"}, {"colName": "finding_status", "colExpr": "case when lower(vendor_status) in ('dismissed','resolved','suppressed') then 'Closed' when lower(vendor_status) not in ('dismissed','resolved','suppressed') and activity_status='Inactive' then 'Closed' Else 'Open' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_severity", "colExpr": "case when lower(properties.severity) in ('informational','low','medium') then INITCAP(properties.severity) when lower(properties.severity) = 'high' then 'Critical' else 'Other' end"}, {"colName": "finding_detection_source", "colExpr": "properties.productName"}, {"colName": "affected_resource_id", "colExpr": "CONCAT_WS(',',filter(properties.resourceIdentifiers.azureResourceId, element -> element IS NOT NULL))"}, {"colName": "affected_resource_name", "colExpr": "case when affected_resource_id is not null then regexp_extract(affected_resource_id,'[\\/:]+([^\\/:]+)$') else account_id  end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_resolved_date", "colExpr": "case when finding_status = 'Closed' then IFNULL(last_updated_attrs.finding_status.last_changed.updated_at,last_active_date) else NULL end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(id, '/([a-zA-Z0-9-]+-+[a-zA-Z0-9-]+)/')"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "vendor_status", "colExpr": "INITCAP(properties.status)"}, {"colName": "vendor_severity", "colExpr": "INITCAP(properties.severity)"}, {"colName": "affected_resource_type", "colExpr": "case when REGEXP_EXTRACT(ARRAY_MIN(TRANSFORM(FILTER(properties.resourceIdentifiers, x -> x.type = 'AzureResource'),x -> x.azureResourceId )),'\\/providers\\/[^\\/]+\\/(.+)') = '' then SPLIT(ARRAY_MIN(TRANSFORM(FILTER(properties.resourceIdentifiers, x -> x.type = 'AzureResource'),x -> x.azureResourceId )),'/')[1] else SPLIT(REGEXP_EXTRACT(ARRAY_MIN(TRANSFORM(FILTER(properties.resourceIdentifiers, x -> x.type = 'AzureResource'),x -> x.azureResourceId )),'\\/providers\\/[^\\/]+\\/(.+)'),'/')[0] end"}, {"colName": "azure_alert_product_component_name", "colExpr": "properties.productComponentName"}, {"colName": "azure_alert_first_seen_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.startTimeUtc)))"}, {"colName": "azure_alert_last_seen_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.endTimeUtc)))"}, {"colName": "azure_alert_intent", "colExpr": "properties.intent"}, {"colName": "azure_alert_name", "colExpr": "name"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Center Alerts", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_center_alerts"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__finding__ms_azure_security_center_alerts__id"}