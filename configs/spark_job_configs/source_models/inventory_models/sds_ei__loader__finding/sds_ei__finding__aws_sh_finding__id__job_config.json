{"primaryKey": "lower(Id)", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_stand", "colExpr": "transform(Compliance.AssociatedStandards,x->REGEXP_EXTRACT(upper(x.StandardsId), '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'Cloud'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "aws_finding_first_seen_time", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "aws_finding_last_seen_time", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "finding_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_title", "colExpr": "title"}, {"colName": "finding_type", "colExpr": "'AWS SH Findings'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_sub_type", "colExpr": "REGEXP_EXTRACT(CONCAT_WS(', ', types), '\\/(.*)$')"}, {"colName": "finding_status", "colExpr": "case when lower(vendor_status) in ('suppressed','resolved') then 'Closed' when lower(vendor_status) not in ('suppressed','resolved') and activity_status='Inactive' then 'Closed' Else 'Open' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "workflow_status", "colExpr": "case when lower(Workflow.Status) IN ('new','notified') then 'Unresolved' else INITCAP(Workflow.Status) end"}, {"colName": "finding_severity", "colExpr": "INITCAP(Severity.Label)"}, {"colName": "finding_detection_source", "colExpr": "productName"}, {"colName": "affected_resource_id", "colExpr": "cast(Resources.Id as array<string>)[0]"}, {"colName": "affected_resource_name", "colExpr": "case when length(regexp_extract(affected_resource_id,'[\\/:]+([^\\/:]+)$'))<2 then regexp_extract(affected_resource_id,'[\\/:]+([^\\/]+)$') else regexp_extract(affected_resource_id,'[\\/:]+([^\\/:]+)$') end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "finding_resolved_date", "colExpr": "case when finding_status = 'Closed' then IFNULL(last_updated_attrs.finding_status.last_changed.updated_at,last_active_date) else NULL end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_standard", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}, {"colName": "compliance_status", "colExpr": "case when LOWER(compliance.status) = 'not_available' then 'Not Applicable' else INITCAP(compliance.status) END"}, {"colName": "account_id", "colExpr": "CAST(AwsAccountId as string)"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_control", "colExpr": "upper(Compliance.SecurityControlId)"}, {"colName": "associated_assessment", "colExpr": "ProductFields.RelatedAWSResources__0__name"}], "sourceSpecificProperties": [{"colName": "vendor_status", "colExpr": "INITCAP(Workflow.Status)"}, {"colName": "vendor_severity", "colExpr": "INITCAP(Severity.Label)"}, {"colName": "affected_resource_type", "colExpr": "cast(Resources.Type[0] as string)"}, {"colName": "aws_finding_first_seen_time", "colExpr": "case when firstobservedat is null then UNIX_MILLIS(TIMESTAMP(to_timestamp(createdat))) else UNIX_MILLIS(TIMESTAMP(to_timestamp(firstobservedat))) end"}, {"colName": "aws_finding_last_seen_time", "colExpr": "case when lastobservedat is null then UNIX_MILLIS(TIMESTAMP(to_timestamp(event_timestamp_ts))) else UNIX_MILLIS(TIMESTAMP(to_timestamp(lastobservedat))) end"}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__finding__aws_sh_finding__id"}