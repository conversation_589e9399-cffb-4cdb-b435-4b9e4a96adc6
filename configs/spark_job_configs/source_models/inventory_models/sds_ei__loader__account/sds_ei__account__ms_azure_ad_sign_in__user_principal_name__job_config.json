{"primaryKey": "temp_primary_key_upn", "origin": "'MS Azure AD Sign-in Logs'", "temporaryProperties": [{"colName": "temp_primary_key_upn", "colExpr": "CONCAT_WS(':',userPrincipalName,'Azure AD')"}, {"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}, {"colName": "temp_failed_count", "colExpr": "case when status.errorCode != 0 then 1 else null end"}, {"colName": "temp_location", "colExpr": "case when location.countryOrRegion in ('AD',\"Andorra\") then \"Andorra\" when location.countryOrRegion in ('AE',\"United Arab Emirates\") then \"United Arab Emirates\" when location.countryOrRegion in ('AF',\"Afghanistan\") then \"Afghanistan\" when location.countryOrRegion in ('AG',\"Antigua and Barbuda\") then \"Antigua and Barbuda\" when location.countryOrRegion in ('AI',\"Anguilla\") then \"Anguilla\" when location.countryOrRegion in ('AL',\"Albania\") then \"Albania\" when location.countryOrRegion in ('AM',\"Armenia\") then \"Armenia\" when location.countryOrRegion in ('AO',\"Angola\") then \"Angola\" when location.countryOrRegion in ('AR',\"Argentina\") then \"Argentina\" when location.countryOrRegion in ('AS',\"American Samoa\") then \"American Samoa\" when location.countryOrRegion in ('AT',\"Austria\") then \"Austria\" when location.countryOrRegion in ('AU',\"Australia\") then \"Australia\" when location.countryOrRegion in ('AW',\"Aruba\") then \"Aruba\" when location.countryOrRegion in ('AX',\"Aland Islands\") then \"Aland Islands\" when location.countryOrRegion in ('AZ',\"Azerbaijan\") then \"Azerbaijan\" when location.countryOrRegion in ('BA',\"Bosnia and Herzegovina\") then \"Bosnia and Herzegovina\" when location.countryOrRegion in ('BB',\"Barbados\") then \"Barbados\" when location.countryOrRegion in ('BD',\"Bangladesh\") then \"Bangladesh\" when location.countryOrRegion in ('BE',\"Belgium\") then \"Belgium\" when location.countryOrRegion in ('BF',\"Burkina Faso\") then \"Burkina Faso\" when location.countryOrRegion in ('BG',\"Bulgaria\") then \"Bulgaria\" when location.countryOrRegion in ('BH',\"Bahrain\") then \"Bahrain\" when location.countryOrRegion in ('BI',\"Burundi\") then \"Burundi\" when location.countryOrRegion in ('BJ',\"Benin\") then \"Benin\" when location.countryOrRegion in ('BL',\"Saint Barthelemy\") then \"Saint Barthelemy\" when location.countryOrRegion in ('BM',\"Bermuda\") then \"Bermuda\" when location.countryOrRegion in ('BN',\"Brunei Darussalam\") then \"Brunei Darussalam\" when location.countryOrRegion in ('BO',\"Bolivia (Plurinational State of)\") then \"Bolivia (Plurinational State of)\" when location.countryOrRegion in ('BQ',\"Bonaire, Sint Eustatius and Saba\") then \"Bonaire, Sint Eustatius and Saba\" when location.countryOrRegion in ('BR',\"Brazil\") then \"Brazil\" when location.countryOrRegion in ('BS',\"Bahamas\") then \"Bahamas\" when location.countryOrRegion in ('BT',\"Bhutan\") then \"Bhutan\" when location.countryOrRegion in ('BW',\"Botswana\") then \"Botswana\" when location.countryOrRegion in ('BY',\"Belarus\") then \"Belarus\" when location.countryOrRegion in ('BZ',\"Belize\") then \"Belize\" when location.countryOrRegion in ('CA',\"Canada\") then \"Canada\" when location.countryOrRegion in ('CD',\"Congo (Democratic Republic of the)\") then \"Congo (Democratic Republic of the)\" when location.countryOrRegion in ('CF',\"Central African Republic\") then \"Central African Republic\" when location.countryOrRegion in ('CG',\"Congo\") then \"Congo\" when location.countryOrRegion in ('CH',\"Switzerland\") then \"Switzerland\" when location.countryOrRegion in ('CI',\"Cote D'ivoire\") then \"Cote D'ivoire\" when location.countryOrRegion in ('CK',\"Cook Islands\") then \"Cook Islands\" when location.countryOrRegion in ('CL',\"Chile\") then \"Chile\" when location.countryOrRegion in ('CM',\"Cameroon\") then \"Cameroon\" when location.countryOrRegion in ('CN',\"China\") then \"China\" when location.countryOrRegion in ('CO',\"Colombia\") then \"Colombia\" when location.countryOrRegion in ('CR',\"Costa Rica\") then \"Costa Rica\" when location.countryOrRegion in ('CU',\"Cuba\") then \"Cuba\" when location.countryOrRegion in ('CV',\"Cabo Verde\") then \"Cabo Verde\" when location.countryOrRegion in ('CW',\"Curacao\") then \"Curacao\" when location.countryOrRegion in ('CY',\"Cyprus\") then \"Cyprus\" when location.countryOrRegion in ('CZ',\"Czechia\") then \"Czechia\" when location.countryOrRegion in ('DE',\"Germany\") then \"Germany\" when location.countryOrRegion in ('DJ',\"Djibouti\") then \"Djibouti\" when location.countryOrRegion in ('DK',\"Denmark\") then \"Denmark\" when location.countryOrRegion in ('DM',\"Dominica\") then \"Dominica\" when location.countryOrRegion in ('DO',\"Dominican Republic\") then \"Dominican Republic\" when location.countryOrRegion in ('DZ',\"Algeria\") then \"Algeria\" when location.countryOrRegion in ('EC',\"Ecuador\") then \"Ecuador\" when location.countryOrRegion in ('EE',\"Estonia\") then \"Estonia\" when location.countryOrRegion in ('EG',\"Egypt\") then \"Egypt\" when location.countryOrRegion in ('ER',\"Eritrea\") then \"Eritrea\" when location.countryOrRegion in ('ES',\"Spain\") then \"Spain\" when location.countryOrRegion in ('ET',\"Ethiopia\") then \"Ethiopia\" when location.countryOrRegion in ('FI',\"Finland\") then \"Finland\" when location.countryOrRegion in ('FJ',\"Fiji\") then \"Fiji\" when location.countryOrRegion in ('FK',\"Falkland Islands (Malvinas)\") then \"Falkland Islands (Malvinas)\" when location.countryOrRegion in ('FM',\"Micronesia (Federated States of)\") then \"Micronesia (Federated States of)\" when location.countryOrRegion in ('FO',\"Faroe Islands\") then \"Faroe Islands\" when location.countryOrRegion in ('FR',\"France\") then \"France\" when location.countryOrRegion in ('GA',\"Gabon\") then \"Gabon\" when location.countryOrRegion in ('GB',\"United Kingdom\",\"United Kingdom of Great Britain and Northern Ireland\") then \"United Kingdom of Great Britain and Northern Ireland\" when location.countryOrRegion in ('GD',\"Grenada\") then \"Grenada\" when location.countryOrRegion in ('GE',\"Georgia\") then \"Georgia\" when location.countryOrRegion in ('GF',\"French Guiana\") then \"French Guiana\" when location.countryOrRegion in ('GG',\"Guernsey\") then \"Guernsey\" when location.countryOrRegion in ('GH',\"Ghana\") then \"Ghana\" when location.countryOrRegion in ('GI',\"Gibraltar\") then \"Gibraltar\" when location.countryOrRegion in ('GL',\"Greenland\") then \"Greenland\" when location.countryOrRegion in ('GM',\"Gambia\") then \"Gambia\" when location.countryOrRegion in ('GN',\"Guinea\") then \"Guinea\" when location.countryOrRegion in ('GP',\"Guadeloupe\") then \"Guadeloupe\" when location.countryOrRegion in ('GQ',\"Equatorial Guinea\") then \"Equatorial Guinea\" when location.countryOrRegion in ('GR',\"Greece\") then \"Greece\" when location.countryOrRegion in ('GS',\"South Georgia and The South Sandwich Islands\") then \"South Georgia and The South Sandwich Islands\" when location.countryOrRegion in ('GT',\"Guatemala\") then \"Guatemala\" when location.countryOrRegion in ('GU',\"Guam\") then \"Guam\" when location.countryOrRegion in ('GW',\"Guinea-Bissau\") then \"Guinea-Bissau\" when location.countryOrRegion in ('GY',\"Guyana\") then \"Guyana\" when location.countryOrRegion in ('HK',\"Hong Kong\") then \"Hong Kong\" when location.countryOrRegion in ('HN',\"Honduras\") then \"Honduras\" when location.countryOrRegion in ('HR',\"Croatia\") then \"Croatia\" when location.countryOrRegion in ('HT',\"Haiti\") then \"Haiti\" when location.countryOrRegion in ('HU',\"Hungary\") then \"Hungary\" when location.countryOrRegion in ('ID',\"Indonesia\") then \"Indonesia\" when location.countryOrRegion in ('IE',\"Ireland\") then \"Ireland\" when location.countryOrRegion in ('IL',\"Israel\") then \"Israel\" when location.countryOrRegion in ('IM',\"Isle of Man\") then \"Isle of Man\" when location.countryOrRegion in ('IN',\"India\") then \"India\" when location.countryOrRegion in ('IO',\"British Indian Ocean Territory\") then \"British Indian Ocean Territory\" when location.countryOrRegion in ('IQ',\"Iraq\") then \"Iraq\" when location.countryOrRegion in ('IR',\"Iran (Islamic Republic of)\") then \"Iran (Islamic Republic of)\" when location.countryOrRegion in ('IS',\"Iceland\") then \"Iceland\" when location.countryOrRegion in ('IT',\"Italy\") then \"Italy\" when location.countryOrRegion in ('JE',\"Jersey\") then \"Jersey\" when location.countryOrRegion in ('JM',\"Jamaica\") then \"Jamaica\" when location.countryOrRegion in ('JO',\"Jordan\") then \"Jordan\" when location.countryOrRegion in ('JP',\"Japan\") then \"Japan\" when location.countryOrRegion in ('KE',\"Kenya\") then \"Kenya\" when location.countryOrRegion in ('KG',\"Kyrgyzstan\") then \"Kyrgyzstan\" when location.countryOrRegion in ('KH',\"Cambodia\") then \"Cambodia\" when location.countryOrRegion in ('KI',\"Kiribati\") then \"Kiribati\" when location.countryOrRegion in ('KM',\"Comoros\") then \"Comoros\" when location.countryOrRegion in ('KN',\"Saint Kitts and Nevis\") then \"Saint Kitts and Nevis\" when location.countryOrRegion in ('KP',\"Korea (Democratic People's Republic of)\") then \"Korea (Democratic People's Republic of)\" when location.countryOrRegion in ('KR',\"Korea (Republic of)\") then \"Korea (Republic of)\" when location.countryOrRegion in ('KW',\"Kuwait\") then \"Kuwait\" when location.countryOrRegion in ('KY',\"Cayman Islands\") then \"Cayman Islands\" when location.countryOrRegion in ('KZ',\"Kazakhstan\") then \"Kazakhstan\" when location.countryOrRegion in ('LA',\"Lao People's Democratic Republic\") then \"Lao People's Democratic Republic\" when location.countryOrRegion in ('LB',\"Lebanon\") then \"Lebanon\" when location.countryOrRegion in ('LC',\"Saint Lucia\") then \"Saint Lucia\" when location.countryOrRegion in ('LI',\"Liechtenstein\") then \"Liechtenstein\" when location.countryOrRegion in ('LK',\"Sri Lanka\") then \"Sri Lanka\" when location.countryOrRegion in ('LR',\"Liberia\") then \"Liberia\" when location.countryOrRegion in ('LS',\"Lesotho\") then \"Lesotho\" when location.countryOrRegion in ('LT',\"Lithuania\") then \"Lithuania\" when location.countryOrRegion in ('LU',\"Luxembourg\") then \"Luxembourg\" when location.countryOrRegion in ('LV',\"Latvia\") then \"Latvia\" when location.countryOrRegion in ('LY',\"Libya\") then \"Libya\" when location.countryOrRegion in ('MA',\"Morocco\") then \"Morocco\" when location.countryOrRegion in ('MC',\"Monaco\") then \"Monaco\" when location.countryOrRegion in ('MD',\"Moldova (Republic of)\") then \"Moldova (Republic of)\" when location.countryOrRegion in ('ME',\"Montenegro\") then \"Montenegro\" when location.countryOrRegion in ('MF',\"Saint Martin (French Part)\") then \"Saint Martin (French Part)\" when location.countryOrRegion in ('MG',\"Madagascar\") then \"Madagascar\" when location.countryOrRegion in ('MH',\"Marshall Islands\") then \"Marshall Islands\" when location.countryOrRegion in ('MK',\"North Macedonia\") then \"North Macedonia\" when location.countryOrRegion in ('ML',\"Mali\") then \"Mali\" when location.countryOrRegion in ('MM',\"Myanmar\") then \"Myanmar\" when location.countryOrRegion in ('MN',\"Mongolia\") then \"Mongolia\" when location.countryOrRegion in ('MO',\"Macao\") then \"Macao\" when location.countryOrRegion in ('MP',\"Northern Mariana Islands\") then \"Northern Mariana Islands\" when location.countryOrRegion in ('MQ',\"Martinique\") then \"Martinique\" when location.countryOrRegion in ('MR',\"Mauritania\") then \"Mauritania\" when location.countryOrRegion in ('MS',\"Montserrat\") then \"Montserrat\" when location.countryOrRegion in ('MT',\"Malta\") then \"Malta\" when location.countryOrRegion in ('MU',\"Mauritius\") then \"Mauritius\" when location.countryOrRegion in ('MV',\"Maldives\") then \"Maldives\" when location.countryOrRegion in ('MW',\"Malawi\") then \"Malawi\" when location.countryOrRegion in ('MX',\"Mexico\") then \"Mexico\" when location.countryOrRegion in ('MY',\"Malaysia\") then \"Malaysia\" when location.countryOrRegion in ('MZ',\"Mozambique\") then \"Mozambique\" when location.countryOrRegion in ('NA',\"Namibia\") then \"Namibia\" when location.countryOrRegion in ('NC',\"New Caledonia\") then \"New Caledonia\" when location.countryOrRegion in ('NE',\"Niger\") then \"Niger\" when location.countryOrRegion in ('NF',\"Norfolk Island\") then \"Norfolk Island\" when location.countryOrRegion in ('NG',\"Nigeria\") then \"Nigeria\" when location.countryOrRegion in ('NI',\"Nicaragua\") then \"Nicaragua\" when location.countryOrRegion in ('NL',\"Netherlands\") then \"Netherlands\" when location.countryOrRegion in ('NO',\"Norway\") then \"Norway\" when location.countryOrRegion in ('NP',\"Nepal\") then \"Nepal\" when location.countryOrRegion in ('NR',\"Nauru\") then \"Nauru\" when location.countryOrRegion in ('NU',\"Niue\") then \"Niue\" when location.countryOrRegion in ('NZ',\"New Zealand\") then \"New Zealand\" when location.countryOrRegion in ('OM',\"Oman\") then \"Oman\" when location.countryOrRegion in ('PA',\"Panama\") then \"Panama\" when location.countryOrRegion in ('PE',\"Peru\") then \"Peru\" when location.countryOrRegion in ('PF',\"French Polynesia\") then \"French Polynesia\" when location.countryOrRegion in ('PG',\"Papua New Guinea\") then \"Papua New Guinea\" when location.countryOrRegion in ('PH',\"Philippines\") then \"Philippines\" when location.countryOrRegion in ('PK',\"Pakistan\") then \"Pakistan\" when location.countryOrRegion in ('PL',\"Poland\") then \"Poland\" when location.countryOrRegion in ('PM',\"Saint Pierre and Miquelon\") then \"Saint Pierre and Miquelon\" when location.countryOrRegion in ('PN',\"Pitcairn\") then \"Pitcairn\" when location.countryOrRegion in ('PR',\"Puerto Rico\") then \"Puerto Rico\" when location.countryOrRegion in ('PS',\"Palestine, State of\") then \"Palestine, State of\" when location.countryOrRegion in ('PT',\"Portugal\") then \"Portugal\" when location.countryOrRegion in ('PW',\"Palau\") then \"Palau\" when location.countryOrRegion in ('PY',\"Paraguay\") then \"Paraguay\" when location.countryOrRegion in ('QA',\"Qatar\") then \"Qatar\" when location.countryOrRegion in ('RE',\"Reunion\") then \"Reunion\" when location.countryOrRegion in ('RO',\"Romania\") then \"Romania\" when location.countryOrRegion in ('RS',\"Serbia\") then \"Serbia\" when location.countryOrRegion in ('RU',\"Russian Federation\") then \"Russian Federation\" when location.countryOrRegion in ('RW',\"Rwanda\") then \"Rwanda\" when location.countryOrRegion in ('SA',\"Saudi Arabia\") then \"Saudi Arabia\" when location.countryOrRegion in ('SB',\"Solomon Islands\") then \"Solomon Islands\" when location.countryOrRegion in ('SC',\"Seychelles\") then \"Seychelles\" when location.countryOrRegion in ('SD',\"Sudan\") then \"Sudan\" when location.countryOrRegion in ('SE',\"Sweden\") then \"Sweden\" when location.countryOrRegion in ('SG',\"Singapore\") then \"Singapore\" when location.countryOrRegion in ('SH',\"Saint Helena, Ascension and Tristan Da Cunha\") then \"Saint Helena, Ascension and Tristan Da Cunha\" when location.countryOrRegion in ('SI',\"Slovenia\") then \"Slovenia\" when location.countryOrRegion in ('SK',\"Slovakia\") then \"Slovakia\" when location.countryOrRegion in ('SL',\"Sierra Leone\") then \"Sierra Leone\" when location.countryOrRegion in ('SM',\"San Marino\") then \"San Marino\" when location.countryOrRegion in ('SN',\"Senegal\") then \"Senegal\" when location.countryOrRegion in ('SO',\"Somalia\") then \"Somalia\" when location.countryOrRegion in ('SR',\"Suriname\") then \"Suriname\" when location.countryOrRegion in ('SS',\"South Sudan\") then \"South Sudan\" when location.countryOrRegion in ('ST',\"Sao Tome and Principe\") then \"Sao Tome and Principe\" when location.countryOrRegion in ('SV',\"El Salvador\") then \"El Salvador\" when location.countryOrRegion in ('SX',\"Sint Maarten (Dutch Part)\") then \"Sint Maarten (Dutch Part)\" when location.countryOrRegion in ('SY',\"Syrian Arab Republic\") then \"Syrian Arab Republic\" when location.countryOrRegion in ('SZ',\"Eswatini\") then \"Eswatini\" when location.countryOrRegion in ('TC',\"Turks and Caicos Islands\") then \"Turks and Caicos Islands\" when location.countryOrRegion in ('TD',\"Chad\") then \"Chad\" when location.countryOrRegion in ('TG',\"Togo\") then \"Togo\" when location.countryOrRegion in ('TH',\"Thailand\") then \"Thailand\" when location.countryOrRegion in ('TJ',\"Tajikistan\") then \"Tajikistan\" when location.countryOrRegion in ('TK',\"Tokelau\") then \"Tokelau\" when location.countryOrRegion in ('TL',\"Timor-Leste\") then \"Timor-Leste\" when location.countryOrRegion in ('TM',\"Turkmenistan\") then \"Turkmenistan\" when location.countryOrRegion in ('TN',\"Tunisia\") then \"Tunisia\" when location.countryOrRegion in ('TO',\"Tonga\") then \"Tonga\" when location.countryOrRegion in ('TR',\"Turkey\") then \"Turkey\" when location.countryOrRegion in ('TT',\"Trinidad and Tobago\") then \"Trinidad and Tobago\" when location.countryOrRegion in ('TV',\"Tuvalu\") then \"Tuvalu\" when location.countryOrRegion in ('TW',\"Taiwan (Province of China)\") then \"Taiwan (Province of China)\" when location.countryOrRegion in ('TZ',\"Tanzania, United Republic of\") then \"Tanzania, United Republic of\" when location.countryOrRegion in ('UA',\"Ukraine\") then \"Ukraine\" when location.countryOrRegion in ('UG',\"Uganda\") then \"Uganda\" when location.countryOrRegion in ('UM',\"United States Minor Outlying Islands\") then \"United States Minor Outlying Islands\" when location.countryOrRegion in ('US',\"United States of America\") then \"United States of America\" when location.countryOrRegion in ('UY',\"Uruguay\") then \"Uruguay\" when location.countryOrRegion in ('UZ',\"Uzbekistan\") then \"Uzbekistan\" when location.countryOrRegion in ('VA',\"Holy See\") then \"Holy See\" when location.countryOrRegion in ('VC',\"Saint Vincent and The Grenadines\") then \"Saint Vincent and The Grenadines\" when location.countryOrRegion in ('VE',\"Venezuela (Bolivarian Republic of)\") then \"Venezuela (Bolivarian Republic of)\" when location.countryOrRegion in ('VG',\"Virgin Islands (British)\") then \"Virgin Islands (British)\" when location.countryOrRegion in ('VI',\"Virgin Islands (U.S.)\") then \"Virgin Islands (U.S.)\" when location.countryOrRegion in ('VN',\"Viet Nam\") then \"Vietnam\" when location.countryOrRegion in ('VU',\"Vanuatu\") then \"Vanuatu\" when location.countryOrRegion in ('WF',\"Wallis and Futuna\") then \"Wallis and Futuna\" when location.countryOrRegion in ('WS',\"Samoa\") then \"Samoa\" when location.countryOrRegion in ('YE',\"Yemen\") then \"Yemen\" when location.countryOrRegion in ('YT',\"Mayotte\") then \"Mayotte\" when location.countryOrRegion in ('ZA',\"South Africa\") then \"South Africa\" when location.countryOrRegion in ('ZM',\"Zambia\") then \"Zambia\" when location.countryOrRegion in ('ZW',\"Zimbabwe\") then \"Zimbabwe\" when location.countryOrRegion in ('CX',\"Christmas Island\") then \"Christmas Island\" when location.countryOrRegion in ('CC',\"Cocos (Keeling) Islands\") then \"Cocos (Keeling) Islands\" when location.countryOrRegion in ('EU',\"Europe\") then \"Europe\" when location.countryOrRegion in ('AQ',\"Antarctica\") then \"Antarctica\" when location.countryOrRegion in ('BV',\"Bouvet Island\") then \"Bouvet Island\" when location.countryOrRegion in ('TF',\"French Southern Territories\") then \"French Southern Territories\" else null end"}, {"colName": "temp_failed_attempt_location", "colExpr": "CASE WHEN status.errorCode != 0 THEN temp_location ELSE null END"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "LOWER(userPrincipalName)"}, {"colName": "source_of_identity", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership_of_identity", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "last_logged_in_location", "colExpr": "CASE WHEN status.errorCode = 0 THEN temp_location ELSE NULL END"}, {"colName": "failed_login_attempt_location", "colExpr": "CASE WHEN status.errorCode != 0 THEN temp_location ELSE NULL END"}, {"colName": "service", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "case when status.errorCode = 0 then event_timestamp_epoch else null end", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "is_mfa_enabled", "colExpr": "case when status.additionalDetails = 'MFA requirement satisfied by claim in the token' then True else Null end"}, {"colName": "account_display_name", "colExpr": "temp_primary_key_upn"}, {"colName": "failed_logon_count", "colExpr": "sum(temp_failed_count) OVER (partition by primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "failed_count_flag", "colExpr": "case when failed_logon_count > 15 then True else False end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_signin_attempt", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_failed_signin_attempt", "colExpr": "CASE WHEN status.errorCode != 0 THEN event_timestamp_epoch ELSE NULL END"}], "dataSource": {"name": "MS Azure AD", "feedName": "Sign-in Logs", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user_sign_in"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__ms_azure_ad_sign_in__user_principal_name"}