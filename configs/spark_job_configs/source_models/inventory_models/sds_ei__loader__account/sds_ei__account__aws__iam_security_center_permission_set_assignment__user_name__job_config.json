{"primaryKey": "temp_primary_key", "origin": "'AWS IAM Center'", "filterBy": "UserName is not null", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CONCAT_WS(':',lower(UserName),'AWS',AccountId)"}, {"colName": "temp_role_struct", "colExpr": "struct(Permission<PERSON>et<PERSON><PERSON>,Description)"}, {"colName": "temp_role_concat", "colExpr": "concat(temp_role_struct.PermissionSetName,' : ',temp_role_struct.Description)"}, {"colName": "temp_role_collect", "colExpr": "collect_set(temp_role_concat) OVER (partition by temp_primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_role_final", "colExpr": "concat_ws('<-->', temp_role_collect)"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "aws_account_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "lower(username)"}, {"colName": "source_of_identity", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership_of_identity", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "service", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_service", "colExpr": "'IAM Center'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "accountid"}, {"colName": "account_display_name", "colExpr": "CONCAT_WS(':',lower(username),'AWS',AccountId)"}, {"colName": "entitlement_details", "colExpr": "temp_role_final"}, {"colName": "operational_status", "colExpr": "CASE WHEN recency=0 then 'Active' Else 'Disabled' End", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "entitlement_value", "colExpr": "collect_set(PermissionSetName) OVER (partition by primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "sourceSpecificProperties": [{"colName": "aws_account_created_date", "colExpr": "CASE WHEN (createddate != '' AND (createddate IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createddate))) ELSE NULL END"}], "dataSource": {"name": "AWS", "feedName": "IAM Center", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__iam_security_center_permission_set_assignment"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__aws__iam_security_center_permission_set_assignment__user_name"}