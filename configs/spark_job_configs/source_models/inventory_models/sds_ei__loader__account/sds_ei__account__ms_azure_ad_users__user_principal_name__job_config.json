{"primaryKey": "temp_primary_key_upn", "origin": "'MS Azure AD Users'", "temporaryProperties": [{"colName": "temp_primary_key_upn", "colExpr": "CONCAT_WS(':',userPrincipalName,'Azure AD')"}, {"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "aad_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "officeLocation"}], "entitySpecificProperties": [{"colName": "operational_status", "colExpr": "CASE WHEN TRIM(accountEnabled) = 'false' THEN 'Disabled' WHEN TRIM(accountEnabled) like 'true' THEN 'Active' ELSE NULL END"}, {"colName": "account_name", "colExpr": "LOWER(userPrincipalName)"}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership_of_identity", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "source_of_identity", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "service", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_display_name", "colExpr": "temp_primary_key_upn"}, {"colName": "privilege_account", "colExpr": "cast('false' as <PERSON><PERSON><PERSON>)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aad_created_date", "colExpr": "CASE WHEN (createdDateTime != '' AND (createdDateTime IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime))) ELSE NULL END"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country = e.region"}], "dataSource": {"name": "MS Azure AD", "feedName": "Users", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__ms_azure_ad_users__user_principal_name"}