{"primaryKey": "temp_primary_key", "origin": "'AWS IAM Users'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CONCAT_WS(':',lower(UserName),'AWS',REGEXP_EXTRACT(Arn,':([0-9]+):'))"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "aws_account_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "password_last_used", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "lower(username)"}, {"colName": "source_of_identity", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership_of_identity", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "service", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_service", "colExpr": "'IAM Users'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(arn,':([0-9]+):')"}, {"colName": "account_display_name", "colExpr": "temp_primary_key"}, {"colName": "password_last_used", "colExpr": "CASE WHEN (passwordlastused != '' AND (passwordlastused IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(passwordlastused))) ELSE NULL END"}], "sourceSpecificProperties": [{"colName": "aws_account_created_date", "colExpr": "CASE WHEN (createdate != '' AND (createdate IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createdate))) ELSE NULL END"}], "dataSource": {"name": "AWS", "feedName": "IAM Users", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__iam_list_users"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__aws__iam_list_users__user_name"}