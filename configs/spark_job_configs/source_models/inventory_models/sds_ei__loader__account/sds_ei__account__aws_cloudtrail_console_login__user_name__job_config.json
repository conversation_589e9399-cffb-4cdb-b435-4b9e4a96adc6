{"primaryKey": "temp_primary_key", "filterBy": "EventName='ConsoleLogin'", "origin": "'AWS Cloudtrail ConsoleLogin'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CONCAT_WS(':',lower(Username),'AWS',CloudTrailEvent.userIdentity.accountId)"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "login_last_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "lower(Username)"}, {"colName": "login_last_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "source_of_identity", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership_of_identity", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "service", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_service", "colExpr": "'Cloudtrail'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "CloudTrailEvent.userIdentity.accountId"}, {"colName": "account_display_name", "colExpr": "temp_primary_key"}], "dataSource": {"name": "AWS", "feedName": "Cloudtrail", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__cloudtrail"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__aws_cloudtrail_console_login__user_name"}