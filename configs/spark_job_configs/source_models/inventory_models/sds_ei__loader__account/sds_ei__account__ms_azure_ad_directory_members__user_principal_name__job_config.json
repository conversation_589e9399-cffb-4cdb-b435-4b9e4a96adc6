{"primaryKey": "temp_primary_key", "origin": "'MS Azure AD Directory Members'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CONCAT_WS(':',userPrincipalName,'Azure AD')"}, {"colName": "temp_priv_role_name", "colExpr": "case when is_privileged='true' then role_name else Null end"}, {"colName": "temp_role_struct", "colExpr": "struct(role_name,role_description)"}, {"colName": "temp_role_concat", "colExpr": "concat(temp_role_struct.role_name,' : ',temp_role_struct.role_description)"}, {"colName": "temp_role_collect", "colExpr": "collect_set(temp_role_concat) OVER (partition by temp_primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_role_final", "colExpr": "concat_ws('<-->', temp_role_collect)"}, {"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "location_city", "colExpr": "officeLocation"}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "LOWER(userPrincipalName)"}, {"colName": "source_of_identity", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership_of_identity", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "entitlement_id", "colExpr": "collect_set(role_id) OVER (partition by primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "entitlement_value", "colExpr": "collect_set(role_name) OVER (partition by primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "entitlement_description", "colExpr": "role_description"}, {"colName": "entitlement_details", "colExpr": "temp_role_final"}, {"colName": "privilege_account", "colExpr": "case when is_privileged='true' then True else False end", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "privilege_roles", "colExpr": "collect_set(temp_priv_role_name) OVER (partition by temp_primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "privilege_roles_count", "colExpr": "case when privilege_roles is not null then size(privilege_roles) else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_display_name", "colExpr": "temp_primary_key"}], "dataSource": {"name": "MS Azure AD", "feedName": "Directory Members", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_directory_members"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__ms_azure_ad_directory_members__user_principal_name"}