{"primaryKey": "temp_primary_key_upn", "origin": "'MS Azure AD User Registration'", "temporaryProperties": [{"colName": "temp_primary_key_upn", "colExpr": "CONCAT_WS(':',userPrincipalName,'Azure AD')"}, {"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "LOWER(userPrincipalName)"}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership_of_identity", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "source_of_identity", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_mfa_enabled", "colExpr": "CAST(isMfaRegistered AS BOOLEAN)"}, {"colName": "default_mfa_method", "colExpr": "defaultMfaMethod"}, {"colName": "is_sspr_registered", "colExpr": "CAST(isSsprRegistered AS BOOLEAN)"}, {"colName": "service", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_display_name", "colExpr": "temp_primary_key_upn"}, {"colName": "privilege_account", "colExpr": "cast(<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>)"}, {"colName": "password_not_required", "colExpr": "CAST(isPasswordlessCapable AS BOOLEAN)"}, {"colName": "auth_methods_registered", "colExpr": "methodsRegistered"}], "sourceSpecificProperties": [], "dataSource": {"name": "MS Azure AD", "feedName": "User Registration Details", "srdm": "<%SRDM_SCHEMA_NAME%>.ms_azure_ad__user_registration_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__account__ms_azure_ad_user_registration__user_principal_name"}