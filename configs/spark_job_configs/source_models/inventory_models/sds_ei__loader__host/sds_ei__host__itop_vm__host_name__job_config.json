{"primaryKey": "temp_host_name", "filterBy": "temp_host_name IS NOT NULL", "origin": "'iTOP VMware'", "temporaryProperties": [{"colName": "temp_host_name", "colExpr": "COALESCE(sname,objects.fields.server_name)"}, {"colName": "temp_moveToproduction", "colExpr": "objects.fields.move2production"}], "commonProperties": [{"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "temp_host_name"}, {"colName": "os", "colExpr": "itop_vm_hypervisor_full_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "REGEXP_EXTRACT(itop_vm_hypervisor_full_name, '([0-9]+\\.[0-9]+\\.[0-9]+)',1)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_build", "colExpr": "regexp_extract(vname, '([0-9]+)$', 1)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,itop_class)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "itop_vmware_name", "colExpr": "COALESCE(sname,objects.fields.server_name)"}, {"colName": "itop_vm_hypervisor_obsolete_status", "colExpr": "objects.fields.obsolescence_flag"}, {"colName": "itop_vmware_obsolete_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(objects.fields.obsolescence_date,'yyyy-MM-dd')))"}, {"colName": "itop_vm_organization", "colExpr": "COALESCE(org,objects.fields.organization_name)"}, {"colName": "itop_vm_hypervisor_full_name", "colExpr": "vname"}, {"colName": "itop_vm_hypervisor_family_name", "colExpr": "fname"}, {"colName": "itop_vm_farm_name", "colExpr": "COALESCE(farm,objects.fields.farm_name)"}, {"colName": "itop_vm_server_obsolete_status", "colExpr": "server_id_obsolescence_flag"}, {"colName": "itop_vm_business_criticity", "colExpr": "COALESCE(objects.fields.business_criticity,businessCriticity)"}, {"colName": "itop_vm_move_to_production_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(temp_moveToproduction,'yyyy-MM-dd')))"}, {"colName": "itop_class", "colExpr": "objects.fields.finalclass"}], "dataSource": {"name": "iTOP", "feedName": "VMware", "srdm": "<%SRDM_SCHEMA_NAME%>.itop__vmware"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop_vm__host_name"}