{"primaryKey": "lower(properties.resourceDetails.Id)", "filterBy": "regexp_like(lower(properties.resourceDetails.Id), '/microsoft\\.compute/virtualmachines/(?!.*\\/)') AND lower(type) IN ('microsoft.security/assessments')", "origin": "'MS Azure Security Resources'", "commonProperties": [{"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "upper(regexp_extract(resource_id,'\\\\/([^\\\\/]+)$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "regexp_extract(id, '/subscriptions/([^/]+)/', 1)"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_assessments__cloud_resource_id"}