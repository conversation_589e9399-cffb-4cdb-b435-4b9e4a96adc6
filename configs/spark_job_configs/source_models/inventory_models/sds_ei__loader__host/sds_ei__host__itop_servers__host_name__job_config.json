{"primaryKey": "temp_name", "origin": "'iTOP Server'", "temporaryProperties": [{"colName": "temp_move_to_production_date", "colExpr": "move2production"}, {"colName": "temp_end_of_warranty_date", "colExpr": "end_of_warranty"}, {"colName": "temp_purchase_date", "colExpr": "purchase_date"}, {"colName": "temp_name", "colExpr": "COALESCE(server_name,name)"}], "commonProperties": [{"colName": "type", "colExpr": "'Server'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "itop_server_display_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "CONCAT(itop_server_os_family,' ',itop_server_os_version)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "itop_server_os_version", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_manufacturer", "colExpr": "itop_server_brand", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_model", "colExpr": "itop_server_model", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_serial_number", "colExpr": "itop_server_serial_number", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "itop_server_management_ip", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,itop_class,hardware_model)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "itop_server_display_name", "colExpr": "temp_name"}, {"colName": "itop_server_org_unit", "colExpr": "COALESCE(org_unit,organization_name)"}, {"colName": "itop_server_org_id", "colExpr": "org_id"}, {"colName": "itop_server_status", "colExpr": "status"}, {"colName": "itop_server_business_criticality", "colExpr": "COALESCE(business_criticity,BusinessCriticity)"}, {"colName": "itop_server_serial_number", "colExpr": "serialnumber"}, {"colName": "itop_server_location", "colExpr": "COALESCE(location_name,location)"}, {"colName": "itop_server_rack_name", "colExpr": "rack_name"}, {"colName": "itop_server_enclosure_name", "colExpr": "enclosure_name"}, {"colName": "itop_server_obsolete_status", "colExpr": "obsolescence_flag"}, {"colName": "itop_server_obsolete_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(obsolescence_date,'yyyy-MM-dd')))"}, {"colName": "itop_server_brand", "colExpr": "brand_name"}, {"colName": "itop_server_model", "colExpr": "model_name"}, {"colName": "itop_server_os_family", "colExpr": "COALESCE(osfamily_name,os_family)"}, {"colName": "itop_server_os_version", "colExpr": "COALESCE(osversion_name,os_name)"}, {"colName": "itop_server_cpu", "colExpr": "cpu"}, {"colName": "itop_server_ram", "colExpr": "ram"}, {"colName": "itop_server_management_ip", "colExpr": "managementip"}, {"colName": "itop_server_os_license", "colExpr": "oslicence_name"}, {"colName": "itop_server_asset_number", "colExpr": "asset_number"}, {"colName": "itop_server_move_to_production_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(temp_move_to_production_date,'yyyy-MM-dd')))"}, {"colName": "itop_server_end_of_warranty_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(temp_end_of_warranty_date,'yyyy-MM-dd')))"}, {"colName": "itop_server_purchase_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(TO_DATE(temp_purchase_date,'yyyy-MM-dd')))"}, {"colName": "itop_server_primary_power_source", "colExpr": "powerA_name"}, {"colName": "itop_server_secondary_power_source", "colExpr": "powerB_name"}, {"colName": "itop_class", "colExpr": "finalclass"}], "dataSource": {"name": "iTOP", "feedName": "Server", "srdm": "<%SRDM_SCHEMA_NAME%>.itop__servers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__itop_servers__host_name"}