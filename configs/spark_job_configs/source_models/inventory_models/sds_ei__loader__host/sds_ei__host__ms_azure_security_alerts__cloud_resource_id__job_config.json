{"primaryKey": "lower(temp_resource_id)", "filterBy": "regexp_like(lower(temp_resource_id), '/microsoft\\.compute/virtualmachines/(?!.*\\/)')", "origin": "'MS Azure Security Center Alerts'", "temporaryProperties": [{"colName": "temp_resource_identifiers", "colExpr": "explode_outer(properties.resourceIdentifiers)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_id", "colExpr": "temp_resource_identifiers.azureResourceId"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "lower(regexp_extract(resource_id,\"\\\\/([^\\\\/]+)$\"))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Center Alerts", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_center_alerts"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_security_alerts__cloud_resource_id"}