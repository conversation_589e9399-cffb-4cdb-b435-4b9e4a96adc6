{"primaryKey": "device_id", "origin": "'CrowdStrike Host'", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(fqdn,dns_name,host_name,ip[0],primary_key))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,crowdstrike_onboarding_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(crowdstrike_last_report_date, crowdstrike_reboot_date, crowdstrike_agent_local_date, crowdstrike_modified_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(CASE WHEN hostname RLIKE '^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$' THEN NULL WHEN hostname RLIKE '^[^.]*\\\\.[^.]*$' AND (LOWER(hostname) LIKE '%.local' OR LOWER(hostname) LIKE '%.lan' OR LOWER(hostname) LIKE '%.home' OR LOWER(hostname) LIKE '%.bbrouter') THEN split(hostname, '\\\\.')[0] WHEN hostname RLIKE '^[^.]*\\\\.[^.]*$' THEN hostname  WHEN hostname RLIKE '^[^.]*\\\\.[^.]*\\\\.[^.].*' THEN split(hostname, '\\\\.')[0] ELSE hostname END)"}, {"colName": "os", "colExpr": "case when os_version is NULL or lower(os_version) like '%unknown%' then NULL when lower(os_version) like '%other%' then 'Other' ELSE concat_ws(' ',os_version, case when not regexp_like(os_build,'(?i)other|unknown') then os_build end, case when not regexp_like(major_version,'(?i)other|unknown') then major_version end, case when not regexp_like(minor_version,'(?i)other|unknown')then minor_version end) end"}, {"colName": "os_version", "colExpr": "os_version"}, {"colName": "os_build", "colExpr": "initcap(os_build)"}, {"colName": "domain", "colExpr": "LOWER(CASE WHEN machine_domain RLIKE '^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$' THEN NULL ELSE machine_domain END)"}, {"colName": "hardware_manufacturer", "colExpr": "system_manufacturer"}, {"colName": "hardware_model", "colExpr": "system_product_name"}, {"colName": "hardware_serial_number", "colExpr": "UPPER(serial_number)"}, {"colName": "hardware_bios_manufacturer", "colExpr": "bios_manufacturer"}, {"colName": "hardware_bios_version", "colExpr": "bios_version"}, {"colName": "hardware_chassis_type", "colExpr": "chassis_type_desc"}, {"colName": "edr_product", "colExpr": "CASE WHEN crowdstrike_onboarding_status=true THEN 'CrowdStrike' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_mac_address", "colExpr": "connection_mac_address"}, {"colName": "edr_last_scan_date", "colExpr": "crowdstrike_last_report_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "crowdstrike_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "CASE WHEN lower(service_provider) LIKE '%aws%' THEN 'AWS' WHEN lower(service_provider) LIKE '%azure%' then 'Azure' ELSE 'No Data' END"}, {"colName": "account_id", "colExpr": "service_provider_account_id"}, {"colName": "cloud_instance_id", "colExpr": "instance_id"}, {"colName": "native_type", "colExpr": "CASE WHEN cloud_instance_id is not null and cloud_provider LIKE '%AWS%' THEN 'AWS EC2 Instance'  WHEN cloud_instance_id is not null and cloud_provider  LIKE '%AZURE%' THEN 'Azure Virtual Machine'  ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "crowdstrike_provision_state", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "case when cloud_instance_id is not null then 'Virtual Machine' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mac_address", "colExpr": "regexp_replace(mac_address, '-', ':')"}, {"colName": "ip", "colExpr": "array(crowdstrike_external_ip, crowdstrike_local_ip)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_last_reboot_date", "colExpr": "crowdstrike_reboot_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',hardware_chassis_type,crowdstrike_tags,crowdstrike_product_type_desc)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "crowdstrike_product_type_desc", "colExpr": "product_type_desc"}, {"colName": "crowdstrike_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(modified_timestamp)))"}, {"colName": "crowdstrike_last_report_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_seen)))"}, {"colName": "crowdstrike_onboarding_status", "colExpr": "CASE WHEN lower(crowdstrike_provision_state)='provisioned' THEN True ELSE False END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "crowdstrike_connection_ip", "colExpr": "connection_ip"}, {"colName": "crowdstrike_external_ip", "colExpr": "external_ip"}, {"colName": "crowdstrike_local_ip", "colExpr": "local_ip"}, {"colName": "crowdstrike_operational_state", "colExpr": "status"}, {"colName": "crowdstrike_provision_state", "colExpr": "provision_status"}, {"colName": "crowdstrike_agent_local_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(agent_local_time)))"}, {"colName": "crowdstrike_device_id", "colExpr": "device_id"}, {"colName": "crowdstrike_customer_id", "colExpr": "cid"}, {"colName": "crowdstrike_tags", "colExpr": "array_join(tags, ',')"}, {"colName": "crowdstrike_onboarding_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(first_seen)))"}, {"colName": "crowdstrike_reboot_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_reboot)))"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country_name = e.region", "sourcePreTransform": [{"colName": "country_name", "colExpr": "regexp_extract(cast(ou as string), 'Location:([^\"]+)', 1)"}]}], "dataSource": {"name": "CrowdStrike", "feedName": "Host List", "srdm": "<%SRDM_SCHEMA_NAME%>.crowdstrike_host"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike_host__device_id"}