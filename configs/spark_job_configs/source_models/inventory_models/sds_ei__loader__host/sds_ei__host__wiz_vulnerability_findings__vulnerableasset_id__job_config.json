{"primaryKey": "vulnerableAsset.id", "filterBy": "lower(vulnerableAsset.type) !='container'", "origin": "'Wiz Vulnerability Findings'", "temporaryProperties": [{"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstDetectedAt)))"}], "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(fqdn,dns_name,host_name,ip[0],primary_key))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "vulnerability_last_observed_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(vulnerableAsset.name)"}, {"colName": "ip", "colExpr": "vulnerableAsset.ipAddresses"}, {"colName": "cloud_resource_type", "colExpr": "case when vulnerableAsset.type='VIRTUAL_MACHINE' THEN 'Virtual Machine' when vulnerableAsset.type ='CONTAINER' THEN 'Container' else  Null end"}, {"colName": "vm_product", "colExpr": "'Wiz'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_last_observed_date", "colExpr": "case when lower(status)='open' then UNIX_MILLIS(TIMESTAMP(to_timestamp(lastDetectedAt))) else cast(null as long) end"}, {"colName": "vm_onboarding_status", "colExpr": "wiz_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "wiz_onboarding_status", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "wiz_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Wiz", "feedName": "Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.wiz__vulnerability_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__wiz_vulnerability_findings__vulnerableasset_id"}