{"primaryKey": "lower(InstanceId)", "origin": "'AWS EC2 Instance'", "commonProperties": [{"colName": "last_active_date", "colExpr": "GREATEST(aws_instance_launch_date,aws_instance_usage_update_time,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "dns_name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "concat_ws(',', aws_instance_private_ip_address,aws_instance_public_ip_address)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "CASE WHEN PlatformDetails = 'Linux/UNIX' THEN 'Linux' ELSE PlatformDetails END"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Instance'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN 'Active'  WHEN LOWER(aws_operational_state) IN ('stopped', 'pending', 'shutting-down', 'stopping', 'failed', 'inactive', 'delete_in_progress', 'terminated') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_type", "colExpr": "InstanceType"}, {"colName": "image", "colExpr": "aws_instance_image_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "OwnerId"}, {"colName": "cloud_instance_lifecycle", "colExpr": "case when lower(InstanceLifecycle)='spot' then lower(InstanceLifecycle) else 'scheduled' end"}, {"colName": "dns_name", "colExpr": "COALESCE(PublicDnsName,PrivateDnsName)"}, {"colName": "instance_name", "colExpr": "lower(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'Name' THEN x.Value END)), array(NULL))[0])", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name,native_type)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "CASE WHEN lower(cloud_instance_lifecycle) LIKE '%spot%' THEN True when array_contains(aws_tags.Key,'aws:ec2spot:fleet-request-id') or array_contains(aws_tags.Key,'aws:autoscaling:groupName') or array_contains(aws_tags.Key,'spotinst:aws:ec2:group:id') or array_contains(aws_tags.Key,'aws:ec2:fleet-id') or array_contains(aws_tags.Key,'gitlab_autoscaler_token') or array_contains(aws_tags.Key,'aws:elasticmapreduce:job-flow-id') or array_contains(aws_tags.Key,'spotinst:aws:ec2:group:createdBy:spotinst') then True Else False end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "CASE WHEN lower(cloud_instance_lifecycle) LIKE '%spot%' THEN True when array_contains(aws_tags.Key,'aws:ec2spot:fleet-request-id') or array_contains(aws_tags.Key,'aws:autoscaling:groupName') or array_contains(aws_tags.Key,'spotinst:aws:ec2:group:id') or array_contains(aws_tags.Key,'aws:ec2:fleet-id') or array_contains(aws_tags.Key,'gitlab_autoscaler_token') or array_contains(aws_tags.Key,'aws:elasticmapreduce:job-flow-id') or array_contains(aws_tags.Key,'spotinst:aws:ec2:group:createdBy:spotinst') then True Else False end", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_tags", "colExpr": "Tags"}, {"colName": "aws_instance_launch_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LaunchTime)))"}, {"colName": "aws_instance_usage_update_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(UsageOperationUpdateTime)))"}, {"colName": "aws_operational_state", "colExpr": "lower(State.Name)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_instance_private_ip_address", "colExpr": "PrivateIpAddress"}, {"colName": "aws_instance_public_ip_address", "colExpr": "PublicIpAddress"}, {"colName": "aws_instance_image_id", "colExpr": "ImageId"}], "dataSource": {"name": "AWS", "feedName": "EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__ec2_describe_instances"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_ec2_instance__instanceid"}