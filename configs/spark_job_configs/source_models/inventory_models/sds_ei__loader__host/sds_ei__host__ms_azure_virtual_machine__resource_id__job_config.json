{"primaryKey": "case when lower(type)='microsoft.compute/virtualmachinescalesets/virtualmachines' then concat(REGEXP_EXTRACT(id,'(.*\\/virtualMachines\\/)',0),name) else id end", "origin": "'MS Azure Virtual Machine'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "azure_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(COALESCE(properties.osProfile.computerName,name))"}, {"colName": "os", "colExpr": "INITCAP(properties.storageProfile.osDisk.osType)"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "location"}, {"colName": "account_id", "colExpr": "(SPLIT(id,'/')[2])"}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState"}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_lifecycle", "colExpr": "lower(azure_vm_lifecycle)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(azure_vm_power_state) LIKE '%running%' then 'Active' WHEN LOWER(azure_vm_power_state) LIKE '%stopped%' then 'Inactive' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN size(zones) = 1 THEN 'Single' WHEN size(zones) > 1 THEN 'Multiple' ELSE null END"}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',azure_vm_os_name,azure_vm_os_version,os,native_type)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "case when lower(properties.priority) in ('low','spot') then True Else False end"}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated)))"}, {"colName": "azure_vm_lifecycle", "colExpr": "properties.priority"}, {"colName": "azure_vm_os_name", "colExpr": "properties.extended.instanceView.osName"}, {"colName": "azure_vm_os_version", "colExpr": "properties.extended.instanceView.osVersion"}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_vm_power_state", "colExpr": "case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end) LIKE '%running%' THEN event_timestamp_epoch ELSE NULL END"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Virtual Machine", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_virtual_machine__resource_id"}