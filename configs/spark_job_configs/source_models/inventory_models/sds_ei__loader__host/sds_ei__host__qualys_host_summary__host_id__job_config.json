{"primaryKey": "host_id", "origin": "'Qualys Host Summary'", "commonProperties": [{"colName": "last_active_date", "colExpr": "vm_last_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(REGEXP_EXTRACT(dns_name, '^([^.]+)'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os"}, {"colName": "ip", "colExpr": "host_ip"}, {"colName": "dns_name", "colExpr": "dns"}, {"colName": "netbios"}, {"colName": "accessibility", "colExpr": "CASE WHEN LOWER(qualys_tags) LIKE '%internal%' THEN 'Internal' WHEN LOWER(qualys_tags) LIKE '%external%' THEN 'External' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN LOWER(qualys_detection_method) LIKE '%agent%' THEN true when datediff(from_unixtime(updated_at / 1000, 'yyyy-MM-dd'), from_unixtime(vm_last_scan_date / 1000, 'yyyy-MM-dd')) <= 30 THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN lower(qualys_detection_method) LIKE '%agent%' THEN 'Qualys' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_tracking_method", "colExpr": "qualys_detection_method", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(GREATEST (CASE WHEN last_scan_datetime != '[]' AND last_scan_datetime != '(never)' AND last_scan_datetime != 'NULL' THEN last_scan_datetime  end,case WHEN last_vm_scanned_date != '[]' AND last_vm_scanned_date != '(never)' AND last_vm_scanned_date != 'NULL' THEN last_vm_scanned_date END))))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name,qualys_tags)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "qualys_id", "colExpr": "host_id"}, {"colName": "qualys_tags", "colExpr": "tags"}, {"colName": "qualys_detection_method", "colExpr": "CONCAT_WS(' ','Qualys',tracking_method)"}], "dataSource": {"name": "Qualys", "feedName": "Host Summary", "srdm": "<%SRDM_SCHEMA_NAME%>.qualys__host_summary"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__qualys_host_summary__host_id"}