{"primaryKey": "aid", "origin": "'CrowdStrike ZeroTrustAssessment'", "temporaryProperties": [{"colName": "os_signals", "colExpr": "assessment_items.os_signals"}, {"colName": "sensor_signals", "colExpr": "assessment_items.sensor_signals"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "crowdstrike_modified_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os", "colExpr": "case when lower(event_platform) like '%win%' then 'Windows' when lower(event_platform) like '%lin%' then 'Linux' when lower(event_platform) like '%mac%' then 'macOS' else null end"}, {"colName": "hardware_serial_number", "colExpr": "UPPER(system_serial_number)"}, {"colName": "edr_product", "colExpr": "CASE WHEN crowdstrike_onboarding_status=true THEN 'CrowdStrike' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "crowdstrike_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,crowdstrike_product_type_desc)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "crowdstrike_product_type_desc", "colExpr": "product_type_desc"}, {"colName": "crowdstrike_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(modified_time)))"}, {"colName": "crowdstrike_onboarding_status", "colExpr": "CASE WHEN crowdstrike_zta_overall_score is not null THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "crowdstrike_device_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "crowdstrike_customer_id", "colExpr": "cid"}, {"colName": "crowdstrike_zta_sensor_file_deployment_status", "colExpr": "initcap(sensor_file_status)"}, {"colName": "crowdstrike_zta_os_score", "colExpr": "assessment.os"}, {"colName": "crowdstrike_zta_overall_score", "colExpr": "assessment.overall"}, {"colName": "crowdstrike_zta_sensor_score", "colExpr": "assessment.sensor_config"}, {"colName": "crowdstrike_zta_score_version", "colExpr": "assessment.version"}, {"colName": "crowdstrike_zta_os_signal_meet_criteria_yes", "colExpr": "ARRAY_JOIN(ARRAY_EXCEPT(TRANSFORM(os_signals, x -> (CASE WHEN x.meets_criteria = 'yes' THEN x.criteria END)), array(NULL) ), ', ')"}, {"colName": "crowdstrike_zta_os_signal_meet_criteria_no", "colExpr": "ARRAY_JOIN(ARRAY_EXCEPT(TRANSFORM(os_signals, x -> (CASE WHEN x.meets_criteria = 'no' THEN x.criteria END)), array(NULL) ), ', ')"}, {"colName": "crowdstrike_zta_os_signal_meet_criteria_unknown", "colExpr": "ARRAY_JOIN(ARRAY_EXCEPT(TRANSFORM(os_signals, x -> (CASE WHEN x.meets_criteria = 'unknown' THEN x.criteria END)), array(NULL) ), ', ')"}, {"colName": "crowdstrike_zta_sensor_signal_meet_criteria_yes", "colExpr": "ARRAY_JOIN(ARRAY_EXCEPT(TRANSFORM(sensor_signals , x -> (CASE WHEN x.meets_criteria = 'yes' THEN x.criteria END)), array(NULL) ), ', ')"}, {"colName": "crowdstrike_zta_sensor_signal_meet_criteria_no", "colExpr": "ARRAY_JOIN(ARRAY_EXCEPT(TRANSFORM(sensor_signals , x -> (CASE WHEN x.meets_criteria = 'no' THEN x.criteria END)), array(NULL) ), ', ')"}, {"colName": "crowdstrike_zta_sensor_signal_meet_criteria_unknown", "colExpr": "ARRAY_JOIN(ARRAY_EXCEPT(TRANSFORM(sensor_signals , x -> (CASE WHEN x.meets_criteria = 'unknown' THEN x.criteria END)), array(NULL) ), ', ')"}], "dataSource": {"name": "CrowdStrike", "feedName": "ZeroTrustAssessment", "srdm": "<%SRDM_SCHEMA_NAME%>.crowdstrike_zta"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__crowdstrike_zta__aid"}