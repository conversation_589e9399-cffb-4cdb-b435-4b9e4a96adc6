{"primaryKey": "temp_primary_key_exploded", "filterBy": "temp_type='dnsname'", "origin": "'Tenable.sc Asset'", "temporaryProperties": [{"colName": "temp_primary_key_exploded", "colExpr": "explode_outer(split(typeFields.definedDNSNames,','))", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "asset_groups_names", "colExpr": "transform(groups,x->x.name)"}, {"colName": "temp_tenable_repositories", "colExpr": "transform(repositories, x -> x.repository.name)"}, {"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(TRIM(CASE WHEN UPPER(primary_key) RLIKE '(.*\\\\.[\\\\d\\\\w\\\\s]++\\\\.(.)*$)' THEN REGEXP_EXTRACT(primary_key,'^(.*?)\\\\.',1) ELSE NULL END))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "LOWER(CASE WHEN primary_key RLIKE '^(?:\\\\d{1,3}\\\\.){3}\\\\d{1,3}-.*?\\\\.(.*)' THEN REGEXP_EXTRACT(primary_key,'^(?:\\\\d{1,3}\\\\.){3}\\\\d{1,3}-.*?\\\\.(.*)',1) WHEN primary_key RLIKE '(.*\\\\.[A-Za-z]++\\\\.(.)*$)' THEN REGEXP_EXTRACT(primary_key,'(\\\\.(.*))',2) ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "CASE WHEN primary_key RLIKE '^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$' THEN primary_key ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "CASE WHEN primary_key NOT RLIKE '(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$' AND primary_key RLIKE '.*\\\\..*\\\\..*' THEN primary_key WHEN primary_key RLIKE '(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$' THEN NULL ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "case when lower(tags) like '%linux%' then 'Linux' when lower(tags) like '%windows%' then 'Windows' when lower(tags) like '%mac%'then 'macOS' else null end"}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name,tenablesc_asset_groups)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "tenablesc_repositories", "colExpr": "temp_tenable_repositories"}, {"colName": "tenablesc_assets", "colExpr": "(collect_set(name) over (partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING))"}, {"colName": "tenablesc_asset_tags", "colExpr": "NULLIF(CONCAT_WS(', ',collect_set(tags) over (partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)),'')"}, {"colName": "tenablesc_asset_name", "colExpr": "name"}, {"colName": "tenablesc_asset_groups", "colExpr": "asset_groups_names"}], "dataSource": {"name": "Tenable.sc", "feedName": "Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.emea_tenable_vuln_tenable_sc_assets"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc_assets__defineddnsnames"}