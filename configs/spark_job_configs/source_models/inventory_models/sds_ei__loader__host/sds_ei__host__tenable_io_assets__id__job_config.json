{"primaryKey": "id", "origin": "'Tenable.io <PERSON>'", "temporaryProperties": [{"colName": "temp_azure_vm_id", "colExpr": "collect_set(azure_vm_id) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_gcp_instance_id", "colExpr": "collect_set(gcp_instance_id) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_aws_ec2_instance_id", "colExpr": "collect_set(aws_ec2_instance_id) OVER (PARTITION BY primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "temp_asset_source_name", "colExpr": "transform( sources.name, x -> CASE WHEN lower(trim(x)) = 'nessus_agent' THEN 'Nessus Agent' WHEN lower(trim(x)) = 'aws' THEN 'AWS' WHEN lower(trim(x)) = 'nessus_scan' THEN 'Nessus Scan' WHEN lower(trim(x)) = 'clouddiscoveryconnector' THEN 'Cloud Discovery Connector' WHEN lower(trim(x)) = 'was' THEN 'WAS' WHEN lower(trim(x)) = 'pvs' THEN 'Nessus Network Monitor' WHEN lower(trim(x)) = 'gcp' THEN 'GCP' ELSE NULL END )"}], "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(fqdn,dns_name,host_name,ip[0],primary_key))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "tenable_io_onboarding_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(vm_last_scan_date, tenable_io_asset_aws_terminated_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "CASE WHEN SIZE(hostnames) = 0 OR hostnames IS NULL THEN FILTER( TRANSFORM(fqdns, fqdn -> LOWER(REGEXP_EXTRACT(fqdn, '(^([^.]+))', 1)) ), fqdn -> fqdn IS NOT NULL ) ELSE FILTER( TRANSFORM(hostnames, hostname -> LOWER(REGEXP_EXTRACT(hostname, '(^([^.]+))', 1)) ), hostname -> hostname IS NOT NULL ) END"}, {"colName": "netbios", "colExpr": "netbios_names"}, {"colName": "ip", "colExpr": "tenable_io_ipv4_addresses", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fqdn", "colExpr": "CASE WHEN size(filter(fqdns, f -> LOWER(f) LIKE '%ip-%')) > 0 THEN NULL WHEN size(filter(fqdns, f -> regexp_like(f, '^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-zA-Z0-9-]+[.]?)$'))) > 0 THEN transform(filter(fqdns, f -> regexp_like(f, '^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-zA-Z0-9-]+[.]?)$')), f -> LOWER(f)) ELSE NULL END"}, {"colName": "os", "colExpr": "operating_systems[0]"}, {"colName": "mac_address", "colExpr": "explode(mac_addresses)"}, {"colName": "vm_product", "colExpr": "'Tenable.io'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "GREATEST(tenable_io_last_authenticated_scan_date, tenable_io_asset_last_licensed_scan_date, tenable_io_last_scan_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN tenable_io_onboarding_date IS NOT NULL THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_tracking_method", "colExpr": "temp_asset_source_name"}, {"colName": "cloud_instance_id", "colExpr": "lower(COALESCE(temp_azure_vm_id, temp_gcp_instance_id, temp_aws_ec2_instance_id))"}, {"colName": "image", "colExpr": "aws_ec2_instance_ami_id"}, {"colName": "resource_name", "colExpr": "lower(coalesce(aws_ec2_name, REGEXP_EXTRACT(azure_resource_id, '.*\\\\/(.*)', 1)))"}, {"colName": "account_id", "colExpr": "coalesce(aws_owner_id, REGEXP_EXTRACT(azure_resource_id, '\\\\/subscriptions\\\\/(.+?)\\\\/resourcegroups\\\\/', 1),REGEXP_EXTRACT(azure_resource_id, '\\\\/subscriptions\\\\/(.+?)\\\\/resourceGroups\\\\/', 1))"}, {"colName": "cloud_provider", "colExpr": "CASE WHEN azure_vm_id IS NOT NULL THEN 'Azure' WHEN gcp_instance_id IS NOT NULL THEN 'GCP' WHEN aws_ec2_instance_id IS NOT NULL THEN 'AWS' ELSE 'No Data' END"}, {"colName": "region", "colExpr": "aws_region"}, {"colName": "cloud_instance_type", "colExpr": "aws_ec2_instance_type"}, {"colName": "instance_name", "colExpr": "coalesce(aws_ec2_name, REGEXP_EXTRACT(azure_resource_id, '.*\\\\/(.*)', 1))"}, {"colName": "resource_id", "colExpr": "azure_resource_id"}, {"colName": "operational_state", "colExpr": "CASE  WHEN LOWER(aws_ec2_instance_state_name) IN ('active', 'running') THEN 'Active'  WHEN LOWER(aws_ec2_instance_state_name) IN ('stopped', 'pending', 'shutting-down', 'stopping', 'failed', 'inactive', 'delete_in_progress', 'terminated') THEN 'Inactive' ELSE NULL  END"}, {"colName": "zone_availability", "colExpr": "CASE WHEN aws_availability_zone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN aws_availability_zone= 'Not Applicable'  THEN 'Not Applicable' WHEN aws_availability_zone= 'Regional'  THEN 'Regional' WHEN aws_availability_zone IS NULL THEN NULL WHEN gcp_zone LIKE '%,%' THEN 'Multiple'   WHEN gcp_zone = 'Not Applicable' THEN 'Not Applicable'   WHEN gcp_zone = 'Regional' THEN 'Regional'   WHEN gcp_zone IS NULL THEN NULL  ELSE 'Single' END"}, {"colName": "native_type", "colExpr": "case when array_contains(transform(system_types, x -> lower(x)), 'aws-ec2-instance')  then 'AWS EC2 INSTANCE' when array_contains(transform(system_types, x -> lower(x)), 'azure-instance') then 'Azure Virtual Machine' when array_contains(transform(system_types, x -> lower(x)), 'gcp-instance') then 'GCP Compute Engine' END"}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name,tenable_io_system_type)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "tenable_io_id", "colExpr": "id"}, {"colName": "tenable_io_onboarding_date", "colExpr": "LEAST( MIN(UNIX_MILLIS(TIMESTAMP(to_timestamp(created_at)))) over ( partition by primary_key rows between unbounded preceding and unbounded following ), MIN(UNIX_MILLIS(TIMESTAMP(to_timestamp(first_seen)))) over ( partition by primary_key rows between unbounded preceding and unbounded following ), MIN( UNIX_MILLIS(TIMESTAMP(to_timestamp(first_scan_time))) ) over ( partition by primary_key rows between unbounded preceding and unbounded following ) )"}, {"colName": "tenable_io_asset_agent_status", "colExpr": "has_agent"}, {"colName": "tenable_io_asset_source_name", "colExpr": "sources.name"}, {"colName": "tenable_io_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_scan_time)))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "tenable_io_asset_updated_at", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(updated_at)))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "tenable_io_last_authenticated_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_authenticated_scan_date)))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "tenable_io_asset_last_licensed_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(last_licensed_scan_date)))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "tenable_io_asset_plugin_status", "colExpr": "has_plugin_results"}, {"colName": "tenable_io_asset_aws_terminated_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(terminated_at)))", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_vpc_id", "colExpr": "aws_vpc_id"}, {"colName": "azure_vm_id", "colExpr": "azure_vm_id"}, {"colName": "aws_operational_state", "colExpr": "aws_ec2_instance_state_name"}, {"colName": "qualys_id", "colExpr": "qualys_host_ids"}, {"colName": "qualys_asset_id", "colExpr": "qualys_asset_ids"}, {"colName": "tenable_io_asset_license_status", "colExpr": "CASE WHEN tenable_io_asset_last_licensed_scan_date IS NOT NULL THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenable_io_ipv4_addresses", "colExpr": "ARRAY_JOIN(ipv4s, ', ')"}, {"colName": "tenable_io_ipv6_addresses", "colExpr": "ARRAY_JOIN(ipv6s, ', ')"}, {"colName": "tenable_io_system_type", "colExpr": "explode(system_types)"}, {"colName": "tenable_io_acr_score", "colExpr": "acr_score"}, {"colName": "tenable_io_exposure_score", "colExpr": "exposure_score"}, {"colName": "gcp_project_id", "colExpr": "gcp_project_id"}, {"colName": "gcp_zone", "colExpr": "gcp_zone"}, {"colName": "gcp_instance_id", "colExpr": "gcp_instance_id"}, {"colName": "aws_owner_id", "colExpr": "aws_owner_id"}, {"colName": "aws_availability_zone", "colExpr": "aws_availability_zone"}, {"colName": "aws_subnet_id", "colExpr": "aws_subnet_id"}, {"colName": "aws_ec2_product_code", "colExpr": "aws_ec2_product_code"}], "dataSource": {"name": "Tenable.io", "feedName": "Assets", "srdm": "<%SRDM_SCHEMA_NAME%>.tenable_io_asset"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_io_assets__id"}