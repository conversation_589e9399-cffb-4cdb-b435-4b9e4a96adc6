{"primaryKey": "device_hostname", "filterBy": "event_label='gateway-auth' AND event_status='success'", "origin": "'GlobalProtect'", "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(device_hostname)"}, {"colName": "login_last_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "GlobalProtect", "feedName": "GlobalProtect", "srdm": "<%SRDM_SCHEMA_NAME%>.palo_alto__global_protect"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__globalprotect_vpn__device_hostname"}