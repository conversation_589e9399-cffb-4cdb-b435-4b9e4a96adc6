{"primaryKey": "id", "origin": "'MS Azure AD Devices'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,aad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aad_created_date,aad_enrolled_date, ad_last_sync_date,login_last_date,aad_deleted_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(displayName,'^([^\\\\r\\\\n]+?)(?>_([0-9]++[-\\\\/]){2}|\\\\$|$)'))"}, {"colName": "os", "colExpr": "CASE WHEN operatingSystem IS NULL THEN NULL\nWHEN LOWER(operatingSystem) LIKE '%unknown%' THEN null\nWHEN LOWER(operatingSystem) LIKE '%other%' THEN 'Other'\nELSE CONCAT_WS(' ',operatingSystem,CASE WHEN NOT regexp_like(operatingSystemVersion,'(?i)unknown|other') THEN operatingSystemVersion END)\nEND"}, {"colName": "dns_name", "colExpr": "array_max(transform(hostnames, x->CASE WHEN x LIKE '%.%' THEN x else hostnames[0] END))"}, {"colName": "hardware_manufacturer", "colExpr": "manufacturer"}, {"colName": "login_last_date", "colExpr": "CASE WHEN (approximateLastSignInDateTime != '' AND approximateLastSignInDateTime IS NOT NULL)THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(approximateLastSignInDateTime))) END"}, {"colName": "hardware_model", "colExpr": "model"}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name,aad_device_category,aad_profile_type,hardware_model,ARRAY_JOIN(aad_system_label, ', '),hardware_manufacturer )", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "ad_last_sync_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(onPremisesLastSyncDateTime)))"}, {"colName": "aad_id", "colExpr": "id"}, {"colName": "intune_ownership_status", "colExpr": "deviceOwnership"}, {"colName": "aad_device_id", "colExpr": "deviceId"}, {"colName": "aad_device_category", "colExpr": "deviceCategory"}, {"colName": "aad_profile_type", "colExpr": "profileType"}, {"colName": "aad_enrolled_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(registrationDateTime)))"}, {"colName": "aad_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime)))"}, {"colName": "aad_management_service", "colExpr": "managementType"}, {"colName": "aad_system_label", "colExpr": "systemLabels"}, {"colName": "aad_deleted_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(deletedDateTime)))"}, {"colName": "aad_operational_status", "colExpr": "CASE WHEN TRIM(accountEnabled) like 'false' THEN 'Disabled' WHEN TRIM(accountEnabled) like 'true' THEN 'Active' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}], "dataSource": {"name": "MS Azure AD", "feedName": "Devices", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_device"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__ms_azure_ad_devices__device_id"}