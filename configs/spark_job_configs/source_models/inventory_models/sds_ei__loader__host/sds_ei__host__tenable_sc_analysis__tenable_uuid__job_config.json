{"primaryKey": "COALESCE(tenable_uuid,netbiosName,macAddress,dnsName ,ip)", "origin": "'Tenable.sc Analysis'", "commonProperties": [{"colName": "last_active_date", "colExpr": "tenablesc_last_active_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(TRIM(CASE WHEN netbios IS NOT NULL AND netbios NOT RLIKE '^\\\\s*$' THEN REGEXP_EXTRACT(netbios,'.*\\\\\\\\(.*)')  WHEN dns_name RLIKE \"^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$\" THEN NULL WHEN dns_name IS NOT NULL AND dns_name NOT RLIKE '^\\\\s*$' THEN REGEXP_EXTRACT(dns_name,'(^[^.]+)') ELSE NULL END))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "operatingSystem"}, {"colName": "domain", "colExpr": "LOWER(CASE WHEN netbios IS NOT NULL AND netbios NOT RLIKE '^\\s*$' THEN REGEXP_EXTRACT(netbios,'(.*)\\\\\\\\.*')  WHEN dns_name RLIKE \"^(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$\" THEN NULL WHEN dns_name IS NOT NULL AND dns_name NOT RLIKE '^\\s*$' THEN REGEXP_EXTRACT(dns_name,'^[^.]+\\.(.*)') ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "ip"}, {"colName": "dns_name", "colExpr": "CASE WHEN dnsName NOT RLIKE '(?:[0-9]{1,3}\\\\.){3}[0-9]{1,3}$' AND dnsName RLIKE '.*\\\\..*\\\\..*' THEN dnsName ELSE NULL END"}, {"colName": "netbios", "colExpr": "netbiosName"}, {"colName": "mac_address", "colExpr": "<PERSON><PERSON><PERSON><PERSON>"}, {"colName": "vm_product", "colExpr": "CASE WHEN tenablesc_onboarding_status = true THEN 'Tenable.sc' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "tenablesc_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_tracking_method", "colExpr": "tenable_repo.dataFormat", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,dns_name)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "tenablesc_repositories", "colExpr": "tenable_repo.name", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenablesc_last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenablesc_onboarding_status", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenable_repo", "colExpr": "from_json(repository,'STRUCT<dataFormat:STRING,description:STRING,id:STRING,name:STRING >'  )"}], "dataSource": {"name": "Tenable.sc", "feedName": "Analysis", "srdm": "<%SRDM_SCHEMA_NAME%>.emea_tenable_vuln_tenable_sc_vulns"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__tenable_sc_analysis__tenable_uuid"}