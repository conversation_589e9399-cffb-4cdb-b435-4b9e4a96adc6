{"primaryKey": "lower(Ec2InstanceId)", "origin": "'AWS EMR EC2 Instance'", "commonProperties": [{"colName": "last_active_date", "colExpr": "GREATEST(aws_emr_end_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "aws_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "ip", "colExpr": "concat_ws(',', aws_instance_private_ip_address,aws_instance_public_ip_address)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Instance'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN 'Active'  WHEN LOWER(aws_operational_state) IN ('stopped', 'pending', 'shutting-down', 'stopping', 'failed', 'inactive', 'delete_in_progress', 'terminated') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_type", "colExpr": "InstanceType"}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(clusterArn,':([0-9]+):')"}, {"colName": "cloud_instance_lifecycle", "colExpr": "case when lower(Market)='spot' then 'spot' when lower(Market)='ON_DEMAND' then 'on-demand' else null end"}, {"colName": "dns_name", "colExpr": "COALESCE(PublicDnsName,PrivateDnsName)"}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',dns_name,native_type)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.CreationDateTime)))"}, {"colName": "aws_emr_end_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.EndDateTime)))"}, {"colName": "aws_operational_state", "colExpr": "lower(Status.State)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_instance_private_ip_address", "colExpr": "PrivateIpAddress"}, {"colName": "aws_instance_public_ip_address", "colExpr": "PublicIpAddress"}, {"colName": "aws_region", "colExpr": "regexp_extract(clusterArn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region = e.region", "sourcePreTransform": [{"colName": "aws_region", "colExpr": "regexp_extract(clusterArn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)"}]}], "dataSource": {"name": "AWS", "feedName": "EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_instance"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__host__aws_emr_ec2_instance__instanceid"}