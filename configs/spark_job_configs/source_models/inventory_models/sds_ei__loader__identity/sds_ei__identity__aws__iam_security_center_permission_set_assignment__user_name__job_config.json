{"primaryKey": "lower(UserName)", "origin": "'AWS IAM Center'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "displayname"}, {"colName": "operational_status", "colExpr": "CASE WHEN recency=0 then 'Active' Else 'Disabled' End", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internal_service", "colExpr": "'IAM Center'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "IAM Center", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__iam_security_center_permission_set_assignment"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__aws__iam_security_center_permission_set_assignment__user_name"}