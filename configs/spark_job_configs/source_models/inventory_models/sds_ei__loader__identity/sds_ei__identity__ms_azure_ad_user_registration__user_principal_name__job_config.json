{"primaryKey": "lower(userPrincipalName)", "origin": "'MS Azure AD User Registration'", "temporaryProperties": [{"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN (SIZE(methodsRegistered)=0 AND lower(userType) != 'guest')  THEN 'Non-Human' ELSE 'Human' END"}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'User Principal Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "aad_id", "colExpr": "id"}, {"colName": "user_principal_name", "colExpr": "lower(userPrincipalName)"}, {"colName": "identity_display_name", "colExpr": "userDisplayName"}, {"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_mfa_enabled", "colExpr": "CAST(isMfaRegistered AS BOOLEAN)"}, {"colName": "default_mfa_method", "colExpr": "defaultMfaMethod"}, {"colName": "is_sspr_registered", "colExpr": "CAST(isSsprRegistered AS BOOLEAN)"}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "password_not_required", "colExpr": "CAST(isPasswordlessCapable AS BOOLEAN)"}, {"colName": "auth_methods_registered", "colExpr": "methodsRegistered"}], "sourceSpecificProperties": [], "dataSource": {"name": "MS Azure AD", "feedName": "User Registration Details", "srdm": "<%SRDM_SCHEMA_NAME%>.ms_azure_ad__user_registration_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_user_registration__user_principal_name"}