{"primaryKey": "lower(userPrincipalName)", "origin": "'MS Azure AD Users'", "temporaryProperties": [{"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN ((employeeId IS NULL) AND userType != 'Guest')  THEN 'Non-Human' ELSE 'Human' END"}, {"colName": "first_seen_date", "colExpr": "aad_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "officeLocation"}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'User Principal Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "aad_id", "colExpr": "id"}, {"colName": "user_principal_name", "colExpr": "lower(userPrincipalName)"}, {"colName": "email_id", "colExpr": "lower(mail)"}, {"colName": "employee_id", "colExpr": "employeeId"}, {"colName": "identity_display_name", "colExpr": "displayName"}, {"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_status", "colExpr": "CASE WHEN TRIM(accountEnabled) = 'false' THEN 'Disabled' WHEN TRIM(accountEnabled) like 'true' THEN 'Active' ELSE NULL END"}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aad_created_date", "colExpr": "CASE WHEN (createdDateTime != '' AND (createdDateTime IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime))) ELSE NULL END"}, {"colName": "ad_last_sync_date", "colExpr": "CASE WHEN (onPremisesLastSyncDateTime != '' AND (onPremisesLastSyncDateTime IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(onPremisesLastSyncDateTime))) ELSE NULL END"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country = e.region"}], "dataSource": {"name": "MS Azure AD", "feedName": "Users", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_users__user_principal_name"}