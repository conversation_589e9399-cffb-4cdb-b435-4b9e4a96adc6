{"primaryKey": "deviceId", "origin": "'MS Azure AD Devices'", "commonProperties": [{"colName": "type", "colExpr": "'Non-Human'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "aad_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aad_created_date,login_last_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'AAD Device ID'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aad_id", "colExpr": "id"}, {"colName": "identity_display_name", "colExpr": "displayName"}, {"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "CASE WHEN (approximateLastSignInDateTime != '' AND approximateLastSignInDateTime IS NOT NULL)THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(approximateLastSignInDateTime))) END"}, {"colName": "operational_status", "colExpr": "CASE WHEN TRIM(accountEnabled) = 'false' THEN 'Disabled' WHEN TRIM(accountEnabled) like 'true' THEN 'Active' ELSE NULL END"}], "sourceSpecificProperties": [{"colName": "ad_last_sync_date", "colExpr": "CASE WHEN (onPremisesLastSyncDateTime != '' AND (onPremisesLastSyncDateTime IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(onPremisesLastSyncDateTime))) ELSE NULL END"}, {"colName": "aad_created_date", "colExpr": "CASE WHEN (createdDateTime!='' AND (createdDateTime IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime))) ELSE NULL END"}], "dataSource": {"name": "MS Azure AD", "feedName": "Devices", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_device"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_devices__device_id"}