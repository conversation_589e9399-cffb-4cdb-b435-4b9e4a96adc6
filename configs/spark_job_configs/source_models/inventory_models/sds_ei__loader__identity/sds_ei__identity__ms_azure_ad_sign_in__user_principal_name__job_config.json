{"primaryKey": "lower(userPrincipalName)", "origin": "'MS Azure AD Sign-in Logs'", "temporaryProperties": [{"colName": "temp_upn_domain_extracted", "colExpr": "split(user<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,'@')[1]"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'User Principal Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership", "colExpr": "CASE WHEN temp_upn_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_upn_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "first_seen_date", "colExpr": "first_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_logged_in_location", "colExpr": "CASE WHEN status.errorCode = 0 THEN signed_in_country ELSE NULL END"}, {"colName": "user_principal_name", "colExpr": "lower(userPrincipalName)"}, {"colName": "identity_display_name", "colExpr": "userDisplayName"}, {"colName": "is_mfa_enabled", "colExpr": "case when status.additionalDetails = 'MFA requirement satisfied by claim in the token' then True else Null end"}, {"colName": "login_last_date", "colExpr": "case when status.errorCode = 0 then event_timestamp_epoch else null end", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_provider", "colExpr": "'Azure AD'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_signin_attempt", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_accessed_in_one_day", "colExpr": "collect_set(signed_in_country) OVER (partition by primary_key,event_timestamp_date  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "location_accessed_flag", "colExpr": "case when location_accessed_in_one_day is not null and size(location_accessed_in_one_day)>1 then True else False end", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"preTransform": [{"colName": "signed_in_country", "colExpr": "location_country"}], "tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["signed_in_country"]}, "joinCondition": "s.country_name = e.region", "sourcePreTransform": [{"colName": "country_name", "colExpr": "location.countryOrRegion"}]}], "dataSource": {"name": "MS Azure AD", "feedName": "Sign-in Logs", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user_sign_in"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_sign_in__user_principal_name"}