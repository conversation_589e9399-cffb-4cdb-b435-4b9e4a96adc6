{"primaryKey": "lower(temp_primary_key_exploded)", "origin": "'MS Azure AD Users'", "temporaryProperties": [{"colName": "temp_primary_key_exploded", "colExpr": "explode_outer(otherMails)"}, {"colName": "temp_other_mail_domain_extracted", "colExpr": "split(temp_primary_key_exploded,'@')[1]"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN ((employeeId IS NULL) AND userType != 'Guest')  THEN 'Non-Human' ELSE 'Human' END"}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "identity_format", "colExpr": "'External Email'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_display_name", "colExpr": "displayName"}, {"colName": "email_id", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "company_email_domains", "colExpr": "cast(null as array<string>)"}, {"colName": "ownership", "colExpr": "CASE WHEN temp_other_mail_domain_extracted IS NULL THEN NULL WHEN array_contains(company_email_domains,temp_other_mail_domain_extracted) THEN 'Corp' ELSE 'External' END"}, {"colName": "identity_primary_key", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "MS Azure AD", "feedName": "Users", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__ms_azure_ad_users__other_mails"}