{"primaryKey": "sam_account_name_with_domain_temp", "origin": "'MS Active Directory'", "temporaryProperties": [{"colName": "domain_temp", "colExpr": "regexp_extract(distinguishedName,'DC=([^,]++)')"}, {"colName": "sam_account_name_temp", "colExpr": "RTRIM('$',sAMAccountName)"}, {"colName": "sam_account_name_with_domain_temp", "colExpr": "CASE WHEN sam_account_name_temp IS NULL OR domain_temp IS NULL THEN NULL ELSE CONCAT_WS('\\\\',domain_temp,sam_account_name_temp) END"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN ad_sam_account_type IS NULL THEN NULL WHEN ad_sam_account_type='NORMAL_USER_ACCOUNT' and REGEXP_LIKE(lower(ad_distinguished_name),'(?i)service[ ]?account|sccm|scom') THEN 'Non-Human' WHEN lower(ad_sam_account_type) LIKE '%user%' THEN 'Human' ELSE 'Non-Human' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(ad_created_date,first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_password_change_date,login_last_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "operational_status", "colExpr": "CASE WHEN userAccountControl is NULL THEN NULL WHEN LOWER(userAccountControl) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END"}, {"colName": "identity_provider", "colExpr": "'Active Directory'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "identity_format", "colExpr": "'SAM Account Name'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ownership", "colExpr": "'Corp'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "user_principal_name", "colExpr": "lower(userPrincipalName)"}, {"colName": "identity_display_name", "colExpr": "displayName"}, {"colName": "login_last_date", "colExpr": "CASE WHEN (lastLogon != '[]' AND lastLogon  != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogon))) ELSE NULL END"}, {"colName": "last_password_change_date", "colExpr": "CASE WHEN (pwdLastSet != '[]' AND pwdLastSet != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(pwdLastSet))) ELSE NULL END"}, {"colName": "account_never_expire", "colExpr": "CASE WHEN accountExpires = 0 THEN True ELSE False END"}, {"colName": "password_not_required", "colExpr": "CAST(null AS BOOLEAN)"}, {"colName": "password_never_expire", "colExpr": "CAST(null AS BOOLEAN)"}, {"colName": "identity_primary_key", "colExpr": "CONCAT_WS(':',primary_key,identity_provider)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_configured_time", "colExpr": "5", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_count_flag", "colExpr": "case when badPwdCount=bad_password_configured_time then True else False end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_configured_time", "colExpr": "5", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_count_flag", "colExpr": "case when badPwdCount=bad_password_configured_time then True else False end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_configured_time", "colExpr": "5", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "bad_password_count_flag", "colExpr": "case when badPwdCount=bad_password_configured_time then True else False end", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "ad_distinguished_name", "colExpr": "<PERSON><PERSON><PERSON>"}, {"colName": "ad_domain", "colExpr": "domain_temp"}, {"colName": "ad_sam_account_name_with_domain", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ad_sam_account_name", "colExpr": "LOWER(sam_account_name_temp)"}, {"colName": "ad_created_date", "colExpr": "CASE WHEN (whenCreated != '[]' AND whenCreated != '(never)') THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(whenCreated))) ELSE NULL END"}, {"colName": "ad_sam_account_type", "colExpr": "sAMAccountType"}], "dataSource": {"name": "MS Active Directory", "feedName": "MS Active Directory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__active_directory"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__identity__active_directory__sam_account_name_with_domain"}