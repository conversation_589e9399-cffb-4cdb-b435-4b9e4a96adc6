{"primaryKey": "lower(id)", "origin": "'MS Azure Queue Storage'", "commonProperties": [{"colName": "type", "colExpr": "'Queue Service'"}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "case when azure_region in ('global') then 'Global' when azure_region in ('australiacentral') then \"Australia\" when azure_region in ('australiaeast') then \"Australia\" when azure_region in ('australiasoutheast') then \"Australia\" when azure_region in ('austriaeast') then \"Austria\" when azure_region in ('belgiumcentral') then \"Belgium\" when azure_region in ('brazilsouth') then \"Brazil\" when azure_region in ('canadacentral') then \"Canada\" when azure_region in ('canadaeast') then \"Canada\" when azure_region in ('centralindia') then \"India\" when azure_region in ('centralus') then \"United States\" when azure_region in ('chilecentral') then \"Chile\" when azure_region in ('chinaeast') then \"China\" when azure_region in ('chinaeast2') then \"China\" when azure_region in ('chinanorth') then \"China\" when azure_region in ('chinanorth2') then \"China\" when azure_region in ('chinanorth3') then \"China\" when azure_region in ('denmarkeast') then \"Denmark\" when azure_region in ('eastasia') then \"China\" when azure_region in ('eastus') then \"United States\" when azure_region in ('eastus2') then \"United States\" when azure_region in ('eastus3') then \"United States\" when azure_region in ('finlandcentral') then \"Finland\" when azure_region in ('francecentral') then \"France\" when azure_region in ('germanywestcentral') then \"Germany\" when azure_region in ('greececentral') then \"Greece\" when azure_region in ('indiasouthcentral') then \"India\" when azure_region in ('indonesiacentral') then \"Indonesia\" when azure_region in ('israelcentral') then \"Israel\" when azure_region in ('italynorth') then \"Italy\" when azure_region in ('japaneast') then \"Japan\" when azure_region in ('japanwest') then \"Japan\" when azure_region in ('koreacentral') then \"South Korea\" when azure_region in ('malaysiawest') then \"Malaysia\" when azure_region in ('mexicocentral') then \"Mexico\" when azure_region in ('newzealandnorth') then \"New Zealand\" when azure_region in ('northcentralus') then \"United States\" when azure_region in ('northeurope') then \"Ireland\" when azure_region in ('norwayeast') then \"Norway\" when azure_region in ('polandcentral') then \"Poland\" when azure_region in ('qatarcentral') then \"Qatar\" when azure_region in ('saudiarabiacentral') then \"Saudi Arabia\" when azure_region in ('southafricanorth') then \"South Africa\" when azure_region in ('southcentralus') then \"United States\" when azure_region in ('southeastasia') then \"Singapore\" when azure_region in ('southindia') then \"India\" when azure_region in ('spaincentral') then \"Spain\" when azure_region in ('swedencentral') then \"Sweden\" when azure_region in ('switzerlandnorth') then \"Switzerland\" when azure_region in ('taiwannorth') then \"Taiwan\" when azure_region in ('uaenorth') then \"United Arab Emirates\" when azure_region in ('uksouth') then \"United Kingdom\" when azure_region in ('ukwest') then \"United Kingdom\" when azure_region in ('usdodcentral') then \"United States\" when azure_region in ('usdodeast') then \"United States\" when azure_region in ('usgovarizona') then \"United States\" when azure_region in ('usgovtexas') then \"United States\" when azure_region in ('usgovvirginia') then \"United States\" when azure_region in ('usseceast') then \"Undisclosed\" when azure_region in ('ussecwest') then \"Undisclosed\" when azure_region in ('ussecwestcentral') then \"Undisclosed\" when azure_region in ('westcentralus') then \"United States\" when azure_region in ('westeurope') then \"Netherlands\" when azure_region in ('westus') then \"United States\" when azure_region in ('westus2') then \"United States\" when azure_region in ('westus3') then \"United States\" when azure_region in ('koreasouth') then \"South Korea\" when azure_region in ('westindia') then \"India\" else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "case when azure_region in ('global') then 'Global' when azure_region in ('westus3') then 'Arizona' when azure_region in ('usgovarizona') then 'Arizona' when azure_region in ('greececentral') then 'Athens' when azure_region in ('newzealandnorth') then 'Auckland' when azure_region in ('chinanorth') then 'Beijing' when azure_region in ('chinanorth2') then 'Beijing' when azure_region in ('belgiumcentral') then 'Brussels' when azure_region in ('westus') then 'California' when azure_region in ('australiacentral') then 'Canberra' when azure_region in ('ukwest') then 'Cardiff' when azure_region in ('southindia') then 'Chennai' when azure_region in ('denmarkeast') then 'Copenhagen' when azure_region in ('qatarcentral') then 'Doha' when azure_region in ('uaenorth') then 'Dubai' when azure_region in ('germanywestcentral') then 'Frankfurt' when azure_region in ('swedencentral') then 'Gävle' when azure_region in ('eastus3') then 'Georgia' when azure_region in ('chinanorth3') then 'Hebei' when azure_region in ('finlandcentral') then 'Helsinki' when azure_region in ('eastasia') then 'Hong Kong' when azure_region in ('indiasouthcentral') then 'Hyderabad' when azure_region in ('northcentralus') then 'Illinois' when azure_region in ('centralus') then 'Iowa' when azure_region in ('usdodcentral') then 'Iowa' when azure_region in ('northeurope') then 'Ireland' when azure_region in ('israelcentral') then 'Israel' when azure_region in ('indonesiacentral') then 'Jakarta' when azure_region in ('southafricanorth') then 'Johannesburg' when azure_region in ('malaysiawest') then 'Kuala Lumpur' when azure_region in ('uksouth') then 'London' when azure_region in ('spaincentral') then 'Madrid' when azure_region in ('italynorth') then 'Milan' when azure_region in ('westeurope') then 'Netherlands' when azure_region in ('australiaeast') then 'New South Wales' when azure_region in ('japanwest') then 'Osaka' when azure_region in ('norwayeast') then 'Oslo' when azure_region in ('francecentral') then 'Paris' when azure_region in ('centralindia') then 'Pune' when azure_region in ('canadaeast') then 'Quebec City' when azure_region in ('mexicocentral') then 'Queretaro' when azure_region in ('chilecentral') then 'Santiago' when azure_region in ('brazilsouth') then 'São Paulo State' when azure_region in ('saudiarabiacentral') then 'Saudi Arabia' when azure_region in ('koreacentral') then 'Seoul' when azure_region in ('chinaeast') then 'Shanghai' when azure_region in ('chinaeast2') then 'Shanghai' when azure_region in ('southeastasia') then 'Singapore' when azure_region in ('taiwannorth') then 'Taipei' when azure_region in ('southcentralus') then 'Texas' when azure_region in ('usgovtexas') then 'Texas' when azure_region in ('japaneast') then 'Tokyo, Saitama' when azure_region in ('canadacentral') then 'Toronto' when azure_region in ('usseceast') then 'Undisclosed' when azure_region in ('ussecwest') then 'Undisclosed' when azure_region in ('ussecwestcentral') then 'Undisclosed' when azure_region in ('australiasoutheast') then 'Victoria' when azure_region in ('austriaeast') then 'Vienna' when azure_region in ('eastus') then 'Virginia' when azure_region in ('eastus2') then 'Virginia' when azure_region in ('usdodeast') then 'Virginia' when azure_region in ('usgovvirginia') then 'Virginia' when azure_region in ('polandcentral') then 'Warsaw' when azure_region in ('westus2') then 'Washington' when azure_region in ('westcentralus') then 'Wyoming' when azure_region in ('switzerlandnorth') then 'Zürich' when azure_region in ('westindia') then 'Mumbai' when azure_region in ('koreasouth') then 'Busan' else azure_region end", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Queue Storage'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN azure_availability_zone = 1 THEN 'Single' WHEN azure_availability_zone > 1 THEN 'Multiple' WHEN azure_availability_zone IS NULL THEN 'Not Applicable' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_in_transit", "colExpr": "azure_supports_http_traffic_only", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_at_rest", "colExpr": "case when azure_encryption_key_source is not null then true else false end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "azure_tags.Environment", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "storage_account_access_tier", "colExpr": "SAaccessTier"}, {"colName": "storage_account_name", "colExpr": "REGEXP_extract(id,'/storageAccounts/(.*?)/queueServices', 1)"}], "sourceSpecificProperties": [{"colName": "azure_resource_group", "colExpr": "REGEXP_extract(id,'/resourceGroups/(.*?)/providers', 1)"}, {"colName": "azure_availability_zone", "colExpr": "CASE WHEN zones IS NULL OR size(zones) = 0  THEN NULL ELSE size(zones) END"}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_region", "colExpr": "location"}, {"colName": "azure_ou", "colExpr": "azure_tags.OU", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_storage_account_id", "colExpr": "REGEXP_extract(id,'/subscriptions/.*?/providers/Microsoft.Storage/storageAccounts/.*?(?=/queueServices)', 0)"}, {"colName": "azure_encryption_key_source", "colExpr": "encryptionkeySource"}, {"colName": "azure_supports_http_traffic_only", "colExpr": "supportsHttpsTrafficOnly"}, {"colName": "azure_large_file_shares_state", "colExpr": "largeFileSharesState"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Queue Storage", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__queue_storage"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_storage__ms_azure_queue_storage__id"}