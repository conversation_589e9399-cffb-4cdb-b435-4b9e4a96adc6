{"primaryKey": "lower(temp_resource_id)", "filterBy": "lower(temp_resource_type) IN ('awss3bucket','awsec2volume','awsefsfilesystem')", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_resource", "colExpr": "explode_outer(Resources)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_id", "colExpr": "temp_resource.Id"}, {"colName": "temp_resource_type", "colExpr": "temp_resource.Type"}, {"colName": "temp_resource_details", "colExpr": "temp_resource.Details"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "CASE WHEN lower(temp_resource_type) LIKE '%awss3bucket%' THEN 'Bucket'  WHEN lower(temp_resource_type) LIKE '%awsec2volume%' THEN 'Volume'  WHEN lower(temp_resource_type) LIKE '%awsefsfilesystem%' THEN 'File System Service'  ELSE lower(temp_resource_type) END"}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "AwsAccountId"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "Resources.Tags.Environment[0]"}, {"colName": "native_type", "colExpr": "CASE WHEN lower(temp_resource_type) LIKE '%awss3bucket%' THEN 'AWS S3 Bucket'  WHEN lower(temp_resource_type) LIKE '%awsec2volume%' THEN 'AWS EC2 Volume (EBS)'  WHEN lower(temp_resource_type) LIKE '%awsefsfilesystem%' THEN 'AWS Elastic File System (EFS)' ELSE lower(temp_resource_type) END"}], "sourceSpecificProperties": [{"colName": "aws_region", "colExpr": "Region"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.Region = e.region"}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_storage__aws_sh_findings__id"}