{"primaryKey": "lower(subscriptionId)", "origin": "'MS Azure Subscriptions'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "case when lower(account_status)='active' then last_found_date else null end", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "lower(displayName)"}, {"colName": "account_id", "colExpr": "primary_key"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_status", "colExpr": "CASE WHEN LOWER(TRIM(state)) = 'active' OR LOWER(TRIM(state)) = 'enabled' OR LOWER(TRIM(state)) = 'past due' THEN 'Active' ELSE 'Inactive' END"}], "sourceSpecificProperties": [{"colName": "azure_subscription_authorization_source", "colExpr": "authorizationSource"}, {"colName": "azure_subscription_managed_by_tenants", "colExpr": "managedByTenants"}, {"colName": "azure_subscription_state", "colExpr": "state"}, {"colName": "azure_subscription_policies", "colExpr": "subscriptionPolicies"}, {"colName": "azure_subscription_location_placement_id", "colExpr": "subscriptionPolicies.locationPlacementId"}, {"colName": "azure_subscription_quota_id", "colExpr": "subscriptionPolicies.quotaId"}, {"colName": "azure_subscription_spending_limit", "colExpr": "subscriptionPolicies.spendingLimit"}, {"colName": "azure_subscription_tags", "colExpr": "tags"}, {"colName": "azure_subscription_tenantId", "colExpr": "tenantId"}], "dataSource": {"name": "MS Azure", "feedName": "Subscriptions", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__list_subscription"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__ms_azure_subscriptions__subscriptionid"}