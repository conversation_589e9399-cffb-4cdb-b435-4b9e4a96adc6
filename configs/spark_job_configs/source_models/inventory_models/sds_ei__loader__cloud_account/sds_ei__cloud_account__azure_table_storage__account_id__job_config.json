{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)'))", "origin": "'MS Azure Table Storage'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Table Storage", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__table_storage"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_table_storage__account_id"}