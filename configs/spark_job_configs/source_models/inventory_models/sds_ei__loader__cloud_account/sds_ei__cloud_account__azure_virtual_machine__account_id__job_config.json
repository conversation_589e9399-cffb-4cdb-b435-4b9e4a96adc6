{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)'))", "origin": "'MS Azure Virtual Machine'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Virtual Machine", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_virtual_machine__account_id"}