{"primaryKey": "lower(subscriptionExternalId)", "origin": "'Wiz Cloud Resource'", "commonProperties": [{"colName": "type", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='kubernetes' and lower(graphEntity.properties.kubernetes_kubernetesFlavor)='aks' then 'Azure Subscription' when lower(graphEntity.properties.cloudPlatform)='kubernetes' and lower(graphEntity.properties.kubernetes_kubernetesFlavor)='eks' then 'AWS Account' when lower(graphEntity.properties.cloudPlatform)='aws' then 'AWS Account' when lower(graphEntity.properties.cloudPlatform)='azure' then 'Azure Subscription' else null end"}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}, {"colName": "account_name", "colExpr": "lower(subscriptionName)"}, {"colName": "cloud_provider", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='kubernetes' and lower(graphEntity.properties.kubernetes_kubernetesFlavor)='aks' then 'Azure' when lower(graphEntity.properties.cloudPlatform)='kubernetes' and lower(graphEntity.properties.kubernetes_kubernetesFlavor)='eks' then 'AWS' when lower(graphEntity.properties.cloudPlatform)='aws' then 'AWS' when lower(graphEntity.properties.cloudPlatform)='azure' then 'Azure' else null end"}], "dataSource": {"name": "Wiz", "feedName": "Cloud Resource", "srdm": "<%SRDM_SCHEMA_NAME%>.wiz__cloud_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__wiz_cloud_resource__account_id"}