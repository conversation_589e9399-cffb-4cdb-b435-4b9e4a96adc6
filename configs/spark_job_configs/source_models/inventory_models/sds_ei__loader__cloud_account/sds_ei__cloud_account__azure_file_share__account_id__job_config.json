{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)'))", "origin": "'MS Azure File Share'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "File Share", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__file_share"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_file_share__account_id"}