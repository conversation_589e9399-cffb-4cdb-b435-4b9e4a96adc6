{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)'))", "origin": "'MS Azure Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_resource_details__account_id"}