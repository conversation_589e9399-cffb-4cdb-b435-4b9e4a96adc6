{"primaryKey": "lower(REGEXP_EXTRACT(containerArn,':([0-9]+):'))", "origin": "'AWS ECS Task Container'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "ECS Task Container", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__ecs_task_containers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_ecs_task_container__account_id"}