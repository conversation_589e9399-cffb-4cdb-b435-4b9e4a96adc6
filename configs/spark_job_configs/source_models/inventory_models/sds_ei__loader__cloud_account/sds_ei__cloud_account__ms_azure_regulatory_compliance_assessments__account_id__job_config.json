{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\\\/[^\\\\\\/]+\\\\\\/([^\\\\\\/]+)'))", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols/regulatorycomplianceassessments')", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__ms_azure_regulatory_compliance_assessments__account_id"}