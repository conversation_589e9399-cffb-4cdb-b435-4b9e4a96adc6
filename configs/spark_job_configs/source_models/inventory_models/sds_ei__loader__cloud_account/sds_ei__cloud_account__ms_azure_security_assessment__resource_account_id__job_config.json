{"primaryKey": "LOWER(REGEXP_EXTRACT(id, '/([a-zA-Z0-9-]+-+[a-zA-Z0-9-]+)/'))", "origin": "'MS Azure Security Resources'", "filterBy": "properties.resourceDetails.ResourceType=='subscription'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__ms_azure_security_assessment__resource_account_id"}