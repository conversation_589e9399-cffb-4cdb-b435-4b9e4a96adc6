{"primaryKey": "lower(OwnerId)", "origin": "'AWS EC2 Instance'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__ec2_describe_instances"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_ec2_instance__owner_id"}