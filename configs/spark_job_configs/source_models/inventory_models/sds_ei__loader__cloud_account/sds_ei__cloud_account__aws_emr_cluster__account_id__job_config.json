{"primaryKey": "lower(REGEXP_EXTRACT(ClusterArn,':([0-9]+):'))", "origin": "'AWS EMR Cluster'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "EMR Cluster", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_clusters"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_emr_cluster__account_id"}