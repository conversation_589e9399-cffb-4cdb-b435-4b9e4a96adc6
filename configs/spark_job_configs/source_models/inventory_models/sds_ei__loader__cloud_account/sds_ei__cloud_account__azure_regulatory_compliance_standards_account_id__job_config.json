{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\\\/[^\\\\\\/]+\\\\\\/([^\\\\\\/]+)'))", "origin": "'MS Azure Security Resources'", "filterBy": "lower(type) IN ('microsoft.security/regulatorycompliancestandards')", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(name)"}], "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_regulatory_compliance_standards_account_id"}