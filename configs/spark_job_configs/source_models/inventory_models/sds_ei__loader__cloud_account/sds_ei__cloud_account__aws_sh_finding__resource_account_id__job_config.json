{"primaryKey": "LOWER(regexp_extract(temp_resource_id, 'AWS::::Account:(\\\\d+)'))", "filterBy": "lower(temp_resource_type) IN ('awsaccount')", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_stand_acc", "colExpr": "transform(Compliance.AssociatedStandards,x->REGEXP_EXTRACT(upper(x.StandardsId), '\\/(.*)$'))"}, {"colName": "temp_resource", "colExpr": "explode_outer(Resources)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_type", "colExpr": "temp_resource.Type"}, {"colName": "temp_resource_id", "colExpr": "temp_resource.Id"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_sh_finding__resource_account_id"}