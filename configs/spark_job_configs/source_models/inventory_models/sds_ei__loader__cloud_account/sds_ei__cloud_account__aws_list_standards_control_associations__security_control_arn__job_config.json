{"primaryKey": "lower(REGEXP_EXTRACT(SecurityControlArn, '([0-9]{12})'))", "origin": "'AWS List Standards Control Associations'", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(REGEXP_EXTRACT(StandardsArn, '\\/(.*)$'))"}, {"colName": "temp_stand_acc", "colExpr": "collect_set(REGEXP_EXTRACT(upper(StandardsArn), '\\/(.*)$')) OVER (partition by SecurityControlId,DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "List Standards Control Associations", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_list_standards_control_associations"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_list_standards_control_associations__security_control_arn"}