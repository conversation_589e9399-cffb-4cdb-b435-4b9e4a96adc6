{"primaryKey": "lower(REGEXP_EXTRACT(clusterArn,'arn:aws:eks:[^:]+:(\\\\d+):.*'))", "origin": "'AWS EKS Container'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "<PERSON><PERSON> Container", "srdm": "<%SRDM_SCHEMA_NAME%>.aws_eks_container"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_eks_container__account_id"}