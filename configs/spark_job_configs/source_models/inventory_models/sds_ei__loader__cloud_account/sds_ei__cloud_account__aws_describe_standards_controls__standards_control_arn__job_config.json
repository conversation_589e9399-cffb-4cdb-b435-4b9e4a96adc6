{"primaryKey": "lower(REGEXP_EXTRACT(StandardsControlArn, '([0-9]{12})'))", "origin": "'AWS Describe Standards Controls'", "temporaryProperties": [{"colName": "temp_id", "colExpr": "upper(REGEXP_EXTRACT(StandardsSubscriptionArn, '\\/(.*)$'))"}, {"colName": "temp_stand_acc", "colExpr": "collect_set(REGEXP_EXTRACT(upper(StandardsSubscriptionArn), '\\/(.*)$')) OVER (partition by split(RemediationUrl,'/')[5],DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Describe Standards Controls", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_describe_standards_controls"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_describe_standards_controls__standards_control_arn"}