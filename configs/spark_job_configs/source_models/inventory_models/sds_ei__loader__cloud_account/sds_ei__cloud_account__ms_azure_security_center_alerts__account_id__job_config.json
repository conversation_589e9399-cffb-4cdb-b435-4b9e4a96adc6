{"primaryKey": "LOWER(REGEXP_EXTRACT(id, '/([a-zA-Z0-9-]+-+[a-zA-Z0-9-]+)/'))", "origin": "'MS Azure Security Center Alerts'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Center Alerts", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_center_alerts"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__ms_azure_security_center_alerts__account_id"}