{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)'))", "origin": "'MS Azure Queue Storage'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Queue Storage", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__queue_storage"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_queue_storage__account_id"}