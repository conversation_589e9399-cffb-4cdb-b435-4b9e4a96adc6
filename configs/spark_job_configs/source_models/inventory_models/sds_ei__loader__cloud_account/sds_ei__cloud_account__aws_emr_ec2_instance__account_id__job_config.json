{"primaryKey": "lower(REGEXP_EXTRACT(clusterArn,':([0-9]+):'))", "origin": "'AWS EMR EC2 Instance'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "EMR EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_instance"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_emr_ec2_instance__account_id"}