{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\\\/[^\\\\\\/]+\\\\\\/([^\\\\\\/]+)'))", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols')", "temporaryProperties": [{"colName": "temp_id", "colExpr": "id"}, {"colName": "temp_standard", "colExpr": "upper(REGEXP_EXTRACT(temp_id, 'regulatoryComplianceStandards/([^/]+)/'))"}, {"colName": "temp_stand_acc", "colExpr": "collect_set(temp_standard) OVER (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_regulatory_compliance_control__account_id"}