{"primaryKey": "lower(REGEXP_EXTRACT(id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)'))", "origin": "'MS Azure Blob Storage Container'", "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Blob Storage Container", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__blob_storage_container"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__azure_blob_storage_container__account_id"}