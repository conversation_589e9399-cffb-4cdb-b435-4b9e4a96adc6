{"primaryKey": "lower(Id)", "origin": "'AWS Organizations'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "case when lower(account_status)='active' then last_found_date else null end", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "account_name", "colExpr": "lower(name)"}, {"colName": "account_id", "colExpr": "primary_key"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_status", "colExpr": "CASE WHEN LOWER(TRIM(Status)) IN ('active', 'pending_closure') THEN 'Active' ELSE 'Inactive' END"}], "sourceSpecificProperties": [{"colName": "aws_account_arn", "colExpr": "arn"}, {"colName": "aws_account_email", "colExpr": "Email"}, {"colName": "aws_account_name", "colExpr": "Name"}, {"colName": "aws_account_status", "colExpr": "Status"}, {"colName": "aws_account_joined_method", "colExpr": "Joined<PERSON>ethod"}, {"colName": "aws_account_joined_timestamp", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(JoinedTimestamp)))"}], "dataSource": {"name": "AWS", "feedName": "Organizations List Accounts", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__organization_list_accounts"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_organizations__id"}