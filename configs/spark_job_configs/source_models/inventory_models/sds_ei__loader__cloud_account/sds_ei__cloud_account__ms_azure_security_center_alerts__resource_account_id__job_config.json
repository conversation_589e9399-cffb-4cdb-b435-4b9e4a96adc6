{"primaryKey": "LOWER(REGEXP_EXTRACT(id, '/([a-zA-Z0-9-]+-+[a-zA-Z0-9-]+)/'))", "origin": "'MS Azure Security Center Alerts'", "filterBy": "lower(temp_type) IN ('subscriptions')", "temporaryProperties": [{"colName": "temp_type", "colExpr": "case when REGEXP_EXTRACT(ARRAY_MIN(TRANSFORM(FILTER(properties.resourceIdentifiers, x -> x.type = 'AzureResource'),x -> x.azureResourceId )),'\\\\/providers\\\\/[^\\\\/]+\\\\/(.+)') = '' then SPLIT(ARRAY_MIN(TRANSFORM(FILTER(properties.resourceIdentifiers, x -> x.type = 'AzureResource'),x -> x.azureResourceId )),'/')[1] else SPLIT(REGEXP_EXTRACT(ARRAY_MIN(TRANSFORM(FILTER(properties.resourceIdentifiers, x -> x.type = 'AzureResource'),x -> x.azureResourceId )),'\\\\/providers\\\\/[^\\\\/]+\\\\/(.+)'),'/')[0] end"}], "commonProperties": [{"colName": "type", "colExpr": "'Azure Subscription'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Center Alerts", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_center_alerts"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__ms_azure_security_center_alerts__resource_account_id"}