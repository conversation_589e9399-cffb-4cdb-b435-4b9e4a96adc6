{"primaryKey": "lower(accountId)", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_resource_details__account_id"}