{"primaryKey": "lower(REGEXP_EXTRACT(clusterArn,':([0-9]+):'))", "origin": "'AWS EMR EC2 Fleet'", "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'"}], "entitySpecificProperties": [{"colName": "account_id", "colExpr": "primary_key"}], "dataSource": {"name": "AWS", "feedName": "EMR EC2 Fleet", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_fleet"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_emr_ec2_fleet__account_id"}