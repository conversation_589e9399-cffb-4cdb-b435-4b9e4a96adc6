{"primaryKey": "lower(REGEXP_EXTRACT(StandardsSubscriptionArn, '([0-9]{12})'))", "origin": "'AWS Get Enabled Standards'", "temporaryProperties": [{"colName": "temp_stand_id", "colExpr": "upper(REGEXP_EXTRACT(StandardsArn, '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Account'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Get Enabled Standards", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_get_enabled_standards"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_account__aws_get_enabled_standards__standards_subscription_arn"}