{"primaryKey": "software", "origin": "'Mega'", "commonProperties": [{"colName": "type", "colExpr": "initcap(type)"}, {"colName": "activity_status", "colExpr": "case when lower(operational_status)='active' then 'Active' else 'Inactive' end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "retired_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "app_name", "colExpr": "lower(REPLACE(REPLACE(software, '_', ' '), '-', ' '))"}, {"colName": "category", "colExpr": "CASE WHEN software_category IN ('Off the Shelf-Customised','Off the Shelf Customized') THEN 'Off The Shelf Customised' ELSE INITCAP(REPLACE(REPLACE(software_category, '_', ' '), '-', ' ')) END"}, {"colName": "risk_category", "colExpr": "lower(risk_category)"}, {"colName": "criticality", "colExpr": "app_criticality"}, {"colName": "operational_status", "colExpr": "case when lower(lifecycle)!='retired' then 'Active' else 'Disabled' end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "lifecycle", "colExpr": "app_lifecycle"}, {"colName": "derived_criticality", "colExpr": "case when lower(risk_category) IN ('cat1','cat2') then true else false end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "internet_facing", "colExpr": "internet_facing"}, {"colName": "retired_date", "colExpr": "case when lower(app_lifecycle)='retired' then event_timestamp_epoch end"}, {"colName": "sensitive_information", "colExpr": "sensitive_info_present"}], "dataSource": {"name": "Mega", "feedName": "List Applications", "srdm": "<%SRDM_SCHEMA_NAME%>.mega"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__application__mega__software"}