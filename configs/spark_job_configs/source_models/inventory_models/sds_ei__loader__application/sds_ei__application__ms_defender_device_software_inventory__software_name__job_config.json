{"primaryKey": "temp_primary_key", "origin": "'MS Defender Device Software Inventory'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CONCAT_WS('',softwareName,softwareVersion)"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "'Software'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,app_first_seen)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "app_name", "colExpr": "lower(REPLACE(REPLACE(softwareName, '_', ' '), '-', ' '))"}, {"colName": "app_vendor", "colExpr": "softwareVendor"}, {"colName": "app_version", "colExpr": "softwareVersion"}], "sourceSpecificProperties": [{"colName": "app_first_seen", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(softwareFirstSeenTimestamp)))"}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Inventory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_software"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__application__ms_defender_device_software_inventory__software_name"}