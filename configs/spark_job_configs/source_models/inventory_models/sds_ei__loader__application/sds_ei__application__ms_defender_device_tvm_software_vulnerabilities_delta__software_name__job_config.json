{"primaryKey": "temp_primary_key", "origin": "'MS Defender Device TVM Software Vulnerability Delta'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CONCAT_WS('',softwareName,softwareVersion)"}, {"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "app_name", "colExpr": "lower(REPLACE(REPLACE(softwareName, '_', ' '), '-', ' '))"}, {"colName": "app_vendor", "colExpr": "softwareVendor"}, {"colName": "app_version", "colExpr": "softwareVersion"}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__application__ms_defender_device_tvm_software_vulnerabilities_delta__software_name"}