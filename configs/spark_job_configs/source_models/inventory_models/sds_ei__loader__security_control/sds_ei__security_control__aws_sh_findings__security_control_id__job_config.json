{"primaryKey": "lower(Compliance.SecurityControlId)", "origin": "'AWS SH Findings'", "temporaryProperties": [{"colName": "temp_stand", "colExpr": "transform(Compliance.AssociatedStandards,x->REGEXP_EXTRACT(upper(x.StandardsId), '\\/(.*)$'))"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Control'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "upper(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "status", "colExpr": "'Enabled'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "title", "colExpr": "Title"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_standards", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}], "dataSource": {"name": "AWS", "feedName": "SH Findings", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_findings"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_sh_findings__security_control_id"}