{"primaryKey": "lower(split(RemediationUrl,'/')[5])", "origin": "'AWS Describe Standards Controls'", "temporaryProperties": [{"colName": "temp_id", "colExpr": "split(RemediationUrl,'/')[5]"}, {"colName": "temp_stand", "colExpr": "collect_set(REGEXP_EXTRACT(upper(StandardsSubscriptionArn), '\\/(.*)$')) OVER (partition by split(RemediationUrl,'/')[5],DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}], "commonProperties": [{"colName": "type", "colExpr": "'AWS Compliance Control'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "Description"}], "entitySpecificProperties": [{"colName": "id", "colExpr": "upper(temp_id)"}, {"colName": "title", "colExpr": "Title"}, {"colName": "status", "colExpr": "initcap(ControlStatus)"}, {"colName": "associated_standards", "colExpr": "transform(temp_stand,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))"}, {"colName": "severity", "colExpr": "initcap(SeverityRating)"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "AWS", "feedName": "Describe Standards Controls", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__sh_describe_standards_controls"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__security_control__aws_describe_standards_controls__control_id"}