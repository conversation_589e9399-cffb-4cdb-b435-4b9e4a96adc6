{"primaryKey": "lower(containerID)", "origin": "'AWS EKS Container'", "commonProperties": [{"colName": "type", "colExpr": "'Kubernetes Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,aws_resource_created_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_eks_finish_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(clusterArn,'arn:aws:eks:[^:]+:(\\\\d+):.*')"}, {"colName": "operational_state", "colExpr": "case when lower(aws_container_status) like '%running%' then 'Active' when lower(aws_container_status) like '%terminated%' then 'Inactive' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "image", "colExpr": "aws_container_image", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_serverless", "colExpr": "false", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_privileged", "colExpr": "aws_container_privileged", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_tty_enabled", "colExpr": "aws_container_is_tty_enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_root", "colExpr": "aws_container_runs_as_root", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EKS Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "container_memory", "colExpr": "cast(resources.limits.memory as double)"}, {"colName": "container_volume_name", "colExpr": "volumeMounts.name"}, {"colName": "container_port_protocol", "colExpr": "array_distinct(cast(ports.protocol as array<string>))"}, {"colName": "container_environment_variables", "colExpr": "env"}, {"colName": "container_standard_input", "colExpr": "stdin"}, {"colName": "container_standard_input_once", "colExpr": "stdinOnce"}, {"colName": "container_privilege_escalation", "colExpr": "cast(securityContext.allowPrivilegeEscalation as string)"}, {"colName": "kubernetes_cluster_id", "colExpr": "clusterArn"}, {"colName": "kuberenetes_namespace", "colExpr": "namespace"}, {"colName": "kubernetes_pod_name", "colExpr": "podName"}, {"colName": "container_read_only_root_filesystem", "colExpr": "cast(securityContext.readOnlyRootFilesystem as string)"}, {"colName": "kubernetes_cluster_name", "colExpr": "split(clusterArn,'/')[1]"}, {"colName": "container_hostname", "colExpr": "nodeName"}, {"colName": "container_cpu", "colExpr": "resources.limits.cpu"}, {"colName": "container_application", "colExpr": "applicationName"}], "sourceSpecificProperties": [{"colName": "aws_resource_created_date", "colExpr": "COALESCE(UNIX_MILLIS(TIMESTAMP(to_timestamp(state.running.startedAt))),UNIX_MILLIS(TIMESTAMP(to_timestamp(state.terminated.startedAt))))"}, {"colName": "aws_container_status", "colExpr": "case when state.terminated.finishedAt is null then 'Running' else 'Terminated' end"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(aws_container_status) like '%running%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_container_image", "colExpr": "image"}, {"colName": "aws_container_port", "colExpr": "cast(ports.containerPort as array<string>)"}, {"colName": "aws_container_host_port", "colExpr": "cast(ports.hostPort as array<string>)[0]"}, {"colName": "aws_container_privileged", "colExpr": "securityContext.privileged"}, {"colName": "aws_container_is_tty_enabled", "colExpr": "tty"}, {"colName": "aws_container_runs_as_root", "colExpr": "case when cast(securityContext.runAsUser as string) like '0' then true else false end"}, {"colName": "aws_eks_finish_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(state.terminated.finishedAt)))"}, {"colName": "aws_region", "colExpr": "REGEXP_EXTRACT(clusterArn,'arn:aws:eks:([^:]+):.*')"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region = e.region", "sourcePreTransform": [{"colName": "aws_region", "colExpr": "REGEXP_EXTRACT(clusterArn,'arn:aws:eks:([^:]+):.*')"}]}], "dataSource": {"name": "AWS", "feedName": "<PERSON><PERSON> Container", "srdm": "<%SRDM_SCHEMA_NAME%>.aws_eks_container"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__aws_eks_container__containerid"}