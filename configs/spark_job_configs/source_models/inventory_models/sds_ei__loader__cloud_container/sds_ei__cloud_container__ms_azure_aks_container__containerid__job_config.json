{"primaryKey": "lower(containerID)", "origin": "'MS Azure AKS Container'", "commonProperties": [{"colName": "type", "colExpr": "'Kubernetes Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,azure_resource_created_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(azure_aks_finish_date,last_updated_attrs.operational_state.last_changed.last_found_date, CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "case when lower(azure_container_state) like '%running%' then 'Active' when lower(azure_container_state) like '%terminated%' then 'Inactive' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "image", "colExpr": "azure_container_image", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_serverless", "colExpr": "false", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_privileged", "colExpr": "azure_container_privileged", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_tty_enabled", "colExpr": "azure_container_is_tty_enabled", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_root", "colExpr": "azure_container_runs_as_root", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure AKS Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "container_memory", "colExpr": "cast(resources.limits.memory as double)"}, {"colName": "container_volume_name", "colExpr": "volumeMounts.name"}, {"colName": "container_port_protocol", "colExpr": "array_distinct(cast(ports.protocol as array<string>))"}, {"colName": "container_environment_variables", "colExpr": "env"}, {"colName": "container_standard_input", "colExpr": "stdin"}, {"colName": "container_standard_input_once", "colExpr": "stdinOnce"}, {"colName": "container_privilege_escalation", "colExpr": "cast(securityContext.allowPrivilegeEscalation as string)"}, {"colName": "kubernetes_cluster_id", "colExpr": "clusterArn"}, {"colName": "kubernetes_namespace", "colExpr": "namespace"}, {"colName": "kubernetes_pod_name", "colExpr": "podName"}, {"colName": "container_read_only_root_filesystem", "colExpr": "cast(securityContext.readOnlyRootFilesystem as string)"}, {"colName": "container_hostname", "colExpr": "nodeName"}, {"colName": "container_cpu", "colExpr": "resources.limits.cpu"}, {"colName": "container_application", "colExpr": "applicationName"}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(state.running.startedAt)))"}, {"colName": "azure_container_state", "colExpr": "case when state.terminated.finishedAt is null then 'Running' else 'Terminated' end"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(azure_container_state) like '%running%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "azure_container_image", "colExpr": "image"}, {"colName": "azure_container_port", "colExpr": "cast(ports.containerPort as array<string>)"}, {"colName": "azure_container_host_port", "colExpr": "cast(ports.hostPort as array<string>)[0]"}, {"colName": "azure_container_privileged", "colExpr": "securityContext.privileged"}, {"colName": "azure_container_is_tty_enabled", "colExpr": "tty"}, {"colName": "azure_aks_finish_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(state.terminated.finishedAt)))"}, {"colName": "azure_container_runs_as_root", "colExpr": "case when cast(securityContext.runAsUser as string) like '0' then true else false end"}], "dataSource": {"name": "Microsoft Azure", "feedName": "AKS Container", "srdm": "<%SRDM_SCHEMA_NAME%>.azure_aks_container"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__ms_azure_aks_container__containerid"}