{"primaryKey": "lower(concat(clusterId,'/','containers','/',name))", "origin": "'MS Azure ACI Container'", "commonProperties": [{"colName": "type", "colExpr": "'Serverless Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,azure_resource_created_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(azure_aci_finish_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "regexp_extract(azure_aci_cluster_id, '/subscriptions/([^/]+)/', 1)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "image", "colExpr": "azure_container_image", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_container_serverless", "colExpr": "true", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(azure_container_state) IN ('running') THEN 'Active' WHEN LOWER(azure_container_state) IN ('waiting','terminated') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure ACI Container'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "container_memory", "colExpr": "cast(properties.resources.requests.memoryInGB as double)"}, {"colName": "container_volume_name", "colExpr": "properties.volumeMounts.name"}, {"colName": "container_port_protocol", "colExpr": "properties.ports.protocol"}, {"colName": "container_environment_variables", "colExpr": "properties.environmentVariables"}, {"colName": "container_cpu", "colExpr": "cast(properties.resources.requests.cpu as int)"}, {"colName": "aci_cluster_name", "colExpr": "clusterName"}], "sourceSpecificProperties": [{"colName": "azure_container_image", "colExpr": "properties.image"}, {"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(to_timestamp(array_min(array_except(transform(properties.instanceView.events, x -> CASE WHEN x.name = 'Started' THEN x.firstTimestamp ELSE NULL END), array(null)))))"}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_region", "colExpr": "location"}, {"colName": "azure_container_port", "colExpr": "properties.ports.port"}, {"colName": "azure_container_state", "colExpr": "properties.instanceView.currentState.state"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(azure_container_state) like '%running%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "azure_container_gpu", "colExpr": "properties.resources.requests.gpu.sku"}, {"colName": "azure_container_gpu_count", "colExpr": "properties.resources.requests.gpu.count"}, {"colName": "azure_aci_finish_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.instanceView.currentState.finishTime)))"}, {"colName": "azure_aci_cluster_id", "colExpr": "clusterId"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "ACI Container", "srdm": "<%SRDM_SCHEMA_NAME%>.azure__aci_containers"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__ms_azure_aci_container__resource_id"}