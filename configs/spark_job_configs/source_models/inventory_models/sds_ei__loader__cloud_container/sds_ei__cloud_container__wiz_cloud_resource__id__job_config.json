{"primaryKey": "id", "filterBy": "lower(type) IN ('container')", "origin": "'Wiz Cloud Resource'", "temporaryProperties": [{"colName": "temp_type", "colExpr": "type"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN lower(graphEntity.properties.nativeType) LIKE '%ecs%' OR (lower(graphEntity.properties.nativeType) LIKE '%container%' AND lower(graphEntity.properties.cloudPlatform) LIKE '%aws%') THEN 'Container Service Container'  WHEN lower(graphEntity.properties.nativeType) LIKE '%containerapps%' OR lower(graphEntity.properties.nativeType) LIKE '%containerinstance%' THEN 'Serverless Container'  WHEN lower(graphEntity.properties.cloudPlatform) LIKE '%kubernetes%' THEN 'Kubernetes Container' ELSE NULL END"}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,aws_resource_created_date,azure_resource_created_date,wiz_onboarding_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(wiz_last_scan_date,wiz_modified_date,active_operational_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "graphEntity.properties.tags.Department"}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='aws' then graphEntity.providerUniqueId when lower(graphEntity.properties.cloudPlatform)='azure' then graphEntity.properties.externalId when lower(graphEntity.properties.cloudPlatform) = 'kubernetes' then coalesce(graphEntity.properties.providerUniqueId, graphEntity.properties.externalId) else null end"}, {"colName": "account_id", "colExpr": "graphEntity.properties.subscriptionExternalId"}, {"colName": "billing_tag", "colExpr": "graphEntity.properties.tags.billing"}, {"colName": "environment", "colExpr": "lower(graphEntity.properties._environments)"}, {"colName": "image", "colExpr": "graphEntity.properties.image"}, {"colName": "is_container_serverless", "colExpr": "graphEntity.properties.serverlessContainer"}, {"colName": "is_container_privileged", "colExpr": "graphEntity.properties.securityContext_privileged"}, {"colName": "is_container_tty_enabled", "colExpr": "graphEntity.properties.tty"}, {"colName": "is_container_root", "colExpr": "graphEntity.properties.securityContext_runsAsRoot"}, {"colName": "container_memory", "colExpr": "graphEntity.properties.memoryGB"}, {"colName": "container_volume_name", "colExpr": "graphEntity.properties.kubernetes_volumeMounts.name"}, {"colName": "container_port_protocol", "colExpr": "graphEntity.properties.boundPorts.networkProtocol"}, {"colName": "container_environment_variables", "colExpr": "array(graphEntity.properties.env)"}, {"colName": "container_standard_input", "colExpr": "graphEntity.properties.stdin"}, {"colName": "container_standard_input_once", "colExpr": "graphEntity.properties.stdinOnce"}, {"colName": "container_privilege_escalation", "colExpr": "graphEntity.properties.securityContext_kubernetes_allowPrivilegeEscalation"}, {"colName": "container_read_only_root_filesystem", "colExpr": "graphEntity.properties.securityContext_kubernetes_readOnlyRootFilesystem"}, {"colName": "cloud_provider", "colExpr": "CASE WHEN lower(graphEntity.properties.cloudPlatform) = 'kubernetes' AND lower(graphEntity.properties.kubernetes_kubernetesFlavor) = 'aks' THEN 'Azure' WHEN lower(graphEntity.properties.cloudPlatform) = 'kubernetes' AND lower(graphEntity.properties.kubernetes_kubernetesFlavor) = 'eks' THEN 'AWS' ELSE graphEntity.properties.cloudPlatform END"}, {"colName": "operational_state", "colExpr": "CASE WHEN lower(wiz_operational_state) = 'active' THEN 'Active'  WHEN lower(wiz_operational_state) = 'inactive' OR lower(wiz_operational_state) = 'error' THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "CASE WHEN lower(graphEntity.properties.nativeType) LIKE '%hostedcontainer%' THEN 'Hosted Container' WHEN lower(graphEntity.properties.nativeType) LIKE '%containerapps%' THEN 'Azure Container Apps Container' WHEN lower(graphEntity.properties.nativeType) LIKE '%ecs%' OR (lower(graphEntity.properties.nativeType) LIKE '%container%' AND lower(graphEntity.properties.cloudPlatform) LIKE '%aws%') THEN 'AWS ECS Container' WHEN lower(graphEntity.properties.nativeType) LIKE '%containerinstance%' THEN 'Azure ACI Container' WHEN lower(graphEntity.properties.nativeType) LIKE '%container%' AND lower(graphEntity.properties.cloudPlatform) LIKE '%kubernetes%' AND lower(graphEntity.properties.kubernetes_kubernetesFlavor) LIKE '%eks%' THEN 'AWS EKS Container' WHEN lower(graphEntity.properties.nativeType) LIKE '%container%' AND lower(graphEntity.properties.cloudPlatform) LIKE '%kubernetes%' AND lower(graphEntity.properties.kubernetes_kubernetesFlavor) LIKE '%aks%' THEN 'Azure AKS Container'  ELSE graphEntity.properties.nativeType END"}, {"colName": "region", "colExpr": "graphEntity.properties.region"}, {"colName": "is_accessible_from_internet", "colExpr": "graphEntity.typedProperties.accessibleFrom.internet"}, {"colName": "has_high_privileges", "colExpr": "graphEntity.typedProperties.hasHighPrivileges"}, {"colName": "has_admin_privileges", "colExpr": "graphEntity.typedProperties.hasAdminPrivileges"}, {"colName": "open_to_all_internet", "colExpr": "graphEntity.typedProperties.openToAllInternet"}, {"colName": "kubernetes_flavor", "colExpr": "graphEntity.properties.kubernetes_kubernetesFlavor"}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "CASE WHEN lower(graphEntity.properties.cloudPlatform)='kubernetes' AND lower(graphEntity.properties.kubernetes_kubernetesFlavor)='aks' THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate))) WHEN lower(graphEntity.properties.cloudPlatform)='azure' THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate))) END"}, {"colName": "aws_resource_created_date", "colExpr": "case when lower(graphEntity.properties.cloudPlatform)='kubernetes' and LOWER(graphEntity.properties.kubernetes_kubernetesFlavor)='eks' THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate))) when lower(graphEntity.properties.cloudPlatform) ='aws' then UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.creationDate))) else null end"}, {"colName": "wiz_operational_state", "colExpr": "graphEntity.properties.status"}, {"colName": "wiz_modified_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.properties.updatedAt)))"}, {"colName": "wiz_active_services", "colExpr": "graphEntity.properties.boundPorts"}, {"colName": "wiz_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(graphEntity.lastSeen)))"}, {"colName": "wiz_is_default_security_context", "colExpr": "graphEntity.properties.isDefaultSecurityContext"}, {"colName": "wiz_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "active_operational_date", "colExpr": "CASE WHEN lower(wiz_operational_state) IN ('active') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.graphEntity.properties.region = e.region"}], "dataSource": {"name": "Wiz", "feedName": "Cloud Resource", "srdm": "<%SRDM_SCHEMA_NAME%>.wiz__cloud_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_container__wiz_cloud_resource__id"}