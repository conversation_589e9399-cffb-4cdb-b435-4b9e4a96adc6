{"primaryKey": "temp_primary_key", "filterBy": "REGEXP_EXTRACT(to_json(variables), '\"sf_id\":\\s*\"([^\"]*)\"', 1) IS NOT NULL OR REGEXP_EXTRACT(to_json(variables), '\"employee_name\":\\s*\"([^\"]*)\"', 1) IS NOT NULL", "origin": "'ServiceNow ITSM'", "temporaryProperties": [{"colName": "temp_primary_key", "colExpr": "CASE WHEN REGEXP_EXTRACT(to_json(variables), '\"sf_id\":\\s*\"([^\"]*)\"', 1) = '' THEN NULL ELSE REGEXP_EXTRACT(to_json(variables), '\"sf_id\":\"([^\"]*)\"', 1) END || CASE WHEN REGEXP_EXTRACT(to_json(variables), '\"employee_name\":\\s*\"([^\"]*)\"', 1) = '' THEN NULL ELSE REGEXP_EXTRACT(to_json(variables), '\"employee_name\":\"([^\"]*)\"', 1) END"}], "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,ad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"employee_classification\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"employee_classification\":\"([^\"]*)\"',1) END"}, {"colName": "business_unit", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"business_unit\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"business_unit\":\"([^\"]*)\"',1) END"}, {"colName": "location_country", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"location\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"location\":\"([^\"]*)\"',1) END"}, {"colName": "department", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"department\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"department\":\"([^\"]*)\"',1) END"}, {"colName": "ad_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp((CASE WHEN regexp_extract(to_json(variables),'\"start_date\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"start_date\":\"([^\"]*)\"',1) END))))"}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(CASE WHEN regexp_extract(to_json(variables),'\"employee_name\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"employee_name\":\"([^\"]*)\"',1) END)"}, {"colName": "email_id", "colExpr": "LOWER(CASE WHEN regexp_extract(to_json(variables),'\"employee_email\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"employee_email\":\"([^\"]*)\"',1) END)"}, {"colName": "manager", "colExpr": "INITCAP(CASE WHEN regexp_extract(to_json(variables),'\"manager\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"manager\":\"([^\"]*)\"',1) END)"}, {"colName": "employee_id", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"sf_id\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"sf_id\":\"([^\"]*)\"',1) END"}, {"colName": "job_title", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"job_title\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"job_title\":\"([^\"]*)\"',1) END"}, {"colName": "cost_center", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"costcenter\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"costcenter\":\"([^\"]*)\"',1) END"}, {"colName": "phone_number", "colExpr": "CASE WHEN regexp_extract(to_json(variables),'\"phone_number\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"phone_number\":\"([^\"]*)\"',1) END"}, {"colName": "last_known_termination_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp((CASE WHEN regexp_extract(to_json(variables),'\"end_date\":\\s*\"([^\"]*)\"',1)='' THEN NULL ELSE regexp_extract(to_json(variables),'\"end_date\":\"([^\"]*)\"',1) END))))"}], "dataSource": {"name": "ServiceNow", "feedName": "Service Catalog ITSM", "srdm": "<%SRDM_SCHEMA_NAME%>.servicenow__service_catalog"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__snow_itsm__sf_id_employee_id"}