{"primaryKey": "cast(User__ID as string)", "origin": "'SuccessFactors'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,recruit_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "Employee__Class"}, {"colName": "business_unit", "colExpr": "Business__Unit"}, {"colName": "location_city", "colExpr": "city"}, {"colName": "department", "colExpr": "Employee__Department"}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(concat_ws(' ',first_name,middle_name,last_name))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_name", "colExpr": "INITCAP(First__Name)"}, {"colName": "last_name", "colExpr": "INITCAP(Last__Name)"}, {"colName": "middle_name", "colExpr": "Middle__Name"}, {"colName": "email_id", "colExpr": "LOWER(Email)"}, {"colName": "manager", "colExpr": "INITCAP(CONCAT_WS(' ',Manager__s__First__Name,Manager__s__Last__Name))"}, {"colName": "manager_id", "colExpr": "Manager__ID"}, {"colName": "employee_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "external_email_id", "colExpr": "LOWER(External__Business__Email__Address)"}, {"colName": "recruit_date", "colExpr": "to_unix_timestamp(Recruit__date,'d[/][-]M[/][-]y[ HH:mm:ss]')*1000"}, {"colName": "last_known_termination_date", "colExpr": "to_unix_timestamp(Termination__Date,'d[/][-]M[/][-]y[ HH:mm:ss]')*1000", "fieldsSpec": {"replaceExpression": false}}, {"colName": "contract_end_date", "colExpr": "to_unix_timestamp(Contract__End__Date,'dd/MM/yyyy[ HH:mm:ss]')*1000"}, {"colName": "job_title", "colExpr": "Job__Title"}, {"colName": "job_position_id", "colExpr": "CAST(Position__ID as string)"}, {"colName": "legal_entity", "colExpr": "Legal__Entity"}, {"colName": "cost_center", "colExpr": "Cost__Center"}, {"colName": "employee_status", "colExpr": "CASE WHEN LOWER(Employee__Status) LIKE '%terminated%' THEN 'Terminated' ELSE 'Active' END"}, {"colName": "termination_date", "colExpr": "to_unix_timestamp(Termination__Date,'d[/][-]M[/][-]y[ HH:mm:ss]')*1000", "fieldsSpec": {"persistNonNullValue": false, "replaceExpression": false}}], "sourceSpecificProperties": [{"colName": "sf_employee_status", "colExpr": "Employee__Status"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country = e.region"}], "dataSource": {"name": "SuccessFactors", "feedName": "HR", "srdm": "<%SRDM_SCHEMA_NAME%>.sap__successfactors"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__successfactors_hr__employee_id"}