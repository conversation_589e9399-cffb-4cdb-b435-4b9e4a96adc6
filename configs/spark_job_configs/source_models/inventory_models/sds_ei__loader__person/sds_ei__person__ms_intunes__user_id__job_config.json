{"primaryKey": "userId", "origin": "'MS Intune'", "commonProperties": [{"colName": "last_active_date", "colExpr": "login_last_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(userDisplayName)"}, {"colName": "email_id", "colExpr": "LOWER(emailAddress)"}, {"colName": "phone_number", "colExpr": "phoneNumber"}, {"colName": "login_last_date", "colExpr": "CASE WHEN lastLogOnDateTime is not null THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(lastLogOnDateTime))) ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}], "sourceSpecificProperties": [{"colName": "aad_user_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "MS Intune", "feedName": "MDM", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft__intune"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_intunes__user_id"}