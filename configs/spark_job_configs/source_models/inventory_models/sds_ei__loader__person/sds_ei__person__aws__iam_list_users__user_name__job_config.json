{"primaryKey": "UserName", "filterBy": "PasswordLastUsed is not null", "origin": "'AWS IAM Users'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "aws_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "password_last_used", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(UserName)"}, {"colName": "password_last_used", "colExpr": "CASE WHEN (passwordlastused != '' AND (passwordlastused IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(passwordlastused))) ELSE NULL END"}, {"colName": "aws_iam_user_name", "colExpr": "UserName"}], "sourceSpecificProperties": [{"colName": "aws_created_date", "colExpr": "CASE WHEN (CreateDate != '' AND (CreateDate IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(CreateDate))) ELSE NULL END"}], "dataSource": {"name": "AWS", "feedName": "IAM Users", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__iam_list_users"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__aws__iam_list_users__user_name"}