{"primaryKey": "id", "filterBy": "id is not null and TRIM(id)!='' and not(usageLocation is null and creationType is null and mail like '%prevalent%' or lower(displayName) like '%account%' or lower(displayName) like '%support%' or lower(displayName) like '%events%' or lower(displayName) like '%training%' or lower(displayName) like '%service%' or lower(displayName) like '%aws%' or lower(displayName) like '%dast%' or lower(displayName) like '%enquiries%' or lower(displayName) like '%team%' or lower(displayName) like '%l&d%'  or lower(displayName) like '%forum%' or lower(displayName) like '%learning%'  or lower(displayName) like '%admin%'  or lower(displayName) like '%payroll%'  or lower(displayName) like '%notifications%'  or lower(displayName) like '%posh%' or lower(displayName) like '%prevalent%' or lower(displayName) like '%skype%' or lower(displayName) like '%system%' or lower(displayName) like '%talent%' or lower(displayName) like '%jira%' or lower(displayName) like '%monitor%' or lower(displayName) like '%test%' or lower(displayName) like '%platform%' or lower(displayName) like '%contact%' or lower(displayName) like '%info%' or lower(displayName) like '%pai%')", "origin": "'MS Azure AD Users'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date,aad_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "aad_deleted_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department"}], "entitySpecificProperties": [{"colName": "full_name", "colExpr": "INITCAP(displayName)"}, {"colName": "first_name", "colExpr": "INITCAP(givenName)"}, {"colName": "last_name", "colExpr": "<PERSON><PERSON><PERSON><PERSON>(surname)"}, {"colName": "email_id", "colExpr": "LOWER(mail)"}, {"colName": "employee_id", "colExpr": "employeeId"}, {"colName": "company", "colExpr": "companyName"}, {"colName": "job_title", "colExpr": "jobTitle"}, {"colName": "phone_number", "colExpr": "mobilePhone"}, {"colName": "external_email_id", "colExpr": "transform(filter(otherMails, em -> em is not null and em not rlike '^[ \\t]*+$'), em -> lower(em))"}], "sourceSpecificProperties": [{"colName": "ad_last_sync_date", "colExpr": "CASE WHEN (onPremisesLastSyncDateTime != '' AND (onPremisesLastSyncDateTime IS NOT NULL )) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(onPremisesLastSyncDateTime))) ELSE NULL END"}, {"colName": "aad_deleted_date", "colExpr": "CASE WHEN (deletedDateTime != '' AND (deletedDateTime IS NOT NULL)) THEN UNIX_MILLIS(TIMESTAMP(to_timestamp(deletedDateTime))) ELSE NULL END"}, {"colName": "aad_created_date", "colExpr": "CASE WHEN (createdDateTime != '' AND (createdDateTime IS NOT NULL )) THEN  UNIX_MILLIS(TIMESTAMP(to_timestamp(createdDateTime))) ELSE NULL END"}, {"colName": "aad_user_id", "colExpr": "id"}, {"colName": "aad_operational_status", "colExpr": "CASE WHEN TRIM(accountEnabled) like 'false' THEN 'Disabled' WHEN TRIM(accountEnabled) like 'true' THEN 'Active' ELSE NULL END", "fieldsSpec": {"persistNonNullValue": false}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country"]}, "joinCondition": "s.country = e.region"}], "dataSource": {"name": "MS Azure AD", "feedName": "Users", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__ad_user"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__person__ms_azure_ad_users__aad_id"}