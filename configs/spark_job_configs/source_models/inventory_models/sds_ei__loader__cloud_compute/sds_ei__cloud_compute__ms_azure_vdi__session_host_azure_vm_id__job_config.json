{"primaryKey": "lower(SessionHostAzureVmId)", "filterBy": "SessionHostAzureVmId != '<>'", "origin": "'MS Azure VDI'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "Greatest(last_found_date,azure_resource_created_date,last_updated_attrs.operational_state.last_changed.last_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(Greatest(last_found_date,azure_resource_created_date,last_updated_attrs.operational_state.last_changed.last_found_date),first_found_date,azure_resource_created_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "CASE WHEN SessionHostName='<>' THEN NULL ELSE lower(SessionHostName) END"}, {"colName": "os", "colExpr": "CASE WHEN (CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END) IS NULL THEN NULL WHEN LOWER(CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END) LIKE '%unknown%' \n THEN null WHEN LOWER(CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',CASE WHEN SessionHostOSDescription='<>' THEN NULL ELSE SessionHostOSDescription END,\n CASE WHEN NOT regexp_like((CASE WHEN SessionHostOSVersion='<>' THEN NULL ELSE SessionHostOSVersion END),'(?i)unknown|other') THEN (CASE WHEN SessionHostOSVersion='<>' THEN NULL ELSE SessionHostOSVersion END) END) END"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip", "colExpr": "case when SessionHostIPAddress ='<>' then Null Else regexp_replace(SessionHostIPAddress,'â‰¤|â‰¥','') end", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "login_last_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "case when lower(State) IN ('connected', 'started') then UserName end"}, {"colName": "cloud_instance_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "'Active'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "azure_resource_created_date", "colExpr": "case when lower(State) IN ('connected', 'started') then event_timestamp_epoch end", "fieldsSpec": {"aggregateFunction": "min"}}, {"colName": "azure_operational_state", "colExpr": "State"}], "dataSource": {"name": "Microsoft Azure", "feedName": "VDI", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__vdi"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_vdi__session_host_azure_vm_id"}