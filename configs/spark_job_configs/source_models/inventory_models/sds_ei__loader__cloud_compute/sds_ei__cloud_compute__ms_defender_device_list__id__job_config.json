{"primaryKey": "id", "filterBy": "vmMetadata.resourceId IS NOT NULL", "origin": "'MS Defender Device List'", "commonProperties": [{"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date,defender_onboarding_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(defender_onboarding_date,edr_last_scan_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "cloud_provider", "colExpr": "vmMetadata.cloudProvider"}, {"colName": "resource_id", "colExpr": "lower(vmMetadata.resourceId)"}, {"colName": "resource_name", "colExpr": "lower(computerDnsName)"}, {"colName": "os", "colExpr": "CASE WHEN osPlatform IS NULL OR lower(osPlatform) like '%unknown%' THEN NULL \nWHEN regexp_like(osPlatform,'(?i)other') THEN 'Other' \nELSE concat_ws(' ',osPlatform,CASE WHEN NOT regexp_like(osVersion,'(?i)other|unknown') THEN osVersion END,CASE WHEN NOT regexp_like(osBuild,'(?i)other|unknown') THEN osBuild END,CASE WHEN NOT regexp_like(osArchitecture,'(?i)other|unknown') THEN osArchitecture END,CASE WHEN NOT regexp_like(osProcessor,'(?i)other|unknown') THEN osProcessor END)\nEND"}, {"colName": "vm_tracking_method", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN 'MS Defender Agent' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN TRUE ELSE FALSE END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN lower(defender_onboarding_status) ='onboarded' THEN 'MS Defender' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_product", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN 'MS Defender' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_onboarding_status", "colExpr": "CASE WHEN lower(defender_onboarding_status)='onboarded' THEN true ELSE false END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip", "colExpr": "lastIpAddress"}, {"colName": "edr_last_scan_date", "colExpr": "CASE WHEN lastSeen IS NOT NULL THEN UNIX_MILLIS(TIMESTAMP(CONCAT(SUBSTRING(lastSeen,1,19), 'Z'))) ELSE NULL END"}, {"colName": "account_id", "colExpr": "vmMetadata.subscriptionId"}, {"colName": "cloud_instance_id", "colExpr": "lower(vmMetadata.vmId)"}, {"colName": "native_type", "colExpr": "case when lower(cloud_provider)='azure' then 'Azure Virtual Machine' when lower(cloud_provider)='aws' then 'AWS EC2 Instance' else null end", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "defender_exposure_level", "colExpr": "exposureLevel"}, {"colName": "defender_onboarding_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(CONCAT(SUBSTRING(firstSeen,1,19), 'Z')))"}, {"colName": "aad_device_id", "colExpr": "aadDeviceId"}, {"colName": "defender_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_onboarding_status", "colExpr": "onboardingStatus"}, {"colName": "defender_detection_method", "colExpr": "CASE WHEN lastExternalIpAddress  is null then 'Network Scan' else 'Defender Agent' end"}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device List", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_list"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_list__id"}