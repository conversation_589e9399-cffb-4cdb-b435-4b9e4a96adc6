{"primaryKey": "CAST(ID as string)", "origin": "'Qualys Host List'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN lower(CLOUD_SERVICE) LIKE '%ec2%' OR lower(CLOUD_SERVICE) LIKE '%vm%' OR lower(CLOUD_SERVICE) LIKE '%virtualmachine%' THEN 'Virtual Machine' ELSE NULL END"}, {"colName": "last_active_date", "colExpr": "vm_last_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os", "colExpr": "OS"}, {"colName": "cloud_provider", "colExpr": "CLOUD_PROVIDER"}, {"colName": "resource_name", "colExpr": "CASE WHEN lower(CLOUD_PROVIDER) LIKE 'aws' THEN lower(CLOUD_RESOURCE_ID) ELSE NULL END"}, {"colName": "private_ip", "colExpr": "IP"}, {"colName": "vm_tracking_method", "colExpr": "qualys_detection_method", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN lower(qualys_detection_method) LIKE '%agent%' THEN 'Qualys' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN lower(qualys_detection_method) LIKE '%agent%' THEN TRUE ELSE FALSE END"}, {"colName": "vm_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(GREATEST (CASE WHEN LAST_VM_SCANNED_DATE != '[]' AND LAST_VM_SCANNED_DATE != '(never)' AND LAST_VM_SCANNED_DATE != 'NULL' THEN LAST_VM_SCANNED_DATE end,CASE WHEN LAST_VULN_SCAN_DATETIME != '[]' AND LAST_VULN_SCAN_DATETIME != '(never)' AND LAST_VULN_SCAN_DATETIME != 'NULL' THEN LAST_VULN_SCAN_DATETIME end,case WHEN LAST_VM_AUTH_SCANNED_DATE != '[]' AND LAST_VM_AUTH_SCANNED_DATE != '(never)' AND LAST_VM_AUTH_SCANNED_DATE != 'NULL' THEN LAST_VM_AUTH_SCANNED_DATE end,case WHEN LAST_COMPLIANCE_SCAN_DATETIME != '[]' AND LAST_COMPLIANCE_SCAN_DATETIME != '(never)' AND LAST_COMPLIANCE_SCAN_DATETIME != 'NULL' THEN LAST_COMPLIANCE_SCAN_DATETIME end,case WHEN LAST_SCAP_SCAN_DATETIME != '[]' AND LAST_SCAP_SCAN_DATETIME != '(never)' AND LAST_SCAP_SCAN_DATETIME != 'NULL' THEN LAST_SCAP_SCAN_DATETIME end))))"}, {"colName": "cloud_instance_id", "colExpr": "CASE WHEN lower(CLOUD_PROVIDER) LIKE 'azure' OR lower(CLOUD_PROVIDER) LIKE 'aws' THEN lower(CLOUD_RESOURCE_ID) ELSE NULL END"}, {"colName": "native_type", "colExpr": "CASE WHEN lower(CLOUD_SERVICE) LIKE '%ec2%' THEN 'AWS EC2 Instance'  WHEN lower(CLOUD_SERVICE) LIKE '%vm%' or lower(CLOUD_SERVICE) LIKE '%virtualmachine%' THEN 'Azure Virtual Machine' ELSE NULL END"}], "sourceSpecificProperties": [{"colName": "qualys_detection_method", "colExpr": "CONCAT_WS(' ','Qualys',TRACKING_METHOD)"}], "dataSource": {"name": "Qualys", "feedName": "Host List", "srdm": "<%SRDM_SCHEMA_NAME%>.qualys__host_list"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__qualys_host_list__id"}