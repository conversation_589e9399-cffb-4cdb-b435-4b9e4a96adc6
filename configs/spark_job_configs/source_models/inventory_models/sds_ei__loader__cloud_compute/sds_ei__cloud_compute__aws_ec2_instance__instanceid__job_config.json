{"primaryKey": "lower(InstanceId)", "origin": "'AWS EC2 Instance'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_instance_launch_date,aws_instance_attach_time,aws_instance_usage_update_time, aws_instance_state_update_time,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "case when PlatformDetails ='Linux/UNIX' then 'Linux' else  PlatformDetails end"}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "billing_tag", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'Billing' THEN x.Value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags, x -> (CASE WHEN x.Key = 'Environment' THEN lower(x.Value) END)),ARRAY(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "OwnerId"}, {"colName": "private_ip", "colExpr": "aws_instance_private_ip_address", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "public_ip", "colExpr": "aws_instance_public_ip_address", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_dns_name", "colExpr": "PrivateDnsName"}, {"colName": "public_dns_name", "colExpr": "PublicDnsName"}, {"colName": "accessibility", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "instance_name", "colExpr": "lower(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'Name' THEN x.Value END)), array(NULL))[0])", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN 'Active' WHEN LOWER(aws_operational_state) IN ('stopped', 'pending', 'shutting-down', 'stopping', 'failed', 'inactive', 'delete_in_progress', 'terminated') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "image", "colExpr": "aws_instance_image_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_security_group_id", "colExpr": "aws_instance_security_group_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_network_interface_count", "colExpr": "case when aws_instance_network_interface_id is null then null else size(aws_instance_network_interface_id) end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_type", "colExpr": "InstanceType"}, {"colName": "cloud_instance_lifecycle", "colExpr": "aws_instance_lifecycle", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Instance'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN aws_availability_zone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN aws_availability_zone= 'Not Applicable'  THEN 'Not Applicable' WHEN aws_availability_zone= 'Regional'  THEN 'Regional' WHEN aws_availability_zone IS NULL THEN NULL ELSE 'Single' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "CASE WHEN lower(aws_instance_lifecycle) LIKE '%spot%' THEN True when array_contains(aws_tags.Key,'aws:ec2spot:fleet-request-id') or array_contains(aws_tags.Key,'aws:autoscaling:groupName') or array_contains(aws_tags.Key,'spotinst:aws:ec2:group:id') or array_contains(aws_tags.Key,'aws:ec2:fleet-id') or array_contains(aws_tags.Key,'gitlab_autoscaler_token') or array_contains(aws_tags.Key,'aws:elasticmapreduce:job-flow-id') or array_contains(aws_tags.Key,'spotinst:aws:ec2:group:createdBy:spotinst') then True Else False end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_group_name", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'aws:autoscaling:groupName' THEN x.Value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_cluster_name", "colExpr": "COALESCE(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'eks:cluster-name' THEN x.Value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'aws:eks:cluster-name' THEN x.Value END)), array(NULL))[0])", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "kubernetes_node_group_name", "colExpr": "COALESCE(ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'eks:nodegroup-name' THEN x.Value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'aws:eks:nodegroup-name' THEN x.Value END)), array(NULL))[0])", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ec2fleet_id", "colExpr": "ARRAY_EXCEPT(TRANSFORM(aws_tags , x -> (CASE WHEN x.Key = 'aws:ec2:fleet-id' THEN x.Value END)), array(NULL))[0]", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "aws_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "instance_imdsv2_status", "colExpr": "INITCAP(MetadataOptions.HttpTokens)"}], "sourceSpecificProperties": [{"colName": "aws_operational_state", "colExpr": "lower(State.Name)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(aws_operational_state) IN ('active', 'running') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_tags", "colExpr": "Tags"}, {"colName": "aws_tag", "colExpr": "concat_ws(',', transform(Tags, d -> concat(d.Key,' : ',d.Value)))"}, {"colName": "aws_instance_image_id", "colExpr": "ImageId"}, {"colName": "aws_instance_private_ip_address", "colExpr": "PrivateIpAddress"}, {"colName": "aws_instance_public_ip_address", "colExpr": "PublicIpAddress"}, {"colName": "aws_instance_monitoring_state", "colExpr": "INITCAP(Monitoring.State)"}, {"colName": "aws_instance_launch_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(LaunchTime)))"}, {"colName": "aws_instance_usage_update_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(UsageOperationUpdateTime)))"}, {"colName": "aws_instance_state_update_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(REGEXP_EXTRACT(StateTransitionReason, '([0-9]{4}-[0-9]{2}-[0-9]{2})'))))"}, {"colName": "aws_instance_attach_time", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(array_max(NetworkInterfaces.Attachment.AttachTime))))"}, {"colName": "aws_instance_vpc_id", "colExpr": "VpcId"}, {"colName": "aws_instance_network_interface_id", "colExpr": "NetworkInterfaces.NetworkInterfaceId"}, {"colName": "aws_instance_volume_id", "colExpr": "BlockDeviceMappings.Ebs.VolumeId"}, {"colName": "aws_instance_subnet_id", "colExpr": "SubnetId"}, {"colName": "aws_instance_security_group_id", "colExpr": "SecurityGroups.GroupId"}, {"colName": "aws_instance_image_architecture", "colExpr": "Architecture"}, {"colName": "aws_instance_lifecycle", "colExpr": "case when lower(InstanceLifecycle)='spot' then lower(InstanceLifecycle) else 'scheduled' end"}, {"colName": "aws_availability_zone", "colExpr": "Placement.AvailabilityZone"}, {"colName": "aws_container_node_key", "colExpr": "PrivateDnsName"}, {"colName": "aws_region", "colExpr": "REGEXP_EXTRACT(Placement.AvailabilityZone, '^(.*).$', 1)"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region_loc = e.region", "sourcePreTransform": [{"colName": "aws_region_loc", "colExpr": "REGEXP_EXTRACT(Placement.AvailabilityZone, '^(.*).$', 1)"}]}], "dataSource": {"name": "AWS", "feedName": "EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__ec2_describe_instances"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_ec2_instance__instanceid"}