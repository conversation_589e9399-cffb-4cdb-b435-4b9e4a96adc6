{"primaryKey": "deviceId", "origin": "'MS Defender Device TVM Software Vulnerability'", "temporaryProperties": [{"colName": "cve_last_seen_epoch", "colExpr": "CASE WHEN lastSeenTimestamp IS NOT NULL THEN UNIX_MILLIS(TIMESTAMP(CONCAT(SUBSTRING(lastSeenTimestamp,1,19), 'Z'))) ELSE NULL END"}, {"colName": "first_seen_timestamp_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(firstSeenTimestamp)))"}], "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "vulnerability_last_observed_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os", "colExpr": "CASE WHEN osPlatform IS NULL THEN NULL WHEN LOWER(osPlatform) LIKE '%unknown%' THEN NULL WHEN LOWER(osPlatform) LIKE '%other%' THEN 'Other' ELSE CONCAT_WS(' ',osPlatform,CASE WHEN NOT regexp_like(osVersion,'(?i)unknown|other') THEN osVersion END,CASE WHEN NOT regexp_like(osArchitecture,'(?i)unknown|other') THEN osArchitecture END) END"}, {"colName": "resource_name", "colExpr": "lower(deviceName)"}, {"colName": "vm_last_scan_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vulnerability_last_observed_date", "colExpr": "cve_last_seen_epoch", "fieldsSpec": {"aggregateFunction": "max"}}], "sourceSpecificProperties": [{"colName": "defender_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'"}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_software_vuln_delta"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_tvm_software_vulnerabilities_delta__device_id"}