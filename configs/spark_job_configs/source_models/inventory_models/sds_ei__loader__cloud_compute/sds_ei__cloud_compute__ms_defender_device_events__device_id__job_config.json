{"primaryKey": "DeviceId", "origin": "'MS Defender Device Events'", "temporaryProperties": [{"colName": "was_remediated", "colExpr": "(CAST(CASE WHEN REGEXP_EXTRACT(AdditionalFields,'WasRemediated\"\\s*:(?<wasRemediated>[^\\r\\n\",]*)',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'WasRemediated\"\\s*:(?<wasRemediated>[^\\r\\n\",]*)',1) END as <PERSON><PERSON><PERSON>) )"}, {"colName": "threat_name", "colExpr": "(CASE WHEN REGEXP_EXTRACT(AdditionalFields,'ThreatName\"\\s*:\"(?<threatName>[^\\r\\n\"]*)\"',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'ThreatName\"\\s*:\"(?<threatName>[^\\r\\n\"]*)\"',1) END)"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(DeviceName)"}, {"colName": "login_last_user", "colExpr": "(CASE WHEN REGEXP_EXTRACT(AdditionalFields,'User\"\\s*:\"(?<user>[^\\r\\n\"]*)\"',1)='' THEN NULL ELSE REGEXP_EXTRACT(AdditionalFields,'User\"\\s*:\"(?<user>[^\\r\\n\"]*)\"',1) END)"}, {"colName": "av_status", "colExpr": "case when CAST(defender_action_type AS STRING) like '%AntivirusScanCompleted%' then true else false end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_last_scan_date", "colExpr": "case when CAST(ActionType  AS STRING) like '%AntivirusScanCompleted%' then event_timestamp_epoch end", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "edr_threat_count", "colExpr": "defender_threat_count", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "defender_action_type", "colExpr": "collect_set(ActionType ) OVER (partition by primary_key  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "defender_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_threat_name", "colExpr": "collect_set(CASE WHEN was_remediated=false OR was_remediated is null THEN threat_name END) OVER (partition by primary_key  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_threat_count", "colExpr": "CASE WHEN size(defender_threat_name)<0 THEN 0 ELSE size(defender_threat_name) END", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Events", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_device_events"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_events__device_id"}