{"primaryKey": "lower(Id)", "origin": "'AWS EMR EC2 Fleet'", "commonProperties": [{"colName": "type", "colExpr": "'Compute Instance Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "aws_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_emr_end_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(Name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN aws_operational_state IN ('PROVISIONING', 'BOOTSTRAPPING', 'RUNNING', 'RESIZING') THEN 'Active' WHEN aws_operational_state IN ('SUSPENDED', 'TERMINATING', 'TERMINATED') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Fleet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(aws_emr_cluster_arn,':([0-9]+):')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "scaling_instance_type", "colExpr": "array_distinct(InstanceTypeSpecifications.InstanceType)"}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ec2fleet_id", "colExpr": "Id"}, {"colName": "emr_cluster_id", "colExpr": "clusterId"}], "sourceSpecificProperties": [{"colName": "aws_operational_state", "colExpr": "lower(Status.State)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN aws_operational_state IN ('PROVISIONING', 'BOOTSTRAPPING', 'RUNNING', 'RESIZING') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.CreationDateTime)))"}, {"colName": "aws_ec2fleet_on_demand_target_capacity", "colExpr": "TargetOnDemandCapacity"}, {"colName": "aws_ec2fleet_spot_target_capacity", "colExpr": "TargetSpotCapacity"}, {"colName": "aws_ec2fleet_provisioned_on_demand_capacity", "colExpr": "ProvisionedOnDemandCapacity"}, {"colName": "aws_ec2fleet_provisioned_spot_capacity", "colExpr": "ProvisionedSpotCapacity"}, {"colName": "aws_emr_instance_fleet_type", "colExpr": "InstanceFleetType"}, {"colName": "aws_emr_end_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.EndDateTime)))"}, {"colName": "aws_emr_cluster_arn", "colExpr": "clusterArn"}, {"colName": "aws_region", "colExpr": "regexp_extract(aws_emr_cluster_arn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region_loc = e.region", "sourcePreTransform": [{"colName": "aws_region_loc", "colExpr": "regexp_extract(clusterArn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)"}]}], "dataSource": {"name": "AWS", "feedName": "EMR EC2 Fleet", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_fleet"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_ec2_fleet__resource_id"}