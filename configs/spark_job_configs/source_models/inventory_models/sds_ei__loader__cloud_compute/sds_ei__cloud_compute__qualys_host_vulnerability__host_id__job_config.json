{"primaryKey": "host_id", "origin": "'Qualys Host Vulnerability'", "temporaryProperties": [{"colName": "first_found_datetime_epoch", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(first_found_datetime)))"}], "commonProperties": [{"colName": "last_active_date", "colExpr": "vm_last_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os"}, {"colName": "private_ip", "colExpr": "host_ip"}, {"colName": "vm_product", "colExpr": "CASE WHEN lower(qualys_detection_method) LIKE '%agent%' THEN 'Qualys' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(GREATEST (CASE WHEN last_scan_datetime != '[]' AND last_scan_datetime != '(never)' AND last_scan_datetime != 'NULL' THEN last_scan_datetime  end,case WHEN last_vm_scanned_date != '[]' AND last_vm_scanned_date != '(never)' AND last_vm_scanned_date != 'NULL' THEN last_vm_scanned_date END))))"}, {"colName": "vm_tracking_method", "colExpr": "qualys_detection_method", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "CASE WHEN lower(qualys_detection_method) LIKE '%agent%' THEN TRUE ELSE FALSE END", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "qualys_detection_method", "colExpr": "CONCAT_WS(' ','Qualys',tracking_method)"}], "dataSource": {"name": "Qualys", "feedName": "Host Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.qualys__host_vulnerability"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__qualys_host_vulnerability__host_id"}