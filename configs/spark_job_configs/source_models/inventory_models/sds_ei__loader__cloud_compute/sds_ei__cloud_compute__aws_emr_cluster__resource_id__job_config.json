{"primaryKey": "lower(Id)", "origin": "'AWS EMR Cluster'", "commonProperties": [{"colName": "type", "colExpr": "'MapReduce Cluster'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "aws_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_emr_end_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(Name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "public_dns_name", "colExpr": "MasterPublicDnsName"}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(aws_emr_cluster_arn,':([0-9]+):')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN aws_operational_state IN ('STARTING', 'BOOTSTRAPPING', 'RUNNING', 'WAITING') THEN 'Active' WHEN aws_operational_state IN ('TERMINATING', 'TERMINATED', 'TERMINATED_WITH_ERRORS') THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EMR Cluster'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN aws_availability_zone = 'Multiple Availability Zones' THEN 'Multiple'  WHEN aws_availability_zone= 'Not Applicable'  THEN 'Not Applicable' WHEN aws_availability_zone= 'Regional'  THEN 'Regional' WHEN aws_availability_zone IS NULL THEN NULL ELSE 'Single' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "emr_cluster_id", "colExpr": "Id"}], "sourceSpecificProperties": [{"colName": "aws_tags", "colExpr": "Tags"}, {"colName": "aws_operational_state", "colExpr": "lower(Status.State)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN aws_operational_state IN ('STARTING', 'BOOTSTRAPPING', 'RUNNING', 'WAITING') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.CreationDateTime)))"}, {"colName": "aws_instance_subnet_id", "colExpr": "Ec2InstanceAttributes.Ec2SubnetId"}, {"colName": "aws_availability_zone", "colExpr": "Ec2InstanceAttributes.Ec2AvailabilityZone"}, {"colName": "aws_emr_master_security_group_id", "colExpr": "Ec2InstanceAttributes.EmrManagedMasterSecurityGroup"}, {"colName": "aws_emr_slave_security_group_id", "colExpr": "Ec2InstanceAttributes.EmrManagedSlaveSecurityGroup"}, {"colName": "aws_emr_service_security_group_id", "colExpr": "Ec2InstanceAttributes.ServiceAccessSecurityGroup"}, {"colName": "aws_emr_instance_collection_type", "colExpr": "InstanceCollectionType"}, {"colName": "aws_emr_version", "colExpr": "ReleaseLabel"}, {"colName": "aws_emr_auto_terminate", "colExpr": "AutoTerminate"}, {"colName": "aws_emr_end_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.EndDateTime)))"}, {"colName": "aws_emr_visible_to_users", "colExpr": "VisibleToAllUsers"}, {"colName": "aws_emr_applications", "colExpr": "concat_ws(', ', transform(Applications, d -> concat(d.Name,' ',d.Version)))"}, {"colName": "aws_emr_volume", "colExpr": "EbsRootVolumeSize"}, {"colName": "aws_emr_cluster_name", "colExpr": "lower(Name)"}, {"colName": "aws_emr_cluster_arn", "colExpr": "clusterArn"}, {"colName": "aws_region", "colExpr": "regexp_extract(aws_emr_cluster_arn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region_loc = e.region", "sourcePreTransform": [{"colName": "aws_region_loc", "colExpr": "regexp_extract(ClusterArn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)"}]}], "dataSource": {"name": "AWS", "feedName": "EMR Cluster", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_clusters"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_cluster__resource_id"}