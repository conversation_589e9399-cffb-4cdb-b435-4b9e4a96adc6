{"primaryKey": "deviceId", "origin": "'MS Defender Device Software Inventory'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(deviceName)"}, {"colName": "os", "colExpr": "osPlatform"}], "sourceSpecificProperties": [{"colName": "defender_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device Software Inventory", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_software"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_device_software_inventory__device_id"}