{"primaryKey": "DeviceId", "origin": "'MS Defender Device TVM'", "temporaryProperties": [{"colName": "is_applicable", "colExpr": "CAST(IsApplicable as INT)"}, {"colName": "is_compliant", "colExpr": " CAST(IsCompliant as INT)"}], "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os", "colExpr": "OSPlatform"}, {"colName": "resource_name", "colExpr": "lower(DeviceName)"}, {"colName": "av_signature_update_date", "colExpr": "CASE WHEN lower(ConfigurationId) like '%scid-2011%' and  is_applicable =1 AND is_compliant =1 then event_timestamp_epoch    WHEN lower(ConfigurationId) like '%scid-5095%' and  is_applicable =1 AND is_compliant =1 then event_timestamp_epoch WHEN lower(ConfigurationId) like '%scid-6095%' and  is_applicable =1 AND is_compliant =1 then event_timestamp_epoch ELSE NULL END"}, {"colName": "av_block_malicious_code_status", "colExpr": "max(case when is_applicable=1 and lower(ConfigurationId) like '%scid-2012%' then (case when is_compliant=1 then true else false end) when is_applicable=1 and lower(ConfigurationId) like '%scid-5090%' then (case when is_compliant=1 then true else false end) when is_applicable=1 and lower(ConfigurationId) like '%scid-6090%' then (case when is_compliant=1 then true else false end) else null end) OVER (partition by DeviceId ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "fw_status", "colExpr": "max(case when is_applicable=1 and lower(ConfigurationId) like '%scid-2070%' then (case when is_compliant=1 then true else false end) when is_applicable=1 and lower(ConfigurationId) like '%scid-5007%' then (case when is_compliant=1 then true else false end) else null end) OVER (partition by DeviceId ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)", "fieldsSpec": {"persistNonNullValue": false}}], "sourceSpecificProperties": [{"colName": "defender_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "defender_detection_method", "colExpr": "'Defender Agent'", "fieldsSpec": {"isInventoryDerived": true}}], "dataSource": {"name": "Microsoft Defender For Endpoint", "feedName": "Device TVM Secure Config", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__defender_tvm_secure_config"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_defender_tvm__device_id"}