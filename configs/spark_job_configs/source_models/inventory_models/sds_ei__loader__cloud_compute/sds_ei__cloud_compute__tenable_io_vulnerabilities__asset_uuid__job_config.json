{"primaryKey": "asset.uuid", "origin": "'Tenable.io Vulnerabilities'", "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN  asset.device_type like '%aws-ec2-instance%' OR asset.device_type like '%azure-instance%' OR asset.device_type like '%gcp-instance%' THEN 'Virtual Machine' ELSE NULL END"}, {"colName": "last_active_date", "colExpr": "tenable_io_last_authenticated_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os", "colExpr": "asset.operating_system[0]"}, {"colName": "native_type", "colExpr": "case when asset.device_type like '%aws-ec2-instance%'  then 'AWS EC2 INSTANCE' when asset.device_type like '%azure-instance%' then 'Azure Virtual Machine' when asset.device_type like '%gcp-instance%'  then 'GCP Compute Engine' END"}, {"colName": "public_ip", "colExpr": "tenable_io_ipv4_addresses", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "CASE WHEN tenable_io_onboarding_status is true THEN 'Tenable.io' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_onboarding_status", "colExpr": "tenable_io_onboarding_status", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_last_scan_date", "colExpr": "tenable_io_last_authenticated_scan_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_tracking_method", "colExpr": "TRANSFORM(collect_set(source) over (partition by primary_key ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) , x -> (CASE WHEN lower(x) = 'agent' THEN 'Nessus Agent' when lower(x) -> 'nnm' then 'Nessus Network Monitor' when lower(x) -> 'nessus' then 'Nessus Scan' END))", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "tenable_io_last_authenticated_scan_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(asset.last_authenticated_results)))"}, {"colName": "tenable_io_system_types", "colExpr": "TRANSFORM(array(asset.device_type), x -> INITCAP(REPLACE(x, '-', ' ')))"}, {"colName": "tenable_io_id", "colExpr": "asset.uuid"}, {"colName": "tenable_io_ipv4_addresses", "colExpr": "asset.ipv4"}, {"colName": "tenable_io_ipv6_addresses", "colExpr": "asset.ipv6"}, {"colName": "tenable_io_onboarding_status", "colExpr": "TRUE", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenable_io_agent_uuid", "colExpr": "asset.agent_uuid"}], "dataSource": {"name": "Tenable.io", "feedName": "Vulnerability", "srdm": "<%SRDM_SCHEMA_NAME%>.tenable_io_Vulnerabilities"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__tenable_io_vulnerabilities__asset_uuid"}