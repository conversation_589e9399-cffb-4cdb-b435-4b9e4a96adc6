{"primaryKey": "CONCAT(COALESCE(ARRAY_EXCEPT(TRANSFORM(tags , x -> (CASE WHEN x.key = 'eks:cluster-name' THEN x.value END)), array(NULL))[0],ARRAY_EXCEPT(TRANSFORM(tags , x -> (CASE WHEN x.key = 'aws:eks:cluster-name' THEN x.value END)), array(NULL))[0]),awsRegion)", "filterBy": "lower(resourceType) in ('aws::autoscaling::autoscalinggroup')", "origin": "'AWS Resource Details'", "commonProperties": [{"colName": "type", "colExpr": "'Kubernetes Cluster'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "aws_resource_configuration_change_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "native_type", "colExpr": "'AWS EKS Cluster'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_eks_cluster_key", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "aws_resource_configuration_change_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(configurationItemCaptureTime)))"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.awsRegion = e.region"}], "dataSource": {"name": "AWS", "feedName": "Resource Details", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__resource_details"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_resource_details__aws_eks_cluster_key"}