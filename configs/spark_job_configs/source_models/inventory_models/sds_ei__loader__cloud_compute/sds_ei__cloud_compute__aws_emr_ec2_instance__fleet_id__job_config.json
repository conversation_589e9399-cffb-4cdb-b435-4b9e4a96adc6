{"primaryKey": "lower(InstanceFleetId)", "origin": "'AWS EMR EC2 Instance'", "commonProperties": [{"colName": "type", "colExpr": "'Compute Instance Group'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Fleet'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "aws_emr_cluster_arn", "colExpr": "clusterArn"}, {"colName": "aws_region", "colExpr": "regexp_extract(aws_emr_cluster_arn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region_loc = e.region", "sourcePreTransform": [{"colName": "aws_region_loc", "colExpr": "regexp_extract(clusterArn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)"}]}], "dataSource": {"name": "AWS", "feedName": "EMR EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_instance"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_ec2_instance__fleet_id"}