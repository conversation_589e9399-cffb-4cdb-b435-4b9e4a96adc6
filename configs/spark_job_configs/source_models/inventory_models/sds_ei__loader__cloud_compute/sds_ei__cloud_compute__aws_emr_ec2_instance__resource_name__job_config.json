{"primaryKey": "lower(Ec2InstanceId)", "origin": "'AWS EMR EC2 Instance'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "aws_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(aws_emr_end_date,last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'AWS'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_ip", "colExpr": "aws_instance_private_ip_address", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "public_ip", "colExpr": "aws_instance_public_ip_address", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "private_dns_name", "colExpr": "PrivateDnsName"}, {"colName": "public_dns_name", "colExpr": "PublicDnsName"}, {"colName": "accessibility", "colExpr": "CAST(NULL as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "Ec2InstanceId"}, {"colName": "operational_state", "colExpr": "CASE WHEN aws_operational_state IN ('AWAITING_FULFILLMENT', 'PROVISIONING', 'BOOTSTRAPPING', 'RUNNING') THEN 'Active' WHEN aws_operational_state = 'TERMINATED' THEN 'Inactive' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'AWS EC2 Instance'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_type", "colExpr": "InstanceType"}, {"colName": "cloud_instance_lifecycle", "colExpr": "lower(aws_instance_lifecycle)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "aws_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(aws_emr_cluster_arn,':([0-9]+):')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ec2fleet_id", "colExpr": "InstanceFleetId"}, {"colName": "emr_cluster_id", "colExpr": "clusterId"}], "sourceSpecificProperties": [{"colName": "aws_operational_state", "colExpr": "lower(Status.State)"}, {"colName": "active_operational_date", "colExpr": "CASE WHEN aws_operational_state IN ('AWAITING_FULFILLMENT', 'PROVISIONING', 'BOOTSTRAPPING', 'RUNNING') THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "aws_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.CreationDateTime)))"}, {"colName": "aws_instance_private_ip_address", "colExpr": "PrivateIpAddress"}, {"colName": "aws_instance_public_ip_address", "colExpr": "PublicIpAddress"}, {"colName": "aws_instance_volume_id", "colExpr": "EbsVolumes.VolumeId"}, {"colName": "aws_instance_lifecycle", "colExpr": "case when lower(Market)='spot' then 'spot' when lower(Market)='ON_DEMAND' then 'on-demand' else null end "}, {"colName": "aws_emr_end_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(Status.Timeline.EndDateTime)))"}, {"colName": "aws_emr_cluster_arn", "colExpr": "clusterArn"}, {"colName": "aws_region", "colExpr": "regexp_extract(aws_emr_cluster_arn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)", "fieldsSpec": {"isInventoryDerived": true}}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.aws_region_loc = e.region", "sourcePreTransform": [{"colName": "aws_region_loc", "colExpr": "regexp_extract(clusterArn, 'arn:aws:elasticmapreduce:(.*):(.+):', 1)"}]}], "dataSource": {"name": "AWS", "feedName": "EMR EC2 Instance", "srdm": "<%SRDM_SCHEMA_NAME%>.aws__emr_ec2_instance"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__aws_emr_ec2_instance__resource_name"}