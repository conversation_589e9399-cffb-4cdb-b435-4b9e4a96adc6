{"primaryKey": "case when lower(type)='microsoft.compute/virtualmachinescalesets/virtualmachines' then concat(REGEXP_EXTRACT(id,'(.*\\/virtualMachines\\/)',0),name) else id end", "origin": "'MS Azure Virtual Machine'", "commonProperties": [{"colName": "type", "colExpr": "'Virtual Machine'"}, {"colName": "first_seen_date", "colExpr": "azure_resource_created_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(last_updated_attrs.operational_state.last_changed.last_found_date,CASE WHEN operational_state LIKE  '%Active%' THEN last_found_date ELSE NULL END,active_operational_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "2", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "azure_tags.BusinessUnit", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "azure_tags.Department", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(name)"}, {"colName": "resource_id", "colExpr": "primary_key", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "purchase_plan_name", "colExpr": "azure_plan", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "total_disk_size", "colExpr": "Coalesce(properties.storageProfile.osDisk.diskSizeGB + aggregate(cast(properties.storageProfile.dataDisks.diskSizeGB as array<integer>),0,(acc, x) -> acc+x) , properties.storageProfile.osDisk.diskSizeGB)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "properties"}, {"colName": "os", "colExpr": "INITCAP(properties.storageProfile.osDisk.osType)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'"}, {"colName": "billing_tag", "colExpr": "azure_tags.Billing", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "environment", "colExpr": "lower(azure_tags.Environment)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "REGEXP_EXTRACT(resource_id, '\\\\/[^\\\\/]+\\\\/([^\\\\/]+)')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "lower(properties.vmId)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "operational_state", "colExpr": "CASE WHEN LOWER(azure_vm_power_state) LIKE '%running%' then 'Active' WHEN LOWER(azure_vm_power_state) LIKE '%stopped%' then 'Inactive' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "image", "colExpr": "azure_vm_image_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "properties.provisioningState", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_security_group_id", "colExpr": "azure_vm_network_security_group_id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_network_interface_count", "colExpr": "case when azure_vm_network_interface_id is null then null else size(azure_vm_network_interface_id) end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_type", "colExpr": "properties.hardwareProfile.vmSize", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_lifecycle", "colExpr": "lower(azure_vm_lifecycle)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "native_type", "colExpr": "'Azure Virtual Machine'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "CASE WHEN azure_availability_zone = 1 THEN 'Single' WHEN azure_availability_zone > 1 THEN 'Multiple' WHEN azure_availability_zone IS NULL THEN 'Not Applicable' ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "azure_region", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "is_ephemeral", "colExpr": "case when lower(properties.priority) in ('low','spot') then True Else False end"}, {"colName": "scaling_group_name", "colExpr": "REGEXP_extract(id, '/virtualMachineScaleSets/([^/]+)', 1)"}, {"colName": "organisational_unit", "colExpr": "azure_tags.OU", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "project", "colExpr": "azure_tags.Project", "fieldsSpec": {"isInventoryDerived": true}}], "sourceSpecificProperties": [{"colName": "azure_vm_storage_account_type", "colExpr": "properties.storageProfile.osDisk.managedDisk.storageAccountType", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_disable_password_authentication", "colExpr": "properties.osProfile.linuxConfiguration.disablePasswordAuthentication", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_resource_created_date", "colExpr": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.timeCreated)))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_data_disk", "colExpr": "CASE WHEN properties.storageProfile.dataDisks IS NOT NULL THEN SIZE(properties.storageProfile.dataDisks) ELSE NULL END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_lifecycle", "colExpr": "properties.priority", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_image_id", "colExpr": "properties.storageProfile.imageReference.id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_managed_disk_id", "colExpr": "properties.storageProfile.osDisk.managedDisk.id", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_network_interface_id", "colExpr": "cast(properties.networkProfileConfiguration.networkInterfaceConfigurations.properties.networkSecurityGroup.id as array<string>)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_vm_network_security_group_id", "colExpr": "cast(properties.networkProfile.networkInterfaceConfigurations.networkSecurityGroup.id as array<string>)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_tags", "colExpr": "tags"}, {"colName": "azure_vm_power_state", "colExpr": "case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "active_operational_date", "colExpr": "CASE WHEN LOWER(case when lower(cast(properties.instanceView.statuses.code as string)) like '%running%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%creating%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%starting%' then 'Running' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopping%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%stopped%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocated%' then 'Stopped' when lower(cast(properties.instanceView.statuses.code as string)) like '%deallocating%' then 'Stopped' else null end) LIKE '%running%' THEN event_timestamp_epoch ELSE NULL END", "fieldsSpec": {"aggregateFunction": "max"}}, {"colName": "azure_aks_node_pool_names", "colExpr": "azure_tags.aks__managed__poolName", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "azure_plan", "colExpr": "coalesce(plan.name,properties.purchasePlan.name)"}, {"colName": "azure_availability_zone", "colExpr": "CASE WHEN zones IS NULL OR size(zones) = 0 THEN NULL ELSE size(zones) END"}, {"colName": "azure_region", "colExpr": "location"}, {"colName": "azure_vm_computer_name", "colExpr": "COALESCE(properties.instanceView.ComputerName,properties.extended.instanceView.ComputerName)"}, {"colName": "azure_vm_os_version", "colExpr": "COALESCE(properties.instanceView.osVersion,properties.extended.instanceView.osVersion)"}, {"colName": "azure_vm_os_name", "colExpr": "COALESCE(properties.instanceView.osName,properties.extended.instanceView.osName)"}], "enrichments": [{"lookupInfo": {"tableName": "<%EI_LOOKUP_SCHEMA_NAME%>.location_lookup", "enrichmentColumns": ["location_country", "location_city"]}, "joinCondition": "s.location = e.region"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Virtual Machine", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__virtual_machine"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__cloud_compute__ms_azure_virtual_machine__id"}