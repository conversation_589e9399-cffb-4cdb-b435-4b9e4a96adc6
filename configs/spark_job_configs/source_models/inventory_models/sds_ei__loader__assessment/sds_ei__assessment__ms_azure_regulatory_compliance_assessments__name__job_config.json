{"primaryKey": "lower(name)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(properties.state) IN ('passed','failed','skipped') AND lower(type) IN ('microsoft.security/regulatorycompliancestandards/regulatorycompliancecontrols/regulatorycomplianceassessments')", "temporaryProperties": [{"colName": "temp_standard", "colExpr": "upper(REGEXP_EXTRACT(id, 'regulatoryComplianceStandards/([^/]+)/'))"}, {"colName": "temp_control", "colExpr": "REGEXP_EXTRACT(id,'regulatoryComplianceControls/([^/]+)/')"}], "commonProperties": [{"colName": "type", "colExpr": "'Azure Regulatory Compliance Assessment'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "id", "colExpr": "name"}, {"colName": "title", "colExpr": "LTRIM(case when regexp_like(properties.description,'\\\\[.*\\\\](.*)') then REGEXP_EXTRACT(properties.description, '\\\\[.*\\\\](.*)',1) else properties.description end)"}, {"colName": "standard_name", "colExpr": "collect_set(temp_standard) OVER (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "associated_standards", "colExpr": "transform(standard_name,x->REGEXP_REPLACE(x, '[/\\\\-]', ' '))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "associated_controls", "colExpr": "collect_set(temp_control) OVER (partition by lower(name),DATE(TO_TIMESTAMP(event_timestamp_epoch / 1000))  ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING)"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "tenant_id", "colExpr": "tenantId"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__ms_azure_regulatory_compliance_assessments__name"}