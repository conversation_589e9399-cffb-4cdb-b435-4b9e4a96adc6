{"primaryKey": "lower(name)", "origin": "'MS Azure Security Resources'", "filterBy": "lower(type) IN ('microsoft.security/assessments')", "commonProperties": [{"colName": "type", "colExpr": "'Azure Regulatory Compliance Assessment'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "regexp_replace(properties.metadata.description, '<[^>]++>', ' ')"}], "entitySpecificProperties": [{"colName": "id", "colExpr": "name"}, {"colName": "title", "colExpr": "LTRIM(case when regexp_like(properties.displayName,'\\\\[.*\\\\](.*)') then REGEXP_EXTRACT(properties.displayName, '\\\\[.*\\\\](.*)',1) else properties.displayName end)"}, {"colName": "severity", "colExpr": "case when lower(properties.metadata.severity) in ('informational','low','medium') then INITCAP(properties.metadata.severity) when lower(properties.metadata.severity) = 'high' then 'Critical' else 'Other' end"}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "status", "colExpr": "case when lower(properties.status.code)= 'notapplicable' and lower(properties.status.cause)= 'offbypolicy' then 'Disabled' else 'Enabled' end"}], "dataSource": {"name": "Microsoft Azure", "feedName": "Security Resources", "srdm": "<%SRDM_SCHEMA_NAME%>.microsoft_azure__security_resources"}, "outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__assessment__ms_azure_security_assessments__name"}