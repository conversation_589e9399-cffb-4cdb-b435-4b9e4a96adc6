{"entityClass": "Cloud Container", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(resource_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "location_country", "billing_tag", "environment", "operational_state", "cloud_provider", "resource_id", "resource_name", "private_ip", "encryption_status"], "entity": {"name": "Cloud Container"}, "sourceSpecificProperties": [{"colName": "aws_availability_zone", "colExpr": "COALESCE(aws_availability_zone, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_health_status", "colExpr": "COALESCE(aws_container_health_status, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_host_port", "colExpr": "COALESCE(aws_container_host_port, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') or array_contains(data_source_subset_name,'AWS EKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_image", "colExpr": "COALESCE(aws_container_image, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') or array_contains(data_source_subset_name,'AWS EKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_launch_type", "colExpr": "COALESCE(aws_container_launch_type, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_runtime_id", "colExpr": "COALESCE(aws_container_runtime_id, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_status", "colExpr": "COALESCE(aws_container_status, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') or array_contains(data_source_subset_name,'AWS EKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecr_encryption_type", "colExpr": "COALESCE(aws_ecr_encryption_type, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecr_image_tag_mutability", "colExpr": "COALESCE(aws_ecr_image_tag_mutability, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecr_repository_name", "colExpr": "COALESCE(aws_ecr_repository_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_region", "colExpr": "COALESCE(aws_region, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') or array_contains(data_source_subset_name,'AWS EKS Container') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'AWS SH Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_tag", "colExpr": "COALESCE(aws_tag, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_ad_auth_as_arm_policy", "colExpr": "COALESCE(azure_acr_ad_auth_as_arm_policy, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_encryption_status", "colExpr": "COALESCE(azure_acr_encryption_status, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_export_policy_status", "colExpr": "COALESCE(azure_acr_export_policy_status, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_network_rule_bypass_options", "colExpr": "COALESCE(azure_acr_network_rule_bypass_options, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_quarantine_policy_status", "colExpr": "COALESCE(azure_acr_quarantine_policy_status, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_retention_policy_status", "colExpr": "COALESCE(azure_acr_retention_policy_status, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_soft_delete_policy", "colExpr": "COALESCE(azure_acr_soft_delete_policy, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_trust_policy_status", "colExpr": "COALESCE(azure_acr_trust_policy_status, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_zone_redundency", "colExpr": "COALESCE(azure_acr_zone_redundency, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_container_host_port", "colExpr": "COALESCE(azure_container_host_port, CASE WHEN array_contains(data_source_subset_name,'MS Azure AKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_container_image", "colExpr": "COALESCE(azure_container_image, CASE WHEN array_contains(data_source_subset_name,'MS Azure ACI Container') or array_contains(data_source_subset_name,'MS Azure AKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_container_state", "colExpr": "COALESCE(azure_container_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure ACI Container') or array_contains(data_source_subset_name,'MS Azure AKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_region", "colExpr": "COALESCE(azure_region, CASE WHEN array_contains(data_source_subset_name,'MS Azure ACI Container') or array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_active_services", "colExpr": "COALESCE(wiz_active_services, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_id", "colExpr": "COALESCE(wiz_id, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') or array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_operational_state", "colExpr": "COALESCE(wiz_operational_state, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "active_operational_date", "colExpr": "COALESCE(active_operational_date, CASE WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') or array_contains(data_source_subset_name,'AWS EKS Container') or array_contains(data_source_subset_name,'MS Azure ACI Container') or array_contains(data_source_subset_name,'MS Azure AKS Container') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_configuration_change_date", "colExpr": "COALESCE(aws_resource_configuration_change_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_created_date", "colExpr": "COALESCE(aws_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'AWS EKS Container') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_created_date", "colExpr": "COALESCE(azure_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure ACI Container') or array_contains(data_source_subset_name,'MS Azure AKS Container') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_last_modified_date", "colExpr": "COALESCE(azure_resource_last_modified_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vulnerability_first_observed", "colExpr": "COALESCE(vulnerability_first_observed, CASE WHEN array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vulnerability_last_observed_date", "colExpr": "COALESCE(vulnerability_last_observed_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_modified_date", "colExpr": "COALESCE(wiz_modified_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_privileged", "colExpr": "COALESCE(cast(aws_container_privileged as string), CASE WHEN array_contains(data_source_subset_name,'AWS EKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_runs_as_root", "colExpr": "COALESCE(cast(aws_container_runs_as_root as string), CASE WHEN array_contains(data_source_subset_name,'AWS EKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecr_scan_on_push", "colExpr": "COALESCE(cast(aws_ecr_scan_on_push as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_admin_user_enabled", "colExpr": "COALESCE(cast(azure_acr_admin_user_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_anonymous_pull_enabled", "colExpr": "COALESCE(cast(azure_acr_anonymous_pull_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_acr_data_endpoint_enabled", "colExpr": "COALESCE(cast(azure_acr_data_endpoint_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_container_privileged", "colExpr": "COALESCE(cast(azure_container_privileged as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure AKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_container_runs_as_root", "colExpr": "COALESCE(cast(azure_container_runs_as_root as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure AKS Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_is_default_security_context", "colExpr": "COALESCE(cast(wiz_is_default_security_context as string), CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_network_interface_attc_id", "colExpr": "CASE WHEN size(aws_container_network_interface_attc_id) > 0 THEN aws_container_network_interface_attc_id WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_container_port", "colExpr": "CASE WHEN size(aws_container_port) > 0 THEN aws_container_port WHEN array_contains(data_source_subset_name,'AWS ECS Task Container') or array_contains(data_source_subset_name,'AWS EKS Container') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}]}