{"entityClass": "Cloud Account", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(account_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN account_status IS NOT NULL THEN account_status WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "1", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_city", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [], "lastUpdateFields": ["account_status", "azure_subscription_state", "aws_account_status"], "entity": {"name": "Cloud Account"}, "sourceSpecificProperties": [{"colName": "aws_account_arn", "colExpr": "COALESCE(aws_account_arn, CASE WHEN array_contains(data_source_subset_name,'AWS Organizations') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_account_email", "colExpr": "COALESCE(aws_account_email, CASE WHEN array_contains(data_source_subset_name,'AWS Organizations') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_account_joined_method", "colExpr": "COALESCE(aws_account_joined_method, CASE WHEN array_contains(data_source_subset_name,'AWS Organizations') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_account_status", "colExpr": "COALESCE(aws_account_status, CASE WHEN array_contains(data_source_subset_name,'AWS Organizations') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_subscription_authorization_source", "colExpr": "COALESCE(azure_subscription_authorization_source, CASE WHEN array_contains(data_source_subset_name,'MS Azure Subscriptions') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_subscription_location_placement_id", "colExpr": "COALESCE(azure_subscription_location_placement_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Subscriptions') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_subscription_quota_id", "colExpr": "COALESCE(azure_subscription_quota_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Subscriptions') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_subscription_spending_limit", "colExpr": "COALESCE(azure_subscription_spending_limit, CASE WHEN array_contains(data_source_subset_name,'MS Azure Subscriptions') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_subscription_state", "colExpr": "COALESCE(azure_subscription_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Subscriptions') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_subscription_tenantId", "colExpr": "COALESCE(azure_subscription_tenantId, CASE WHEN array_contains(data_source_subset_name,'MS Azure Subscriptions') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_account_joined_timestamp", "colExpr": "COALESCE(aws_account_joined_timestamp, CASE WHEN array_contains(data_source_subset_name,'AWS Organizations') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}]}