{"entityClass": "Identity", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(identity_display_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN 'No Data' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["type", "first_seen_date", "user_principal_name", "identity_display_name", "email_id", "ownership", "last_password_change_date", "last_logged_in_location", "password_never_expire", "password_not_required", "account_never_expire", "operational_status", "aad_id", "login_last_date", "is_mfa_enabled", "identity_format", "authentication_factors", "location_country"], "entity": {"name": "Identity"}, "sourceSpecificProperties": [{"colName": "ad_distinguished_name", "colExpr": "COALESCE(ad_distinguished_name, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "ad_domain", "colExpr": "COALESCE(ad_domain, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "ad_sam_account_name_with_domain", "colExpr": "COALESCE(ad_sam_account_name_with_domain, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "ad_sam_account_type", "colExpr": "COALESCE(ad_sam_account_type, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}]}