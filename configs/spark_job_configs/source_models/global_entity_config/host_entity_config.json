{"entityClass": "Host", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(instance_name,fqdn,dns_name,host_name,aad_device_id,hardware_serial_number,ip,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN 'No Data' WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "CASE WHEN business_unit IS NULL THEN 'No Data' ELSE business_unit END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "inter"}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "CASE WHEN lower(type) != 'no data' then type else (CASE WHEN (LOWER(internal_contributor) IN ('unknown', 'other', '') OR internal_contributor IS NULL OR TRIM(internal_contributor) = '')   AND (SIZE(host_name)=0) THEN 'No Data' WHEN LOWER(internal_contributor) RLIKE '(?i).*server.*' OR (LOWER(internal_contributor) RLIKE '.*ibm .*/400.*|.*db server|.*red hat.*enterprise.*linux.*|.*dell.*remote.*access.*|.*domain controller.*|.*hp-ux 11.*|.*hp tru64.*|.*aix.*|.*solaris.*|.*openvms.*|.*as/400.*|.*ubuntu / tiny core linux.*|.*oracle.*|.*datacenter.*|.*amazon linux.*|.*suse.*|.*red.?hat.*|.*i86pc.*|.*ilom.*|.*rhel.*|.*z/vse.*|.*qnap nas.*|.*euleros.*|.*hp.*ux.*|.*sql.*enterprise.*|.*teradata.*|.*sunos.*|.*[.]el.*|.*active directory.*|.*redhat.*|.*red hat.*|.*proliant.*|.*poweredge.*|.*idrac.*|.*nutanix.*|.*super micro.*|.*centos.*|.*netapp.*|.*hp onboard administrator.*|.*x86_64 linux/5.15.0-94-generic.*|.*ppc linux.*|.*i686 linux.*|.*unix/samba 3.6.3-.*|.*generic linux 2.6.18.*|.*rocky.*linux.*|.*freebsd(?!.*network).*|.*citrix.*virtual.*app.*|.*xenserver.*|.*bastion.*' AND LOWER(internal_contributor) NOT RLIKE '.*workstation.*') THEN 'Server' WHEN LOWER(internal_contributor) RLIKE '(?i).*r2.*windows.*|.*windows.*2003.*|.*windows.*2008.*|.*windows.*2012.*|.*windows.*2016.*|.*windows.*2022.*|.*nt.*windows.*|.*windows.*2000.*' AND LOWER(internal_contributor) LIKE '%server%' THEN 'Server' WHEN LOWER(internal_contributor) RLIKE '.*windows 2000 lan manager.*|.*linux 2.6.*|.*linux 2.4.*' THEN 'Server' WHEN LOWER(internal_contributor) RLIKE '(?i).*network device|.*freebsd.*jnpr.*network.*|.*cisco ios|.*fortinet.*|.*fortios.*|.*juniper.*|.*junos.*|.*sonicos.*|.*pulse secure.*|.*big.*ip.*|.*pulse connect.*|.*netscreen.*|.*netscaler.*|.*router.*|.*huawei ar151.*|.*ironport.*|.*.*wireless access point.*' OR LOWER(internal_contributor) RLIKE '.*biometric.*|.*switch.*|.*arista.*|.*check point gaia.*|.*aironet.*|.*cisco.*|.*pan-os.*|.*panos.*|.*palo alto.*|.*pfsense.*|.*forti.*|.*polycom.*|.*blue.*coat.*|.*mikrotik.*|.*asterisk.*|.*okilan.*|.*qnap.*|.*vxworks.*|.*extremexos.*|.*axis-.*|.*f5.*networks.*|.*openwrt.*|.*jnpr.*|.*citrix.*|.*simatic net.*|.*arista eos.*|.*brocade.*|.*fabric.*|.*timos.*|.*mcafee.*|.*alcatel.*|.*acme.*|.*cabletron.*|.*ciena.*|.*covaro.*|.*extreme networks.*|.*fibrenet.*|.*profinet.*|.*lightwave.*|.*microchip.*|.*net optics.*|.*nextep.*|.*nortel.*|.*oneaccess.*|.*optix.*|.*powertel.*|.*telstra.*|.*alteon.*|.*apc network.*|.*aruba.*|.*neterra.*|.*buffalo terastation.*|.*emcnetwork.*|.*network router.*|.*firewall.*|.*netgear.*' THEN 'Network Device' WHEN (LOWER(internal_contributor) RLIKE '.*ipad.*|.*ipod.*|.*iphone.*|.*android.*|.*windowsphone.*|.*tizen.*|.*tecno ch7n.*|.*lenovo tb-.*|.*sm-.*|.*moto.*|.*huawei.*vog-.*|.*eml-.*|.*ane-.*|.*mar-.*|.*vivo.*|.*tecno.*|.*swift.*|.*nokia.*|.*pixel.*|.*nexus.*|.*oneplus.*|.*redmi .*|.*asus_.*|.*phone.*|.*poco.*|.*realme.*|.*one-plus.*|.*tablet.*|.*appleios.*' OR lower(internal_contributor) LIKE 'ios%') AND (LOWER(internal_contributor) NOT RLIKE '.*windows.*') THEN 'Mobile' WHEN LOWER(internal_contributor) RLIKE '.*vmware.*|.*esx.*' THEN 'Hypervisor' WHEN LOWER(internal_contributor) RLIKE '.*xerox.*|.*printer.*|.*canon.*|.*hp.*laser.*|.*hp.*jetdirect.*|.*samsung x4220r.*|.*varioprint.*|.*sato network printing version.*|.*lexmark.*|.*lantronix.*|.*kyocera.*|.*hp ethernet.*|.*ricoh.*printer.*|.*epson.*printer.*' THEN 'Printer' WHEN LOWER(internal_contributor) RLIKE '.*windows 7.*|.*windows7.*|.*windows 8.*|.*windows8.*|.*windows 10.*|.*windows10.*|.*windows 11.*|.*windows11.*|.*windows xp.*|.*windows vista.*|.*desktop.*|.*workstation.*|.*chromeos.*|.*chrome os.*|.*mac.*' AND LOWER(internal_contributor) NOT RLIKE '.*2019.*|.*20.*' THEN 'Workstation' WHEN LOWER(internal_contributor) RLIKE '.*laptop.*|.*virtual host.*|.*vdi.*|.*mac.*|.*linux.*|.*win.*|.*win10.*|.*win7.*|.*windows.*|.*endpoint.*|.*debian.*|.*ubuntu.*|.*tablet.*|.*optiplex.*|.*book.*|.*hp elite.*|.*prodesk.*|.*pavilion.*|.*surface.*|.*compaq.*|.*latitude.*|.*travelmate.*|.*gaming.*|.*veriton.*|.*precision.*|.*presario.*|.*predator.*|.*inspiron.*|.*vostro.*|.*mini pc.*|.*extensa.*|.*proone.*|.*sff.*|.*tecra.*|.*thin.*|.*alienware.*|.*all-in-one pc.*|.*asus(?!_).*|.*acer.*|.*aspire.*|.*microtower.*|.*spectre.*|.*nitro.*|.*ideapad.*|.*bravo.*|.*rog.*|.*hp pro.*|.*dell xps 13.*|.*dell xps 15.*|.*lenovo legion.*|.*lenovo yoga.*|.*haiku.*|.*parrot.*|.*webos.*' OR (LOWER(internal_contributor) RLIKE '.*other.*' AND LOWER(CAST(host_name AS string)) RLIKE '.*vdi.*') OR LOWER(CAST(host_name AS string)) RLIKE '.*laptop.*|.*vdi.*|.*wvd.*|.*work.*' THEN 'Workstation' WHEN (LOWER(CAST(host_name AS string)) RLIKE '.*server.*|.*prd.*|.*app.*|.*dev.*|.*dbs.*' and lower(CAST(host_name AS string)) NOT RLIKE '.*work.*' and lower(internal_contributor) NOT RLIKE '.*ios.*|.*android.*|.*work.*') THEN 'Server' ELSE 'Other' END) END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "inter"}}, {"colName": "internal_contributor", "colExpr": "CONCAT_WS(' ',os,azure_vm_os_name,azure_vm_os_version,qualys_tags,crowdstrike_tags,ad_distinguished_name,hardware_chassis_type,hardware_manufacturer,native_type,dns_name,crowdstrike_product_type_desc,itop_class,aad_device_category,aad_profile_type,ARRAY_JOIN(aad_system_label, ', '),tenable_io_system_type,tenablesc_asset_groups)", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "inter"}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "'No Data'", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os_family", "colExpr": "CASE WHEN lower(os_family) != 'no data' THEN os_family ELSE (CASE WHEN COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name) IS NULL OR regexp_like(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name), '(?i)unknown|^-$') OR TRIM(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) = '' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) IN ('none', 'other')   THEN 'No Data'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*macos.*|.*mac.*|.*mac os.*|.*darwin.*|.*catalina.*|.*cheetah.*|.*puma.*|.*jaguar.*|.*panther.*|.*tiger.*|.*leopard.*|.*lion.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*mavericks.*|.*yosemite.*|.*capitan.*|.*sierra.*|.*mojave.*|.*big sur.*|.*monterey.*|.*ventura.*|.*sonoma.*|.*macmdm.*'   THEN 'macOS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%nutanix%'   THEN 'AOS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%vxworks%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%ecos%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%simatic s7%'   THEN 'RTOS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*printer.*|.*hp.*laser.*|.*xerox.*versalink.*|.*xerox.*printer.*|.*ricoh.*printer.*|.*epson.*printer.*'   THEN 'Printer OS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*chromium.*|.*debian.*|.*redhat.*|.*red hat.*|.*suse.*|.*tizen.*|.*openwrt.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*embeddedos.*|.*panos.*|.*stratodesk notouch.*|.*rhel.*|.*data domain.*|.*avaya.*|.*sles.*'   THEN 'Linux'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*|.*simatic net.*|.*panos.*|.*pan-os.*|.*arista eos.*|.*extremexos.*|.*pfsense.*|.*mikrotik.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*aironet.*|.*switch.*|.*qnap.*|.*check point gaia.*|.*check point.*|.*brocade.*|.*f5 networks.*|.*fabric.*|.*fortios.*|.*netapp.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*timos.*|.*mcafee.*|.*ips.*|.*f5.*|.*palo.*alto.*|.*ribbon.*|.*nec.*|.*ceragon.*|.*adva.*|.*rad.*|.*checkpoint.*|.*alcatel.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*huawei.*|.*ericsson.*|.*sandvine.*|.*genie.*|.*remote access.*|.*apple airport.*|.*hitachi nas.*|.*lantronix.*|.*acme.*|.*ciena.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*covaro.*|.*extreme networks.*|.*fibrenet.*|.*lightwave.*|.*microchip.*|.*net optics.*|.*nextep.*|.*nortel.*|.*oneaccess.*|.*optix.*'OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*powertel.*|.*telstra.*|.*alteon.*|.*apc network.*|.*citrix.*|.*aruba.*|.*buffalo terastation.*|.*router.*|.*emcnetwork.*|.*proxysg.*|.*blue coat.*'   THEN 'Network OS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*solaris.*|.*sunos.*|.*hp.*ux.*|.*lights.*|.*ux.*|.*aix.*|.*unix.*|.*freebsd.*|.*netbsd.*|.*busybox.*'   THEN 'Unix'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*ipod.*|.*iphone.*|.*tvos.*'   THEN 'iOS'   WHEN LOWER(TRIM(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name))) LIKE '%android%'   THEN 'Android'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) RLIKE '.*oracle.*|.*teradata.*|.*sql.*'   THEN 'Database'   WHEN LOWER(TRIM(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name))) LIKE '%vmware%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%esx%'   THEN 'Hypervisor OS'   WHEN LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%windows%' OR LOWER(COALESCE(os, itop_pc_os_family, itop_server_os_family, itop_vm_hypervisor_family_name)) LIKE '%win%'   THEN 'Windows'   ELSE 'Other' END) END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "inter"}}, {"colName": "asset_role", "colExpr": "CASE WHEN internal_contributor IS NULL AND host_name IS NULL THEN NULL WHEN LOWER(internal_contributor) RLIKE '.*database.*|.*db server.*|.*database server.*' OR (LOWER(host_name) RLIKE '.*dbs.*' AND LOWER(internal_contributor) <PERSON><PERSON>IKE '.*server.*') THEN 'Database' WHEN LOWER(host_name) RLIKE '.*prd.*|.*prod.*' AND <PERSON>OWER(internal_contributor) RLIKE '.*server.*' THEN 'Production Server' WHEN LOWER(internal_contributor) RLIKE '.*domain controller.*|.*domain_controller.*|.*domaincontroller.*|.*active directory.*|.*ad server.*|.*directory server.*' THEN 'Domain Controller' WHEN LOWER(internal_contributor) RLIKE '.*proxy.*|.*proxies.*' THEN 'Proxy' WHEN LOWER(internal_contributor) RLIKE '.*app.*server.*|.*application.*server.*|.*web application.*' OR (LOWER(host_name) RLIKE '.*app.*' AND LOWER(internal_contributor) RLIKE '.*server.*') THEN 'Application Server' WHEN LOWER(internal_contributor) RLIKE '.*load balancer.*|.*load.*balancer.*' THEN 'Load Balancer' WHEN LOWER(internal_contributor) RLIKE '.*web.*server.*|.*webserver.*|.*web_server.*' THEN 'Web Server' WHEN LOWER(internal_contributor) RLIKE '.*archive server.*' THEN 'Archive Server' WHEN LOWER(internal_contributor) RLIKE '.*citrix workspace.*' THEN 'Citrix Workspace' WHEN LOWER(internal_contributor) RLIKE '.*esxi.*' THEN 'ESXi' WHEN LOWER(internal_contributor) RLIKE '.*xen server.*' THEN 'Xen Server' WHEN LOWER(internal_contributor) RLIKE '.*aix.*server.*' THEN 'AIX Server' WHEN LOWER(internal_contributor) RLIKE '.*nicesystems.*' THEN 'Nice Systems' WHEN LOWER(internal_contributor) RLIKE '.*ivanti.*' THEN 'Ivanti' WHEN LOWER(internal_contributor) RLIKE '.*web portal.*' THEN 'Web Portal' WHEN LOWER(internal_contributor) RLIKE '.*logging system.*|.*centralize log.*|.*centralized log.*' THEN 'Logging System' WHEN LOWER(host_name) RLIKE '.*router.*' OR (LOWER(internal_contributor) RLIKE '.*router.*|.*asr.*|.*mx.*|.*ptx.*|.*ncs.*|.*cx.*|.*atn.*' AND lower(internal_contributor) NOT RLIKE '.*windows.*') THEN 'Router' WHEN LOWER(internal_contributor) RLIKE '.*security gateway.*' THEN 'Security Gateway' WHEN LOWER(internal_contributor) RLIKE '.*api gateway.*' THEN 'API Gateway' WHEN LOWER(internal_contributor) RLIKE '.*remote console manager.*' THEN 'Remote Console Manager' WHEN LOWER(internal_contributor) RLIKE '.*hypervisor.*|.*vmware.*|.*esx.*' THEN 'Hypervisor' WHEN LOWER(internal_contributor) RLIKE '.*jump host.*|.*jumphost.*' THEN 'Jump Host' WHEN LOWER(internal_contributor) RLIKE '.*firewall.*|.*firewall vpn.*|.*fortigate.*' THEN 'Firewall' WHEN LOWER(internal_contributor) RLIKE '.*wap.*' THEN 'Wireless Access Point' WHEN LOWER(internal_contributor) RLIKE '.*network switch.*|.*nexus.*' THEN 'Network Switch' WHEN LOWER(internal_contributor) RLIKE '.*enterprise storage.*' THEN 'Enterprise Storage' WHEN LOWER(internal_contributor) RLIKE '.*analytics.*|.*analytical.*' THEN 'Analytical System' WHEN LOWER(internal_contributor) RLIKE '.*backup.*|.*back up.*|.*veeam.*' THEN 'Backup' WHEN LOWER(internal_contributor) RLIKE '.*data platform.*' THEN 'Data Platform' WHEN LOWER(internal_contributor) RLIKE '.*it management system.*' THEN 'IT Management System' WHEN LOWER(internal_contributor) RLIKE '.*rnd software.*' THEN 'R&D Software' WHEN LOWER(internal_contributor) RLIKE '.*monitoring.*' THEN 'Monitoring' WHEN LOWER(internal_contributor) RLIKE '.*network traffic performance.*' THEN 'Network Performance' WHEN LOWER(internal_contributor) RLIKE '.*power management.*' THEN 'Power Management System' WHEN LOWER(internal_contributor) RLIKE '.*erp.*' THEN 'ERP System' WHEN LOWER(internal_contributor) RLIKE '.*mail.*server.*' THEN 'Mail Server' WHEN LOWER(internal_contributor) RLIKE '.*dns.*server.*|.*dns.*' THEN 'DNS Server' WHEN LOWER(internal_contributor) RLIKE '.*file.*server.*|.*fileserver.*' THEN 'File Server' WHEN LOWER(internal_contributor) RLIKE '.*printer.*|.*print server.*|.*xerox.*|.*printer.*|.*canon.*|.*hp.*laser.*|.*hp.*jetdirect.|.*samsung x4220r.*|.*varioprint.*|.*sato network printing version.*|.*lexmark.*|.*lantronix.*|.*kyocera.*|.*hp ethernet.*' THEN 'Printer' WHEN LOWER(internal_contributor) RLIKE '.*telephony.*' THEN 'Telecom System' WHEN LOWER(internal_contributor) RLIKE '.*general.*server.*|.*pci.*server.*|.*iso.*server.*|.*regular.*server.*' THEN 'General Server' WHEN LOWER(internal_contributor) RLIKE '.*vpn.*' THEN 'VPN' WHEN LOWER(internal_contributor) RLIKE '.*scanner.*|.*nessus.*' THEN 'Scanner' WHEN LOWER(internal_contributor) RLIKE '.*siem.*' THEN 'SIEM' WHEN LOWER(internal_contributor) RLIKE '.*dlp.*' THEN 'Data Loss Prevention' WHEN LOWER(internal_contributor) RLIKE '.*ftp.*' THEN 'File Transfer Protocol' WHEN LOWER(internal_contributor) RLIKE '.*smtp.*' THEN 'Simple Mail Transfer Protocol' WHEN LOWER(internal_contributor) RLIKE '.*webmail.*' THEN 'Web Mail' WHEN LOWER(internal_contributor) RLIKE '.*mdm.*|.*ios .*|.*android.*|.*phone.*' AND lower(internal_contributor) NOT RLIKE '.*windows.*' THEN 'Mobie Device Management' WHEN LOWER(internal_contributor) RLIKE '.*intranet.*' THEN 'Intranet' WHEN LOWER(internal_contributor) RLIKE '.*persistentvdi.*|.*vdi.*' OR lower(host_name) RLIKE '.*wvd.*' THEN 'Virtual Desktop' WHEN LOWER(internal_contributor) RLIKE '.*general.*purpose.*|.*desktop.*|.*laptop.*|.*tablet.*' and LOWER(internal_contributor) NOT LIKE '%server%' THEN 'General Purpose' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fqdn", "colExpr": "LOWER(case when lower(dns_name) like '%ip-%' then null WHEN regexp_like(dns_name,'^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-z0-9-A-Z]+[.]?)$') THEN dns_name END)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "domain", "colExpr": "regexp_extract(fqdn,'^(?:[^.]++[.])((local|corp|[^.]++[^\\\\r\\\\n]++))$')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "ip", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "dns_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "netbios", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "accessibility", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_mac_address", "colExpr": "from_json(null, 'ARRAY<STRING>')", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_version", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_architecture", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "os_build", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'No Data'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "account_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_resource_type", "colExpr": "COALESCE(cloud_resource_type,CASE WHEN cloud_provider='  !' then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "operational_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_instance_id", "colExpr": "COALESCE(cloud_instance_id,CASE WHEN cloud_provider='  !' THEN array('  !') else cloud_instance_id END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "native_type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "provisioning_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "region", "colExpr": "'No Data'", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "zone_availability", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_manufacturer", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_model", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_serial_number", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "hardware_imei", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "login_last_user", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "host_last_reboot_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_product", "colExpr": "COALESCE(mdm_product, CASE WHEN type IN ('Network Device','Printer') then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "mdm_status", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "mdm_compliance_state", "colExpr": "COALESCE(mdm_compliance_state, CASE WHEN type IN ('Network Device','Printer') then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "mdm_enrolled_date", "colExpr": "COALESCE(mdm_enrolled_date, CASE WHEN type in ('Network Device','Printer') THEN 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "mdm_last_sync_date", "colExpr": "COALESCE(mdm_last_sync_date, CASE WHEN type in ('Network Device','Printer') THEN 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "edr_onboarding_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_product", "colExpr": "cast(null as string)", "fieldsSpec": {"persistNonNullValue": false}}, {"colName": "edr_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_last_scan_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_signature_update_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "av_block_malicious_code_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "fw_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "COALESCE(vm_product,CASE WHEN type='Mobile' THEN array('  !') else vm_product END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vm_onboarding_status", "colExpr": "COALESCE(cast(vm_onboarding_status as string), CASE WHEN Type ='Mobile' then '  !' else vm_onboarding_status END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vm_tracking_method", "colExpr": "COALESCE(vm_tracking_method,CASE WHEN type='Mobile' THEN array('  !') else vm_tracking_method END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vm_last_scan_date", "colExpr": "COALESCE(vm_last_scan_date, CASE WHEN type='Mobile' THEN 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vulnerability_last_observed_date", "colExpr": "COALESCE(vulnerability_last_observed_date, CASE WHEN type='Mobile' THEN 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "edr_threat_count", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "threat_count", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "encryption_status", "colExpr": "cast(null as boolean)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "compliance_state", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_account_name", "colExpr": "COALESCE(cloud_account_name,CASE WHEN cloud_provider='  !' then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "cloud_instance_lifecycle", "colExpr": "COALESCE(cloud_instance_lifecycle,CASE WHEN cloud_provider='  !' then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "cloud_instance_type", "colExpr": "COALESCE(cloud_instance_type,CASE WHEN cloud_provider='  !' then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "is_ephemeral", "colExpr": "COALESCE(cast(is_ephemeral as string), CASE WHEN Type ='Mobile' then '  !' else is_ephemeral END)", "fieldsSpec": {"computationPhase": "inter"}}], "fieldLevelSpec": [{"colName": "ip", "fieldsSpec": {"persistNonNullValue": false}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "description", "location_country", "host_name", "fqdn", "ip", "netbios", "accessibility", "os", "cloud_provider", "resource_id", "hardware_model", "hardware_serial_number", "login_last_date", "login_last_user", "edr_onboarding_status", "vm_onboarding_status", "mdm_product", "mdm_status", "mdm_last_sync_date", "edr_last_scan_date", "vm_last_scan_date", "ad_distinguished_name", "ad_last_sync_date", "ad_operational_status", "aad_device_id", "aad_operational_status", "operational_state", "cloud_instance_lifecycle", "is_ephemeral", "mac_address", "account_enabled_status"], "entity": {"name": "Host"}, "sourceSpecificProperties": [{"colName": "aad_device_category", "colExpr": "COALESCE(aad_device_category, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_management_service", "colExpr": "COALESCE(aad_management_service, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_distinguished_name", "colExpr": "COALESCE(ad_distinguished_name, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_object_guid", "colExpr": "COALESCE(ad_object_guid, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_operational_status", "colExpr": "COALESCE(ad_operational_status, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_uac", "colExpr": "COALESCE(ad_uac, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_private_ip_address", "colExpr": "COALESCE(aws_instance_private_ip_address, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_public_ip_address", "colExpr": "COALESCE(aws_instance_public_ip_address, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_operational_state", "colExpr": "COALESCE(aws_operational_state, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_os_name", "colExpr": "COALESCE(azure_vm_os_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_os_version", "colExpr": "COALESCE(azure_vm_os_version, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_power_state", "colExpr": "COALESCE(azure_vm_power_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_customer_id", "colExpr": "COALESCE(crowdstrike_customer_id, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') or array_contains(data_source_subset_name,'CrowdStrike ZeroTrustAssessment') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_external_ip", "colExpr": "COALESCE(crowdstrike_external_ip, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_local_ip", "colExpr": "COALESCE(crowdstrike_local_ip, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_operational_state", "colExpr": "COALESCE(crowdstrike_operational_state, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_product_type_desc", "colExpr": "COALESCE(crowdstrike_product_type_desc, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') or array_contains(data_source_subset_name,'CrowdStrike ZeroTrustAssessment') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_provision_state", "colExpr": "COALESCE(crowdstrike_provision_state, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_tags", "colExpr": "COALESCE(crowdstrike_tags, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_exposure_level", "colExpr": "COALESCE(defender_exposure_level, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_health_status", "colExpr": "COALESCE(defender_health_status, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_management_service", "colExpr": "COALESCE(defender_management_service, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_onboarding_status", "colExpr": "COALESCE(defender_onboarding_status, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_risk_score", "colExpr": "COALESCE(defender_risk_score, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_tags", "colExpr": "COALESCE(defender_tags, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "intune_id", "colExpr": "COALESCE(intune_id, CASE WHEN array_contains(data_source_subset_name,'MS Intune') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "intune_management_service", "colExpr": "COALESCE(intune_management_service, CASE WHEN array_contains(data_source_subset_name,'MS Intune') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "intune_ownership_status", "colExpr": "COALESCE(intune_ownership_status, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') or array_contains(data_source_subset_name,'MS Intune') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_class", "colExpr": "COALESCE(itop_class, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') or array_contains(data_source_subset_name,'iTOP Server') or array_contains(data_source_subset_name,'iTOP VMware') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_device_serial_number", "colExpr": "COALESCE(itop_pc_device_serial_number, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_display_name", "colExpr": "COALESCE(itop_pc_display_name, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_location", "colExpr": "COALESCE(itop_pc_location, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_org_unit", "colExpr": "COALESCE(itop_pc_org_unit, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_os_build", "colExpr": "COALESCE(itop_pc_os_build, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_os_family", "colExpr": "COALESCE(itop_pc_os_family, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_os_version", "colExpr": "COALESCE(itop_pc_os_version, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_status", "colExpr": "COALESCE(itop_pc_status, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_type", "colExpr": "COALESCE(itop_pc_type, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_display_name", "colExpr": "COALESCE(itop_server_display_name, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_location", "colExpr": "COALESCE(itop_server_location, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_os_family", "colExpr": "COALESCE(itop_server_os_family, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_os_version", "colExpr": "COALESCE(itop_server_os_version, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_serial_number", "colExpr": "COALESCE(itop_server_serial_number, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_status", "colExpr": "COALESCE(itop_server_status, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "qualys_tags", "colExpr": "COALESCE(qualys_tags, CASE WHEN array_contains(data_source_subset_name,'Qualys Host List') or array_contains(data_source_subset_name,'Qualys Host Summary') or array_contains(data_source_subset_name,'Qualys Host Vulnerability') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_asset_aws_terminated_date", "colExpr": "COALESCE(tenable_io_asset_aws_terminated_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_last_authenticated_scan_date", "colExpr": "COALESCE(tenable_io_last_authenticated_scan_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') or array_contains(data_source_subset_name,'Tenable.io Vulnerabilities') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_last_scan_date", "colExpr": "COALESCE(tenable_io_last_scan_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_onboarding_date", "colExpr": "COALESCE(tenable_io_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenablesc_asset_tags", "colExpr": "COALESCE(tenablesc_asset_tags, CASE WHEN array_contains(data_source_subset_name,'Tenable.sc Asset') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_id", "colExpr": "COALESCE(wiz_id, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') or array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_operational_state", "colExpr": "COALESCE(wiz_operational_state, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_created_date", "colExpr": "COALESCE(aad_created_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_deleted_date", "colExpr": "COALESCE(aad_deleted_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_enrolled_date", "colExpr": "COALESCE(aad_enrolled_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "active_operational_date", "colExpr": "COALESCE(active_operational_date, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_account_disabled_date", "colExpr": "COALESCE(ad_account_disabled_date, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') or array_contains(data_source_subset_name,'WinEvents 4725') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_created_date", "colExpr": "COALESCE(ad_created_date, CASE WHEN array_contains(data_source_subset_name,'MS Active Directory') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "ad_last_sync_date", "colExpr": "COALESCE(ad_last_sync_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_attach_time", "colExpr": "COALESCE(aws_instance_attach_time, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_launch_date", "colExpr": "COALESCE(aws_instance_launch_date, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_usage_update_time", "colExpr": "COALESCE(aws_instance_usage_update_time, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_created_date", "colExpr": "COALESCE(aws_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_created_date", "colExpr": "COALESCE(azure_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure VDI') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_agent_local_date", "colExpr": "COALESCE(crowdstrike_agent_local_date, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_last_report_date", "colExpr": "COALESCE(crowdstrike_last_report_date, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_modified_date", "colExpr": "COALESCE(crowdstrike_modified_date, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') or array_contains(data_source_subset_name,'CrowdStrike ZeroTrustAssessment') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_onboarding_date", "colExpr": "COALESCE(crowdstrike_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_reboot_date", "colExpr": "COALESCE(crowdstrike_reboot_date, CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_onboarding_date", "colExpr": "COALESCE(defender_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_end_of_warranty_date", "colExpr": "COALESCE(itop_pc_end_of_warranty_date, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_obsolete_date", "colExpr": "COALESCE(itop_pc_obsolete_date, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_pc_purchase_date", "colExpr": "COALESCE(itop_pc_purchase_date, CASE WHEN array_contains(data_source_subset_name,'iTOP PC') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_end_of_warranty_date", "colExpr": "COALESCE(itop_server_end_of_warranty_date, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_obsolete_date", "colExpr": "COALESCE(itop_server_obsolete_date, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "itop_server_purchase_date", "colExpr": "COALESCE(itop_server_purchase_date, CASE WHEN array_contains(data_source_subset_name,'iTOP Server') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenablesc_last_active_date", "colExpr": "COALESCE(tenablesc_last_active_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.sc Analysis') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_last_scan_date", "colExpr": "COALESCE(wiz_last_scan_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_modified_date", "colExpr": "COALESCE(wiz_modified_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_onboarding_date", "colExpr": "COALESCE(wiz_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_onboarding_status", "colExpr": "COALESCE(cast(crowdstrike_onboarding_status as string), CASE WHEN array_contains(data_source_subset_name,'CrowdStrike Host') or array_contains(data_source_subset_name,'CrowdStrike ZeroTrustAssessment') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "mdm_encryption_status", "colExpr": "COALESCE(cast(mdm_encryption_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Intune') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenablesc_onboarding_status", "colExpr": "COALESCE(cast(tenablesc_onboarding_status as string), CASE WHEN array_contains(data_source_subset_name,'Tenable.sc Analysis') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_onboarding_status", "colExpr": "COALESCE(cast(wiz_onboarding_status as string), CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') or array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_threat_count", "colExpr": "COALESCE(defender_threat_count, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device Events') THEN NULL ELSE -2147483648 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_device_id", "colExpr": "CASE WHEN size(aad_device_id) > 0 THEN aad_device_id WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') or array_contains(data_source_subset_name,'MS Azure AD Registered Users') or array_contains(data_source_subset_name,'MS Azure AD Sign-in Logs') or array_contains(data_source_subset_name,'MS Defender Device List') or array_contains(data_source_subset_name,'MS Intune') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aad_system_label", "colExpr": "CASE WHEN size(aad_system_label) > 0 THEN aad_system_label WHEN array_contains(data_source_subset_name,'MS Azure AD Devices') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "crowdstrike_device_id", "colExpr": "CASE WHEN size(crowdstrike_device_id) > 0 THEN crowdstrike_device_id WHEN array_contains(data_source_subset_name,'CrowdStrike Host') or array_contains(data_source_subset_name,'CrowdStrike ZeroTrustAssessment') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_action_type", "colExpr": "CASE WHEN size(defender_action_type) > 0 THEN defender_action_type WHEN array_contains(data_source_subset_name,'MS Defender Device Events') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_detection_method", "colExpr": "CASE WHEN size(defender_detection_method) > 0 THEN defender_detection_method WHEN array_contains(data_source_subset_name,'MS Defender Device Events') or array_contains(data_source_subset_name,'MS Defender Device List') or array_contains(data_source_subset_name,'MS Defender Device Software Inventory') or array_contains(data_source_subset_name,'MS Defender Device TVM Software Vulnerability Delta') or array_contains(data_source_subset_name,'MS Defender Device TVM') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_id", "colExpr": "CASE WHEN size(defender_id) > 0 THEN defender_id WHEN array_contains(data_source_subset_name,'MS Defender Device Events') or array_contains(data_source_subset_name,'MS Defender Device List') or array_contains(data_source_subset_name,'MS Defender Device Software Inventory') or array_contains(data_source_subset_name,'MS Defender Device TVM Software Vulnerability Delta') or array_contains(data_source_subset_name,'MS Defender Device TVM') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_threat_name", "colExpr": "CASE WHEN size(defender_threat_name) > 0 THEN defender_threat_name WHEN array_contains(data_source_subset_name,'MS Defender Device Events') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "qualys_asset_id", "colExpr": "CASE WHEN size(qualys_asset_id) > 0 THEN qualys_asset_id WHEN array_contains(data_source_subset_name,'Qualys Host List') or array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "qualys_detection_method", "colExpr": "CASE WHEN size(qualys_detection_method) > 0 THEN qualys_detection_method WHEN array_contains(data_source_subset_name,'Qualys Host List') or array_contains(data_source_subset_name,'Qualys Host Summary') or array_contains(data_source_subset_name,'Qualys Host Vulnerability') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "qualys_id", "colExpr": "CASE WHEN size(qualys_id) > 0 THEN qualys_id WHEN array_contains(data_source_subset_name,'Qualys Host List') or array_contains(data_source_subset_name,'Qualys Host Summary') or array_contains(data_source_subset_name,'Qualys Host Vulnerability') or array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenablesc_asset_groups", "colExpr": "CASE WHEN size(tenablesc_asset_groups) > 0 THEN tenablesc_asset_groups WHEN array_contains(data_source_subset_name,'Tenable.sc Asset') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenablesc_assets", "colExpr": "CASE WHEN size(tenablesc_assets) > 0 THEN tenablesc_assets WHEN array_contains(data_source_subset_name,'Tenable.sc Asset') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenablesc_repositories", "colExpr": "CASE WHEN size(tenablesc_repositories) > 0 THEN tenablesc_repositories WHEN array_contains(data_source_subset_name,'Tenable.sc Analysis') or array_contains(data_source_subset_name,'Tenable.sc Asset') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}]}