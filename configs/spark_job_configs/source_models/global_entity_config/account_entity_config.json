{"entityClass": "Account", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(COALESCE(account_display_name,account_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(first_found_date,last_active_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "inactivity_period", "colExpr": "30", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "location_country", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["first_seen_date", "account_display_name", "login_last_date", "last_logged_in_location", "authentication_factors", "last_lock_out_time", "privilege_account", "last_password_change_date", "password_never_expire", "password_not_required", "account_never_expire", "ownership_of_identity", "is_mfa_enabled", "privilege_roles_count", "activity_status", "operational_status", "entitlement_id", "entitlement_value", "entitlement_details"], "entity": {"name": "Account"}}