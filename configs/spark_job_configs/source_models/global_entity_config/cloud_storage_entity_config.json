{"entityClass": "Cloud Storage", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(volume_name,resource_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "volume_iops", "colExpr": "COALESCE(volume_iops, CASE WHEN  CASE WHEN type!= 'Volume' THEN '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "volume_size", "colExpr": "COALESCE(volume_size, CASE WHEN  CASE WHEN type!= 'Volume' THEN '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "volume_throughput", "colExpr": "COALESCE(volume_throughput, CASE WHEN  CASE WHEN type!= 'Volume' THEN '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "allow_blob_public_access", "colExpr": "COALESCE(cast(allow_blob_public_access as string), CASE WHEN type!='Storage Account' then '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}], "lastUpdateFields": ["billing_tag", "environment", "public_network_access", "operational_state"], "entity": {"name": "Cloud Storage"}, "sourceSpecificProperties": [{"colName": "aws_availability_zone", "colExpr": "COALESCE(aws_availability_zone, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_csi_name", "colExpr": "COALESCE(aws_ebs_csi_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_snapshot_id", "colExpr": "COALESCE(aws_ebs_snapshot_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_state", "colExpr": "COALESCE(aws_ebs_state, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_efs_backup_policy_status", "colExpr": "COALESCE(aws_efs_backup_policy_status, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_efs_performance_mode", "colExpr": "COALESCE(aws_efs_performance_mode, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_efs_throughput_mode", "colExpr": "COALESCE(aws_efs_throughput_mode, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_efs_transition_to_ia", "colExpr": "COALESCE(aws_efs_transition_to_ia, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_id", "colExpr": "COALESCE(aws_instance_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_kms_key_id", "colExpr": "COALESCE(aws_kms_key_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_region", "colExpr": "COALESCE(aws_region, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'AWS SH Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_bucket_versioning_status", "colExpr": "COALESCE(aws_s3_bucket_versioning_status, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_log_bucket_name", "colExpr": "COALESCE(aws_s3_log_bucket_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_owner_id", "colExpr": "COALESCE(aws_s3_owner_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_server_side_encryption", "colExpr": "COALESCE(aws_s3_server_side_encryption, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_server_side_encryption_kms_master_key", "colExpr": "COALESCE(aws_s3_server_side_encryption_kms_master_key, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_blob_encryption_key_type", "colExpr": "COALESCE(azure_blob_encryption_key_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_bsc_default_encryption_scope", "colExpr": "COALESCE(azure_bsc_default_encryption_scope, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_bsc_public_access", "colExpr": "COALESCE(azure_bsc_public_access, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_controller_types", "colExpr": "COALESCE(azure_disk_controller_types, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_create_option", "colExpr": "COALESCE(azure_disk_create_option, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_encryption_type", "colExpr": "COALESCE(azure_disk_encryption_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_image_id", "colExpr": "COALESCE(azure_disk_image_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_os_type", "colExpr": "COALESCE(azure_disk_os_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_security_type", "colExpr": "COALESCE(azure_disk_security_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_state", "colExpr": "COALESCE(azure_disk_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_tier", "colExpr": "COALESCE(azure_disk_tier, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_unique_id", "colExpr": "COALESCE(azure_disk_unique_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_dns_endpoint_type", "colExpr": "COALESCE(azure_dns_endpoint_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_encryption_key_source", "colExpr": "COALESCE(azure_encryption_key_source, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_encryption_key_type", "colExpr": "COALESCE(azure_file_encryption_key_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_share_access_tier", "colExpr": "COALESCE(azure_file_share_access_tier, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_share_enabled_protocols", "colExpr": "COALESCE(azure_file_share_enabled_protocols, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_identity_type", "colExpr": "COALESCE(azure_identity_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_large_file_shares_state", "colExpr": "COALESCE(azure_large_file_shares_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_lease_state", "colExpr": "COALESCE(azure_lease_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_minimum_tls_version", "colExpr": "COALESCE(azure_minimum_tls_version, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_network_acls_bypass", "colExpr": "COALESCE(azure_network_acls_bypass, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_network_policy_access", "colExpr": "COALESCE(azure_network_policy_access, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_ou", "colExpr": "COALESCE(azure_ou, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_region", "colExpr": "COALESCE(azure_region, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_group", "colExpr": "COALESCE(azure_resource_group, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_id", "colExpr": "COALESCE(azure_storage_account_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_kind", "colExpr": "COALESCE(azure_storage_account_kind, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_primary_location", "colExpr": "COALESCE(azure_storage_account_primary_location, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_secondary_location", "colExpr": "COALESCE(azure_storage_account_secondary_location, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_type", "colExpr": "COALESCE(azure_storage_account_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_id", "colExpr": "COALESCE(azure_vm_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_name", "colExpr": "COALESCE(azure_vm_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vmss_name", "colExpr": "COALESCE(azure_vmss_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "active_operational_date", "colExpr": "COALESCE(active_operational_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_attach_date", "colExpr": "COALESCE(aws_ebs_attach_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_configuration_change_date", "colExpr": "COALESCE(aws_resource_configuration_change_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_created_date", "colExpr": "COALESCE(aws_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_blob_encryption_enabled_date", "colExpr": "COALESCE(azure_blob_encryption_enabled_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_deleted_date", "colExpr": "COALESCE(azure_deleted_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_last_ownership_update", "colExpr": "COALESCE(azure_disk_last_ownership_update, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_encryption_enabled_date", "colExpr": "COALESCE(azure_file_encryption_enabled_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_share_access_tier_change_date", "colExpr": "COALESCE(azure_file_share_access_tier_change_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_geo_replication_last_sync", "colExpr": "COALESCE(azure_geo_replication_last_sync, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_created_date", "colExpr": "COALESCE(azure_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_last_modified_date", "colExpr": "COALESCE(azure_resource_last_modified_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_key1_creation_date", "colExpr": "COALESCE(azure_storage_account_key1_creation_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_key2_creation_date", "colExpr": "COALESCE(azure_storage_account_key2_creation_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_delete_on_termination", "colExpr": "COALESCE(cast(aws_ebs_delete_on_termination as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_multi_attach_enabled", "colExpr": "COALESCE(cast(aws_ebs_multi_attach_enabled as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_block_public_acls", "colExpr": "COALESCE(cast(aws_s3_block_public_acls as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_block_public_policy", "colExpr": "COALESCE(cast(aws_s3_block_public_policy as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_bucket_encryption_key_enabled", "colExpr": "COALESCE(cast(aws_s3_bucket_encryption_key_enabled as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_ignore_public_acls", "colExpr": "COALESCE(cast(aws_s3_ignore_public_acls as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_is_mfa_delete_enabled", "colExpr": "COALESCE(cast(aws_s3_is_mfa_delete_enabled as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_requester_charged", "colExpr": "COALESCE(cast(aws_s3_requester_charged as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_s3_restrict_public_buckets", "colExpr": "COALESCE(cast(aws_s3_restrict_public_buckets as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_allow_cross_tenant_replication", "colExpr": "COALESCE(cast(azure_allow_cross_tenant_replication as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_blob_encryption_enabled", "colExpr": "COALESCE(cast(azure_blob_encryption_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_bsc_deny_encryption_scope_override", "colExpr": "COALESCE(cast(azure_bsc_deny_encryption_scope_override as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_bsc_has_immutability_policy", "colExpr": "COALESCE(cast(azure_bsc_has_immutability_policy as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_bsc_has_legal_hold", "colExpr": "COALESCE(cast(azure_bsc_has_legal_hold as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_default_o_auth", "colExpr": "COALESCE(cast(azure_default_o_auth as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_deleted", "colExpr": "COALESCE(cast(azure_deleted as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_disk_supports_hibernation", "colExpr": "COALESCE(cast(azure_disk_supports_hibernation as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_encryption_enabled", "colExpr": "COALESCE(cast(azure_file_encryption_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_is_hns_enabled", "colExpr": "COALESCE(cast(azure_is_hns_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_storage_account_allow_shared_key", "colExpr": "COALESCE(cast(azure_storage_account_allow_shared_key as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_supports_http_traffic_only", "colExpr": "COALESCE(cast(azure_supports_http_traffic_only as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_version_level_immutability_support", "colExpr": "COALESCE(cast(azure_version_level_immutability_support as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_file_share_quota", "colExpr": "COALESCE(azure_file_share_quota, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_remaining_retention_days", "colExpr": "COALESCE(azure_remaining_retention_days, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_size", "colExpr": "COALESCE(aws_ebs_size, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -2147483648 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_availability_zone", "colExpr": "COALESCE(azure_availability_zone, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE -2147483648 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ebs_attachment_state", "colExpr": "CASE WHEN size(aws_ebs_attachment_state) > 0 THEN aws_ebs_attachment_state WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_multi_attached_instances", "colExpr": "CASE WHEN size(aws_multi_attached_instances) > 0 THEN aws_multi_attached_instances WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}]}