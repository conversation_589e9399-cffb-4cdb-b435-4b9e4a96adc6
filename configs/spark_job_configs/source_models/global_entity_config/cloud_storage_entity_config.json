{"entityClass": "Cloud Storage", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(volume_name,resource_name,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_inactivity_period", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}], "lastUpdateFields": ["billing_tag", "environment", "public_network_access", "operational_state"], "entity": {"name": "Cloud Storage"}, "sourceSpecificProperties": [{"colName": "aws_availability_zone", "colExpr": "COALESCE(aws_availability_zone, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_ebs_csi_name", "colExpr": "COALESCE(aws_ebs_csi_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_ebs_snapshot_id", "colExpr": "COALESCE(aws_ebs_snapshot_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_ebs_state", "colExpr": "COALESCE(aws_ebs_state, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_efs_backup_policy_status", "colExpr": "COALESCE(aws_efs_backup_policy_status, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_efs_performance_mode", "colExpr": "COALESCE(aws_efs_performance_mode, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_efs_throughput_mode", "colExpr": "COALESCE(aws_efs_throughput_mode, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_efs_transition_to_ia", "colExpr": "COALESCE(aws_efs_transition_to_ia, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_instance_id", "colExpr": "COALESCE(aws_instance_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_kms_key_id", "colExpr": "COALESCE(aws_kms_key_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_region", "colExpr": "COALESCE(aws_region, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'AWS SH Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_s3_bucket_versioning_status", "colExpr": "COALESCE(aws_s3_bucket_versioning_status, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_s3_log_bucket_name", "colExpr": "COALESCE(aws_s3_log_bucket_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_s3_owner_id", "colExpr": "COALESCE(aws_s3_owner_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_s3_server_side_encryption", "colExpr": "COALESCE(aws_s3_server_side_encryption, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_s3_server_side_encryption_kms_master_key", "colExpr": "COALESCE(aws_s3_server_side_encryption_kms_master_key, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_blob_encryption_key_type", "colExpr": "COALESCE(azure_blob_encryption_key_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_bsc_default_encryption_scope", "colExpr": "COALESCE(azure_bsc_default_encryption_scope, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_bsc_public_access", "colExpr": "COALESCE(azure_bsc_public_access, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_controller_types", "colExpr": "COALESCE(azure_disk_controller_types, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_create_option", "colExpr": "COALESCE(azure_disk_create_option, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_encryption_type", "colExpr": "COALESCE(azure_disk_encryption_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_image_id", "colExpr": "COALESCE(azure_disk_image_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_os_type", "colExpr": "COALESCE(azure_disk_os_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_security_type", "colExpr": "COALESCE(azure_disk_security_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_state", "colExpr": "COALESCE(azure_disk_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_tier", "colExpr": "COALESCE(azure_disk_tier, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_disk_unique_id", "colExpr": "COALESCE(azure_disk_unique_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_dns_endpoint_type", "colExpr": "COALESCE(azure_dns_endpoint_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_encryption_key_source", "colExpr": "COALESCE(azure_encryption_key_source, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_file_encryption_key_type", "colExpr": "COALESCE(azure_file_encryption_key_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_file_share_access_tier", "colExpr": "COALESCE(azure_file_share_access_tier, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_file_share_enabled_protocols", "colExpr": "COALESCE(azure_file_share_enabled_protocols, CASE WHEN array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_identity_type", "colExpr": "COALESCE(azure_identity_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_large_file_shares_state", "colExpr": "COALESCE(azure_large_file_shares_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_lease_state", "colExpr": "COALESCE(azure_lease_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_minimum_tls_version", "colExpr": "COALESCE(azure_minimum_tls_version, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_network_acls_bypass", "colExpr": "COALESCE(azure_network_acls_bypass, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_network_policy_access", "colExpr": "COALESCE(azure_network_policy_access, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_ou", "colExpr": "COALESCE(azure_ou, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_region", "colExpr": "COALESCE(azure_region, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_resource_group", "colExpr": "COALESCE(azure_resource_group, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_storage_account_id", "colExpr": "COALESCE(azure_storage_account_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Blob Storage Container') or array_contains(data_source_subset_name,'MS Azure File Share') or array_contains(data_source_subset_name,'MS Azure Queue Storage') or array_contains(data_source_subset_name,'MS Azure Table Storage') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_storage_account_kind", "colExpr": "COALESCE(azure_storage_account_kind, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_storage_account_primary_location", "colExpr": "COALESCE(azure_storage_account_primary_location, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_storage_account_secondary_location", "colExpr": "COALESCE(azure_storage_account_secondary_location, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_storage_account_type", "colExpr": "COALESCE(azure_storage_account_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_vm_id", "colExpr": "COALESCE(azure_vm_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_vm_name", "colExpr": "COALESCE(azure_vm_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "azure_vmss_name", "colExpr": "COALESCE(azure_vmss_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_ebs_attachment_state", "colExpr": "CASE WHEN size(aws_ebs_attachment_state) > 0 THEN aws_ebs_attachment_state WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computation_phase": "inter"}}, {"colName": "aws_multi_attached_instances", "colExpr": "CASE WHEN size(aws_multi_attached_instances) > 0 THEN aws_multi_attached_instances WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computation_phase": "inter"}}]}