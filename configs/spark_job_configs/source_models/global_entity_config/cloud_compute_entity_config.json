{"entityClass": "Cloud Compute", "commonProperties": [{"colName": "display_label", "colExpr": "UPPER(coalesce(instance_name,public_dns_name,private_dns_name,resource_name,cloud_instance_id,primary_key))", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "activity_status", "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": true, "computation_phase": "all"}}, {"colName": "inactivity_period", "colExpr": "180", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "business_unit", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "department", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "description", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "type", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "LEAST(last_active_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_severity", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "exposure_score", "colExpr": "cast(null as int)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "os_family", "colExpr": "CASE WHEN os IS NULL OR regexp_like(os,'(?i)unknown|^-$') THEN NULL WHEN LOWER(os) LIKE '%windows%' THEN 'Windows' WHEN LOWER(os) LIKE '%macos%' or LOWER(os) LIKE '%mac%' THEN 'macOS' WHEN lower(os) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*debian.*|.*redhat.*|.*tizen.*|.*panos.*|openwrt|.*embeddedos.*' THEN 'Linux' WHEN lower(os) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*' THEN 'Network OS' WHEN lower(os) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*iphone.*' THEN 'iOS' WHEN LOWER(TRIM(os)) LIKE '%android%' THEN 'Android' ELSE 'Other' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_name", "colExpr": "cast(null as string)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "edr_threat_count", "colExpr": "cast(null as integer)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "vm_product", "colExpr": "COALESCE(vm_product,CASE WHEN type='Mobile' THEN array('  !') else vm_product END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vm_tracking_method", "colExpr": "COALESCE(vm_tracking_method,CASE WHEN type='Mobile' THEN array('  !') else vm_tracking_method END)", "fieldsSpec": {"computationPhase": "inter"}}], "lastUpdateFields": ["type", "first_seen_date", "business_unit", "department", "location_country", "purchase_plan_name", "os_family", "billing_tag", "environment", "edr_onboarding_status", "vm_onboarding_status", "operational_state", "accessibility", "os", "cloud_provider", "resource_id", "resource_name", "login_last_date", "login_last_user", "edr_last_scan_date", "vm_last_scan_date", "private_ip", "public_ip", "cloud_instance_lifecycle", "is_ephemeral"], "entity": {"name": "Cloud Compute"}, "sourceSpecificProperties": [{"colName": "aad_device_id", "colExpr": "COALESCE(aad_device_id, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_availability_zone", "colExpr": "COALESCE(aws_availability_zone, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_default_target_capacity_type", "colExpr": "COALESCE(aws_ec2fleet_default_target_capacity_type, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_type", "colExpr": "COALESCE(aws_ec2fleet_type, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecs_assign_public_ip", "colExpr": "COALESCE(aws_ecs_assign_public_ip, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecs_platform_version", "colExpr": "COALESCE(aws_ecs_platform_version, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecs_scheduling_strategy", "colExpr": "COALESCE(aws_ecs_scheduling_strategy, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ecs_service_name", "colExpr": "COALESCE(aws_ecs_service_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_eks_cluster_security_group_id", "colExpr": "COALESCE(aws_eks_cluster_security_group_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_eks_network_ip_family", "colExpr": "COALESCE(aws_eks_network_ip_family, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_applications", "colExpr": "COALESCE(aws_emr_applications, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_cluster_arn", "colExpr": "COALESCE(aws_emr_cluster_arn, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_cluster_name", "colExpr": "COALESCE(aws_emr_cluster_name, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_instance_collection_type", "colExpr": "COALESCE(aws_emr_instance_collection_type, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_instance_fleet_type", "colExpr": "COALESCE(aws_emr_instance_fleet_type, CASE WHEN array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_master_security_group_id", "colExpr": "COALESCE(aws_emr_master_security_group_id, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_service_security_group_id", "colExpr": "COALESCE(aws_emr_service_security_group_id, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_slave_security_group_id", "colExpr": "COALESCE(aws_emr_slave_security_group_id, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_version", "colExpr": "COALESCE(aws_emr_version, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_image_architecture", "colExpr": "COALESCE(aws_instance_image_architecture, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_image_id", "colExpr": "COALESCE(aws_instance_image_id, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_lifecycle", "colExpr": "COALESCE(aws_instance_lifecycle, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_monitoring_state", "colExpr": "COALESCE(aws_instance_monitoring_state, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_private_ip_address", "colExpr": "COALESCE(aws_instance_private_ip_address, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_public_ip_address", "colExpr": "COALESCE(aws_instance_public_ip_address, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_subnet_id", "colExpr": "COALESCE(aws_instance_subnet_id, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_vpc_id", "colExpr": "COALESCE(aws_instance_vpc_id, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_lambda_runtime", "colExpr": "COALESCE(aws_lambda_runtime, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_operational_state", "colExpr": "COALESCE(aws_operational_state, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_region", "colExpr": "COALESCE(aws_region, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'AWS SH Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_configuration_id", "colExpr": "COALESCE(aws_sagemaker_configuration_id, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_default_code_repository", "colExpr": "COALESCE(aws_sagemaker_default_code_repository, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_direct_internet_access_status", "colExpr": "COALESCE(aws_sagemaker_direct_internet_access_status, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_execution_role_arn", "colExpr": "COALESCE(aws_sagemaker_execution_role_arn, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_lifecycle_config_name", "colExpr": "COALESCE(aws_sagemaker_lifecycle_config_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_available_gpu", "colExpr": "COALESCE(aws_sagemaker_model_available_gpu, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_cache_root", "colExpr": "COALESCE(aws_sagemaker_model_cache_root, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_data_url", "colExpr": "COALESCE(aws_sagemaker_model_data_url, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_datasource_s3_uri", "colExpr": "COALESCE(aws_sagemaker_model_datasource_s3_uri, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_name", "colExpr": "COALESCE(aws_sagemaker_model_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_primary_container_image", "colExpr": "COALESCE(aws_sagemaker_model_primary_container_image, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_model_primary_container_mode", "colExpr": "COALESCE(aws_sagemaker_model_primary_container_mode, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_notebook_instance_name", "colExpr": "COALESCE(aws_sagemaker_notebook_instance_name, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_role_arn", "colExpr": "COALESCE(aws_sagemaker_role_arn, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_root_access_status", "colExpr": "COALESCE(aws_sagemaker_root_access_status, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_tag", "colExpr": "COALESCE(aws_tag, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aci_instance_state", "colExpr": "COALESCE(azure_aci_instance_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aci_restart_policy", "colExpr": "COALESCE(azure_aci_restart_policy, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_auto_upgrade_type", "colExpr": "COALESCE(azure_aks_auto_upgrade_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_dns_prefix", "colExpr": "COALESCE(azure_aks_dns_prefix, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_node_resource_group", "colExpr": "COALESCE(azure_aks_node_resource_group, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_power_state", "colExpr": "COALESCE(azure_aks_power_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_private_fqdn", "colExpr": "COALESCE(azure_aks_private_fqdn, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_support_plan", "colExpr": "COALESCE(azure_aks_support_plan, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_plan", "colExpr": "COALESCE(azure_plan, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_public_network_access", "colExpr": "COALESCE(azure_public_network_access, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_region", "colExpr": "COALESCE(azure_region, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_computer_name", "colExpr": "COALESCE(azure_vm_computer_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_image_id", "colExpr": "COALESCE(azure_vm_image_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_lifecycle", "colExpr": "COALESCE(azure_vm_lifecycle, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_managed_disk_id", "colExpr": "COALESCE(azure_vm_managed_disk_id, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_os_name", "colExpr": "COALESCE(azure_vm_os_name, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_os_version", "colExpr": "COALESCE(azure_vm_os_version, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_power_state", "colExpr": "COALESCE(azure_vm_power_state, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_storage_account_type", "colExpr": "COALESCE(azure_vm_storage_account_type, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_exposure_level", "colExpr": "COALESCE(defender_exposure_level, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_onboarding_status", "colExpr": "COALESCE(defender_onboarding_status, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_asset_aws_terminated_date", "colExpr": "COALESCE(tenable_io_asset_aws_terminated_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_asset_updated_at", "colExpr": "COALESCE(tenable_io_asset_updated_at, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_last_authenticated_scan_date", "colExpr": "COALESCE(tenable_io_last_authenticated_scan_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') or array_contains(data_source_subset_name,'Tenable.io Vulnerabilities') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_last_scan_date", "colExpr": "COALESCE(tenable_io_last_scan_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "tenable_io_onboarding_date", "colExpr": "COALESCE(tenable_io_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'Tenable.io Assets') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_id", "colExpr": "COALESCE(wiz_id, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') or array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_operational_state", "colExpr": "COALESCE(wiz_operational_state, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "active_operational_date", "colExpr": "COALESCE(active_operational_date, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_attach_time", "colExpr": "COALESCE(aws_instance_attach_time, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_launch_date", "colExpr": "COALESCE(aws_instance_launch_date, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_state_update_time", "colExpr": "COALESCE(aws_instance_state_update_time, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_usage_update_time", "colExpr": "COALESCE(aws_instance_usage_update_time, CASE WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_lambda_last_modified_date", "colExpr": "COALESCE(aws_lambda_last_modified_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_configuration_change_date", "colExpr": "COALESCE(aws_resource_configuration_change_date, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_resource_created_date", "colExpr": "COALESCE(aws_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') or array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_created_date", "colExpr": "COALESCE(azure_resource_created_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure VDI') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_resource_last_modified_date", "colExpr": "COALESCE(azure_resource_last_modified_date, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_onboarding_date", "colExpr": "COALESCE(defender_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'MS Defender Device List') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "vulnerability_last_observed_date", "colExpr": "COALESCE(vulnerability_last_observed_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_last_scan_date", "colExpr": "COALESCE(wiz_last_scan_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_modified_date", "colExpr": "COALESCE(wiz_modified_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_onboarding_date", "colExpr": "COALESCE(wiz_onboarding_date, CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE 0 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_replace_unhealthy_instances", "colExpr": "COALESCE(cast(aws_ec2fleet_replace_unhealthy_instances as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_terminate_instances", "colExpr": "COALESCE(cast(aws_ec2fleet_terminate_instances as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_eks_endpoint_private_access", "colExpr": "COALESCE(cast(aws_eks_endpoint_private_access as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_eks_endpoint_public_access", "colExpr": "COALESCE(cast(aws_eks_endpoint_public_access as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_auto_terminate", "colExpr": "COALESCE(cast(aws_emr_auto_terminate as string), CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_visible_to_users", "colExpr": "COALESCE(cast(aws_emr_visible_to_users as string), CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_sagemaker_network_isolation_status", "colExpr": "COALESCE(cast(aws_sagemaker_network_isolation_status as string), CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_azure_policy_status", "colExpr": "COALESCE(cast(azure_aks_azure_policy_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_azure_rbac_status", "colExpr": "COALESCE(cast(azure_aks_azure_rbac_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_defender_status", "colExpr": "COALESCE(cast(azure_aks_defender_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_enable_rbac", "colExpr": "COALESCE(cast(azure_aks_enable_rbac as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_image_cleaner_enabled_status", "colExpr": "COALESCE(cast(azure_aks_image_cleaner_enabled_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_key_vault_secrets_status", "colExpr": "COALESCE(cast(azure_aks_key_vault_secrets_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_monitor_enabled", "colExpr": "COALESCE(cast(azure_aks_monitor_enabled as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_oms_agent_status", "colExpr": "COALESCE(cast(azure_aks_oms_agent_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_storage_snapshot_controller_enabled_status", "colExpr": "COALESCE(cast(azure_aks_storage_snapshot_controller_enabled_status as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_disable_password_authentication", "colExpr": "COALESCE(cast(azure_vm_disable_password_authentication as string), CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') or array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_is_cloud_managed", "colExpr": "COALESCE(cast(wiz_is_cloud_managed as string), CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "wiz_onboarding_status", "colExpr": "COALESCE(cast(wiz_onboarding_status as string), CASE WHEN array_contains(data_source_subset_name,'Wiz Cloud Resource') or array_contains(data_source_subset_name,'Wiz Vulnerability Findings') THEN NULL ELSE '  !' END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_autoscaling_instance_max_size", "colExpr": "COALESCE(aws_autoscaling_instance_max_size, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_autoscaling_instance_min_size", "colExpr": "COALESCE(aws_autoscaling_instance_min_size, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_on_demand_target_capacity", "colExpr": "COALESCE(aws_ec2fleet_on_demand_target_capacity, CASE WHEN array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_provisioned_on_demand_capacity", "colExpr": "COALESCE(aws_ec2fleet_provisioned_on_demand_capacity, CASE WHEN array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_provisioned_spot_capacity", "colExpr": "COALESCE(aws_ec2fleet_provisioned_spot_capacity, CASE WHEN array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_spot_target_capacity", "colExpr": "COALESCE(aws_ec2fleet_spot_target_capacity, CASE WHEN array_contains(data_source_subset_name,'AWS EMR EC2 Fleet') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_ec2fleet_total_target_capacity", "colExpr": "COALESCE(aws_ec2fleet_total_target_capacity, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_emr_volume", "colExpr": "COALESCE(aws_emr_volume, CASE WHEN array_contains(data_source_subset_name,'AWS EMR Cluster') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_lambda_code_size", "colExpr": "COALESCE(aws_lambda_code_size, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_lambda_memory_size", "colExpr": "COALESCE(aws_lambda_memory_size, CASE WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_max_node_pools", "colExpr": "COALESCE(azure_aks_max_node_pools, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vmss_vm_count", "colExpr": "COALESCE(azure_vmss_vm_count, CASE WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE -9223372036854775808 END)", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_autoscaling_instance_types", "colExpr": "CASE WHEN size(aws_autoscaling_instance_types) > 0 THEN aws_autoscaling_instance_types WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_autoscaling_override_mixed_instance_type", "colExpr": "CASE WHEN size(aws_autoscaling_override_mixed_instance_type) > 0 THEN aws_autoscaling_override_mixed_instance_type WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_network_interface_id", "colExpr": "CASE WHEN size(aws_instance_network_interface_id) > 0 THEN aws_instance_network_interface_id WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_security_group_id", "colExpr": "CASE WHEN size(aws_instance_security_group_id) > 0 THEN aws_instance_security_group_id WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "aws_instance_volume_id", "colExpr": "CASE WHEN size(aws_instance_volume_id) > 0 THEN aws_instance_volume_id WHEN array_contains(data_source_subset_name,'AWS EC2 Instance') or array_contains(data_source_subset_name,'AWS EMR EC2 Instance') or array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_aad_profile_admin_group_object_ids", "colExpr": "CASE WHEN size(azure_aks_aad_profile_admin_group_object_ids) > 0 THEN azure_aks_aad_profile_admin_group_object_ids WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_node_pool_node_image_version", "colExpr": "CASE WHEN size(azure_aks_node_pool_node_image_version) > 0 THEN azure_aks_node_pool_node_image_version WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_node_pool_type", "colExpr": "CASE WHEN size(azure_aks_node_pool_type) > 0 THEN azure_aks_node_pool_type WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_aks_node_pool_vm_sizes", "colExpr": "CASE WHEN size(azure_aks_node_pool_vm_sizes) > 0 THEN azure_aks_node_pool_vm_sizes WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_network_interface_id", "colExpr": "CASE WHEN size(azure_vm_network_interface_id) > 0 THEN azure_vm_network_interface_id WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vm_network_security_group_id", "colExpr": "CASE WHEN size(azure_vm_network_security_group_id) > 0 THEN azure_vm_network_security_group_id WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') or array_contains(data_source_subset_name,'MS Azure Virtual Machine') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "azure_vmss_vm_size", "colExpr": "CASE WHEN size(azure_vmss_vm_size) > 0 THEN azure_vmss_vm_size WHEN array_contains(data_source_subset_name,'MS Azure Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "cloud_instance_id", "colExpr": "CASE WHEN size(cloud_instance_id) > 0 THEN cloud_instance_id WHEN array_contains(data_source_subset_name,'AWS Resource Details') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_action_type", "colExpr": "CASE WHEN size(defender_action_type) > 0 THEN defender_action_type WHEN array_contains(data_source_subset_name,'MS Defender Device Events') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_detection_method", "colExpr": "CASE WHEN size(defender_detection_method) > 0 THEN defender_detection_method WHEN array_contains(data_source_subset_name,'MS Defender Device Events') or array_contains(data_source_subset_name,'MS Defender Device List') or array_contains(data_source_subset_name,'MS Defender Device Software Inventory') or array_contains(data_source_subset_name,'MS Defender Device TVM Software Vulnerability') or array_contains(data_source_subset_name,'MS Defender Device TVM') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_id", "colExpr": "CASE WHEN size(defender_id) > 0 THEN defender_id WHEN array_contains(data_source_subset_name,'MS Defender Device Events') or array_contains(data_source_subset_name,'MS Defender Device List') or array_contains(data_source_subset_name,'MS Defender Device Software Inventory') or array_contains(data_source_subset_name,'MS Defender Device TVM Software Vulnerability') or array_contains(data_source_subset_name,'MS Defender Device TVM') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "defender_threat_name", "colExpr": "CASE WHEN size(defender_threat_name) > 0 THEN defender_threat_name WHEN array_contains(data_source_subset_name,'MS Defender Device Events') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}, {"colName": "qualys_detection_method", "colExpr": "CASE WHEN size(qualys_detection_method) > 0 THEN qualys_detection_method WHEN array_contains(data_source_subset_name,'Qualys Host List') or array_contains(data_source_subset_name,'Qualys Host Summary') or array_contains(data_source_subset_name,'Qualys Host Vulnerability') THEN NULL ELSE array('  !') END", "fieldsSpec": {"computationPhase": "inter"}}]}