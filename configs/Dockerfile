###testing
ARG PYTHON_BASE_IMAGE="prevalentai/python:3.8.12-bullseye11.8"
FROM ${PYTHON_BASE_IMAGE}
USER root

WORKDIR "/opt/airflow"

RUN mkdir /opt/airflow/ei_configs
COPY ./scripts /opt/airflow/ei_configs/scripts
COPY ./documents /opt/airflow/ei_configs/documents
COPY ./orchestration_variables /opt/airflow/ei_configs/orchestration_variables
COPY ./orchestration_shared_fs /opt/airflow/ei_configs/orchestration_shared_fs
COPY ./spark_job_configs /opt/airflow/ei_configs/spark_job_configs
#COPY ./druid_indexing_configs /opt/airflow/ei_configs/druid_indexing_configs


