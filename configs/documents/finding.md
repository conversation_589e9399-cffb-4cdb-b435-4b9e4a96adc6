[comment]: <> (Knowledge Graph)
[comment]: <> (Finding)
[comment]: <> (#entity-inventory/finding)

###### Introduction
A finding refers to a specific instance or result of a security assessment, audit, or analysis, essential for identifying and addressing security threats proactively.
Finding indicates a potential security weakness, vulnerability, or risk within an organization's IT infrastructure, systems, applications, or processes.

###### Data Sources
1. AWS Security Hub Findings

2. Microsoft Azure Security Center Alerts

3. Microsoft Azure Security Assessments

###### Finding High Level Diagram

<img src="finding_high_level.png" width="60%">

###### Finding in Cloud Landscape

<img src="finding_cloud_landscape.png" width="60%">

###### Finding Severity
The severity level of a Finding refers to the level of potential impact or risk associated with the security issue. This helps us to prioritize which issues need immediate attention and remediation. Severity Level includes values such as:

- INFORMATIONAL - No issue was found.

- LOW - The issue does not require action on its own.

- MEDIUM - The issue must be addressed but not urgently.

- HIGH - The issue must be addressed as a priority.

- CRITICAL - The issue must be remediated immediately to avoid it escalating.

###### Status
Finding status indicates the current stage of handling a security finding within the workflow.

- Open - Indicates findings that require attention or action.

- Closed - Indicates findings that have been addressed or do not require further action.

###### Compliance Status
Compliance status refers to the state of a finding in relation to a specific compliance standard or framework. This status indicates whether resources and configurations comply with the best practices and requirements defined by the standard.

###### Affected Resource
Affected Resource refers to the specific Cloud resource that is implicated by a security finding. This information helps identify which parts of your environment are impacted by a particular security issue. We are capturing below details of a resource.

- Resource Type - The type of resource affected by the finding, such as an Account, EC2 instance, S3 bucket, IAM role, RDS instance, etc.

- Resource ID - The unique identifier of the affected resource.


###### Finding Relationship
Findings are linked to various aspects of the cloud environment. A high-level relationship diagram can be represented as below:

<img src="finding_relationship.png" width="50%">