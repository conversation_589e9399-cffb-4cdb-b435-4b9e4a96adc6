[comment]: <> (Knowledge Graph)
[comment]: <> (Cloud Account)
[comment]: <> (#entity-inventory/cloud-account)

###### Introduction
Cloud account holistically represents the logical container of cloud resources. Cloud accounts are a fundamental concept in cloud computing, as they allow individuals, organizations, and developers to leverage the capabilities of cloud providers like Azure, AWS & GCP to build, deploy, and manage applications and infrastructure in a scalable and flexible manner.

###### Cloud Account High Level Diagram


<img src="cloud_account_high_level.png" width="50%">

Cloud Account includes popular attributes like:


###### Account Name

Account name refers to the identifier or name associated with a user account or a cloud service account. This name is used to identify and manage access to various cloud services, resources, and administrative functions.

###### Account Id

An Account ID is a unique string of numbers and/or letters assigned to an account. This ensures that each account can be distinctly identified, even if multiple accounts have similar names. Unlike an account name, which can often be changed, an Account ID typically remains constant throughout the life of the account.

###### Account Status

Account Status indicates whether a user account or service account is currently active or inactive.

###### Cloud Account Relationship
The below diagram represents relationship between Cloud Account with other entities.

<img src="cloud_account_relationship.png" width="50%">