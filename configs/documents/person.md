[comment]: <> (Knowledge Graph)
[comment]: <> (Person)
[comment]: <> (#entity-inventory/person)

###### Introduction

The Person entity represents individuals within an organization or system. These individuals may have different roles, responsibilities, and employment statuses. Understanding and managing personnel information is essential for various organizational processes, including HR management, access control, and communication.

###### Person High Level Diagram

<img src="PersonEntity.png" width="60%">

###### Permanent
Permanent personnel are individuals employed by an organization on a long-term basis. They typically have ongoing employment contracts and are considered regular employees. Permanent employees may receive benefits such as healthcare, retirement plans, and paid time off.

###### Contract
Contract personnel are individuals engaged by an organization for a specific project or duration. They work under contractual agreements outlining terms and conditions, including project scope, compensation, and duration of engagement. Contract employees may be hired for specialized tasks or to address temporary resource needs.

###### Member
Member designation refers to individuals who hold membership status within an organization, community, or group. Membership may entail various rights, privileges, and responsibilities based on the organization's structure and objectives. Memberships can be voluntary or require specific qualifications or criteria for eligibility.

###### Guest
Guest users are typically not part of the organization's internal directory but are granted limited access to specific applications, services, or resources.

###### Person Relationship
The below diagram represents relationship between Person with other entities.

<img src="PersonRelationship.png" width="100%">