import os, json
import copy, pendulum
from urllib.parse import urljoin

from airflow.operators.python import PythonOperator
from airflow.operators.empty import EmptyOperator
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory

import logging, base64, requests, logging
from airflow.exceptions import AirflowSkipException, AirflowException
from commons.pe.common_utils import Utils
from airflow.models.variable import Variable
from airflow.models.dagrun import DagRun
from commons.pe.sds_date_utils import SDSDateUtils
from airflow.models.connection import Connection


def find_task_run(dag_run, task_name, dag_name):
    analytical_job_configs = json.loads(Variable.get("SDS_EI_ANALYTICAL_JOBS_VARIABLES"))
    analysis_period = analytical_job_configs['analysis_period']
    logger = logging.getLogger("get_analytical_job_tasks")
    dag_runs = DagRun.find(dag_id=dag_name, state="success") + DagRun.find(
        dag_id=dag_name,
        state="failed")

    prev_dag_runs = [run for run in dag_runs if
                     dag_run.execution_date.timestamp() > run.execution_date.timestamp()]
    logger.info(f"DAG RUNS: {prev_dag_runs}, len: {len(prev_dag_runs)}")
    prev_dag_runs.sort(key=lambda x: x.execution_date, reverse=True)
    ei_default_start_epoch = Variable.get("sds_ei_start_epoch_default", default_var=000000000)
    logger.info(f"Previous DAG run counts: {len(prev_dag_runs)}")
    logger.info(f"Previous DAG runs: {prev_dag_runs}")
    if len(prev_dag_runs) == 0:
        logger.info(f"This is the first DAG run, updating start epoch as {ei_default_start_epoch}")
        return ei_default_start_epoch
    elif prev_dag_runs[0].state == 'success':
        last_finished_task = prev_dag_runs[0].get_task_instance(
            task_id=f"{task_name.replace('_config', '')}")
        if last_finished_task is not None:
            logger.info("This is not the first Task run, hence updating the start epoch with batch start date")
            return SDSDateUtils.get_end_date(pendulum.instance(prev_dag_runs[0].data_interval_start), analysis_period,
                                             'utc')
        else:
            logger.info(
                f"This is the first Task run, hence proceeding to update the start epoch as {ei_default_start_epoch}")
            return ei_default_start_epoch
    elif prev_dag_runs[0].state == 'failed':
        raise AirflowException("Aborting current run due to previous DAG run failure.")

def update_exec_instance_config(config: dict) -> dict:
    if config.get("conf", {}).get("spark.dynamicAllocation.enabled") == "true":
        original_executors = config.get("executor_instances", "1")
        if original_executors != "1":
            config["executor_instances"] = "1"
            config["conf"]["spark.dynamicAllocation.maxExecutors"] = str(original_executors)
    return config

class AnalyticalUtils:
    """Class containing Analytical utils"""
    EVENT_TIMESTAMP = "{{ get_end_date(data_interval_start, 'day','utc') }}"

    @staticmethod
    def config_component_handler(base_orchestration_config, custom_confs, override_config, config_path, task_id):
        base_orchestration_config["args"].extend(custom_confs)
        base_orchestration_config["app_name"] = task_id
        base_orchestration_config.setdefault("conf", {})["spark.app.name"] = task_id
        if "base_config_path" in base_orchestration_config:
            del base_orchestration_config["base_config_path"]
        merged_configs = Utils.merge_dictionaries(base_orchestration_config, override_config)
        merged_configs["args"].extend(config_path)
        for key in ("driver_cores", "executor_cores", "executor_instances"):
            merged_configs[key] = int(merged_configs.get(key))
        return merged_configs

    @staticmethod
    def get_analytical_job_configs(task_id, parsed_interval_start, config_type="loader"):
        base_orchestration_config = Variable.get(f"sds_ei_{config_type}_orchestration_config_template",
                                                 deserialize_json=True)
        try:
            override_config = Variable.get(task_id, deserialize_json=True)
        except KeyError:
            logging.info(f"No override config found for {task_id}")
            override_config = {}

        config_path = [
            "--config-path",
            f"{override_config.get('base_config_path', '') or base_orchestration_config.get('base_config_path', '')}{task_id}"
        ]
        custom_confs = [
            "--parsed-interval-start",
            f"{parsed_interval_start}",
            "--parsed-interval-end",
            "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}",
            "--event-timestamp-end",
            AnalyticalUtils.EVENT_TIMESTAMP,
            "--previous-end-epoch",
            "{{ get_end_date(prev_data_interval_start_success, 'day','utc') if prev_data_interval_start_success is not none else -1 }}"
        ]

        merged_configs = AnalyticalUtils.config_component_handler(base_orchestration_config, custom_confs,
                                                                  override_config, config_path, task_id)
        final_config = copy.deepcopy(json.dumps(merged_configs))
        return final_config

    @staticmethod
    def get_data_quality_job_configs(task, group):
        conn = Connection.get_connection_from_secrets("management_api")
        host = conn.host
        port = conn.port
        schema = conn.schema
        table_name = group.replace("_data_quality", "")
        profiling_schema_name = Variable.get("data_quality_schema_name", "ei_profiling")
        url = urljoin(f"{schema}://{host}:{port}/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"
                      f"", f"sds_ei__{group}")
        base_variable = Variable.get("sds_ei_data_quality_job_template", default_var={},
                                     deserialize_json=True)
        override_variable = Variable.get("sds_ei__{group}_job_config".format(group=group, task=task), {},
                                         deserialize_json=True)
        schema_name = Variable.get("EI_SCHEMA_NAME", "ei")
        base_variable["application_file"] = os.path.join(base_variable["application_file"], task + ".py")
        base_variable["app_name"] = (task + group).replace("_", "-")
        configs = [
            "--tableName",
            "iceberg_catalog.{ei_schema_name}.sds_ei__{table_name}".format(ei_schema_name=schema_name,
                                                                           table_name=table_name),
            "--startEpoch",
            "{{ get_start_date(data_interval_start, 'day','utc') }}",
            "--endEpoch",
            "{{ get_end_date(data_interval_start, 'day','utc') }}",
            "--outputPath",
            "iceberg_catalog.{ei_schema_name}.sds_ei__data_quality".format(ei_schema_name=profiling_schema_name),
            "--configURL",
            url
        ]
        base_variable["args"] = configs
        if override_variable != {}:
            base_variable.update(override_variable)
            return base_variable
        else:
            return base_variable

    @staticmethod
    def get_disambiguated_job_configs(task_id, config_type="intra"):
        base_orchestration_config = Variable.get(f"sds_ei_{config_type}_orchestration_config_template",
                                                 deserialize_json=True)
        try:
            override_config = Variable.get(task_id, deserialize_json=True)
        except KeyError:
            logging.info(f"No override config found for {task_id}")
            override_config = {}

        config_path = [
            "--config-path",
            f"{override_config.get('base_config_path', '') or base_orchestration_config.get('base_config_path', '')}{task_id}"
        ]

        if config_type == "intra" or config_type == "inter":
            custom_confs = [
                "--current-updated-date",
                AnalyticalUtils.EVENT_TIMESTAMP,
                "--previous-updated-date",
                "{{ get_end_date(prev_data_interval_start_success, 'day','utc') if prev_data_interval_start_success is not none else -1 }}"
            ]

        elif config_type == "relation_disambiguation":
            custom_confs = [
                "--current-updated-date",
                AnalyticalUtils.EVENT_TIMESTAMP
            ]
        else:
            raise Exception("Invalid config type")

        merged_configs = AnalyticalUtils.config_component_handler(base_orchestration_config, custom_confs,
                                                                  override_config, config_path, task_id)
        final_config = copy.deepcopy(json.dumps(merged_configs))
        return final_config

    @staticmethod
    def get_dynamic_job_configs(dag_run,task_id, config_type, dag_name):
            """
            Generic function to get dynamic analytical job configurations

            Args:
                task_id (str): The identifier for the task
                config_type (str): Type of configuration to process

            Returns:
                str: JSON string containing the merged configuration
            """
            # Get Dag level over_ride
            base_orchestration_config = Variable.get(f"sds_ei_{config_type}_orchestration_config_template",
                                                     deserialize_json=True)
            
            try:
                dag_override_config = Variable.get(f"{dag_name}_dag", deserialize_json=True)
            except KeyError:
                logging.info(f"No override config found for {dag_name}_dag")
                dag_override_config = {}

            pre_base_path = dag_override_config.get('base_config_path', '') or base_orchestration_config.get('base_config_path',
                                                                                                     '')
            # Get override configuration if exists
            try:
                task_override_config = Variable.get(task_id, deserialize_json=True)
            except KeyError:
                logging.info(f"No override config found for {task_id}")
                task_override_config = {}

            # Prepare config path
            base_path = task_override_config.get('base_config_path', '') or pre_base_path
            full_path = f"{base_path}{task_id}"
            config_path = ["--config-path",
                           full_path if full_path.lower().startswith(('http://', 'https://')) else f"{full_path}.json"]

        
            # Define custom configurations
            custom_confs = [
                "--current-updated-date",
                AnalyticalUtils.EVENT_TIMESTAMP,
                "--parsed-interval-end",
                "{{ str(pendulum.parse(dag_run.conf['data_interval_end']).timestamp()*1000).split('.')[0] }}"
            ]

            pre_merged_configs = Utils.merge_dictionaries(dag_override_config, task_override_config)

            # Merge configurations
            merged_configs = AnalyticalUtils.config_component_handler(base_orchestration_config,custom_confs,pre_merged_configs,config_path,task_id)
            spec_updated_config=update_exec_instance_config(merged_configs)
            # Create final configuration
            final_config = copy.deepcopy(json.dumps(spec_updated_config))
            logging.info(f"Final Config {final_config}")
            return final_config


    @staticmethod
    def get_publisher_job_tasks(analytical_variables, update_druid_dimension):
        task_run_status_list = []
        for each_variable in analytical_variables:
            sds_ei_analytics_task = SparkOperatorFactory.get(task_id=each_variable,
                                                             show_logs=True,
                                                             retries=2,
                                                             from_var=f"{each_variable}")
            table_name = each_variable.replace('__job_config', '')
            druid_var_update = PythonOperator(
                task_id=f"{table_name}_druid_var",
                python_callable=update_druid_dimension,
                op_kwargs={"table_name": table_name}
            )
            task_run_status_list.append(sds_ei_analytics_task >> druid_var_update)
        return task_run_status_list

    @staticmethod
    def get_druid_vars_update_job_tasks(analytical_variables, update_druid_dimension):
        task_run_status_list = []
        for each_variable in analytical_variables:
            table_name = each_variable.replace('__model', '')
            druid_var_update = PythonOperator(
                task_id=f"{table_name}_druid_var",
                python_callable=update_druid_dimension,
                op_kwargs={"table_name": table_name}
            )
            task_run_status_list.append(druid_var_update)
        return task_run_status_list
