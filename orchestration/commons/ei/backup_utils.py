from typing import List

from airflow.operators.bash import BashOperator

class BackupUtils:
    """
    Class containing backup utils
    """

    @staticmethod
    def generate_backup_tasks(list_of_backup_data_sources: List, backup_config_variable: str, task_prefix: str,
                              analysis_period: str = 'day'):
        backup_tasks = [
            BashOperator(
                task_id=f"{task_prefix}_backup_{each_data_source}",
                retries=2,
                bash_command=F"aws s3 cp {{{{ var.json.{backup_config_variable}.get('{each_data_source}').get('source_s3_key')}}}} "
                             F"{{{{var.json.{backup_config_variable}.get('{each_data_source}').get('destination_s3_key')}}}}"
                             F"{{{{ pendulum.from_timestamp(get_start_date(data_interval_start, '{analysis_period}' ,'utc')//1000).strftime('%Y_%m_%d') }}}}"
                             F"{{{{var.json.{backup_config_variable}.get('{each_data_source}').get('destination_s3_suffix_key')}}}} --recursive"
            )
            for each_data_source in list_of_backup_data_sources
        ]

        return backup_tasks
