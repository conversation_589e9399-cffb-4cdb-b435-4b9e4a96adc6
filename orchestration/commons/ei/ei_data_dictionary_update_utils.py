from commons.pe.common_utils import Utils
from commons.ei.ei_constants.ei_constants import EIConstants
from urllib.parse import urljoin
import requests
from requests.exceptions import RequestException
from commons.pe.common_utils import Utils
import json,os,logging
from airflow.models.connection import Connection
from airflow.models.variable import Variable
from commons.pe.common_utils import Utils
import re
import time


ssl_verify = Variable.get("MANAGEMENT_API_SSL_VERIFY", default_var=True, deserialize_json=True)
keycloak_host = os.getenv("KEYCLOAK_HOST")
headers = {'Content-Type': 'application/json',"Authorization": Utils.get_keycloak_token()}
logger = logging.getLogger("ei_data_dictionary_validator")
logger.info(headers)
conn = Connection.get_connection_from_secrets("management_api")
HOST = conn.host
PORT = conn.port
SCHEMA = conn.schema
list_url = urljoin(f"{SCHEMA}://{HOST}:{PORT}/", EIConstants.LIST_CONFIG_META_API_ENDPOINT)
merger_url =os.getenv("CONFIG_MERGER_API_ENDPOINT")
get_url = merger_url.rstrip('/') + "/data_dictionary_update/"
patch_url = urljoin(f"{SCHEMA}://{HOST}:{PORT}/", EIConstants.PATCH_STATUS_MANAGER)



def get_metadata_api_response():
    api_url = f"{list_url}"
    logger.info(f"Making request to API: {api_url}")
    try:
        # SSL needed for security
        response = requests.get(api_url, headers=headers, verify=ssl_verify)
        logger.info(f"Request headers: {headers}")
        response.raise_for_status()
        
        # Get the JSON response
        data_dictionary = response.json()
        names = []
        
        # Loop through all items in the response
        for item in data_dictionary:
            if "config_item_type" in item and "name" in item:
                item_type = item["config_item_type"]
                item_name = item["name"]
                
                # Check if the item matches either of the required config item types
                if item_type == "data_dictionary_config" or item_type == "relationship_data_dictionary_config":
                    names.append(item_name)
        
        # If names are populated, return them; otherwise, log that no valid items were found
        if names:
            logger.info(f"Found {len(names)} items matching the criteria.")
            return names
        else:
            logger.info("No items found matching 'data_dictionary_config' or 'relationship_data_dictionary_config'.")
            return []  # Return an empty list if no matches found

    except requests.exceptions.RequestException as e:
        logger.error(f"Error making request to {api_url}: {e}")
        raise requests.exceptions.RequestException(f"Unable to get the response. Error: {e}")

            


class EntityDatadictionaryUpdate:
    def __init__(self):
        # Constructor is intentionally left empty as no initialization is required.
        pass

    def get_api_response(self, entity_data_dict):
        if entity_data_dict is not None:
            api_url = f"{get_url}{entity_data_dict}"
            logger.info(api_url)

            retries = 3  # Max retries
            delay = 5    # Time delay between retries (in seconds)
            
            for attempt in range(retries):
                try:
                    response = requests.get(api_url, headers=headers, verify=ssl_verify)
                    logger.info(headers)
                    logger.info(f"Response Status Code: {response.status_code}")
                    
                    if response.status_code == 200:
                        logger.info("Request successful!")
                        return response  # Return the response if status is 200
                    
                    # If status is not 200 and not 503, retry
                    elif response.status_code != 200:
                        logger.warning(f"Received status code {response.status_code}. Retrying... ({attempt + 1}/{retries})")
                        time.sleep(delay)  # Wait before retrying
                        continue  # Retry the request
                    
                except RequestException as e:
                    logger.error(f"Error occurred during request: {e}")
                    logger.warning(f"Retrying... ({attempt + 1}/{retries})")
                    time.sleep(delay)  # Wait before retrying
                    continue  # Retry the request

            # If the function doesn't return successfully, raise an exception after max retries
            logger.error(f"Failed after {retries} attempts.")
            raise requests.exceptions.RequestException("Unable to get a valid response after retries.")
        

    def get_patch_api_response(self):
            api_url = f"{patch_url}"
            logger.info(api_url)
            entity_patch_url = api_url + "data_dictionary_config"
            relationship_patch_url = api_url + "relationship_data_dictionary_config"

            retries = 3  # Max retries
            delay = 5    # Time delay between retries (in seconds)
            
            for attempt in range(retries):
                try:
                    response = requests.get(entity_patch_url, headers=headers, verify=ssl_verify )
                    logger.info(f"Response Status Code: {response.status_code}")
                    patch_data=response.json()
                    logger.info(f"patch response {patch_data}")
                    version=patch_data['version']
                    logger.info(f"patch version {version}")
                    response_data = {"current_state": "trigger_complete"}
                    params = {"version": version}
                    entity_response = requests.patch(entity_patch_url, json=response_data,  headers=headers, params=params, verify=ssl_verify)
                    
                    relationship_response = requests.patch(relationship_patch_url, json=response_data,  headers=headers, params=params, verify=ssl_verify)

                    logger.info(f"entity_content : {entity_response.content} , entity_content : {relationship_response.content}")
                    logger.info(f"Entity_patch_url {entity_response.url}, Relationship_patch_url {relationship_response.url}")
                    
                    if entity_response.status_code == 200 and relationship_response.status_code == 200:
                        logger.info("Request successfully  patched")
                        return entity_response,relationship_response  # Return the response if status is 200
                    
                    # If status is not 200 and not 503, retry
                    elif entity_response.status_code != 200 and relationship_response.status_code != 200  :
                        logger.warning(f"Received status code for entity patch {entity_response.status_code} and relationship patch {relationship_response.status_code}. Retrying... ({attempt + 1}/{retries})")
                        time.sleep(delay)  # Wait before retrying
                        continue  # Retry the request
                    
                except RequestException as e:
                    logger.error(f"Error occurred during request: {e}")
                    logger.warning(f"Retrying... ({attempt + 1}/{retries})")
                    time.sleep(delay)  # Wait before retrying
                    continue  # Retry the request

            # If the function doesn't return successfully, raise an exception after max retries
            logger.error(f"Failed after {retries} attempts.")
            raise requests.exceptions.RequestException("Unable to get a valid response after retries.")
            


    