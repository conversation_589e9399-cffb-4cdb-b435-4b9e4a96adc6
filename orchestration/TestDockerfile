ARG AIRFLOW_BASE_IMAGE_VERSION="1-5-0-16"
ARG AIRFLOW_BASE_IMAGE="prevalentai/airflow:sds-pe-orchestration-"
# Using Platform airflow image as base image which has all platform pipelines and requirements.
FROM ${AIRFLOW_BASE_IMAGE}${AIRFLOW_BASE_IMAGE_VERSION}

# Adding ei orchestration components into the image
ADD --chown=airflow:0 ./dags ${AIRFLOW_HOME}/dags/
ADD --chown=airflow:0 ./ei_plugins ${AIRFLOW_HOME}/ei_plugins
ADD --chown=airflow:0 ./ei_commons ${AIRFLOW_HOME}/ei_commons

ADD --chown=airflow:0 ./tests ${AIRFLOW_HOME}/tests
ADD --chown=airflow:0 ./tests/config ${AIRFLOW_HOME}/shared
ADD --chown=airflow:0 ./start_test.sh ${AIRFLOW_HOME}

CMD ["./start_test.sh"]