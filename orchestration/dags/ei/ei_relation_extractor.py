import json
import pendulum
from datetime import datetime
from urllib.parse import urljoin
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from airflow.models.baseoperator import chain
from commons.ei.ei_constants.ei_constants import EIConstants
import pendulum
import os
from commons.ei.ei_constants.common_constants import DruidApiConstants
from urllib.parse import urljoin
from commons.ei.analytical_utils import AnalyticalUtils,find_task_run

with SDSPipelineDAG(
        pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
        dag_id="sds_ei_relation_extractor_jobs",
        description="SDS EI RELATION EXTRACTOR",
        start_date=datetime(2000, 1, 1),
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=6,
        tags=[EIConstants.SDS_EI_MODULE_NAME, "DATA_ANALYTICS"],
        user_defined_macros={
            "render_variable": Utils.render_variable,
            "get_start_date": SDSDateUtils.get_start_date,
            "find_task_run": find_task_run,
            "get_end_date": SDSDateUtils.get_end_date,
            "get_analytical_job_configs": AnalyticalUtils.get_analytical_job_configs,
            "json": json,
            "str": str,
            "pendulum": pendulum,
            "urljoin": urljoin,
            "DruidApiConstants": DruidApiConstants
        },
) as sds_ei_relation_extractor_jobs:
    sds_ei_relation_extractor_jobs_keys = Utils.read_data_from_shared_filesystem(
        relative_file_path=EIConstants.SDS_EI_RELATION_EXTRACTOR_CONFIG_KEYS)
    sds_ei_relation_extractor_start = EmptyOperator(task_id="sds_ei_relation_extractor_start", wait_for_downstream=True)
    sds_ei_relation_extractor_end = EmptyOperator(task_id="sds_ei_relation_extractor_end")

    with TaskGroup(group_id="sds_ei_analytical_jobs") as sds_ei_analytical_jobs:
        for task in sds_ei_relation_extractor_jobs_keys:
            relation_job_task = SparkOperatorFactory.get(
                        task_id=f"{task}",
                        show_logs=True,
                        from_conf= f"get_analytical_job_configs('{task}',find_task_run(dag_run, 'sds_ei_analytical_jobs.{task}', 'sds_ei_relation_extractor_jobs'), 'relation')",
                        retries=2
                    )
            sds_ei_relation_extractor_start >> relation_job_task >> sds_ei_relation_extractor_end