import json
from datetime import datetime
from airflow.operators.empty import EmptyOperator
from airflow.utils.task_group import TaskGroup
from commons.pe.common_utils import Utils
from commons.pe.sds_dag_orchestrator import SDSPipelineDAG
from commons.pe.sds_date_utils import SDSDateUtils
from airflow.models.baseoperator import chain
from commons.ei.ei_constants.ei_constants import EIConstants
import pendulum
from commons.ei.ei_constants.common_constants import DruidApiConstants
from urllib.parse import urljoin
from commons.pe.operator.spark_operator_factory import SparkOperatorFactory
from commons.ei.analytical_utils import AnalyticalUtils
from commons.ei.ei_utils import EIUtils

with SDSPipelineDAG(
        pipeline_id=EIConstants.SDS_EI_MODULE_NAME,
        dag_id="sds_ei_inter_source_disambiguation",
        description="SDS EI Disambiguation Analytics",
        start_date=datetime(2000, 1, 1),
        render_template_as_native_obj=True,
        catchup=False,
        concurrency=6,
        tags=[EIConstants.SDS_EI_MODULE_NAME, "DATA_ANALYTICS"],
        user_defined_macros={
            "render_variable": Utils.render_variable,
            "get_start_date": SDSDateUtils.get_start_date,
            "get_end_date": SDSDateUtils.get_end_date,
            "get_disambiguated_job_configs": AnalyticalUtils.get_disambiguated_job_configs,
            "json": json,
            "str": str,
            "pendulum": pendulum,
            "urljoin": urljoin,
            "DruidApiConstants": DruidApiConstants
        },
) as sds_ei_analytics:
    sds_ei_inter_source_disambiguated_model_config_keys = Utils.read_data_from_shared_filesystem(
        relative_file_path=EIConstants.SDS_EI_INTER_SOURCE_DISAMBIGUATED_MODEL_CONFIG_KEYS)
    sds_ei_analytics_start = EmptyOperator(task_id="sds_ei_analytics_start", wait_for_downstream=True)
    sds_ei_analytics_end = EmptyOperator(task_id="sds_ei_analytics_end")

    with TaskGroup(group_id="sds_ei_inter_source_disambiguation_analytical_jobs") as sds_ei_analytical_jobs:
            for task in sds_ei_inter_source_disambiguated_model_config_keys:
                inter_job_task = SparkOperatorFactory.get(
                    task_id=f"{task}",
                    show_logs=True,
                    from_conf=f"get_disambiguated_job_configs('{task}', 'inter')",
                    retries=2
                )
                sds_ei_analytics_start >> inter_job_task >> sds_ei_analytics_end