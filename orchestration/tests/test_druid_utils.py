import pytest
from unittest.mock import Mock, patch
from urllib.parse import urljoin
import commons.ei

from commons.ei.ei_druid_utils import GetOlapResponse

DS_QUERY_URL = "/druid/v2/sql/"


class TestGetOlapResponse:
    # Successfully retrieves JSON response when API call is valid
    def test_successful_json_response(self, mocker):
        mocker.patch('os.getenv', side_effect=lambda key: {
            "OLAP_INSIGHT_API_HOST": "http://mocked_keycloak_host",
            "OLAP_INSIGHT_API_ENDPOINT": "/mocked_endpoint"
        }.get(key))
        mocker.patch('commons.pe.common_utils.Utils.get_keycloak_token', return_value='mocked_token')
        mock_response = mocker.Mock()
        mock_response.json.return_value = {"success": True}
        mock_response.raise_for_status = mocker.Mock()
        mocker.patch('requests.post', return_value=mock_response)
        response = GetOlapResponse.get_response(self, {"data": "test"})

        assert response == {"success": True}

    # Returns a list of values for a valid response_data with data_label and key
    def test_extract_data_with_valid_response(self):
        response_data = {
            "data": [
                {
                    "label1": {
                        "data": [{"key1": "value1"}, {"key1": "value2"}]
                    }
                }
            ]
        }
        result = GetOlapResponse.extract_data(response_data, "label1", "key1")
        assert result == ["value1", "value2"]

    # Correctly maps 'CHARACTER VARYING' to 'string' in the output dictionary
    def test_maps_character_varying_to_string(self):
        olap_api_response = {
            'data': [
                {
                    'fields': {
                        'data': [
                            {'COLUMN_NAME': 'name', 'DATA_TYPE': 'CHARACTER VARYING'},
                            {'COLUMN_NAME': 'age', 'DATA_TYPE': 'INTEGER'}
                        ]
                    }
                }
            ]
        }
        expected_output = {'name': 'string', 'age': 'integer'}
        result = GetOlapResponse.extract_type_dict(olap_api_response, 'fields')
        assert result == expected_output
#     @staticmethod
#     def get_mock_conn(mocker):
#         conn_mock = mocker.Mock()
#         conn_mock.host = "localhost"
#         conn_mock.port = "8080"
#         conn_mock.schema = "http"
#         return conn_mock
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_init_valid_connection(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#
#         druid_fetch = GetOlapResponse()
#         assert druid_fetch.url == "http://localhost:8080/druid/v2/sql/"
#         assert druid_fetch.headers == {'Content-Type': 'application/json'}
#         assert druid_fetch.ssl_verify is False
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_entities_returns_list(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#
#         mock_execute_query_and_parse_response = mocker.patch.object(GetOlapResponse,
#                                                                     'execute_query_and_parse_response')
#         mock_execute_query_and_parse_response.return_value = ['entity1', 'entity2', 'entity3']
#         druid_schema_fetch = GetOlapResponse()
#         entities = druid_schema_fetch.get_entities()
#         assert entities == ['entity1', 'entity2', 'entity3']
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_fields_from_inventory_returns_list(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         mock_execute_query_and_parse_response = mocker.patch.object(GetOlapResponse,
#                                                                     'execute_query_and_parse_response')
#         mock_execute_query_and_parse_response.return_value = ['field1', 'field2', 'field3']
#         druid_schema_fetch = GetOlapResponse()
#         fields = druid_schema_fetch.get_fields_from_inventory()
#         assert fields == ['field1', 'field2', 'field3']
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_execute_query_and_parse_response_returns_empty_list(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 400
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         druid_schema_fetch = GetOlapResponse()
#         query = {"query": "SELECT * FROM table"}
#         field_values = druid_schema_fetch.execute_query_and_parse_response(query, "field")
#         assert field_values == []
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_sources_returns_list(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         mock_execute_query_and_parse_response = mocker.patch.object(GetOlapResponse,
#                                                                     'execute_query_and_parse_response')
#         mock_execute_query_and_parse_response.return_value = ['source1', 'source2', 'source3']
#         druid_schema_fetch = GetOlapResponse()
#         sources = druid_schema_fetch.get_sources()
#         assert sources == ['source1', 'source2', 'source3']
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_fields_success(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         mock_execute_query_and_parse_response = mocker.patch.object(GetOlapResponse,
#                                                                     'execute_query_and_parse_response')
#         mock_execute_query_and_parse_response.side_effect = [['field1', 'field2', 'field3'],
#                                                              ['field2', 'field3', 'field4']]
#         druid_schema_fetch = GetOlapResponse()
#         fields = druid_schema_fetch.get_fields()
#         assert sorted(fields) == ['field2', 'field3']
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_relationships_success(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         mock_execute_query_and_parse_response = mocker.patch.object(GetOlapResponse,
#                                                                     'execute_query_and_parse_response')
#         mock_execute_query_and_parse_response.side_effect = [['relationship1', 'relationship2'],
#                                                              ['inverse_relationship1', 'inverse_relationship2']]
#         druid_schema_fetch = GetOlapResponse()
#         relationships = druid_schema_fetch.get_relationships()
#         assert len(relationships) == 4
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_relationship_fields(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         mocker.patch.object(GetOlapResponse,'execute_query_and_parse_response', return_value = ["field1", "field2", "field3"])
#         druid_schema_fetch = GetOlapResponse()
#         relationship_fields = druid_schema_fetch.get_relationship_fields()
#         assert sorted(relationship_fields) == ["field1", "field2", "field3"]
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_fields_from_inventory(self, mock_post, mocker):
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", DS_QUERY_URL))
#         fields = ["field1", "field2", "field3"]
#         druid_schema_fetch = GetOlapResponse()
#         druid_schema_fetch.execute_query_and_parse_response = lambda query, field: fields
#         assert druid_schema_fetch.get_fields_from_inventory() == fields
#
#     @patch('commons.ei.ei_druid_utils.requests.post')
#     def test_get_aggregate_values(self, mock_post, mocker):
#         query="""SELECT MIN(field1) AS min___field1, MAX(field2) AS max___field2, MAX(LENGTH(field1)) as width___field1 FROM sds_ei_relationship"""
#         conn_mock = self.get_mock_conn(mocker)
#         mock_post.return_value.status_code = 200
#         mocker.patch("commons.ei.ei_druid_utils.GetOlapResponse._get_conn_url",
#                      return_value=urljoin(f"{conn_mock.schema}://{conn_mock.host}:{conn_mock.port}/", query))
#         mock_execute_query = mocker.patch.object(GetOlapResponse,'execute_query')
#         mock_execute_query.return_value = [{"min___field1":0,"max___max___field2":17519,"width___field1":6}]
#         druid_schema_fetch = GetOlapResponse()
#         aggregate_values = druid_schema_fetch.get_aggregate_values(query)
#         assert aggregate_values == [{"min___field1":0,"max___max___field2":17519,"width___field1":6}]