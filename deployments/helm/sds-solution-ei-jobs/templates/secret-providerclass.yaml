{{- if .Values.secretVault.enabled -}}
apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: sds-sol-ei-secrets
  labels:
    component: secret-provider-class
    release: {{ .Release.Name }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version  | replace "+" "_" }}"
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  provider: {{ .Values.global.CLOUD_SERVICE_PROVIDER }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  parameters: 
  {{- else if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure"}}
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true"  
    userAssignedIdentityID:  "{{ .Values.global.AZURE_MANAGED_IDENTITY_ID }}"
    keyvaultName: "{{ .Values.global.AZURE_KEYVAULT_NAME }}"
    cloudName: ""
    tenantId: "{{ .Values.global.MICROSOFT_K8S_TENANT_ID }}"
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
    objects: |
      - objectName: {{ tpl .Values.secretVault.aws.secretVaultName . }}
        objectType: "secretsmanager"
        jmesPath:
    {{- range $i, $config := .Values.secretVault.aws.jmesPath }}
          - path: {{ $config.path }}
            objectAlias: {{ $config.objectAlias }}
    {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
    objects: |
     array:
      {{ .Values.secretVault.azure.array | nindent 6 }}
  {{- end }} 
  secretObjects:                
    - secretName: ei-analytics-jar-copy-secret-vault
      type: Opaque
      data:
    {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
    {{- range $i, $config := .Values.secretVault.aws.secretObjectsData }}
        - objectName: {{ $config.objectName }}
          key: {{ $config.key }}
    {{- end }}
    {{- else if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure"}}
    {{- range $i, $config := .Values.secretVault.azure.secretObjectsData }}
        - objectName: {{ $config.objectName }}
          key: {{ $config.key }}
    {{- end }}
    {{- end }}
{{- end }}