{{- if .Values.jobOverrides.sds_ei_profiling_copy_job.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  {{- with .Values.jobAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  name: sds-ei-profiling-copy-job
  labels:
    job: {{ .Values.labelJob }}
    {{- include "k8jobs.labels" . | nindent 4 }}
spec:
  backoffLimit: {{ default .Values.backoffLimit .Values.jobOverrides.sds_ei_profiling_copy_job.backoffLimit }}
  activeDeadlineSeconds: {{ default .Values.activeDeadlineSeconds .Values.jobOverrides.sds_ei_profiling_copy_job.activeDeadlineSeconds }}
  ttlSecondsAfterFinished: {{ default .Values.ttlSecondsAfterFinished .Values.jobOverrides.sds_ei_profiling_copy_job.ttlSecondsAfterFinished }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "k8jobs.selectorLabels" . | nindent 8 }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "k8jobs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      initContainers:
      - name: artifact-check
        image: "{{ .Values.initImage.repository }}:{{ .Values.initImage.tag | default .Chart.AppVersion }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{- with index .Values "jobOverrides" "sds_ei_profiling_copy_job" }}
        {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
        command: {{ tpl (toYaml .init_job_command.aws) $ | nindent 12 }}
        {{- end }}
        {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
        command: {{ tpl (toYaml .init_job_command.azure) $ | nindent 12 }}
        {{- end }}
        {{- end }}
        {{- if .Values.args }}
        args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
        {{- end }}
        env:
        {{- include "custom_ei_profiling_job_environment" . | indent 10 }}                          
            - name: ARTIFACT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: JF_USER
            - name: ARTIFACT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: JF_PASSWORD
            - name: ARTIFACT_URL
              value: https://prevalentai.jfrog.io/
            {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure"}}
            - name: AZURE_STORAGE_ACCOUNT_KEY
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: AZURE_STORAGE_ACCOUNT_KEY
            - name: AZURE_STORAGE_ACCOUNT_NAME
              value: {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }}
          {{- end }}
        volumeMounts:
        - name: empty-volume
          mountPath: /tmp/result
        - mountPath: /mnt/secrets
          name: jfrog-eianalytics-secret-vault
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "sds_ei_profiling_copy_job" }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
          command: {{ tpl (toYaml .job_command.aws) $ | nindent 12 }}
          {{- end }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
          command: {{ tpl (toYaml .job_command.azure) $ | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.args }}
          args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
          {{- end }}
          envFrom: []
       
          env:
          {{- include "custom_ei_profiling_job_environment" . | indent 10 }}                          
            - name: ARTIFACT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: JF_USER
            - name: ARTIFACT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: JF_PASSWORD
            - name: ARTIFACT_URL
              value: https://prevalentai.jfrog.io/
            {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure"}}
            - name: AZURE_STORAGE_ACCOUNT_KEY
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: AZURE_STORAGE_ACCOUNT_KEY
            - name: AZURE_STORAGE_ACCOUNT_NAME
              value: {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }}
            {{- end }}

      
          resources:
            {{- toYaml (index .Values "jobOverrides" "sds_ei_profiling_copy_job" "resources") | nindent 12 }}
          volumeMounts:
            - mountPath: /mnt/secrets
              name: jfrog-eianalytics-secret-vault
            {{- include "extra_volumes_mounts" . | indent 10 }}
            - mountPath: /opt/bundle-config
              name: dependencies
            - mountPath: /tmp/result
              name: empty-volume
        - name: tag-artifact
          image: "{{ .Values.initImage.repository }}:{{ .Values.initImage.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with index .Values "jobOverrides" "sds_ei_profiling_copy_job" }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
          command: {{ tpl (toYaml .post_job_command.aws) $ | nindent 12 }}
          {{- end }}
          {{- if eq $.Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
          command: {{ tpl (toYaml .post_job_command.azure) $ | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.args }}
          args: {{ tpl (toYaml .Values.args) . | nindent 12 }}
          {{- end }}
          env:
          {{- include "custom_ei_profiling_job_environment" . | indent 10 }}                          
            - name: ARTIFACT_USERNAME
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: JF_USER
            - name: ARTIFACT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: JF_PASSWORD
            - name: ARTIFACT_URL
              value: https://prevalentai.jfrog.io/
            {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure"}}
            - name: AZURE_STORAGE_ACCOUNT_KEY
              valueFrom:
                secretKeyRef:
                  name: ei-analytics-jar-copy-secret-vault
                  key: AZURE_STORAGE_ACCOUNT_KEY
            - name: AZURE_STORAGE_ACCOUNT_NAME
              value: {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }}
            {{- end }}
          volumeMounts:
            - mountPath: /mnt/secrets
              name: jfrog-eianalytics-secret-vault
            {{- include "extra_volumes_mounts" . | indent 10 }}
            - mountPath: /opt/bundle-config
              name: dependencies
            - mountPath: /tmp/result
              name: empty-volume

      volumes: 
        - name: jfrog-eianalytics-secret-vault
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: sds-sol-ei-secrets
        - name: empty-volume
          emptyDir:
            sizeLimit: 20Mi

        - name: dependencies
          configMap:
            name: dependencies-configmap-{{ .Release.Name }}

            {{- include "extra_volumes" . | indent 10 }}
{{- end }}
