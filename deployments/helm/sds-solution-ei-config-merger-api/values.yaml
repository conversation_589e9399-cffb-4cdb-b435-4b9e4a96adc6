# Default values for eiConfigMergerApi.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: prevalentai/sds-solution-ei-configs
  tag: ""
  pullPolicy: IfNotPresent



imagePullSecret: 
  - name: docker-secret
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 5000

env:
- name: CONFIG_MANAGER_ENDPOINT
  value: http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080/sds_mgmnt/config-manager/api/v1/config-item
- name: STATUS_MANAGER_ENDPOINT
  value: http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080/sds_mgmnt/config-manager/api/v1/status-manager
- name: EI_CONFIG_MERGE_ENDPOINT
  value: http://ei-config-merger-api.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:5000/ei/config-merge/
- name: <PERSON>EYCLOAK_URL 
  value: https://{{  .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token
- name: PYICEBERG_CATALOG__DEFAULT__URI
  value: 'thrift://hivemetastore.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:9083'
secret:
  - envName: "CLIENT_ID"
    secretName: "ei-config-merger-api-secret-vault"
    secretKey: "CLIENT_ID"
  - envName: "CLIENT_SECRET"
    secretName: "ei-config-merger-api-secret-vault"
    secretKey: "CLIENT_SECRET"


serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

resources:
  limits:
    cpu: "2"
    memory: 4Gi
  requests:
    cpu: "1"
    memory: 2Gi

nodeSelector: {}

tolerations: []

affinity: {}

volumes: |
  - name: ei-config-merger-api-secret-vault
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: ei-config-merger-api-secrets

volumeMounts: |
  - name: ei-config-merger-api-secret-vault
    mountPath: "/mnt/secrets"
    readOnly: true

secretVault:
  enabled: true
  aws:
    managedIdentity:
    azureTenantID:
    secretVaultName: sds3_secrets_{{ .Values.global.ENVIRONMENT }}
    jmesPath:
      - path: clientId
        objectAlias: CLIENT_ID
      - path: clientSecret
        objectAlias: CLIENT_SECRET

    secretObjectsData:
      - objectName: CLIENT_ID
        key: CLIENT_ID
      - objectName: CLIENT_SECRET
        key: CLIENT_SECRET
  azure:
    managedIdentity: "{{ .Values.AZURE_MANAGED_IDENTITY_ID}}"
    azureTenantID: "{{ .Values.MICROSOFT_TENANT_ID}}"
    secretVaultName: "{{ .Values.AZURE_KEYVAULT_NAME }}"
    array: |
      - |
        objectName: clientId
        objectType: secret
        objectVersion: ""
      - |
        objectName: clientSecret
        objectType: secret
        objectVersion: ""
    secretObjectsData:
      - key: CLIENT_ID
        objectName: clientId
      - key: CLIENT_SECRET
        objectName: clientSecret

extraEnv: []       