{{/*
Expand the name of the chart.
*/}}
{{- define "k8jobs.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "k8jobs.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "k8jobs.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "k8jobs.labels" -}}
helm.sh/chart: {{ include "k8jobs.chart" . }}
{{ include "k8jobs.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "k8jobs.selectorLabels" -}}
app.kubernetes.io/name: {{ include "k8jobs.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "k8jobs.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "k8jobs.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{- define "job_environment_from" }}
  {{- $Global := . }}
  {{- with .Values.jobEnvFrom }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "job_environment" }}
  {{- $Global := . }}
  {{- with .Values.jobEnv }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes_mounts" }}
  {{- $Global := . }}
  {{- with .Values.volumeMounts }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "init_job_environment_from" }}
  {{- $Global := . }}
  {{- with .Values.initContainers.initJobEnvFrom }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "init_job_environment" }}
  {{- $Global := . }}
  {{- with .Values.initContainers.initJobEnv }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "init_extra_volumes_mounts" }}
  {{- $Global := . }}
  {{- with .Values.initContainers.initVolumeMounts }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes" }}
  {{- $Global := . }}
  {{- with .Values.volumes }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "k8jobs.secretname" -}}
{{- if and (not .Values.secrets.secretsManager.enabled) (not .Values.secrets.AzureKeyVault.enabled) }}
{{- printf "%s-%s" (include "k8jobs.fullname" .) "sbom" }}
{{- end }}
{{- if and (not .Values.secrets.secretsManager.enabled) ( .Values.secrets.AzureKeyVault.enabled) }}
{{- printf "%s-%s" (include "k8jobs.fullname" .) "keyvault" }}
{{- end }}
{{- if and ( .Values.secrets.secretsManager.enabled) (not .Values.secrets.AzureKeyVault.enabled) }}
{{- printf "%s-%s" (include "k8jobs.fullname" .) "secret-manager" }}
{{- end }}
{{- end }}

{{- define "secret_manager_jmespath" }}
  {{- $Global := . }}
  {{- with .Values.secrets.secretsManager.objectsJmesPath }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "secret_manager_data" }}
  {{- $Global := . }}
  {{- with .Values.secrets.secretsManager.secretObjectsData }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "azre_keyvault_jmespath" }}
  {{- $Global := . }}
  {{- with .Values.secrets.AzureKeyVault.objectsArray }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "azre_keyvault_data" }}
  {{- $Global := . }}
  {{- with .Values.secrets.AzureKeyVault.secretObjectsData }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}