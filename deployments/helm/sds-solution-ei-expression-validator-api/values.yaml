# Default values for expressionValidatorApi.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:  
  repository: prevalentai/spark
  tag: "4-0-0-3.5.1-2.13-iceberg-v1-6-bookworm-12.5-20240513-slim"
  pullPolicy: IfNotPresent

initContainer:
  kubectl:
    image:
      repository: prevalentai/devops-utils
      tag: "kubectl1.27.0-awscliv2-azcpv10-azcli-bookworm-12.5-20240513-slim"
  command:
    aws: ["bash", "-c", "/tmp/aws/bin/aws s3 cp s3://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest/sds-ei-validator.jar /shared/jar/"]
    azure: ["bash", "-c", "az login --identity --username {{ .Values.global.AZURE_MANAGED_IDENTITY_ID}} && az storage blob download --container-name {{ .Values.global.BLOB_APPS_CONTAINER_NAME }} --name /{{ .Values.global.DEPLOY_NAMESPACE}}/sds/data-analytics/lib/latest/sds-ei-validator.jar --file /shared/jar/sds-ei-validator.jar --account-name {{ .Values.global.AZURE_STORAGE_ACCOUNT_NAME }} --auth-mode login"]



imagePullSecret:
  - name: docker-secret
nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 9000

env:
  common:
    - name: AUTH_INTROSPECT_URL
      value: "https://{{  .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token/introspect"
    - name: SDS_DOMAIN_WITH_PORT
      value: "{{  .Values.global.SDS3_DOMAIN_NAME  }}:443"
    - name: LINEAGE_API_URL
      value: "https://{{ .Values.global.SDS3_DOMAIN_NAME }}/sds_mgmnt/config-manager/api/v1/lineage/?deployment_config=true"
    - name: CONFIG_API_URL
      value: "https://{{ .Values.global.SDS3_DOMAIN_NAME }}/sds_mgmnt/config-manager/api/v1/config-item"
    - name: DOMAIN_NAME
      value: "https://{{ .Values.global.SDS3_DOMAIN_NAME }}"
    - name: REDIS_DATABASE
      value: "3"
    - name: VALIDATOR_API_PLAY_MAXMEMORYBUFFER
      value: "5000kilobytes"
    - name: VALIDATOR_API_PLAY_MAXMEMORYBUFFER
      value: "5000kilobytes"
    - name: VALIDATOR_SCHEMA
      value: "ei_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: VALIDATOR_PUBLISH_SCHEMA
      value: "ei_pub_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: VALIDATOR_EI_ENRICH_SCHEMA
      value: "ei_enrich_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: VALIDATOR_EI_FRAGMENT_SCHEMA
      value: "ei_fragment_validator_{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: CONFIG_META_ENDPOINT
      value: "list-configs-meta/?solution_edition=new"
    - name: OUTPUT_TABLE_LIMIT
      value: "1000"
    - name: UPGRADE_STATUS_API_ENDPOINT
      value: "sds_mgmnt/upgrade-manager/check-upgrade-status/ei"
    - name: EXECUTION_MODE
      value: PARALLEL
    - name: SRDM_INTERVAL_DAYS
      value: "10"
    - name: NAMESPACE
      value: "{{ .Values.global.DEPLOY_NAMESPACE }}"
    - name: DISABLE_QUERY_VALIDATION
      value: "false"
    - name: QUERY_CACHE_ENABLED
      value: "false"
    
  aws:
    - name: REDIS_HOST
      value: "{{ .Values.global.AWS_CACHE_REDIS_ENDPOINT }}"
    - name: REDIS_PORT
      value: "6379"
    - name: REDIS_AUTH_ENABLED
      value: "false"
  azure:
    - name: REDIS_HOST
      value: "{{ .Values.global.AZURE_CACHE_REDIS_ENDPOINT }}"
    - name: REDIS_PORT
      value: "6380"
    - name: REDIS_AUTH_ENABLED
      value: "true"

secret:
- envName: "AUTH_CLIENT_ID"
  secretName: "expression-validator-api-secret-vault"
  secretKey: "CLIENT_ID"
- envName: "AUTH_CLIENT_SECRET"
  secretName: "expression-validator-api-secret-vault"
  secretKey: "CLIENT_SECRET"
- envName: "OIDC_CLIENT_ID"
  secretName: "expression-validator-api-secret-vault"
  secretKey: "CLIENT_ID"
- envName: "OIDC_CLIENT_SECRET"
  secretName: "expression-validator-api-secret-vault"
  secretKey: "CLIENT_SECRET"
- envName: "REDIS_PASSWORD"
  secretName: "mgmtapi-secret-vault"
  secretKey: "redis-password"

configMap:
  enabled: true
  common:
    spark.master: "k8s://https://kubernetes.default.svc"
    spark.kubernetes.namespace: "{{ .Values.global.DEPLOY_NAMESPACE }}"
    spark.dynamicAllocation.enabled: "true"
    spark.dynamicAllocation.maxExecutors: "10"
    spark.kubernetes.container.image.pullPolicy: Always
    spark.kubernetes.container.image.pullSecrets: docker-secret
    spark.kubernetes.authenticate.driver.serviceAccountName: spark
    spark.kubernetes.authenticate.executor.serviceAccountName: spark
    spark.kubernetes.authenticate.serviceAccountName: spark
    spark.network.timeout: 10000001s
    spark.driver.port: "22321"
    spark.blockManager.port: "22322"
    spark.driver.host: "expression-validator-api-sts-{{ .Values.global.ENVIRONMENT }}-0"
    spark.driver.bindAddress: "0.0.0.0"
    spark.app.name: SDS EI validator Session
    spark.driver.memory: "8g"
    spark.kubernetes.container.image: prevalentai/spark:3.5.1-2.13-corretto17-bullseye11.8-sds-iceberg-v1-6-test
    spark.kubernetes.driver.container.image: prevalentai/spark:3.5.1-2.13-corretto17-bullseye11.8-sds-iceberg-v1-6-test
    spark.kubernetes.executor.container.image: prevalentai/spark:3.5.1-2.13-corretto17-bullseye11.8-sds-iceberg-v1-6-test
    spark.serializer: org.apache.spark.serializer.KryoSerializer
    spark.sql.extensions: org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
    spark.sql.catalog.iceberg_catalog: org.apache.iceberg.spark.SparkCatalog
    spark.sql.catalog.iceberg_catalog.type: hive
    spark.sql.catalog.iceberg_catalog.uri: "thrift://hivemetastore.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:9083"
    spark.sql.defaultCatalog: iceberg_catalog
    spark.local.dir: /tmp/local-dir-1
    spark.sds.restapi.oidcAuthEnabled: "true"
    spark.sds.restapi.oidcUrl: https://{{  .Values.global.KEYCLOAK_DOMAIN_NAME | default .Values.global.SDS3_DOMAIN_NAME }}/realms/{{ .Values.global.DEPLOY_NAMESPACE }}/protocol/openid-connect/token
    spark.sds.config.artifactory.uri: "http://mgmtapis.{{ .Values.global.DEPLOY_NAMESPACE }}.svc.cluster.local:8080"
    spark.sds.config.delta.base-path: "/sds_mgmnt/config-manager/api/v1/config-delta"
    spark.sds.config.item.base-path: "/sds_mgmnt/config-manager/api/v1/config-item"
    spark.sql.caseSensitive: "true"
    spark.sds.iceberg.table.write.parquet.compression-codec: "snappy"
    spark.sds.iceberg.table.write.spark.accept-any-schema: "true"
    spark.sql.iceberg.merge-schema: "true"
    spark.sql.iceberg.set-all-nullable-field: "true"
    spark.sql.iceberg.check-ordering: "false"
    spark.sql.shuffle.partitions: "100"
    spark.executor.cores: "2"
    spark.executor.instances: "2"
    spark.executor.memory: "10g"
    spark.eventLog.enabled: "true"
  aws:
    spark.sql.catalog.iceberg_catalog.warehouse: s3a://{{ .Values.global.S3_DATALAKE_BUCKET_NAME }}/iceberg/
    spark.checkpoint.dir: s3a://{{ .Values.global.S3_DATALAKE_BUCKET_NAME }}/ei-checkpoint/
    spark.hadoop.fs.s3a.aws.credentials.provider: com.amazonaws.auth.WebIdentityTokenCredentialsProvider
    spark.kubernetes.executor.annotation.eks.amazonaws.com/role-arn: "{{ .Values.global.SERVICE_ACCOUNT_ANNOTATION_ROLE_ARN }}"
    spark.kubernetes.authenticate.submission.caCertFile: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    spark.kubernetes.authenticate.submission.oauthTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    spark.hadoop.fs.s3a.impl: org.apache.hadoop.fs.s3a.S3AFileSystem
    spark.hadoop.fs.s3a.endpoint: s3.{{ .Values.global.AWS_REGION }}.amazonaws.com
    spark.jars: s3a://{{ .Values.global.S3_APPS_BUCKET_NAME }}/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar
    spark.eventLog.dir: "s3a://{{ .Values.global.S3_LOGS_BUCKET_NAME }}/spark-history/"
  azure:
    spark.hadoop.fs.azure.account.auth.type: OAuth
    spark.hadoop.fs.azure.account.oauth.provider.type: org.apache.hadoop.fs.azurebfs.oauth2.MsiTokenProvider
    spark.hadoop.fs.azure.account.oauth2.msi.tenant: "{{ .Values.global.MICROSOFT_K8S_TENANT_ID  }}"
    spark.hadoop.fs.azure.account.oauth2.client.id: "{{ .Values.global.AZURE_MANAGED_IDENTITY_ID  }}"
    spark.sql.catalog.iceberg_catalog.warehouse: "abfs://{{ .Values.global.BLOB_DATALAKE_CONTAINER_NAME  }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/iceberg/"
    spark.checkpoint.dir: "abfs://{{ .Values.global.BLOB_DATALAKE_CONTAINER_NAME  }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/ei-checkpoint/"
    spark.jars: abfs://{{ .Values.global.BLOB_APPS_CONTAINER_NAME }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/{{ .Values.global.DEPLOY_NAMESPACE }}/sds/data-analytics/lib/latest/sds-ei-analytics_2.13.jar
    spark.eventLog.dir: "abfs://{{ .Values.global.BLOB_LOGS_CONTAINER_NAME }}@{{.Values.global.AZURE_STORAGE_ACCOUNT_NAME}}.dfs.core.windows.net/spark-history/"

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: spark

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local



resources:
  limits:
    cpu: 3500m
    memory: 14000Mi
  requests:
    cpu: 3000m
    memory: 12000Mi

autoscaling:
  enabled: false
#   minReplicas: 1
#   maxReplicas: 100
#   targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: 
    jobtype: "medium"


tolerations: 
    - key: "job-resource"
      operator: "Equal"
      value: "medium"
      effect: "NoSchedule"
    - key: "kubernetes.azure.com/scalesetpriority"
      operator: "Equal"
      value: "spot"
      effect: "NoSchedule"

affinity: {}

volumeMounts: |
  - name: expression-validator-api-secret-vault
    mountPath: "/mnt/secrets"
    readOnly: true

volumes: |
  - name: expression-validator-api-secret-vault
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: expression-validator-api-secrets

secretVault:
  enabled: true
  aws:
    managedIdentity:
    azureTenantID:
    secretVaultName: sds3_secrets_{{ .Values.global.ENVIRONMENT }}
    jmesPath:
      - path: clientId
        objectAlias: CLIENT_ID
      - path: clientSecret
        objectAlias: CLIENT_SECRET
      - path: redisPassword
        objectAlias: redisPassword
    secretObjectsData:
      - objectName: CLIENT_ID
        key: CLIENT_ID
      - objectName: CLIENT_SECRET
        key: CLIENT_SECRET
      - objectName: redisPassword
        key: redis-password
  azure:
    managedIdentity: "{{ .Values.AZURE_MANAGED_IDENTITY_ID}}"
    azureTenantID: "{{ .Values.MICROSOFT_TENANT_ID}}"
    secretVaultName: "{{ .Values.AZURE_KEYVAULT_NAME }}"
    array: |
      - |
        objectName: clientId
        objectType: secret
        objectVersion: ""
      - |
        objectName: clientSecret
        objectType: secret
        objectVersion: ""
      - |
        objectName: redisPassword
        objectType: secret
        objectVersion: ""
    secretObjectsData:
      - key: CLIENT_ID
        objectName: clientId
      - key: CLIENT_SECRET
        objectName: clientSecret
      - objectName: redisPassword
        key: redis-password


extraEnv: []

SIGNED_CERT: "{{ .Values.global.SELF_SIGNED_CERT }}"

livenessProbe:
  httpGet:
    path: /healthcheck  # Correct endpoint
    port: 9000
  initialDelaySeconds: 5
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /healthcheck  # Correct endpoint
    port: 9000
  initialDelaySeconds: 5
  periodSeconds: 10
