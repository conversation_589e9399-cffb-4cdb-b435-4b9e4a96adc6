{{/*
Expand the name of the chart.
*/}}
{{- define "expressionValidatorApi.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "expressionValidatorApi.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "expressionValidatorApi.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "expressionValidatorApi.labels" -}}
helm.sh/chart: {{ include "expressionValidatorApi.chart" . }}
{{ include "expressionValidatorApi.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "expressionValidatorApi.selectorLabels" -}}
app.kubernetes.io/name: {{ include "expressionValidatorApi.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "expressionValidatorApi.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "expressionValidatorApi.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}


{{/* User defined Management API environment variables */}}
{{- define "custom_expression_validator_api_environment" }}
  {{- $combinedEnv := dict -}}
  {{- range .Values.env.common }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "aws" }}
  {{- range .Values.env.aws }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- if eq .Values.global.CLOUD_SERVICE_PROVIDER "azure" }}
  {{- range .Values.env.azure }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- end }}
  {{- range .Values.extraEnv }}
  {{- $_ := set $combinedEnv .name .value -}}
  {{- end }}
  {{- range $key, $value := $combinedEnv }}
  - name: {{ $key }}
    value: {{ tpl (quote $value) $ }}
  {{- end }}
  {{- range $i, $config := .Values.secret }}
  - name: {{ $config.envName }}
    valueFrom:
      secretKeyRef:
        name: {{ $config.secretName }}
        key: {{ default "value" $config.secretKey }}
  {{- end }}
{{- end }}

{{- define "extra_volumes_mounts" }}
  {{- $Global := . }}
  {{- with .Values.volumeMounts }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}

{{- define "extra_volumes" }}
  {{- $Global := . }}
  {{- with .Values.volumes }}
  {{- tpl . $Global | nindent 2 }}
  {{- end }}
{{- end }}


{{- define "expressionValidatorApi.getImageTag" -}}
{{- if index .Values "bundle-version" -}}
{{- $version := index .Values "bundle-version" -}}
{{- $newVersion := upper (regexReplaceAll "\\." $version "-") -}}
{{- $newVersion -}}
{{- else -}}
{{- .Values.image.tag -}}
{{- end -}}
{{- end -}}