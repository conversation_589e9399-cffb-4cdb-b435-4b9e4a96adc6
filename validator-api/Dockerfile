ARG JAVA_BASE_IMAGE="prevalentai/spark:3.5.1-2.13-corretto17-bullseye11.8-sds-iceberg-v1-6-test"

FROM ${JAVA_BASE_IMAGE}

RUN mkdir -p /opt/spark/sds-ei-validator/

COPY ./target/scala-2.13/validator_2.13-*.jar /opt/spark/sds-ei-validator/validator_2.13.jar
COPY ../configs/data_model_delta/ /opt/spark/sds-ei-validator/data_model_delta
USER root

WORKDIR /opt/spark/sds-ei-validator/

RUN chown -R spark:root /opt/spark/sds-ei-validator/

RUN find /opt/spark/jars/ -type f -name '*log4j*.jar' -exec rm -f '{}' \; \
    && find /opt/spark/jars/ -type f -name '*guava*.jar' -exec rm -f '{}' \;


USER spark
