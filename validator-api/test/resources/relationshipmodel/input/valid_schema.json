{"name": "Person Has Identity", "inverseRelationshipName": "Identity Associated With Person", "intraSourcePath": "ei.sds_ei_intra_source_resolver", "interSourcePath": "ei.sds_ei_inter_source_resolver", "inputSourceInfo": [{"sdmPath": "srdm.microsoft_azure__ad_user", "origin": "MS Azure AD", "sourceLoaderConfPath": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__person__ms_azure_ad_users__aad_id"], "targetLoaderConfPath": ["<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__ms_azure_ad_users__user_principal_name", "<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__ms_azure_ad_users__sam_account_name", "<%CONFIG_ARTIFACTORY_URI%><%EI_LOADER_CONFIG_BASE_PATH%>sds_ei__identity__ms_azure_ad_users__aad_id"]}], "variablesBasedRelationBuilderStrategySpec": {"blockVariables": ["source_p_id", "target_p_id"]}, "output": {"outputTable": "<%EI_SCHEMA_NAME%>.sds_ei__rel__ms_azure_ad_users__person_has_identity", "prevMiniSDM": "<%EI_SCHEMA_NAME%>.sds_ei__rel_mini_sdm__ms_azure_ad_users__person_has_identity"}}