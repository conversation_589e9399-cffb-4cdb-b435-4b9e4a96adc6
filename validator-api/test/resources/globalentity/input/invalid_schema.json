{
  "entityClass": "Host",
  "commonProperties": [
    {
      "colName": "display_label",
      "colExpr": "UPPER(coalesce(cloud_instance_name,fqdn,dns_name,host_name,aad_device_id,cloud_resource_name,primary_key))",
      "fieldsSpec": {
        "isInventoryDerived": true,
        "postDisambiguationUpdate": true
      }
    },
    {
      "colName": "activity_status",
      "colExpr": "CASE WHEN last_active_date IS NULL THEN NULL WHEN cloud_inactivity_period IS NOT NULL and datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > cloud_inactivity_period  THEN 'Inactive' WHEN datediff(date(to_timestamp(updated_at/1000)), date(to_timestamp(last_active_date/1000))) > inactivity_period THEN 'Inactive' ELSE 'Active' END",
      "fieldsSpec": {
        "isInventoryDerived": true,
        "postDisambiguationUpdate": true
      }
    },
    {
      "colName": "inactivity_period",
      "colExpr": "180",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "business_unit",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "department",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "description",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "location_city",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "location_country",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "type",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "first_seen_date",
      "colExpr": "LEAST(last_active_date,first_found_date)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "last_active_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    }
  ],
  "entitySpecificProperties": [
    {
      "colName": "os_family",
      "colExpr": "CASE WHEN os IS NULL OR regexp_like(os,'(?i)unknown|^-$') THEN NULL WHEN LOWER(os) LIKE '%windows%' THEN 'Windows' WHEN LOWER(os) LIKE '%macos%' or LOWER(os) LIKE '%mac%' THEN 'macOS' WHEN lower(os) RLIKE '.*linux.*|.*ubuntu.*|.*centos.*|.*fedora.*|.*webos.*|.*chromeos.*|.*debian.*|.*redhat.*|.*tizen.*|.*panos.*|openwrt|.*embeddedos.*' THEN 'Linux' WHEN lower(os) RLIKE '.*cisco.*|.*fortinet.*|.*juniper.*|.*sonicos.*' THEN 'Network OS' WHEN lower(os) RLIKE '.*ios.*|.*ipados.*|.*ipad.*|.*iphone.*' THEN 'iOS' WHEN LOWER(TRIM(os)) LIKE '%android%' THEN 'Android' ELSE 'Other' END",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    }
    {
      "colName": "fqdn",
      "colExpr": "LOWER(CASE WHEN regexp_like(dns_name,'^(?!:\\\\/\\\\/)(?=.{1,255}$)((.{1,63}[.]){1,127}(?![0-9]*$)[a-z0-9-A-Z]+[.]?)$') THEN dns_name END)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "domain",
      "colExpr": "regexp_extract(fqdn,'^(?:[^.]++[.])((local|corp|[^.]++[^\\\\r\\\\n]++))$')",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "host_name",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "ip",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "dns_name",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "netbios",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "accessibility",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "mdm_mac_address",
      "colExpr": "from_json(null, 'ARRAY<STRING>')",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "os",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "os_version",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "os_architecture",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "os_build",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_provider",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_account_id",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_resource_id",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_resource_type",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_operational_state",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_instance_id",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_native_type",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "provisioning_state",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_region",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "cloud_zone_availability",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "hardware_manufacturer",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "hardware_model",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "hardware_serial_number",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "hardware_imei",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "login_last_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "login_last_user",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "host_last_reboot_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "mdm_product",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "mdm_status",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "mdm_compliance_state",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "mdm_enrolled_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "mdm_last_sync_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "edr_onboarding_status",
      "colExpr": "cast(null as boolean)",
      "fieldsSpec": {
        "persistNonNullValue": false
      }
    },
    {
      "colName": "edr_product",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "persistNonNullValue": false
      }
    },
    {
      "colName": "edr_last_scan_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "av_status",
      "colExpr": "cast(null as boolean)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "av_last_scan_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "av_signature_update_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "av_block_malicious_code_status",
      "colExpr": "cast(null as boolean)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "fw_status",
      "colExpr": "cast(null as boolean)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "vm_product",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "persistNonNullValue": false
      }
    },
    {
      "colName": "vm_onboarding_status",
      "colExpr": "cast(null as boolean)",
      "fieldsSpec": {
        "persistNonNullValue": false
      }
    },
    {
      "colName": "vm_tracking_method",
      "colExpr": "cast(null as string)",
      "fieldsSpec": {
        "persistNonNullValue": false
      }
    },
    {
      "colName": "vm_last_scan_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    },
    {
      "colName": "vulnerability_last_observed_date",
      "colExpr": "cast(null as bigint)",
      "fieldsSpec": {
        "isInventoryDerived": true
      }
    }
  ],
  "fieldLevelSpec": [
    {
      "colName": "ip",
      "fieldsSpec": {
        "persistNonNullValue": false
      }
    }
  ],
  "lastUpdateFields": [
    "type","first_seen_date","business_unit","department","description","location_country","host_name","fqdn","ip","netbios","accessibility","os","cloud_provider","cloud_resource_id","hardware_model","hardware_serial_number","login_last_date","login_last_user","edr_onboarding_status","vm_onboarding_status","mdm_product","mdm_status","mdm_last_sync_date","edr_last_scan_date","vm_last_scan_date","ad_distinguished_name","ad_last_sync_date","ad_operational_status","aad_device_id","aad_operational_status","cloud_operational_state"
  ]
}