[{"changeType": "<PERSON><PERSON>", "name": "cloud_region", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_region", "New Name": "region"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_native_type", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_native_type", "New Name": "native_type"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_operational_state", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_operational_state", "New Name": "operational_state"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_resource_id", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_resource_id", "New Name": "resource_id"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_account_id", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_account_id", "New Name": "account_id"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_instance_image_id", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_instance_image_id", "New Name": "image"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_instance_name", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_instance_name", "New Name": "instance_name"}}, {"changeType": "<PERSON><PERSON>", "name": "cloud_zone_availability", "reason": "Attribute name was changed to make it uniform across all entities", "category": "Attribute", "message": {"Old Name": "cloud_zone_availability", "New Name": "zone_availability"}}, {"changeType": "Attribute Definition", "name": "type", "reason": "Type field has been updated to use common expression logic in the global entity. This field will no longer be configured at the loader level unless an override is necessary.", "category": "Attribute"}, {"changeType": "<PERSON><PERSON>", "name": "active_operational_state", "reason": "Field name was changed as it indicates the date when operational state was last active", "category": "Attribute", "message": {"Old Name": "active_operational_state", "New Name": "active_operational_date"}}]