include "secure"
include "idp.conf"
include "cache"


play.filters.hosts {
  allowed = ["."]
}

play.http.errorHandler = common.CustomErrorHandler
play.http.secret.key="1qpUy<YxcM8ScZD:>M`d1NWP59CaNhnS2wwEZJmK0S/Q8OJPbKolyQ[JO5IW6ahC"
play.http.secret.key=${?API_SECRET_KEY}

baseUrl="http://localhost:9000"
validatorSchema=${?VALIDATOR_SCHEMA}
publisherSchema=${?VALIDATOR_PUBLISH_SCHEMA}
eiEnrichSchema=${?VALIDATOR_EI_ENRICH_SCHEMA}
eiFragmentSchema=${?VALIDATOR_EI_FRAGMENT_SCHEMA}
namespace=${?NAMESPACE}
config.delta.basePath=/opt/sds-ei-validator/data_model_delta
disableQueryValidation=${?DISABLE_QUERY_VALIDATION}

play.modules.enabled += "SecurityModule"
play.modules.enabled += play.api.cache.redis.RedisCacheModule
play.http.filters = "common.filters.Filters"

# Overriding maxmemory for larger payload
play.http.parser.maxMemoryBuffer=500kilobytes
play.http.parser.maxMemoryBuffer=${?VALIDATOR_API_PLAY_MAXMEMORYBUFFER}
parsers.text.maxLength=2MB
parsers.text.maxLength=${?VALIDATOR_API_TEXT_MAXLENGTH}

pac4j.security {
  rules = [

  {"^((?!healthcheck).)*$" = {
      clients = "HeaderClient"
    }}
  ]
}

play.server.http.idleTimeout=20m
play.server.https.idleTimeout=20m
play.ws.timeout.request=20m
play.ws.timeout.idle=20m

play.server.pidfile.path=/dev/null
query_cache_enabled=${QUERY_CACHE_ENABLED}
