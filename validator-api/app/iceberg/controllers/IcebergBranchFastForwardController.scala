package iceberg.controllers

import scala.concurrent.{ExecutionContext, Future}
import javax.inject.Inject
import common.InvalidJsonError
import org.json4s.DefaultFormats
import play.api.Logger
import play.api.libs.json.{JsValue, Json}
import play.api.mvc._
import iceberg.utils.IcebergBranchFastForwardUtils.{fastForwardIcebergBranch, listTables}
import v2.utils.LiveContext

case class IcebergBranchFastForwardConfig(schema_list: Seq[String], branch_name: String, sparkCatalog: String="iceberg_catalog")
class IcebergBranchFastForwardController @Inject()(cc: ControllerComponents)(implicit ec: ExecutionContext) extends AbstractController(cc) {

  private val LOGGER = Logger(getClass)


  def execute: Action[JsValue] = Action(parse.json) { request =>
    try {
      implicit val formats = DefaultFormats
      try {
        LiveContext.buildSessionFromEnvSpec()
      } catch {
        case ex: Exception =>
          LOGGER.error(ex.getMessage)
          Future.successful(BadRequest("No Active Spark session"))
      }
      val config: IcebergBranchFastForwardConfig = try {
        val body = request.body.toString()
        org.json4s.jackson.JsonMethods.parse(body).extract[IcebergBranchFastForwardConfig]
      } catch {
        case ex: Exception => throw InvalidJsonError(ex.getMessage)

      }
      if (config.schema_list.isEmpty){
        throw InvalidJsonError("schema_list is not provided or is empty")
      }
      val spark = LiveContext.spark
      val tables = config.schema_list.flatMap(schema => listTables(schema, config.sparkCatalog, spark))
      fastForwardIcebergBranch(tables, config.branch_name, config.sparkCatalog, spark)
      val schema_list_string = config.schema_list.mkString(",")
      Ok(Json.obj("status" -> "Completed", "message" -> s"Fast-forwarding completed for tables in schema ${schema_list_string}"))
    } catch {
      case ex: InvalidJsonError => LOGGER.error(ex.getMessage)
        BadRequest(Json.obj("status" -> "Error", "message"-> ex.getMessage))
      case ex: Exception => LOGGER.error(ex.getMessage)
        Ok(Json.obj("status" -> "Error", "message"-> ex.getMessage))

    }
    }
}