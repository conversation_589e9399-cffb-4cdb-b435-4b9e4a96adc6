package v2.common

import com.typesafe.config.ConfigFactory
import common.InvalidSQLError
import net.sf.jsqlparser.expression.operators.conditional.AndExpression
import net.sf.jsqlparser.expression.{Alias, Expression, Parenthesis}
import net.sf.jsqlparser.parser.CCJSqlParserUtil
import net.sf.jsqlparser.schema.Table
import net.sf.jsqlparser.statement.select._
import net.sf.jsqlparser.util.SelectUtils
import play.api.Logger
import v2.models.{DataQuery, Query=>QueryV3, FilterObject}
import scala.jdk.CollectionConverters._

object SQLQueryBuilder {
  private val LOGGER = Logger(this.getClass)

  def handleQueryParams(query: QueryV3, ps: PlainSelect,filterObject: Option[Map[String,Seq[String]]])={
    val conf = ConfigFactory.load
    val disableQueryValidation = conf.getBoolean("disableQueryValidation")
    val queryParamsV3 = query.queryParams
    var fieldList: Seq[SelectItem] = null
    if(queryParamsV3.fields.nonEmpty && queryParamsV3.fields != null){
      fieldList = queryParamsV3.fields.map(field => {
        val fieldSplit = field.split(" withAlias ", 2)
        fieldSplit.length match {
          case 2 => {
            new SelectExpressionItem((CCJSqlParserUtil.parseExpression(fieldSplit(0),false)))
              .withAlias(new Alias(fieldSplit(1)))
          }
          case _ => new SelectExpressionItem((CCJSqlParserUtil.parseExpression(field,false)))
        }
      })
    }
    var aggFieldList: Seq[SelectItem] = null
    if (queryParamsV3.aggregate.isDefined && queryParamsV3.aggregate.get.nonEmpty && queryParamsV3.aggregate.get != null) {
      aggFieldList = queryParamsV3.aggregate.get.map(agTuple => {
        val aggSelect: SelectExpressionItem = new SelectExpressionItem(
          (CCJSqlParserUtil.parseExpression(s"${agTuple._1} (${agTuple._2}) ",false)))
        aggSelect.setAlias(new Alias(s"${agTuple._1}_${agTuple._2.replaceAll("[\\s*]", "_").replaceAll("\"", "")}"))
        aggSelect
      })
    }
    if(fieldList!=null && aggFieldList !=null){ps.setSelectItems(aggFieldList.concat(fieldList).asJava)}
    else if(fieldList!=null && aggFieldList==null){ps.setSelectItems(fieldList.asJava)}
    else if(fieldList==null && aggFieldList!=null){ps.setSelectItems(aggFieldList.asJava)}
    //  Adding group by to select query
    if (queryParamsV3.group.isDefined && queryParamsV3.group.get.nonEmpty) {
      val groupByList: Seq[Expression] = queryParamsV3.group.get.map(gpElement => CCJSqlParserUtil.parseCondExpression(gpElement,disableQueryValidation ))
      val groupByElement: GroupByElement = new GroupByElement()
      groupByElement.setGroupByExpressions(groupByList.asJava)
      ps.setGroupByElement(groupByElement)
    }
    //  Adding having to select query
    if (queryParamsV3.having.isDefined && queryParamsV3.having.get.nonEmpty) {
      if(queryParamsV3.group.isDefined && queryParamsV3.group.get.nonEmpty){
        ps.setHaving(CCJSqlParserUtil.parseCondExpression(queryParamsV3.having.get, false))
      }
      else{
        //  Throwing error if having clause without group by field in select query
        throw InvalidSQLError("Invalid query. HAVING clause requires GROUP BY clause")
      }
    }


    //  Adding filter to select query
    if (queryParamsV3.filter.isDefined && queryParamsV3.filter.get.nonEmpty) {
      ps.setWhere(CCJSqlParserUtil.parseCondExpression(queryParamsV3.filter.get, false))
    }

    //  Adding opa filters to select query
    handleOPAFilter(query,ps, filterObject)
    //  Adding limit to select query
    if (queryParamsV3.limit.isDefined && queryParamsV3.limit.get.nonEmpty) {
      val limit: Limit = new Limit()
      limit.setRowCount(CCJSqlParserUtil.parseCondExpression(s"${queryParamsV3.limit.get}",false))
      ps.setLimit(limit)
    }
    //  Adding sort to select query
    if (queryParamsV3.sort.isDefined && queryParamsV3.sort.get.nonEmpty) {
      val orderByList: Seq[OrderByElement] = queryParamsV3.sort.get.map(sortElement => {
        val order: OrderByElement = new OrderByElement()
        order.setExpression(CCJSqlParserUtil.parseCondExpression(sortElement._1,false))
        order.setAsc(sortElement._2)
        order
      })
      ps.setOrderByElements(orderByList.asJava)
    }
    //  Adding offset to select query
    queryParamsV3.offset match {
      case Some(offset) => {
        ps.setOffset(new Offset().withOffset(CCJSqlParserUtil.parseExpression(offset.toString)))
      }
      case _ =>
    }
    //  Adding distinct fields to select query
    if (queryParamsV3.distinct.getOrElse(false)) {
      val dist: Distinct = new Distinct(false)
      ps.setDistinct(dist)
    }
  }

  def handleOPAFilter(query: QueryV3,ps: PlainSelect,filterObject: Option[Map[String,Seq[String]]])={
    val queryParamsV3 = query.queryParams
    if (filterObject.isDefined && filterObject.get.nonEmpty) {
      var filterKey:String = null
      if(query.dataSource.dataSource.isDefined){
        if(query.alias.isDefined){
          filterKey = s"${query.dataSource.dataSource.get}_${query.alias.getOrElse("")}"
        }else{
          filterKey = s"${query.dataSource.dataSource.get}"
        }
      }
      val opa_Filters:Seq[String] = filterObject.get.getOrElse(filterKey,Seq.empty[String])
      if (opa_Filters.nonEmpty) {
        val andExpression = opa_Filters.map { filterClause => s"${filterClause}"}.mkString(" AND ")
        val opaFilterCondition = new Parenthesis(CCJSqlParserUtil.parseCondExpression(andExpression))
        val whereExp = if(queryParamsV3.filter.isDefined && queryParamsV3.filter.get.nonEmpty) {
          val filterCondition = new Parenthesis(CCJSqlParserUtil.parseCondExpression(ps.getWhere().toString))
          val combinedCondition = new AndExpression(filterCondition, opaFilterCondition)
          combinedCondition.toString
        } else opaFilterCondition.toString
        ps.setWhere(CCJSqlParserUtil.parseCondExpression(whereExp, false))
      }
    }
  }
  def handleSubQuery(query: QueryV3, ps: PlainSelect,filterObject: Option[Map[String,Seq[String]]], customSchema: Option[String])={
    if (query.dataSource.subQuery.isDefined) {
      val subSelect: SubSelect = new SubSelect()
      if(query.dataSource.subQuery.get.alias.isDefined && query.alias.isDefined && query.alias.nonEmpty){
        subSelect.withAlias(new Alias(query.alias.get,false))
      }
      subSelect.withSelectBody(getSQLQuery(query.dataSource.subQuery.get, filterObject, customSchema))
      ps.withFromItem(subSelect)
    }else{
      val tableName = query.dataSource.dataSource.get.split("\\.") match {
        case Array(_, _, _*) => query.dataSource.dataSource.get // Already contains schema info
        case _ => customSchema.map(schema => s"$schema.${query.dataSource.dataSource.get}").getOrElse(query.dataSource.dataSource.get)
      }
      val table = new Table(tableName)
      ps.setFromItem(table)
      if (query.alias.isDefined && query.alias.nonEmpty) {table.setAlias(new Alias(query.alias.get, false))}
    }

  }
  /**
   * Helper method to prepare SQL query from Query object using JSqlParser library.
   * Ref : https://github.com/JSQLParser/JSqlParser
   *
   * @param query input
   * @return
   */
  def handleJoin(query: QueryV3, ps: PlainSelect,filterObject: Option[Map[String,Seq[String]]], customSchema:Option[String]):Unit={
    if (query.join.isDefined && query.join.get.nonEmpty) {
      query.join.get.map(joinElement => {
        val join: Join = new Join()
        if(joinElement.query.dataSource.subQuery.isDefined){
          val subSelect: SubSelect = new SubSelect()
          subSelect.withSelectBody(getSQLQuery(joinElement.query, filterObject, customSchema)).withAlias(new Alias(joinElement.query.alias.getOrElse(""),false))
          join.setRightItem(subSelect)
        }
        else{
          val joinTable = new Table(joinElement.query.dataSource.dataSource.get).withAlias(new Alias(joinElement.query.alias.getOrElse(""),false))
          val newPs = SelectUtils.buildSelectFromTable(joinTable).getSelectBody().asInstanceOf[PlainSelect]
          val subSelect: SubSelect = new SubSelect()
          subSelect.withSelectBody(newPs)
          subSelect.withAlias(new Alias(joinElement.query.alias.getOrElse(""),false))
          handleQueryParams(joinElement.query, newPs, filterObject)
          join.setRightItem(subSelect)
          handleJoin(joinElement.query,newPs, filterObject, customSchema)
        }
        join.withOnExpression(CCJSqlParserUtil.parseCondExpression(joinElement.onCondition))
        joinElement.joinType match {
          case "INNER JOIN" => join.setInner(true)
          case "LEFT JOIN" => join.setLeft(true)
          case "RIGHT JOIN" => join.setRight(true)
          case "FULL JOIN" => join.setFull(true)
          case _ =>
        }
        ps.addJoins(join)
      })
    }
  }
  def getSQLQuery(query: QueryV3, filterObject: Option[Map[String,Seq[String]]], customSchema: Option[String] = None): PlainSelect = {
    try{
      LOGGER.info(s"Starting to prepare query for data source: ${query.dataSource.dataSource} with query label: ${query}")
      val queryParamsV3 = query.queryParams

      // Add table name
      val table = new Table("")
      var ps = SelectUtils.buildSelectFromTable(table).getSelectBody().asInstanceOf[PlainSelect]
      // Add alias to table

      //  Adding subquery fields to select query
      handleSubQuery(query, ps, filterObject, customSchema)
      //  Adding join to select query
      handleJoin(query, ps, filterObject, customSchema)

      //  Adding simple fields & aggregated fields to select query
      handleQueryParams(query, ps, filterObject)

      ps
    } catch {
      case ex: Exception => throw InvalidSQLError(s"Invalid query. Please validate the query : ${ex.getMessage}",ex)
    }

  }
}
