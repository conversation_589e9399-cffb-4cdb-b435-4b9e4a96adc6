package v2.controllers
import ai.prevalent.sdspecore.utils.PidUtils.LOGGER
import play.api.Configuration
import v2.common.{RedisBase, SQLQueryBuilder}
import javax.inject.{Inject, Singleton}
import scala.concurrent.Future
import play.api.mvc._
import play.api.libs.json.{JsValue, Json}
import scala.concurrent.ExecutionContext
import v2.models.{QueryParams, DataQuery => DataQueryV3}
import org.apache.commons.text.StringEscapeUtils
import play.api.libs.json.Format.GenericFormat
import org.apache.spark.sql.DataFrame
import v1.utils.LiveContext
import v2.services.LineageService
import v2.utils.ConfigUtils.replaceAllOccurrences
import v2.utils.CommonUtils.{generateQueryCacheKey, getSecondsUntilMidnight}

@Singleton
class QueryController @Inject()(
                                 cc: ControllerComponents, lineageService: LineageService,
                                 configuration: Configuration, redisBase: RedisBase
                               )(implicit ec: ExecutionContext) extends AbstractController(cc) {

  def query(queryParams: QueryParams): Action[JsValue] = Action.async(parse.json) { implicit request =>
    Future.successful {

      try {
        LiveContext.buildSessionFromEnvSpec()
        LOGGER.error("Validator::: Session Created")
      } catch {
        case ex: Exception =>
          LOGGER.error("Validator::: Error while creating Spark session")
          LOGGER.error(ex.getMessage)
          Future.successful(BadRequest("No Active Spark session"))
      }
      val queryRequest: JsValue = request.body
      val updateSchema = request.queryString.get("update_schema").flatMap(_.headOption).contains("true")

      val updatedQueryRequest: DataQueryV3 = if (updateSchema) {
        val schemaName: String = request.queryString.get("ei_schema").flatMap(_.headOption).get
        LOGGER.info(s"EI_SCHEMA_NAME is :::: $schemaName")
        val fragmentSchema: String = request.queryString.get("fragment_schema").flatMap(_.headOption).get
        LOGGER.info(s"KG_FRAGMENT_SCHEMA is :::: $fragmentSchema")

        val updatedSchema = replaceAllOccurrences(queryRequest, schemaName, configuration.get[String]("validatorSchema"))
        val updatedFragment = replaceAllOccurrences(updatedSchema, fragmentSchema, configuration.get[String]("eiFragmentSchema"))
        updatedFragment.as[DataQueryV3]
      } else {
        queryRequest.as[DataQueryV3]
      }

      LOGGER.info(s"Updated V3 Object:: $updatedQueryRequest")

      // Extract pagination parameters (only if provided)
      val limitOpt = request.queryString.get("limit").flatMap(_.headOption).map(_.toInt)
      val offsetOpt = request.queryString.get("offset").flatMap(_.headOption).map(_.toInt)

      val queryResponse = updatedQueryRequest.dataQuery.map(query => {
        SQLQueryBuilder.getSQLQuery(query.query, None, None)
      }).toList

      val queryString = queryResponse.mkString(", ")
      val escapedSQL = StringEscapeUtils.unescapeJava(queryString)
      LOGGER.info(s"Generated SQL Query::: ${escapedSQL}")

      // **Check if pagination is needed**
      val isPaginated = limitOpt.isDefined && offsetOpt.isDefined

      // **Generate SQL Queries**
      val countSQL = if (isPaginated) Some(s"SELECT COUNT(*) AS total_count FROM ($escapedSQL) AS subquery") else None
      val paginatedSQL = if (isPaginated) Some(s"$escapedSQL LIMIT ${limitOpt.get} OFFSET ${offsetOpt.get}") else Some(escapedSQL)

      LOGGER.info(s"Paginated SQL Query::: ${paginatedSQL.get}")
      LOGGER.info(s"Total Count SQL Query::: ${countSQL.getOrElse("N/A (Not needed)")}")

      val moduleName = updatedQueryRequest.moduleName
      val cacheLabel = request.queryString.get("cache_label").flatMap(_.headOption).getOrElse("")
      val namespace: String = configuration.getOptional[String]("namespace").getOrElse("default")

      // **Generate cache keys**
      val paginatedCacheKey = generateQueryCacheKey(namespace, moduleName, paginatedSQL.get, cacheLabel, "PAGINATED_DATA")
      val countCacheKey = countSQL.map(sql => generateQueryCacheKey(namespace, moduleName, sql, cacheLabel, "TOTAL_COUNT"))

      // **Check if cache is enabled**
      val queryCacheEnabled: Boolean = configuration.getOptional[Boolean]("query_cache_enabled").getOrElse(true)
      if (!queryCacheEnabled) {
        LOGGER.info("Query cache is disabled. Clearing cache entries before proceeding.")
        redisBase.deleteCacheDefault(paginatedCacheKey)
        countCacheKey.map(redisBase.deleteCacheDefault)

      }

      // **Check cache for paginated data**
      redisBase.getCacheDefault(paginatedCacheKey) match {
        case Some(cachedValue) =>
          LOGGER.info(s"Cache hit for query:: ${paginatedSQL.get}")

          // **Check cache for total count (if paginated)**
          val totalRecords = countCacheKey.flatMap(redisBase.getCacheDefault).map(_.toLong)

          // **Build Response JSON Dynamically**
          val responseJson = Json.obj("data" -> Json.toJson(Json.parse(cachedValue)))
          val finalResponse = if (isPaginated) {
            responseJson + ("pagination" -> Json.obj(
              "limit" -> limitOpt.get,
              "offset" -> offsetOpt.get,
              "total" -> totalRecords.getOrElse(0L).toString
            ))
          } else {
            responseJson // No pagination field if not paginated
          }

          Ok(finalResponse)

        case None =>
          LOGGER.info(s"Cache miss. Executing query:: ${paginatedSQL.get}")
          val ttl = getSecondsUntilMidnight

          // **Check if total count is already cached**
          val totalRecords: Option[Long] = countCacheKey.flatMap(redisBase.getCacheDefault).map(_.toLong) match {
            case Some(cachedCount) =>
              LOGGER.info(s"Cache hit for total count:: $cachedCount")
              Some(cachedCount)
            case None if isPaginated =>
              LOGGER.info("Cache miss for total count. Running COUNT(*) query...")
              val countDf: DataFrame = LiveContext.spark.sql(countSQL.get)
              val totalCount: Long = countDf.collect().head.getAs[Long]("total_count")

              // **Cache total count separately**
              redisBase.setCacheDefault(countCacheKey.get, totalCount.toString, Some(ttl)) // Cache for 1 hour
              Some(totalCount)
            case _ => None
          }

          // **Execute the paginated query**
          val resultDf: DataFrame = LiveContext.spark.sql(paginatedSQL.get)
          val jsonOut = resultDf.toJSON.collect().map(Json.parse)

          redisBase.setCacheDefault(paginatedCacheKey, Json.toJson(jsonOut).toString(), Some(ttl))

          // **Build Response JSON Dynamically**
          val responseJson = Json.obj("data" -> Json.toJson(jsonOut))
          val finalResponse = totalRecords match {
            case Some(total) => responseJson + ("pagination" -> Json.obj(
              "limit" -> limitOpt.get,
              "offset" -> offsetOpt.get,
              "total" -> total.toString
            ))
            case None => responseJson // No pagination field if not paginated
          }

          Ok(finalResponse)
      }
    }
  }
}