package v2.controllers

import play.api.Configuration
import play.api.mvc._
import v2.services.{DependencyManagerService, LineageService, ResultsService}

import javax.inject.Inject
import scala.concurrent.ExecutionContext
import v2.common.Modules

class PublisherController @Inject()(
                                       cc: ControllerComponents, dependencyManagerService: DependencyManagerService,appConfig: Configuration,resultsService: ResultsService, lineageService: LineageService
                                     )(implicit ec: ExecutionContext)
  extends BaseExecutionController(cc, dependencyManagerService, appConfig, resultsService, lineageService) {

  override def getModuleName: String = Modules.PUBLISHER
}