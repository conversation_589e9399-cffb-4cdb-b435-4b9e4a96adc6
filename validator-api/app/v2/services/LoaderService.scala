package v2.services

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.specs.{Config => LoaderConfig}
import ai.prevalent.entityinventory.utils.SparkUtil
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReaderFactory
import common.{InvalidJsonError, TableNotFoundError}
import org.apache.spark.sql.SparkSession
import org.json4s.Formats
import play.api.Logger
import play.api.libs.json.{JsValue, Json}
import play.api.mvc.Results._
import v2.utils.{IcebergUtils, LoaderControllerUtils, ResultSetUtils}
import v2.utils.LoaderControllerUtils.{checkTableExist, getTimeStampDetails}

import scala.concurrent.{ExecutionContext, Future}
import javax.inject.Inject
import v2.models.{ExecutionModule, ModuleServiceBase, QueryParams}
import v1.utils.LiveContext
import v2.common.{CacheUtil, Modules, ResolutionLevel}

class LoaderService @Inject()(
                               implicit ec: ExecutionContext,
                               resultsService: ResultsService,
                               cacheUtils: CacheUtil,
                               icebergUtils: IcebergUtils,
                               resultSetUtils: ResultSetUtils
                             ) extends ModuleServiceBase {

  implicit val formats: Formats = Loader.configFormats
  private val LOGGER = Logger(getClass)


  def execute(configName: String, subModuleName: String, params: QueryParams, configJson:JsValue = null, schemaName: String="ei_validator"
              , configApiUrl:String ="/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs/"): Future[Unit] = {
    LOGGER.info(s"Executing Loader submodule: $subModuleName")

    val loaderJobArgs = new EIJobArgs()
    var configData: JsValue = null
    val disambiguation_resolution_level = params.disambiguation_resolution_level.getOrElse(Seq(ResolutionLevel.ENTITY_RESOLUTION)).head

    try {
      val reader = SDSTableReaderFactory.getDefault(LiveContext.spark)
      val selectedCols = params.selectedCols
      val distinct = params.distinct
      val onlyInventoryConfigFields = params.onlyInventoryConfigFields
      val limit: Option[Seq[String]] = params.limit


      configData = resultSetUtils.getConfig(configJson=configJson,configApiUrl=configApiUrl,subModuleName=subModuleName,schemaName = schemaName,Modules.LOADER)
      val config:LoaderConfig= resultSetUtils.getConfig(updatedJson = configData,manifest = manifest[LoaderConfig],formats = formats)

      val srdm = config.dataSource.get.srdm

      Loader.spark = LiveContext.spark
      //TODO commented to read in dev, need to remove this before release
      checkTableExist(srdm, LiveContext.spark)

      val timeStampDetails = getTimeStampDetails(srdm, LiveContext.spark,params.disambiguation_resolution_level.getOrElse(Seq(ResolutionLevel.ENTITY_RESOLUTION)).head)
      if (timeStampDetails._2) {
        loaderJobArgs.parsedIntervalEndEpoch = timeStampDetails._1.parsedIntervalEndEpoch
        loaderJobArgs.parsedIntervalStartEpoch = timeStampDetails._1.parsedIntervalStartEpoch
        loaderJobArgs.prevUpdateDate = timeStampDetails._1.previousEndEpoch
        loaderJobArgs.currentUpdateDate = timeStampDetails._1.eventTimestampEndEpoch



        val cachedStatusOpt = cacheUtils.getCache(configData,subModuleName, loaderJobArgs.currentUpdateDate,resolutionLevel = disambiguation_resolution_level)

        cachedStatusOpt match {
          case Some(cachedStatus) =>
            cachedStatus match {
              case ExecutionModule.COMPLETED =>
                LOGGER.info(s"Validator::: Inside Completed")

                if (configJson != null) {
                  val resultSet = icebergUtils.reader(LiveContext.spark, config.outputTable, loaderJobArgs.currentUpdateDate)

                  resultSetUtils.processResultSet(
                    resultSet,
                    distinct,
                    limit,
                    configJson,
                    configName,
                    subModuleName,
                    Modules.LOADER,
                    selectedCols,
                    onlyInventoryConfigFields,
                  )
                }
                else{
                  resultsService.storeResult(
                    configName = configName,
                    executionFlow = List(s"Loader: ${subModuleName} Completed (loaded from cache)")
                  )
                }

                Future.successful(println("Executing logic for COMPLETED"))

              case ExecutionModule.IN_PROGRESS =>
                LOGGER.info(s"Validator::: Inside In-progress ")
                resultsService.storeResult(
                  configName = configName,
                  executionFlow = List(s"Loader: ${subModuleName} in-progressing")
                )
                Future.successful(println("Executing logic for IN_PROGRESS"))
            }
          case None =>
            LOGGER.info(s"Validator::: Inside Start")
            cacheUtils.setCache(Json.toJson(configData),subModuleName, loaderJobArgs.currentUpdateDate, ExecutionModule.IN_PROGRESS,resolutionLevel = disambiguation_resolution_level)

            val paramsPriority = if (selectedCols.getOrElse(Seq.empty[String]).nonEmpty &&
              onlyInventoryConfigFields.getOrElse(Seq("true")).head.toBoolean) {
              "selectedCols"
            } else {
              "onlyInventoryConfigFields"
            }

            var fields: Array[String] = if (paramsPriority == "selectedCols") {
              val fieldsFrom = SparkUtil.getDependendProperty(config.allProperties, selectedCols.get.head).toList.reverse
              LOGGER.info(s"Validator Fields $fieldsFrom")
              fieldsFrom.toArray
            } else {
              LoaderControllerUtils.getPropertyColNames(config.commonProperties ++
                config.entitySpecificProperties ++ config.sourceSpecificProperties) ++ Array("primary_key")
            }

            val updConfig: LoaderConfig = if (paramsPriority == "selectedCols") {
              val copyField = fields
              val srDMprop = SparkUtil.getSRDMProperty(copyField, config)
              fields = fields ++ srDMprop.map(f => f.colName).toSet
              config.copy(sourceSpecificProperties = config.sourceSpecificProperties ++ srDMprop)
            } else {
              config
            }

            val resultSet = Loader.build(loaderJobArgs, updConfig, reader)

            icebergUtils.writer(LiveContext.spark,resultSet, config.outputTable, restrictOutput=true,output_table_limit = params.output_table_limit)

            resultSetUtils.processResultSet(
              resultSet,
              distinct,
              limit,
              configJson,
              configName,
              subModuleName,
              Modules.LOADER,
              selectedCols,
              onlyInventoryConfigFields,
            )

            cacheUtils.setCache(Json.toJson(configData),subModuleName, loaderJobArgs.currentUpdateDate, ExecutionModule.COMPLETED,resolutionLevel = disambiguation_resolution_level)
            Future.successful(println("None"))
        }
      } else {
        val status = configJson match {
          case null =>
            ExecutionModule.IN_PROGRESS

          case _=> ExecutionModule.ERROR
        }
        resultsService.storeResult(
          configName = configName,
          status = status,
          executionFlow = List(s"Error while execution of loader: ${subModuleName}, No SRDM data")
        )

        Future.successful(())
      }
    } catch {
      case ex: Exception =>
        LOGGER.error("Exception while processing request", ex)
        resultsService.storeResult(
          configName = configName,
          status = ExecutionModule.ERROR,
          executionFlow = List(s"Error while execution of loader: ${subModuleName}, ${ex.getMessage}")
        )
        cacheUtils.deleteCache(Json.toJson(configData),subModuleName, loaderJobArgs.currentUpdateDate,resolutionLevel = disambiguation_resolution_level)
        Future.successful(())

        throw InvalidJsonError(ex.getMessage)
    }
  }
}