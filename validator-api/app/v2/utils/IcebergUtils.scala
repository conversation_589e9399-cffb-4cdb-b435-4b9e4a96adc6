package v2.utils

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.utils.EIUtil
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReaderFactory, SDSTableWriterFactory}
import jakarta.inject.Inject
import org.apache.spark.sql.{Column, DataFrame, SparkSession}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, NullType}
import play.api.{Configuration, Logger}
import v2.models.QueryParams


class IcebergUtils @Inject()(configuration:Configuration){
  val LOGGER = Logger(getClass)

  private def formatTableName(outputTable: String, tabSchema: String): String = {
    val tableName = outputTable.split("\\.").last
    s"$tabSchema.$tableName"
  }


  def writer(spark: SparkSession,df: DataFrame, outputTable: String,partitionCols:Array[Column] = Array(col(UPDATED_AT_TS)), tabSchema: String = configuration.get[String]("validatorSchema"), restrictOutput: Boolean = false, purgeTable: Boolean = true,output_table_limit:Option[Seq[String]]=Some(Seq("1000")) ): Unit = {

    val tableName = formatTableName(outputTable, tabSchema)
    // Drop the table if it exists
    LOGGER.info(s"Validator::: Writing to table $tableName")
    if (purgeTable) {
      spark.sql(s"DROP TABLE IF EXISTS $tableName").count()
    }


    val writer = SDSTableWriterFactory.getDefault(
      spark,tableProperties=Map.empty,
      options = Map("partitionOverwriteMode"->"dynamic")
    )

    val nullFields = df.schema
      .filter(f => f.dataType.isInstanceOf[NullType] || (f.dataType.isInstanceOf[ArrayType] && f.dataType.asInstanceOf[ArrayType].elementType.isInstanceOf[NullType]))
      .map(_.name)

    var outputDF = df.drop(nullFields: _*)

    if (restrictOutput) {
      outputDF = outputDF.limit(output_table_limit.getOrElse(Seq("1000")).head.toInt)
    }

    writer.overwritePartition(outputDF, tableName , partitionCols)
  }

  def reader(spark:SparkSession,outputTable: String, previousEndEpoch: Long, tabSchema: String = configuration.get[String]("validatorSchema")): DataFrame = {
    val tableName = formatTableName(outputTable, tabSchema)
    LOGGER.info(s"Validator::: Reading from table $tableName")
    val reader = SDSTableReaderFactory.getDefault(spark)
    val previousInventory = EIUtil.safeReadEntity(tableName, expr(s"$UPDATED_AT_TS = to_timestamp(${previousEndEpoch}/1000)"), reader)
    previousInventory
  }

}