package v2.utils

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.Disambiguator.readEnrichModels
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.{InventoryModelSpec, build, normalizeInventoryModels}
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.disambiguator.configs.specs.Config
import ai.prevalent.entityinventory.utils.EIUtil
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, expr, lit}
import org.json4s.Formats
import play.api.{Configuration, Logger}
import play.api.libs.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>}
import v1.utils.LiveContext
import v2.common.{DisambiguationConstants, Modules, ResolutionLevel}
import v2.models.ExecutionModule
import v2.services.{LineageService, ResultsService}
import v2.utils.ConfigUtils.replaceExactMatches

import javax.inject.Inject

class ResultSetUtils @Inject()(resultsService: ResultsService ,lineageService: LineageService,configuration: Configuration) {

  implicit val formats: Formats = Loader.configFormats
  private val LOGGER = Logger(getClass)

  def processResultSet(
                        resultSet: DataFrame,
                        distinct: Option[Seq[String]],
                        limit: Option[Seq[String]],
                        configJson: JsValue,
                        configName: String,
                        subModuleName: String,
                        moduleType:String,
                        selectedCols: Option[Seq[String]] = Some(Seq.empty[String]),
                        onlyInventoryConfigFields: Option[Seq[String]] = Some(Seq("false"))
                      ): Unit = {

    val paramsPriority = if (selectedCols.getOrElse(Seq.empty[String]).nonEmpty &&
      onlyInventoryConfigFields.getOrElse(Seq("true")).head.toBoolean) {
      "selectedCols"
    } else {
      "onlyInventoryConfigFields"
    }

    val fields: Array[String] = resultSet.columns

    val (output, reducedSet) = resultsService.processOutput(fields, resultSet, limit, distinct)
    val headers: Array[String] = if (paramsPriority == "selectedCols") {
      fields
    } else {
      reducedSet.columns
    }

    storeResult(
      output,
      configJson,
      configName,
      moduleType,
      subModuleName,
      headers
    )
  }

  def storeResult(
                   result: Array[JsValue],
                   configJson: JsValue,
                   configName: String,
                   moduleType:String,
                   subModuleName: String,
                   headers: Array[String]
                 ): Unit = {

    var status = ExecutionModule.COMPLETED
    var output = result

    if (configJson == null) {
      status = ExecutionModule.IN_PROGRESS
      output = Array.empty[JsValue]
    }

    resultsService.storeResult(
      configName = configName,
      status = status,
      executionFlow = List(s"$moduleType: ${subModuleName} completed"),
      result = Json.toJson(ExecuteExpressionResponse(output = JsArray(output), headers = Json.toJson(headers)))
    )
  }

  def getConfig(configJson:JsValue, configApiUrl :String, subModuleName:String,schemaName:String,moduleType:String,publisherSchema:String="",eiEnrichSchemaName:String="",eiFragmentSchema:String=""):JsValue ={
    val configData = if (configJson == null) {
      val url = configApiUrl + subModuleName
      lineageService.getConfig(url)
    } else {
      configJson
    }
    var updatedJson = replaceExactMatches(configData, schemaName, configuration.get[String]("validatorSchema"))
    updatedJson = replaceExactMatches(updatedJson, eiFragmentSchema, configuration.get[String]("eiFragmentSchema"))
    updatedJson= replaceExactMatches(updatedJson, publisherSchema, configuration.get[String]("publisherSchema"))
    updatedJson=  replaceExactMatches(updatedJson, eiEnrichSchemaName, configuration.get[String]("eiEnrichSchema"))
    LOGGER.info(s"Validator config ::: ${updatedJson.toString()}")

    updatedJson
  }

  def getConfig[C](updatedJson:JsValue,manifest:Manifest[_],formats:Formats):C ={
     val json = org.json4s.jackson.JsonMethods.parse(Json.stringify(updatedJson))
    json.extract(formats,manifest).asInstanceOf[C]
  }

  def disambiguatorCalculationUtil(disambiguation_resolution_level:String, config:Config, disambiguationArgs:EIJobArgs, reader:SDSTableReader): DisambiguatorUtils.Artifacts ={

    val invModelSpecList = disambiguation_resolution_level match {
      case ResolutionLevel.CANDIDATE_KEY_RESOLUTION =>
        val candidateKeyList =  config.disambiguation.candidateKeys.map(_.name)
        val matchAttribute = config.disambiguation.candidateKeys.flatMap(_.matchAttributesList)
        val exceptionFilterRefs = config.disambiguation.candidateKeys.map(_.exceptionFilter.getOrElse("false")).flatMap(exp => expr(exp).expr.references.map(_.name.split("[.]")(0)))
        val reqFields = DisambiguationConstants.filterFields ++ candidateKeyList.toSeq ++ matchAttribute.toSeq ++ exceptionFilterRefs.toSeq
        LOGGER.error(s"============= reqFields  ${reqFields}")
        val specList = readEnrichModels(config.inventoryModelInput, reader, disambiguationArgs.currentUpdateDate)
        specList.map {
          inp =>
            LOGGER.error(s" **************   ${inp.inventoryModel.columns.mkString(",")}   ********8***")
            LOGGER.error(s"+++++++++++++ Validator API ${inp.name} = ${reqFields.intersect(inp.inventoryModel.columns).mkString(",")} +++++++++++++++++")
            val df = inp.inventoryModel.select(reqFields.intersect(inp.inventoryModel.columns).map(col):_*)
            InventoryModelSpec(df, inp.name)
        }
      case ResolutionLevel.ENTITY_RESOLUTION =>
        readEnrichModels(config.inventoryModelInput, reader, disambiguationArgs.currentUpdateDate)

    }

    val prevInv = EIUtil.safeReadEntity(config.output.disambiguatedModelLocation, expr(s"$UPDATED_AT_TS = to_timestamp(${disambiguationArgs.prevUpdateDate}/1000)"), reader)
    if (prevInv.isEmpty) LOGGER.info(s"Previous Inventory (${config.output.disambiguatedModelLocation}) is empty")

    val prevNondis: Option[DataFrame] = config.output.fragmentLocation match {
      case Some(fragmentLocation) =>
        // Read the data and wrap it in Some if it's not empty
        val prevNondisData = EIUtil.safeReadEntity(fragmentLocation, expr(s"$UPDATED_AT_TS = to_timestamp(${disambiguationArgs.prevUpdateDate}/1000)"), reader)


        // Check if the DataFrame is empty or not
        if (prevNondisData.isEmpty) {
          LOGGER.info(s"Previous Fragments ($fragmentLocation) is empty")
        } else {
          LOGGER.info(s"Previous Fragments ($fragmentLocation) is not empty")
        }
        Some(prevNondisData)

      case None =>
        // If fragmentLocation is None, return None
        None
    }

    val normalizedInvModelSpecList = normalizeInventoryModels(invModelSpecList, LiveContext.spark, config.disambiguation)
    val resultSet = build(normalizedInvModelSpecList, prevInv, config, LiveContext.spark, prevNondis)

    resultSet

  }
}