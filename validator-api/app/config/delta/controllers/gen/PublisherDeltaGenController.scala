package config.delta.controllers.gen

import ai.prevalent.entityinventory.delta.{PublisherConfigDelta, RelationshipConfigDelta}
import ai.prevalent.entityinventory.publisher.Publisher
import ai.prevalent.entityinventory.publisher.configs.Config
import config.delta.service.{CMService, DeltaGenerateService, ManualDeltaService}
import org.json4s.Formats
import play.api.mvc.ControllerComponents

import javax.inject.Inject
import scala.concurrent.ExecutionContext

class PublisherDeltaGenController @Inject()(cc: ControllerComponents, manualDeltaService:ManualDeltaService, deltaService: DeltaGenerateService[Config, PublisherConfigDelta],
                                            deltaPublishService:CMService)(implicit ec: ExecutionContext)
   extends BaseDeltaGenController[Config, PublisherConfigDelta](cc,manualDeltaService,deltaService, deltaPublishService) {

  def configItemType = "publisher"
  override def formats: Formats = Publisher.configFormats
}
