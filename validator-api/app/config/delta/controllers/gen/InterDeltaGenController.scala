package config.delta.controllers.gen

import ai.prevalent.entityinventory.delta.DisambiguationConfigDelta
import ai.prevalent.entityinventory.disambiguator.Disambiguator
import ai.prevalent.entityinventory.disambiguator.configs.specs.Config
import ai.prevalent.entityinventory.loader.Loader
import config.delta.service.{CMService, DeltaGenerateService, ManualDeltaService}
import org.json4s.Formats
import play.api.mvc.ControllerComponents

import javax.inject.Inject
import scala.concurrent.ExecutionContext

class InterDeltaGenController @Inject()(cc: ControllerComponents, manualDeltaService:ManualDeltaService, deltaService: DeltaGenerateService[Config, DisambiguationConfigDelta],
                                        deltaPublishService: CMService)(implicit ec: ExecutionContext)
  extends BaseDeltaGenController[Config, DisambiguationConfigDelta](cc,manualDeltaService,deltaService, deltaPublishService) {

  def configItemType = "intersource_disambiguated_models"
  override def formats: Formats = Disambiguator.configFormats
}