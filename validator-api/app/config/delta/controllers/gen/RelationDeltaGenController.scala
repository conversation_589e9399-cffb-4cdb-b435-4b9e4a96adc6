package config.delta.controllers.gen

import ai.prevalent.entityinventory.delta.RelationshipConfigDelta
import ai.prevalent.entityinventory.relationship.extractor.Extractor
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.Config
import config.delta.service.{CMService, DeltaGenerateService, ManualDeltaService}
import org.json4s.Formats
import play.api.mvc.ControllerComponents

import javax.inject.Inject
import scala.concurrent.ExecutionContext

class RelationDeltaGenController @Inject()(cc: ControllerComponents,manualDeltaService:ManualDeltaService, deltaService: DeltaGenerateService[Config, RelationshipConfigDelta],
                                           deltaPublishService:CMService)(implicit ec: ExecutionContext)
   extends BaseDeltaGenController[Config, RelationshipConfigDelta](cc,manualDeltaService,deltaService, deltaPublishService) {

  def configItemType = "relationship_models"
  override def formats: Formats = Extractor.configFormats
}
