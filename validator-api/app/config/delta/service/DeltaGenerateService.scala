package config.delta.service

import scala.collection.parallel.CollectionConverters._
import ai.prevalent.entityinventory.common.configs.EIConfig
import ai.prevalent.entityinventory.delta.{Change, Delta}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.utils.ConfigUtils
import ai.prevalent.sdspecore.utils.api.HTTPUtil
import common.SDSConf
import config.model.{ConfigItem, DeltaResponse}
import org.json4s.{DefaultFormats, Formats}
import org.json4s.jackson.Serialization.write
import play.api.libs.json.Json

import scala.collection.parallel.ParMap
import javax.inject.Inject


class DeltaGenerateService[C <: EIConfig[C, D], D <: Delta] @Inject()(cmService:CMService) extends LoggerBase{

  def configDelta(configType: String, configOldVersion: String, configNewVersion: String)(implicit manifest: Manifest[C],formats:Formats) = {
    val oldConfigsNamesList = cmService.listConfigs(configType = configType, configVersion = configOldVersion).filter(_.solution_config_exists)
    val newConfigsNamesList = cmService.listConfigs(configType = configType, configVersion = configNewVersion).filter(_.solution_config_exists)

    println(s"New Config List for $configType: ${newConfigsNamesList.
      filter(co => oldConfigsNamesList.map(_.name).contains(co.name)).map(_.name)}")
    println(s"item ${newConfigsNamesList.par.
      filter(co => oldConfigsNamesList.map(_.name).contains(co.name))
      .map(c => (c.name, cmService.getConfigByID[C](s"/${c.name}?config_item_level=solution&solution_edition=$configNewVersion&config_item_type=$configType&no_merge=true&solution__name=ei"))).map(_._1)}")
    val newConfigList = newConfigsNamesList.par.
      filter(co => oldConfigsNamesList.map(_.name).contains(co.name))
      .map(c => (c.name, cmService.getConfigByID[C](s"/${c.name}?config_item_level=solution&solution_edition=$configNewVersion&config_item_type=$configType&no_merge=true&solution__name=ei"))
      )
      .toMap
    LOGGER.info(s"New Config List for $configType: ${newConfigList.map(_._1)}")

    val oldConfigList = oldConfigsNamesList.par
      .filter(co => newConfigsNamesList.map(_.name).contains(co.name))
      .map(c => (c.name, cmService.getConfigByID[C](s"/${c.name}?config_item_level=solution&solution_edition=$configOldVersion&config_item_type=$configType&no_merge=true&solution__name=ei")))
      .toMap
    println(s"Old Config List for $configType: ${oldConfigList.map(_._1)}")


    LOGGER.info(s"Old Config List for $configType: ${oldConfigList.map(_._1)}")

    println(s"Finding Solution config delta for type $configType, config names - ${newConfigList.keySet.toList}")
    newConfigList.par.map(c => (c._1, c._2.getConfigDelta(oldConfigList(c._1))))
  }

  def clientConfigDelta(deltas:Map[String,Seq[Change]] , configType: String,
                        configNewVersion: String)(implicit manifest: Manifest[C],formats:Formats): ParMap[String, D] = {
    val clientConfigNameList = cmService.listConfigs(configType = configType, configVersion = configNewVersion)
      .filter(k => k.config_item_level=="client"  )

    val clientConfigs = clientConfigNameList.filter(p => deltas.contains(p.name))
      .map(c => (c.name, cmService.getConfigByID[C](s"/${c.name}?config_item_level=client&solution_edition=$configNewVersion&config_item_type=$configType&no_merge=true")))
      .toMap

    LOGGER.info(s"Finding client config delta for type $configType, config names - ${clientConfigs.keySet.toList}")
    println(s"Finding client config delta for type $configType, config names - ${clientConfigs.keySet.toList}")
    println(s"client configs - ${deltas.filter(_._1.equals("host_entity_config")).map(_._2)}")
    clientConfigs.par.map(c => (c._1, c._2.getConfigDelta(deltas(c._1))))
  }




  def toJson(changes: Seq[Change]) = {
    write(changes)(DefaultFormats)
  }

  def deltaResponse(delta:Seq[D]): DeltaResponse ={
    val changes = delta.flatMap(_.changes())
    val deltaJsonStr = toJson(changes)
    DeltaResponse(changes = Json.parse(deltaJsonStr))
  }
}


