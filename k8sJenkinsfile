@Library('sds-common') _
def highlightCoverageStatusMono(Map config = [:]) {
    
    currentBuild.result = "SUCCESS"
    // coverageReport = sh(returnStdout: true, script: "cat jacocoTestReport.xml").trim()
    // println "*************************************"
    // println "${coverageReport}"
    // println "*************************************"
    output = "<html><table border='1'><tr><td align='center' ><h2>COVERAGE REPORT</h2></td></tr><tr><td><h2>${env.eianalyticsCoverage}</h2></td></tr><tr><td><h2>${env.eiconfigsCoverage}</h2></td></tr><tr><td><h2>${env.eiorchestrationCoverage}</h2></td></tr><tr><td><h2>${env.validatorCoverage}</h2></td></tr></table><table><tr><td><h2>Build Number :</h2></td><td><h2>${config.buildVersion}</h2></td></tr></table><tr><td><h2> Recommended : 80 </h2></td></tr></html>"
    rtp(
        //stableText: "${FILE:index.html}",
        stableText: "${output}",
        unstableAsStable: true,
        failedAsStable:   true,
        parserName:       'HTML',
        abortedAsStable:  true
    )
}
def helmAppofappBuild() {
    def yamlFile = 'values.yaml'
    def existingYAML = readYaml file: yamlFile
    println("${existingYAML}")
    existingYAML.targetRevision = "${env.buildNumber}"
    writeYaml file: yamlFile, data: existingYAML, overwrite: true
    println("${existingYAML}")
    sh "helm package . --version ${env.buildNumber}"
    uploadSpec = """{
    "files":
    [
        {
            "pattern": "sds-solution-ei-deployer*.tgz",
            "target": "${env.jfrogHelmRepoName}/${env.repoName}/",
            "props": "project=${monoRepoName};repotype=${helmRepotype},"
        }
    ]
} """
    rtUpload(
    buildName: 'sds-solution-ei-deployer',
    buildNumber: "${env.buildNumber}",
    serverId: 'pe-jfrog',
    spec: "${uploadSpec}"
)

    rtPublishBuildInfo(
    serverId: 'pe-jfrog',
    buildName: 'sds-solution-ei-deployer',
    buildNumber: "${env.buildNumber}"
)
}
def releaseBundleCreation() {
    withCredentials([usernamePassword(credentialsId: 'jfrog-credentials', usernameVariable: 'ARTIFACTORY_USER', passwordVariable: 'ARTIFACTORY_PASSWORD')]) {
        sh 'apt-get update && apt install curl -y '
        def renamedBranch = env.GIT_BRANCH.replaceAll("/", " :: ")
        def parentBranch = "sds-solution-ei :: ${renamedBranch}"
        def eianalyticsjar = (sh(returnStdout: true, script: "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${env.contentTypeText}' -i ${env.jfrogArtifactoryURL}/${env.jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${env.jfrogJavaRepoName}\",\"@build.name\":{\"\$eq\":\"${env.monoRepoName}-${env.analyticsCompName}\"},\"@build.number\":{\"\$match\":\"${env.buildversion}*\"},\"@build.parentName\":{\"\$eq\":\"${parentBranch}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
        def validatorapijar = (sh(returnStdout: true, script: "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${env.contentTypeText}' -i ${env.jfrogArtifactoryURL}/${env.jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${env.jfrogJavaRepoName}\",\"@build.name\":{\"\$eq\":\"${env.monoRepoName}-${env.eiValidatorCompName}\"},\"@build.number\":{\"\$match\":\"${env.buildversion}*\"},\"@build.parentName\":{\"\$eq\":\"${parentBranch}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
        //def eieuijar = (sh(returnStdout: true, script: "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${env.contentTypeText}' -i ${env.jfrogArtifactoryURL}/${env.jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${env.jfrogGenRepoName}\",\"@build.name\":{\"\$eq\":\"${env.monoRepoName}-${env.euiCompName}\"},\"@build.number\":{\"\$match\":\"${env.buildversion}*\"},\"@build.parentName\":{\"\$eq\":\"${parentBranch}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
        def eiprofilingdep = (sh(returnStdout: true, script: "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${env.contentTypeText}' -i ${env.jfrogArtifactoryURL}/${env.jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${env.jfrogPythonRepoName}\",\"@build.name\":{\"\$eq\":\"${env.monoRepoName}-profiling\"},\"@build.number\":{\"\$match\":\"${env.buildversion}*\"},\"name\":{\"\$match\":\"sds-solution-ei-profiling-dependencies-*.tar.gz\"},\"@build.parentName\":{\"\$eq\":\"${parentBranch}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
        def eiprofilingapp = (sh(returnStdout: true, script: "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD -X POST -k -H '${env.contentTypeText}' -i ${env.jfrogArtifactoryURL}/${env.jfrogArtifactoryAQLPath} --data 'items.find({\"repo\":\"${env.jfrogPythonRepoName}\",\"@build.name\":{\"\$eq\":\"${env.monoRepoName}-profiling\"},\"@build.number\":{\"\$match\":\"${env.buildversion}*\"},\"name\":{\"\$match\":\"sds-solution-ei-profiling-application-*.zip\"},\"@build.parentName\":{\"\$eq\":\"${parentBranch}\"}}).sort({\"\$desc\": [\"created\"]}).limit(1)' | grep name | sed -e 's/.*\"\\(.*\\)\".*/\\1/'")).trim()
        println("${eiprofilingdep}")
        println("${eiprofilingapp}")
        def createbundlejson = [
            'release_bundle_name' : "${env.mainrepo}",
            'release_bundle_version': "${env.buildNumber}",
            'skip_docker_manifest_resolution': false,
            'source_type': 'aql',
            'source': [
                'aql': "items.find({\"\$or\":[{\"\$and\":[{\"repo\":{\"\$match\":\"${env.jfrogJavaRepoName}\"}},{\"name\":{\"\$match\":\"${eianalyticsjar}*\"}}]},{\"\$and\":[{\"repo\":{\"\$match\":\"${env.jfrogJavaRepoName}\"}},{\"name\":{\"\$match\":\"${validatorapijar}*\"}}]},{\"\$and\":[{\"repo\":{\"\$match\":\"${env.jfrogPythonRepoName}\"}},{\"name\":{\"\$match\":\"${eiprofilingdep}*\"}}]},{\"\$and\":[{\"repo\":{\"\$match\":\"${env.jfrogPythonRepoName}\"}},{\"name\":{\"\$match\":\"${eiprofilingapp}*\"}}]},{\"\$and\":[{\"repo\":{\"\$match\":\"${env.jfrogDockerRepoName}\"}},{\"name\":{\"\$match\":\"*${env.dockerImgTag}*\"}}]},{\"\$and\":[{\"repo\":{\"\$match\":\"${env.jfrogHelmRepoName}\"}},{\"name\":{\"\$match\":\"*${env.buildNumber}*\"}}]}]})"
            ]
        ]
                        
        writeJSON file: 'createbundle.json', json: createbundlejson
        sh "curl -u \$ARTIFACTORY_USER:\$ARTIFACTORY_PASSWORD  -H '${env.acceptJsonHeader}\' -H '${env.contentTypeJsonHeader}' -H '${env.jfrogSigningKeyHeader}' -X POST \"${env.jfrogArtifactoryURL}/${env.jfrogArtifactoryBundlePath}\" -T createbundle.json"
    }
}
def helmPackageAndPush(helmChartName, yamlFile, existingYAML){

    sh "helm dependency build ."
    sh "helm package . --version ${env.buildNumber}"
    uploadSpec = """{
    "files":
    [
        {
            "pattern": "${helmChartName}*.tgz",
            "target": "${env.jfrogHelmRepoName}/${env.repoName}/",
            "props": "project=${monoRepoName};repotype=${helmRepotype}"
        }
    ]
} """
    rtUpload(
    buildName: "${helmChartName}",
    buildNumber: "${env.buildNumber}",
    serverId: "${env.jfrogServerId}",
    spec: "${uploadSpec}"
)

    rtPublishBuildInfo(
    serverId: "${env.jfrogServerId}",
    buildName: "${helmChartName}",
    buildNumber: "${env.buildNumber}"
)

}
def helmBuild( helmChartName ) {
    def yamlFile = 'values.yaml'
    def existingYAML = readYaml file: yamlFile
    // existingYAML.expressionValidatorApi.image.expressionValidatorApi.tag = sharedimagetag.sdseivalidator
    existingYAML.eiConfigMergerApi.image.eiConfigMergerApi.tag = sharedimagetag.sdseiconfigmerger

    existingYAML.bundleVersion.solutionEi = "${env.buildNumber}"

    writeYaml file: yamlFile, data: existingYAML, overwrite: true
    println("${existingYAML}")

    
}
pipelines = [
    'QA-AUTOMATION' : {
        // Steps for qa-automation
        echo 'Running sds-jenkins-pipeline-templates/qa-automation'
        build job: 'sds-jenkins-pipeline-templates/docker-build-pipeline-job',
            parameters: [
                string(name: 'repo', value: "sds-solution-ei"),
                string(name: 'branch', value: env.BRANCH_NAME),
                string(name: 'buildNumber', value: env.buildNumber),
                string(name: 'tagMsg', value: env.tagmsg),
                string(name: 'buildVersion', value: env.buildversion),
                string(name: 'vers', value: env.dockerImgTag),
                booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                string(name: 'jfrogRepoName', value: "pai-sbt-local-dev"),
                string(name: 'subRepoName', value: "qa-automation"),
                string(name: 'changeLog', value: env.AFFECTED_FILES),
                booleanParam(name: 'enableUnitTest', value: false)

            ]
    },    
    'EI-ANALYTICS' : {
        // Steps for ei-analytics
        echo 'Running sds-jenkins-pipeline-templates/ei-analytics'
        def eianalyticsBuild = build job: 'sds-jenkins-pipeline-templates/sbt-jar-pipeline-job',
            parameters: [
                string(name: 'repo', value: "sds-solution-ei"),
                string(name: 'branch', value: env.BRANCH_NAME),
                string(name: 'buildNumber', value: env.buildNumber),
                string(name: 'tagMsg', value: env.tagmsg),
                string(name: 'buildVersion', value: env.buildversion),
                string(name: 'vers', value: env.dockerImgTag),
                booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                string(name: 'jfrogRepoName', value: "pai-sbt-local-dev"),
                string(name: 'subRepoName', value: "analytics"),
                string(name: 'changeLog', value: env.AFFECTED_FILES),
                string(name: 'label', value: "sbt"),
                string(name: 'container', value: "sbt-java-17-1-6-2"),
                booleanParam(name: 'enableUnitTest', value: false)

            ]
        env.eianalyticsCoverage = eianalyticsBuild.description
        echo "EI ANALYTICS Coverage: ${env.eianalyticsCoverage}"
    },
    // 'EUI' : {
    //     // Steps for ei-eui
    //     echo 'Running sds-jenkins-pipeline-templates/eui'
    //     build job: 'sds-jenkins-pipeline-templates/tar-build-pipeline-job',
    //         parameters: [
    //             string(name: 'repo', value: "sds-solution-ei"),
    //             string(name: 'branch', value: env.BRANCH_NAME),
    //             string(name: 'buildNumber', value: env.buildNumber),
    //             string(name: 'tagMsg', value: env.tagmsg),
    //             string(name: 'buildVersion', value: env.buildversion),
    //             string(name: 'vers', value: env.dockerImgTag),
    //             booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
    //             string(name: 'jfrogRepoName', value: "pai-general-local-dev"),
    //             string(name: 'subRepoName', value: "eui"),
    //             string(name: 'changeLog', value: env.AFFECTED_FILES)
    //         ]
    // },
    'CONFIGS' : {
        // Step for ei-configs
        echo 'Running sds-jenkins-pipeline-templates/ei-configs'
        def eiconfigBuild= build job: 'sds-jenkins-pipeline-templates/docker-build-pipeline-job',
            parameters: [
                string(name: 'repo', value: "sds-solution-ei"),
                string(name: 'branch', value: env.BRANCH_NAME),
                string(name: 'buildNumber', value: env.buildNumber),
                string(name: 'tagMsg', value: env.tagmsg),
                string(name: 'buildVersion', value: env.buildversion),
                string(name: 'vers', value: env.dockerImgTag),
                string(name: 'subRepoName', value: "configs"),
                string(name: 'buildTool', value: "python"),
                booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                booleanParam(name: 'enableUnitTest', value: false),
                string(name: 'changeLog', value: env.AFFECTED_FILES)

            ]
        env.eiconfigsCoverage = eiconfigBuild.description
        echo "EI CONFIGS Coverage: ${env.eiconfigsCoverage}"
    },
    'CONFIG-MERGER-API' : {
        // Steps for config-merger-api
        echo 'Running sds-jenkins-pipeline-templates/config-merger-api'
        build job: 'sds-jenkins-pipeline-templates/docker-build-pipeline-job',
            parameters: [
                string(name: 'repo', value: "sds-solution-ei"),
                string(name: 'branch', value: env.BRANCH_NAME),
                string(name: 'buildNumber', value: env.buildNumber),
                string(name: 'tagMsg', value: env.tagmsg),
                string(name: 'buildVersion', value: env.buildversion),
                string(name: 'vers', value: env.dockerImgTag),
                booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                string(name: 'subRepoName', value: "config-merger-api"),
                string(name: 'changeLog', value: env.AFFECTED_FILES)
            ]
    }
    ,
    'ORCHESTRATION' : {
        // Steps for ei orchestration
        echo 'Running sds-jenkins-pipeline-templates/ei-orchestration'
        def eiorchestrationBuild = build job: 'sds-jenkins-pipeline-templates/docker-build-pipeline-job',
            parameters: [
                string(name: 'repo', value: "sds-solution-ei"),
                string(name: 'branch', value: env.BRANCH_NAME),
                string(name: 'buildNumber', value: env.buildNumber),
                string(name: 'tagMsg', value: env.tagmsg),
                string(name: 'buildVersion', value: env.buildversion),
                string(name: 'vers', value: env.dockerImgTag),
                booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                string(name: 'subRepoName', value: "orchestration"),
                string(name: 'changeLog', value: env.AFFECTED_FILES),
                booleanParam(name: 'enableUnitTest', value: false),
                booleanParam(name: 'orchestrationBuild', value: true)
            ]
        env.eiorchestrationCoverage = eiorchestrationBuild.description
        echo "EI ORCHESTRATION Coverage: ${env.eiorchestrationCoverage}"
    },
    // 'VALIDATOR-API' : {
    //     // Steps for ei validator api
    //     echo 'Running sds-jenkins-pipeline-templates/validator-api'
    //     def validatorapiBuild = build job: 'sds-jenkins-pipeline-templates/sbt-jar-docker-pipeline-job',
    //         parameters: [
    //             string(name: 'repo', value: "sds-solution-ei"),
    //             string(name: 'branch', value: env.BRANCH_NAME),
    //             string(name: 'buildNumber', value: env.buildNumber),
    //             string(name: 'tagMsg', value: env.tagmsg),
    //             string(name: 'buildVersion', value: env.buildversion),
    //             string(name: 'vers', value: env.dockerImgTag),
    //             booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
    //             string(name: 'subRepoName', value: "validator-api"),
    //             string(name: 'changeLog', value: env.AFFECTED_FILES),
    //             string(name: 'label', value: "sbt"),
    //             string(name: 'container', value: "sbt-java-17-1-6-2"),
    //             booleanParam(name: 'enableUnitTest', value: false),
    //             string(name: 'dependency', value: "analytics")
    //         ]
    //     env.validatorCoverage = validatorapiBuild.description
    //     echo "VALIDATOR Coverage: ${env.validatorCoverage}"
    // },
    'VALIDATOR-API' : {
            // Steps for validator api
            echo 'Running sds-jenkins-pipeline-templates/validator-api'
            def validatorapiBuild = build job: 'sds-jenkins-pipeline-templates/validator-build-template',
                parameters: [
                    string(name: 'repo', value: "sds-solution-ei"),
                    string(name: 'branch', value: env.BRANCH_NAME),
                    string(name: 'buildNumber', value: env.buildNumber),
                    string(name: 'tagMsg', value: env.tagmsg),
                    string(name: 'buildVersion', value: env.buildversion),
                    string(name: 'vers', value: env.dockerImgTag),
                    booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                    string(name: 'jfrogRepoName', value: "pai-sbt-local-dev"),
                    string(name: 'subRepoName', value: "validator-api"),
                    string(name: 'changeLog', value: env.AFFECTED_FILES),
                    string(name: 'label', value: "sbt"),
                    string(name: 'container', value: "sbt-java-17-1-6-2"),
                    booleanParam(name: 'enableUnitTest', value: false)

                ]
            env.validatorapiBuild = validatorapiBuild.description
            echo "Vaildator Api Coverage: ${env.validatorapiBuild}"
        },
    'PROFILING' : {
        // Steps for ei validator api
        echo 'Running sds-jenkins-pipeline-templates/eui'
        build job: 'sds-jenkins-pipeline-templates/tar-build-pipeline-job',
            parameters: [
                string(name: 'repo', value: "sds-solution-ei"),
                string(name: 'branch', value: env.BRANCH_NAME),
                string(name: 'buildNumber', value: env.buildNumber),
                string(name: 'tagMsg', value: env.tagmsg),
                string(name: 'buildVersion', value: env.buildversion),
                string(name: 'vers', value: env.dockerImgTag),
                booleanParam(name: 'chkfullbuild', value: env.chkfullbuild),
                string(name: 'jfrogRepoName', value: "pai-pypi-local-dev"),
                string(name: 'subRepoName', value: "profiling"),
                string(name: 'changeLog', value: env.AFFECTED_FILES)
            ]
    }
]

pipeline {
    options {
        disableConcurrentBuilds()
    }
    agent {
        kubernetes {
            label 'debian'
            defaultContainer 'debian'
        }
    }
    environment {
        sharedimagetag = null
        mainrepo = 'sds-solution-ei'
        monoRepoName = 'sds-solution-ei'

        sbtAgentLabel = 'sbt'
        sbtAgentDefaultContainer = 'sbt-1-6-2'

        debianAgentLabel = 'debian'
        debianAgentDefaultContainer = 'debian'

        pythonAgentLabel = 'python'
        pythonAgentDefaultContainer = 'apt-pkg-python-3-8-12'

        dockerBuildAgentLabel = 'dockerBuild'
        dockerBuildDefaultContainer = 'cosign'

        helmAgentLabel = 'helm'
        helmAgentDefaultContainer = 'helm3-11-7'
        jenkinsFileName = 'k8sJenkinsfile'

        analyticsCompName = 'analytics'
        //euiCompName = 'eui'
        orchestrationCompName = 'orchestration'
        configMergerCompName = 'config-merger-api'
        configsCompName = 'configs'
        eiValidatorCompName = 'validator-api'

        // Need to check to be moved to env variable
        jfrogServerId = 'pe-jfrog'
        jfrogGenRepoName = 'pai-general-local-dev'
        jfrogJavaRepoName = 'pai-sbt-local-dev'
        jfrogHelmRepoName = 'pai-helm-local-dev'
        jfrogPythonRepoName = 'pai-pypi-local-dev'
        helmRepotype = 'helm'
        jfrogDockerRepoName = 'docker-generic-local'

        jfrogArtifactoryURL = 'https://prevalentai.jfrog.io'
        jfrogArtifactoryAQLPath = 'artifactory/api/search/aql'
        jfrogArtifactoryBundlePath = 'lifecycle/api/v2/release_bundle'

        contentTypeText = 'Content-Type:text/plain'
        contentTypeJsonHeader = 'Content-Type: application/json'
        acceptJsonHeader = 'Accept: application/json'
        jfrogSigningKeyHeader = 'X-JFrog-Signing-Key-Name: Monorepo'

        COSIGN_PASSWORD = credentials('cosign-password')
    }
    stages {
        //         // Stage decides full build / Incremental by checking git tags.
        stage('full build-check') {
            steps {
                script {
                    sharedimagetag = [: ]
                    def tagData = getTagDetails.gitTagRelease("KG")
                    env.buildNumber = tagData[0]
                    env.tagmsg = tagData[1]
                    env.buildversion = tagData[2]
                    env.chkfullbuild = checkFullbuild()
                    env.AFFECTED_FILES = changeSetLogs.getLogSet()
                    println("${env.AFFECTED_FILES}")
                    println(" The fullbuild condition is: ${env.chkfullbuild} ")
                    env.dockerImgTag = sh(script: "echo ${env.buildNumber} | sed \"s/+/-/g\" | sed \"s/\\./-/g\"", returnStdout: true).trim()
                }
            }
        }
        stage('parallel pipelines') {
            steps {
                script {
                    // Execute all jobs in parallel
                    parallel pipelines
                }
            }
        }
            // Parallel stages ends here
            stage('helm build') {
                agent {
                        kubernetes {
                            label "${debianAgentLabel}"
                            defaultContainer "${debianAgentDefaultContainer}"
                        }
                }
                when {
                        anyOf {
                            changeset '*/**'
                            expression { env.chkfullbuild == 'true' }
                        }
                }
                environment {
                    
                    registryCredential = 'dockerhub'
                    repoName = 'sds-solution-ei'
                    
                    nonJobHelmPath = 'deployments/helm/sds-solution-ei/'
                    jobHelmPath = 'deployments/helm/sds-solution-ei-jobs/'
                    deployerHelmPath = 'deployments/helm/sds-solution-ei-deployer/'
                }
                stages {
                    stage('values-file-update') {
                        agent {
                            kubernetes {
                                label "${helmAgentLabel}"
                                defaultContainer "${helmAgentDefaultContainer}"
                            }
                        }
                        steps {
                            script {
                                dir("${nonJobHelmPath}") {
                                    def helmChartName = "${repoName}"
                                    def yamlFile = 'values.yaml'
                                    def existingYAML = readYaml file: yamlFile
                                    // existingYAML['sds-solution-ei-expression-validator-api']['image']['tag'] = "${env.dockerImgTag}"
                                    existingYAML['sds-solution-ei-config-merger-api']['image']['tag'] = "${env.dockerImgTag}"                    
                                    writeYaml file: yamlFile, data: existingYAML, overwrite: true
                                    println("${existingYAML}")
                                    helmPackageAndPush( helmChartName, yamlFile, existingYAML  )
                                }
                                dir("${jobHelmPath}") {
                                    def  helmChartName = "${repoName}-jobs"
                                    def yamlFile = 'values.yaml'
                                    def existingYAML = readYaml file: yamlFile
                                    existingYAML.bundleVersion.solutionEi = "${env.buildNumber}"
                                    existingYAML['jobOverrides']['sds_ei_dag_deploy_job'] ['image']['tag'] = "${env.dockerImgTag}"          
                                    writeYaml file: yamlFile, data: existingYAML, overwrite: true
                                    helmPackageAndPush( helmChartName, yamlFile, existingYAML  )
                                }
                                dir("${deployerHelmPath}") {

                                    helmAppofappBuild()
                                }
                            }
                        }
                    }
                    stage('annotate tag') {
                        agent {
                                kubernetes {
                                    label "${debianAgentLabel}"
                                    defaultContainer "${debianAgentDefaultContainer}"
                                }
                        }
                            steps {
                                script {
                                annotateTag()
                                }
                            }
                    }
                }
            }

            stage('release-bundle creation') {
                when {
                    anyOf {
                        changeset '*/**'
                        expression {
                            env.chkfullbuild == 'true'

                        }
                    }
                }
                agent {
                        kubernetes {
                            label "${helmAgentLabel}"
                            defaultContainer "${helmAgentDefaultContainer}"
                        }
                }
                steps {
                    script {
                        releaseBundleCreation()
                        deploy.notifyOnBuild(buildVersion: "${env.buildNumber}")
                        highlightCoverageStatusMono(buildVersion:"${env.buildNumber}")
                        currentBuild.description = "${env.buildNumber}"
                        // currentBuild.displayName = "${env.buildNumber}"
                        promoteBuild("${env.buildNumber}")
                    }
                }
            }

            stage('release-bundle notification') {
                when {
                        allOf {
                        not {
                            changeset '*/**'
                        }
                        expression {
                            env.chkfullbuild != 'true'
                        }
                        }
                }
                agent {
                    kubernetes {
                        label "${debianAgentLabel}"
                        defaultContainer "${debianAgentDefaultContainer}"
                    }
                }
                steps {
                    script {
                        gittag = getTagDetails.getLatestReleaseBundle()
                        deploy.notifyOnBuild(buildVersion: "${gittag}")
                        currentBuild.description = "${gittag}"
                        // currentBuild.displayName = "${gittag}"
                    }
                }
            }
        }
    }

