import sbt._
import Keys.{libraryDependencies, _}

unmanagedBase := baseDirectory.value / "lib"

ThisBuild / name := "sds-ei-analytics"
ThisBuild / scalaVersion := "2.13.9"
ThisBuild / version := sys.env.getOrElse("buildVersion", "1.0.1")
ThisBuild / artifactName := { (sv: ScalaVersion, module: ModuleID, artifact: Artifact) =>
  artifact.name + "_" + sv.binary + "-" + module.revision + "." + artifact.extension
}
lazy val coreRepoName = ProjectRef(uri("ssh://**************/prevalent-ai/sds-pe-core.git#spark_3.5.1-iceberg-read-ei-2.13"), "sds-pe-core")
assembly / assemblyJarName := s"${name.value}_${scalaBinaryVersion.value}-${version.value}.${artifact.value.extension}"

Test / parallelExecution := false
fork in Test := true
javaOptions ++= Seq("-Xms8G", "-Xmx8G", "-XX:MaxMetaspaceSize=1G", "-XX:+UseG1GC")


javaOptions in Test ++= Seq(
  "--add-opens=java.base/java.lang=ALL-UNNAMED",
  "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
  "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
  "--add-opens=java.base/java.io=ALL-UNNAMED",
  "--add-opens=java.base/java.net=ALL-UNNAMED",
  "--add-opens=java.base/java.nio=ALL-UNNAMED",
  "--add-opens=java.base/java.util=ALL-UNNAMED",
  "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
  "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
  "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
  "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
  "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
  "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
  "-Xms1G",
  "-Xmx4G",
  "-XX:ReservedCodeCacheSize=512M",
  "-XX:MaxMetaspaceSize=512M"
)

fork in Test := true

lazy val root = Project(id = "sds-ei-analytics", base = file("."))
  .settings(
    libraryDependencies += "org.apache.spark" %% "spark-core" % "3.5.1" % "provided",
    libraryDependencies += "org.apache.spark" %% "spark-sql" % "3.5.1" % "provided",
    libraryDependencies += "org.apache.spark" %% "spark-graphx" % "3.5.1" % "provided",
//    libraryDependencies += "org.scala-lang" % "scala-library" % "2.12.15" % "provided",
    // https://mvnrepository.com/artifact/org.jgrapht/jgrapht-core
    libraryDependencies += "org.jgrapht" % "jgrapht-core" % "1.3.1",



    libraryDependencies += "org.scalatest" %% "scalatest" % "3.2.2" % Test,
    libraryDependencies += "com.holdenkarau" %% "spark-testing-base" % "3.5.1_1.5.3" % Test,
    libraryDependencies += "ch.qos.logback" % "logback-classic" % "1.2.6" % Test,
    libraryDependencies +="com.github.tomakehurst" % "wiremock" % "1.33" % Test
  )
  .dependsOn(coreRepoName)

assembly / assemblyMergeStrategy := {
  case x if Assembly.isConfigFile(x) =>
    MergeStrategy.concat
  case PathList(ps@_*) if Assembly.isReadme(ps.last) || Assembly.isLicenseFile(ps.last) =>
    MergeStrategy.rename
  case PathList("META-INF", "versions", "9", "module-info.class") => MergeStrategy.discard
  case "module-info.class" => MergeStrategy.discard
  case PathList("META-INF", xs@_*) =>
    xs map {
      _.toLowerCase
    } match {
      case "manifest.mf" :: Nil | "index.list" :: Nil | "dependencies" :: Nil =>
        MergeStrategy.discard
      case ps@x :: xs if ps.last.endsWith(".sf") || ps.last.endsWith(".dsa") =>
        MergeStrategy.discard
      case "plexus" :: xs =>
        MergeStrategy.discard
      case "services" :: xs =>
        MergeStrategy.filterDistinctLines
      case "spring.schemas" :: Nil | "spring.handlers" :: Nil =>
        MergeStrategy.filterDistinctLines
      case _ => MergeStrategy.first
    }
  case _ => MergeStrategy.first
}