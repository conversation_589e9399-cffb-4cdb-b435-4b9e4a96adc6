package ai.prevalent.entityinventory.relation.disambiguation

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{KG_CONTENT_TYPE, UPDATED_AT_TS}
import ai.prevalent.entityinventory.relationship.disambiguation.{DisambiguationUtils, OutputDF}
import ai.prevalent.entityinventory.relationship.disambiguation.DisambiguationUtils.enrichRelationGraphDetails
import ai.prevalent.entityinventory.relationship.disambiguation.config._
import ai.prevalent.entityinventory.relationship.disambiguation.config.grouping.{VariablesBased, VulnerabilityFinding}
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.SchemaEvolutionUtil
import ai.prevalent.sdspecore.sparkbase.SDSConf.spark
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{col, days, lit}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.io.File
import java.util.Random
import scala.reflect.io.Directory

class DisambiguationUtilsSpec extends AnyFlatSpec with IcebergSparkTestWrapper with BeforeAndAfter {
  private var writer: SDSTableWriter = _
  private var reader: SDSTableReader = _

  override def warehousePath: String = {
    val rand = new Random().nextInt(1000)
    new Directory(new File(s"${getClass().getResource("/").getPath}/iceberg_warehouse__$rand")).createDirectory()
    getClass().getResource(s"/iceberg_warehouse__$rand").getPath
  }
  override def beforeAll(): Unit = {
    super.beforeAll()
    writer = SDSTableWriterFactory.getDefault(spark)
    reader = SDSTableReaderFactory.getDefault(spark)
    import org.apache.spark.sql.functions.{col, from_unixtime}
    val A = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/data/defender.json").getPath
    val B = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/data/qualys.json").getPath
    val dataA = spark.read.json(A)
          .withColumn("updated_at_ts", from_unixtime(col("updated_at_ts") / 1000).cast("timestamp"))
          .withColumn("relationship_first_seen_date", col("first_seen_date"))
          .withColumn("relationship_last_seen_date", col("end_epoch"))
    val dataB = spark.read.json(B)
          .withColumn("updated_at_ts", from_unixtime(col("updated_at_ts") / 1000).cast("timestamp"))
          .withColumn("relationship_last_seen_date", col("end_epoch"))
        writer.overwritePartition(dataA, "ei_rel.rel_def")
        writer.overwritePartition(dataB, "ei_rel.rel_qualys")
  }


  "DisambiguationUtils" should "build a DataFrame" in {
    val resolvedPath = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/resolvedDf").getPath
    val nonresolvedPath = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/nonresolvedDf").getPath
    val resolvedPathCSV = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/").getPath +"resolvedcsv"
    val resolverPathCSV = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/").getPath + "resolvercsv"
    val nonresolvedPathCSV="file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/").getPath + "nonresolvedcsv"
    val resolverPath = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/resolverDf").getPath
    val params = new EIJobArgs
        params.currentUpdateDate = 1680000000000L


    val config = Config(
      relationshipModels = List(RelationshipModel("ei_rel.rel_qualys", "qualys","software_version not like '%10.0.99621.1265%'"), RelationshipModel("ei_rel.rel_def", "defender","software_version not like '%10.0.99621.1265%'")),
      disambiguation = RelDisambiguation(disambiguationGrouping = VulnerabilityFinding(List("source_p_id", "target_p_id", "software_name", "software_vendor", "software_version"), "current_status", List("rel_source_name")),
        excludeValues = Array.empty,
        strategy = Some(Strategy(fieldLevelConfidenceMatrix = None, rollingUpFields = Array("origin", "vendor_status", "abc", "ms_recommended_update", "ms_recommended_update_id"), valueConfidence = None, singleSource = List.empty))
      ),
      derivedProperties = Array.empty,
      output = Output(
        disambiguatedModelLocation = "ei_rel.rel_vul_findings",
        resolverLocation = "ei_rel.resolver__rel_vul_findings",
        fragmentLocation = "ei_rel.fragment__rel_vul_findings"
      )
    )
    val result = DisambiguationUtils.build(config, params, reader, spark)
    val expectedResolvedRdd = spark.read.parquet(resolvedPath)
    val expectednonResolvedRdd = spark.read.parquet(nonresolvedPath)
    val expectedResolverRdd = spark.read.parquet(resolverPath)

    println(resolvedPathCSV)
    result.resolvedDF.repartition(1).write.mode("overwrite").parquet(resolvedPathCSV)
    result.resolverDF.repartition(1).write.mode("overwrite").parquet(resolverPathCSV)
    result.nonResolvedDF.repartition(1).write.mode("overwrite").parquet(nonresolvedPathCSV)
    assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedResolvedRdd,result.resolvedDF.drop(KG_CONTENT_TYPE).schema).orderBy("relationship_id"),
      SchemaEvolutionUtil.evolveSchema(result.resolvedDF.drop(KG_CONTENT_TYPE),result.resolvedDF.drop(KG_CONTENT_TYPE).schema).orderBy("relationship_id"))
    assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectedResolverRdd,result.resolverDF.schema).orderBy("relationship_id"),
      SchemaEvolutionUtil.evolveSchema(result.resolverDF,result.resolverDF.schema).orderBy("relationship_id"))
    assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expectednonResolvedRdd, result.nonResolvedDF.drop(KG_CONTENT_TYPE).schema).orderBy("relationship_id"),
      SchemaEvolutionUtil.evolveSchema(result.nonResolvedDF.drop(KG_CONTENT_TYPE), result.nonResolvedDF.drop(KG_CONTENT_TYPE).schema).orderBy("relationship_id"))
    SchemaEvolutionUtil.evolveSchema(expectednonResolvedRdd, result.nonResolvedDF.drop(KG_CONTENT_TYPE).drop(KG_CONTENT_TYPE).schema).orderBy("relationship_id").show(false)
    SchemaEvolutionUtil.evolveSchema(result.nonResolvedDF.drop(KG_CONTENT_TYPE), result.nonResolvedDF.drop(KG_CONTENT_TYPE).schema).orderBy("relationship_id").show(false)
    assert(result.resolvedDF.columns.contains("abc"), true)
    assert(result.resolvedDF.select("last_reopened_date").distinct().rdd.collect().map(_.get(0)).diff(Array(168000000001L, null)), Array.empty)

//    fragments and resolver table

    val relationshipName = result.resolvedDF.select("relationship_name").first().getString(0)
    fragmentAndResolverWriter(relationshipName,result.nonResolvedDF,result.resolverDF,config)
    val outfrag = reader.read("ei_rel.fragment__rel_vul_findings")
    val outresol = reader.read("ei_rel.resolver__rel_vul_findings")

    val resolTableProps = spark.sql("SHOW TBLPROPERTIES iceberg_catalog.ei_rel.resolver__rel_vul_findings").collect()
    val resolPropertiesMap = resolTableProps.map(row => row.getString(0) -> row.getString(1)).toMap
    assert(resolPropertiesMap("graph.cache.enabled") == "true")
    assert(resolPropertiesMap("graph.edge.name") == "VULNERABILITY_FINDING_ON_HOST Has Fragment")
    assert(resolPropertiesMap("graph.edge.source.name") == "VULNERABILITY_FINDING_ON_HOST")
    assert(resolPropertiesMap("graph.edge.target.name") == "Fragment VULNERABILITY_FINDING_ON_HOST")

    val fragTableProps = spark.sql("SHOW TBLPROPERTIES iceberg_catalog.ei_rel.fragment__rel_vul_findings").collect()
    val fragPropertiesMap = fragTableProps.map(row => row.getString(0) -> row.getString(1)).toMap
    assert(fragPropertiesMap("graph.cache.enabled") == "true")
    assert(fragPropertiesMap("graph.vertex.name") == "Fragment VULNERABILITY_FINDING_ON_HOST")
  }

  "DisambiguationUtils" should "build an empty DataFrame" in {
    val relDf1 = spark.emptyDataFrame.withColumn("updated_at_ts", lit("null"))
      .withColumn("relationship_first_seen_date", lit("null"))
      .withColumn("relationship_last_seen_date", lit("null"))
    writer.overwritePartition(relDf1, "ei_rel.rel_wiz")
    val config = Config(
      relationshipModels = List(
        RelationshipModel("ei_rel.rel_wiz", "wiz"),
        RelationshipModel("ei_rel.rel_wiz_test", "wiz_t")
      ),
      disambiguation = RelDisambiguation(
        disambiguationGrouping = VariablesBased(
          List("source_p_id",
            "target_p_id")
        ),
        excludeValues = Array.empty,
        strategy = Some(Strategy(
          fieldLevelConfidenceMatrix = None,
          rollingUpFields = Array("origin"),
          valueConfidence = None,
          singleSource = List.empty
        ))
      ),
      derivedProperties=Array.empty,
      output = Output(
        disambiguatedModelLocation = "ei_rel.rel_variable_based",
        resolverLocation = "ei_rel.resolver",
        fragmentLocation = "ei_rel.rel_non_resolved"
      )
    )
    val params = new EIJobArgs
    params.currentUpdateDate = 1680000000000L
    val output: OutputDF = DisambiguationUtils.build(config, params, reader, spark)
    assert(output.resolverDF.count(),0)
    assert(output.resolvedDF.count(),0)
    assert(output.nonResolvedDF.count(),0)
  }


  "DisambiguationUtils" should "build df without blockcloseVar case: open open close open" in{
    val blockclose_exp = "file:" + getClass.getResource("/entityinventory/relation/disambiguation/expected/expected_blockClose").getPath
    val config_without_blockclose = Config(
      relationshipModels = List(RelationshipModel("ei_rel.rel_qualys", "qualys","software_version like '%10.0.99621.1265%'"), RelationshipModel("ei_rel.rel_def", "defender","software_version like '%10.0.99621.1265%'")),
      disambiguation = RelDisambiguation(disambiguationGrouping = VulnerabilityFinding(List("source_p_id", "target_p_id", "software_name", "software_vendor", "software_version"),"current_status",List()),
        excludeValues = Array.empty,
        strategy = Some(Strategy(fieldLevelConfidenceMatrix = None, rollingUpFields = Array("origin", "vendor_status", "abc", "ms_recommended_update", "ms_recommended_update_id"), valueConfidence = None, singleSource = List.empty))
      ),
      derivedProperties=Array.empty,
      output = Output(
        disambiguatedModelLocation = "ei_rel.rel_vul_findings",
        resolverLocation = "ei_rel.resolver__rel_vul_findings",
        fragmentLocation = "ei_rel.fragment__rel_vul_findings"
      )
    )
    val params = new EIJobArgs
    params.currentUpdateDate = 1680000000000L
    val result_without_blockclose = DisambiguationUtils.build(config_without_blockclose, params, reader, spark)
    val expec = spark.read.parquet(blockclose_exp)
    assertDataFrameDataEquals(SchemaEvolutionUtil.evolveSchema(expec, result_without_blockclose.resolvedDF.schema).orderBy("relationship_id"),
          result_without_blockclose.resolvedDF.orderBy("relationship_id"))
  }

  def getWriter(tableProps: Map[String, String] = Map.empty): SDSTableWriter = SDSTableWriterFactory.getDefault(
    spark, tableProperties = tableProps, options = Map("partitionOverwriteMode" -> "dynamic"))

  def fragmentAndResolverWriter(relationshipName: String,nonresolvedDF: DataFrame, resolverDF: DataFrame, config: Config): Unit = {
    if (config.output.isFragmentOLAPTable) {
      val (nullRemovedResolverDf, nullRemovedNonResolvedDf) = enrichRelationGraphDetails(resolverDF, relationshipName, nonresolvedDF)
      val fragmentTableProps = Map("graph.vertex.name" -> s"Fragment $relationshipName", "graph.cache.enabled" -> "true")
      val resolverTableProps = Map("graph.edge.name" -> s"${relationshipName} Has Fragment", "graph.edge.source.name" -> relationshipName, "graph.edge.target.name" -> s"Fragment $relationshipName", "graph.cache.enabled" -> "true")
      getWriter(fragmentTableProps).overwritePartition(nullRemovedNonResolvedDf, config.output.fragmentLocation, Array(days(col(UPDATED_AT_TS))))
      getWriter(resolverTableProps).overwritePartition(nullRemovedResolverDf, config.output.resolverLocation, Array(days(col(UPDATED_AT_TS))))
    } else {
      val nullRemovedResolverDf = removeNullFields(resolverDF)
      getWriter().overwritePartition(nullRemovedResolverDf, config.output.resolverLocation, Array(days(col(UPDATED_AT_TS)), col("relationship_name"), col("data_source_name"))
      )
    }
  }

}

