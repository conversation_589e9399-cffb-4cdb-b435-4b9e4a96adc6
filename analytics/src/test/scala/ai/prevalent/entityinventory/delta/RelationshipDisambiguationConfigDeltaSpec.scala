package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.relationship.disambiguation.Disambiguation
import ai.prevalent.entityinventory.relationship.disambiguation.config.Config
import ai.prevalent.sdspecore.utils.ConfigUtils
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec

class RelationshipDisambiguationConfigDeltaSpec  extends AnyFlatSpec with BeforeAndAfterAll with DataFrameSuiteBase{
    var idConfigOld: Config = _
  var idConfigNew:Config = _


  override def beforeAll : Unit = {
    super.beforeAll()
    val newPath = "file:" + getClass.getResource("/entityinventory/delta/sds_ei__rel__assessment_measuring_security_control__job_config_new.json").getPath
    val oldPath = "file:" + getClass.getResource("/entityinventory/delta/sds_ei__rel__assessment_measuring_security_control__job_config_old.json").getPath
    idConfigOld = ConfigUtils.getConfig(spark, oldPath , manifest[Config], Disambiguation.configFormats)
    idConfigNew = ConfigUtils.getConfig(spark, newPath, manifest[Config], Disambiguation.configFormats)
  }

//  "it" should "should show changes in relationship grouping type" in {
//    val modelChange = RelationshipDisambiguationConfigDelta.checkDisambiguationGrpingDeltaChange(idConfigOld, idConfigNew)
//    modelChange.map(ch =>{
//      println(ch.name)
//    })
//  }

  "it" should "should show changes in rel model input" in {
    val modelChange = RelationshipDisambiguationConfigDelta.checkModelRelationModelUpdate(idConfigOld, idConfigNew)
    println(modelChange.mkString("Array(", ", ", ")"))
  }

  "it" should "should show changes in output filter" in {
    val filterChange = RelationshipDisambiguationConfigDelta.checkOutputFilterChange(idConfigOld, idConfigNew)
    println(filterChange.mkString("Array(", ", ", ")"))
  }






}
