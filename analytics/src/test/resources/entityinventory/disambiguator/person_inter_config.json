{"inventoryModelInput": [{"path": "ei.sds_ei__person__a", "name": "a"}, {"path": "ei.sds_ei__person__b", "name": "b"}], "disambiguation": {"candidateKeys": [{"name": "fqdn", "exceptionFilter": "fqdn='fqdn343'"}, "azure_id", {"name": "case_sens_key", "caseSensitive": true}], "confidenceMatrix": ["a", "b"], "excludeValues": ["Unknown", "Other", "-"], "strategy": {"fieldLevelConfidenceMatrix": [{"field": "type", "confidenceMatrix": ["b", "a"]}], "rollingUpFields": ["origin", "ip", "abcd"], "valueConfidence": [{"field": "activity_status", "confidenceMatrix": ["Active", "Inactive"]}]}}, "output": {"disambiguatedModelLocation": "ei_frag.sds_ei__person", "fragmentLocation": "ei_frag.sds_ei__fragment__person", "resolverLocation": "ei_frag.sds_ei__resolver_fragment__person"}, "entity": {"name": "Person", "defaultEntitySpec": {"fieldSpec": {}}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn", "abcd"], "commonProperties": [{"colName": "inactivity_period", "colExpr": "cast(abcd as string)"}], "entitySpecificProperties": [{"colName": "abcdefgh", "colExpr": "cast(hhhhhh as string)"}, {"colName": "abcdefgh1", "colExpr": "coalesce(vm_product,dns_name__resolved,host_name__resolved,aad_device_id,primary_key)"}]}}