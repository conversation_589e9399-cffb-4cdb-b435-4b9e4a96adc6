{"relationshipModels": [{"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__azure_regulatory_compliance_assessments__assessments_measuring_security_controls", "name": "sds_ei__rel__azure_regulatory_compliance_assessments__assessments_measuring_security_controls"}, {"tableName": "<%EI_SCHEMA_NAME%>.sds_ei__rel__aws_sh_findings__assessments_measuring_security_controls", "name": "sds_ei__rel__aws_sh_findings__assessments_measuring_security_controls"}], "disambiguation": {"disambiguationGrouping": {"type": "VariablesBased", "blockVariables": ["source_p_id", "target_p_id"]}, "strategy": {"rollingUpFields": ["origin", "relationship_origin"]}}, "output": {"disambiguatedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei__rel__assessment_measuring_security_control", "resolverLocation": "<%EI_SCHEMA_NAME%>.sds_ei_rel_disambiguation_resolver", "nonResolvedModelLocation": "<%EI_SCHEMA_NAME%>.sds_ei_relationship_non_resolved"}}