{"relationshipModels": [{"tableName": "ei_rel.rel_qualys", "name": "qualys"}, {"tableName": "ei_rel.rel_def", "name": "defender"}], "disambiguation": {"disambiguationGrouping": {"type": "VulnerabilityFinding", "blockVariables": ["source_p_id", "target_p_id", "software_name", "software_vendor", "software_version"], "statusField": "current_status"}, "strategy": {"rollingUpFields": ["origin", "vendor_status", "ms_recommended_update", "ms_recommended_update_id"], "aggregation": [{"field": "last_reopened_date", "function": "max"}]}}, "output": {"disambiguatedModelLocation": "ei_rel.rel_vul_findings", "resolverLocation": "ei_rel.rel_vul_findings_inter", "nonResolvedModelLocation": "ei_rel.non_resolved_vul_findings"}}