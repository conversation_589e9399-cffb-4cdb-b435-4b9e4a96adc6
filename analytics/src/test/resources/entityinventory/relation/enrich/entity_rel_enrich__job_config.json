{"entityTableName": "ei_edm_sit_v5.sds_ei__host", "countEnriches": [], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__rel__person_owns_host", "alias": "person_owns_host", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "earliest_person", "aggExpr": "person_owns_host.source_display_label", "aggFunction": "EARLIEST", "colExpr": "earliest_person"}, {"colName": "latest_person", "aggExpr": "person_owns_host.source_display_label", "aggFunction": "LATEST", "colExpr": "latest_person"}, {"colName": "has_owned_person_count_new", "aggExpr": "person_owns_host.source_p_id", "aggFunction": "count", "colExpr": "has_owned_person_count_new"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__person", "alias": "person", "filter": "person.activity_status='Active'", "preTransform": [{"colName": "non_null_location_country", "colExpr": "CASE WHEN person.location_country is NULL THEN 'Unknown' ELSE person.location_country END", "fieldsSpec": {"isInventoryDerived": false, "convertEmptyToNull": true, "caseSensitiveExpression": false}}], "colEnrichments": [{"colName": "sjns", "aggExpr": "non_null_location_country", "aggFunction": "collect_set", "colExpr": "sjns"}, {"colName": "active_person_list", "aggExpr": "person.display_label", "aggFunction": "collect_set", "colExpr": "active_person_list"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__rel__person_has_identity", "alias": "person_has_identity_1", "filter": "true", "preTransform": [], "colEnrichments": [], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__identity", "alias": "identity1", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "host_identity_types", "aggExpr": "identity1.type", "aggFunction": "collect_set", "colExpr": "host_identity_types"}, {"colName": "latest_identity_date_1", "aggExpr": "identity1.ad_created_date", "aggFunction": "max", "colExpr": "latest_identity_date_1"}, {"colName": "person_dist_count_w_ide", "aggExpr": "person.p_id", "aggFunction": "count_distinct"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__rel__identity_has_account", "alias": "identity_has_account", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "host_identity_type", "aggExpr": "identity1.type", "aggFunction": "collect_set", "colExpr": "host_identity_type"}, {"colName": "latest_identity_date", "aggExpr": "identity_has_account.ad_created_date", "aggFunction": "max", "colExpr": "latest_identity_date"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__account", "alias": "account", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "concat_identity_account_service", "aggExpr": "concat(identity1.identity_provider, ':', account.ad_domain)", "aggFunction": "collect_set", "colExpr": "concat_ws(',',concat_identity_account_service)"}], "inheritedPropertyEnrichment": []}]}]}]}]}, {"tableName": "ei_edm_sit_v5.sds_ei__rel__person_has_identity", "alias": "person_has_identity_2", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "identity_recency_min", "aggExpr": "person_has_identity_2.recency_relationship", "aggFunction": "min", "colExpr": "identity_recency_min"}, {"colName": "identity_recency_max", "aggExpr": "person_has_identity_2.recency_relationship", "aggFunction": "max", "colExpr": "identity_recency_max"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__identity", "alias": "identity", "filter": "identity.status='Active'", "preTransform": [], "colEnrichments": [{"colName": "earliest_identity_date", "aggExpr": "identity.ad_created_date", "aggFunction": "min", "colExpr": "earliest_identity_date"}, {"colName": "min_account_status", "aggExpr": "identity.activity_status", "aggFunction": "collect_set", "colExpr": "CASE WHEN array_contains(min_account_status, 'Active') THEN 'Active' ELSE 'Inactive' END"}], "inheritedPropertyEnrichment": []}]}]}, {"tableName": "ei_edm_sit_v5.sds_ei__rel__vulnerability_finding_on_host", "alias": "vulnerability_finding_on_host", "filter": "current_status='Open'", "preTransform": [], "colEnrichments": [{"colName": "has_open_vulnerability_count_new", "aggExpr": "vulnerability_finding_on_host.source_p_id", "aggFunction": "count", "colExpr": "has_open_vulnerability_count_new"}], "inheritedPropertyEnrichment": [{"tableName": "ei_edm_sit_v5.sds_ei__vulnerability", "alias": "vulnerability", "filter": "true", "preTransform": [], "colEnrichments": [{"colName": "count_non_patch_vul", "aggExpr": "CASE WHEN vulnerability.patch_available = true THEN 1 ELSE 0 END", "aggFunction": "SUM", "colExpr": "count_non_patch_vul"}], "inheritedPropertyEnrichment": []}]}], "derivedProperties": [], "output": {"outputTableName": "ei_enrich.sds_ei__entity_enrich"}}