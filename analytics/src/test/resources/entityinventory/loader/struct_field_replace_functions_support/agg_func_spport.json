{"primaryKey": "temp_resource_id", "filterBy": "lower(regexp_extract(temp_resource_id,'\\/providers\\/([^\\/]+\\/[^\\/]+)\\/[^\\/]+')) IN ('microsoft.compute/virtualmachines','microsoft.compute/virtualmachines/extensions','microsoft.compute/virtualmachinescalesets','microsoft.containerservice/managedclusters','microsoft.containerregistry/registries','microsoft.containerinstance/containergroups')", "origin": "'MS Azure Security Center Alerts'", "sourceTable": "srdm.microsoft_azure__security_center_alerts", "outputTable": "ei_temp.sds_ei__host__active_directory__object_guid", "dataSource": {"name": "Microsoft", "feedName": "sjdasjd", "srdm": "sdm.microsoft__active_directory"}, "temporaryProperties": [{"colName": "temp_resource_identifiers", "colExpr": "explode(properties.resourceIdentifiers)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_resource_id", "colExpr": "temp_resource_identifiers.azureResourceId"}, {"colName": "temp_abc", "colExpr": "coalesce(account_name,ad_sam_account_name, temp_resource_id)"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualMachineScaleSets%' THEN 'Virtual Machine Scalesets' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualmachines%' OR (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%virtualMachines%' THEN 'Virtual Machines' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%extensions%' THEN 'Virtual Machine Extensions' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%managedclusters%' OR (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%managedClusters%' THEN 'Managed Clusters' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%registries%' THEN 'Container Registries' WHEN (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) LIKE '%containergroups%' THEN 'Container Groups' ELSE (regexp_extract(resource_id, '\\/([^\\/]+)\\/(?:[^\\/]+)$')) END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "last_found_date", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "resource_name", "colExpr": "lower(regexp_extract(resource_id,'\\/([^\\/]+)$'))", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "resource_id", "colExpr": "lower(primary_key)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "cloud_provider", "colExpr": "'Azure'", "fieldsSpec": {"isInventoryDerived": true}}], "entity": {"fieldSpec": {"persistNonNullValue": true}, "name": "Cloud Compute", "lastUpdateFields": ["os_family"]}}