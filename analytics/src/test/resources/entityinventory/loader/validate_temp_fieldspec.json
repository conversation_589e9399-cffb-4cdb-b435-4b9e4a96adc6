{"primaryKey": "temp_primary_key", "origin": "'Qualys <PERSON>'", "outputTable": "ei_temp.sds_ei__host__active_directory__object_guid", "dataSource": {"name": "Microsoft", "feedName": "sjdasjd", "srdm": "sdm.microsoft__active_directory"}, "temporaryProperties": [{"colName": "temp_cve_id", "colExpr": "explode_outer(from_json(cve, 'array<struct<ID:string, URL:string>>').ID)", "fieldsSpec": {"convertEmptyToNull": false}}, {"colName": "temp_primary_key", "colExpr": "concat_ws('||', qid, temp_cve_id)"}], "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN LOWER(vuln_type) LIKE '%potential vulnerability%' THEN 'Weakness' WHEN LOWER(vuln_type) LIKE '%vulnerability%'  THEN 'Vulnerability' WHEN LOWER(vuln_type) LIKE '%information gathered%' THEN 'Informational' END"}, {"colName": "description", "colExpr": "diagnosis"}, {"colName": "first_seen_date", "colExpr": "cast(null as bigint)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "title"}, {"colName": "cve_id", "colExpr": "temp_cve_id"}, {"colName": "v30_score", "colExpr": "CAST(cvss_v3_base AS DOUBLE)"}, {"colName": "temporal_cvss_score", "colExpr": "CAST(cvss_v3_temporal AS DOUBLE)"}, {"colName": "v30_vector", "colExpr": "cvss_v3_vector_string"}, {"colName": "v30_severity", "colExpr": "CASE WHEN v30_score>9 THEN 'Critical' WHEN v30_score>7 THEN 'High' WHEN v30_score>4 THEN 'Medium' WHEN v30_score>0 THEN 'Low' WHEN v30_score=0 THEN 'None' END", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "software_list"}, {"colName": "patch_available", "colExpr": "CASE WHEN patchable==1 THEN true WHEN patchable==0 THEN false ELSE null END"}, {"colName": "recommendation", "colExpr": "solution"}, {"colName": "published_date", "colExpr": "published_datetime_epoch"}, {"colName": "last_modified_date", "colExpr": "last_service_modification_datetime_epoch"}, {"colName": "vendor_severity", "colExpr": "INITCAP(severity)"}], "sourceSpecificProperties": [{"colName": "vendor_id", "colExpr": "qid"}, {"colName": "found_in_organisation", "colExpr": "false", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "qualys_pci_flag", "colExpr": "pci_flag"}, {"colName": "qualys_consequence", "colExpr": "consequence"}, {"colName": "qualys_category", "colExpr": "category"}, {"colName": "bugtraq_id", "colExpr": "REGEXP_EXTRACT_ALL(cast(bugtraq_list as String), '[\"\\']ID[\"\\']\\\\s*:\\\\s*[\"\\']([^\\'\"]*)[\"\\']')"}, {"colName": "qualys_threat_intel", "colExpr": "to_json(transform(from_json(threat_intel_id,'ARRAY<STRUCT<id:STRING,value:String>>'), s -> CASE WHEN s.id = 1 THEN 'Zero Day' WHEN s.id = 2 THEN 'Exploit_Public' WHEN s.id = 3 THEN 'Active_Attacks' WHEN s.id = 4 THEN 'High_Lateral_Movement' WHEN s.id = 5 THEN 'Easy_Exploit' WHEN s.id = 6 THEN 'High_Data_Loss' WHEN s.id = 7 THEN 'Denial_of_Service' WHEN s.id = 8 THEN 'No_Patch' WHEN s.id = 9 THEN 'Malware' WHEN s.id = 10 THEN 'Exploit_Kit' WHEN s.id = 11 THEN 'Wormable' WHEN s.id = 12 THEN 'Predicted_High_Risk' WHEN s.id = 13 THEN 'Privilage_Escalation' WHEN s.id = 14 THEN 'Unauthenticated_Exploitation' WHEN s.id = 15 THEN 'Remote_Code_Execution' WHEN s.id = 16 THEN 'Ransomware' WHEN s.id = 17 THEN 'Solorigate_Sunburst' WHEN s.id = 18 THEN 'Cisa_Known_Exploited_Vulns' END ))"}], "entityConfig": {"name": "Host"}}