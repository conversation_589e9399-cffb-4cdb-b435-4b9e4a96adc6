{"primaryKey": "object_guid", "filterBy": "LOWER(sam_account_type) LIKE '%machine_account%'", "origin": "'MS Active Directory'", "outputTable": "ei_temp.sds_ei__host__active_directory__object_guid", "dataSource": {"name": "Microsoft", "feedName": "sjdasjd", "srdm": "sdm.microsoft__active_directory"}, "commonProperties": [{"colName": "type", "colExpr": "CASE WHEN ad_distinguished_name IS NULL AND os IS NULL  THEN NULL\nWHEN LOWER(ad_distinguished_name) LIKE '%server%' OR LOWER(os) LIKE '%server%' THEN 'Server'\nELSE 'Endpoint'\nEND", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "first_seen_date", "colExpr": "coalesce(ad_created_date,first_found_date)", "fieldsSpec": {"isInventoryDerived": true}}, {"colName": "last_active_date", "colExpr": "GREATEST(ad_last_sync_date,login_last_date)", "fieldsSpec": {"isInventoryDerived": true}}], "entitySpecificProperties": [{"colName": "host_name", "colExpr": "UPPER(regexp_extract(cn,'^([^.]++)'))"}, {"colName": "os", "colExpr": "CASE WHEN operating_system IS NULL THEN NULL\nWHEN LOWER(operating_system) LIKE '%unknown%' THEN NULL\nWHEN LOWER(operating_system) LIKE '%other%' THEN 'Other'\nELSE CONCAT_WS(' ',operating_system,CASE WHEN NOT regexp_like(operating_system_version,'(?i)unknown|other') THEN operating_system_version END)\nEND"}, {"colName": "dns_name", "colExpr": "dns_hostname"}, {"colName": "login_last_date", "colExpr": "last_logon_epoch"}], "sourceSpecificProperties": [{"colName": "ad_sam_account_name", "colExpr": "sam_account_name"}, {"colName": "ad_sam_account_type", "colExpr": "sam_account_type"}, {"colName": "ad_account_disabled_date", "colExpr": "CASE WHEN LOWER(user_account_control) LIKE '%disable%' THEN event_timestamp_epoch ELSE NULL END"}, {"colName": "ad_distinguished_name", "colExpr": "distinguished_name"}, {"colName": "user_account_control", "colExpr": "user_account_control"}, {"colName": "aad_device_id", "colExpr": "object_guid"}, {"colName": "ad_created_date", "colExpr": "when_created_epoch", "fieldsSpec": {"aggregateFunction": "min"}}, {"colName": "ad_last_sync_date", "colExpr": "last_logon_synced_epoch"}, {"colName": "ad_operational_status", "colExpr": "CASE WHEN user_account_control is NULL THEN NULL WHEN LOWER(user_account_control) LIKE '%disable%' THEN 'Disabled' ELSE 'Active' END", "fieldsSpec": {"isInventoryDerived": false}}], "entity": {"name": "Host", "defaultEntitySpec": {}, "fieldLevelSpec": [], "lastUpdateFields": ["fqdn"]}}