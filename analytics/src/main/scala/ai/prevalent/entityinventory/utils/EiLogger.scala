package ai.prevalent.entityinventory.utils

import ai.prevalent.sdspecore.jobbase.LoggerBase
import org.json4s.{DefaultFormats, Formats}
import org.json4s.jackson.Serialization.write
object EILOGGER extends LoggerBase{

  val seperatorPattern = "***********************************************************************"
   def info(msg: String): Unit = {
    LOGGER.info(seperatorPattern)
    LOGGER.info(msg: String)
    LOGGER.info(seperatorPattern)
  }

  def jsonMiniPrint[T <: AnyRef](message: String, config: T)(implicit formats: Formats = DefaultFormats): Unit = {
    val jsonString: String = write(config)
    EILOGGER.info(f"$message: $jsonString")
    println(f"$message:$jsonString")
  }
  def error(msg: String): Unit = {
    LOGGER.info(seperatorPattern)
    LOGGER.error(msg: String)
    LOGGER.info(seperatorPattern)
  }

  def warn(msg: String): Unit = {
    LOGGER.info(seperatorPattern)
    LOGGER.warn(msg: String)
    LOGGER.info(seperatorPattern)
  }
}
