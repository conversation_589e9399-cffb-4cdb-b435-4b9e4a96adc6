package ai.prevalent.entityinventory.utils

import ai.prevalent.entityinventory.common.configs.{EIJobArgs, <PERSON>tity, Property}
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.loader.Loader.spark
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.catalyst.expressions.{Attribute, Cast, LambdaFunction}
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, LongType, NullType, StringType, StructType}
import org.apache.spark.sql.{Column, DataFrame, SparkSession}
object EIUtil extends  LoggerBase{
  def spark = SparkSession.active

  def readPrevDF(args:EI<PERSON>ob<PERSON>rg<PERSON>, eiDF:DataFrame): DataFrame = {
    val maxUpd = eiDF.filter(expr(s"to_timestamp(${args.currentUpdateDate}/1000) > $UPDATED_AT_TS")).select(max(col(UPDATED_AT_TS)).as(UPDATED_AT_TS))
      .withColumn(UPDATED_AT,unix_millis(col(UPDATED_AT_TS)))
      .map(row => row.getAs[Long](UPDATED_AT))(spark.implicits.newLongEncoder).collect()
      .lastOption

    if(maxUpd.isDefined) {
      args.prevUpdateDate = if(args.prevUpdateDate <= 0) maxUpd.get else args.prevUpdateDate
      args.parsedIntervalStartEpoch = if(args.parsedIntervalStartEpoch <=0 ) maxUpd.get else args.parsedIntervalStartEpoch
    } else {
      args.prevUpdateDate = if(args.prevUpdateDate <=0 ) -1 else args.prevUpdateDate
      args.parsedIntervalStartEpoch = if(args.parsedIntervalStartEpoch <=0) args.srdmHistoricalParsedIntervalStartEpoch else args.parsedIntervalStartEpoch
    }
    LOGGER.info(s"$args")
    eiDF.filter(expr(s"$UPDATED_AT_TS = to_timestamp(${args.prevUpdateDate}/1000)"))
  }

  def getPrevConfig(dataFrame: DataFrame, jobArgs: EIJobArgs): Option[String] = {
    val config = dataFrame.filter(s"$KG_CONTENT_TYPE='config'").select("kg_config").map(row => row.getAs[String]("kg_config"))(spark.implicits.newStringEncoder).collect().lastOption
    LOGGER.info(s"Previous config found - $config")
    if(jobArgs.processDeltaProperty)
      config
    else {
      LOGGER.info("Process delta properties is set to false, not considering the prev config")
      Option.empty
    }
  }

  def prepareInvDFWithConfig(config:String, invDF:DataFrame, args: EIJobArgs) = {
    val cDF = spark.sql(
      s"""SELECT to_timestamp(${args.currentUpdateDate}/1000) AS ${SDSProperties.schema.UPDATED_AT_TS},
         |${args.currentUpdateDate} AS ${SDSProperties.schema.UPDATED_AT},
         |'config' AS $KG_CONTENT_TYPE""".stripMargin).withColumn("kg_config", lit(config)
    )
   SparkUtil.unionByName(invDF.withColumn(s"$KG_CONTENT_TYPE",lit("data")), cDF)
  }

  def safeReadEntity(tableName:String, filterCondition: Column = lit(true), reader:SDSTableReader,
                     default:DataFrame = spark.emptyDataFrame
                 .withColumn(UPDATED_AT,lit(null).cast(LongType))
                 .withColumn(KG_CONTENT_TYPE, lit(null).cast(StringType))
                 .withColumn("kg_config", lit(null).cast(StringType))
                 .withColumn(P_ID,lit(null).cast(StringType))
                 .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                 .withColumn(ORIGIN,lit(null).cast(StringType))
                 .withColumn(LAST_FOUND, lit(null).cast(LongType))
              ) : DataFrame ={
    safeRead(tableName = tableName, filterCondition = filterCondition, reader = reader, default = default)
  }

  def safeReadRelation(tableName: String, filterCondition: Column = lit(true), reader: SDSTableReader,
               default: DataFrame = spark.emptyDataFrame
                 .withColumn(UPDATED_AT, lit(null).cast(LongType))
                 .withColumn(KG_CONTENT_TYPE, lit(null).cast(StringType))
                 .withColumn("kg_config", lit(null).cast(StringType))
                 .withColumn("relationship_id", lit(null).cast(StringType))
                 .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                 .withColumn(LAST_FOUND, lit(null).cast(LongType))

              ): DataFrame = {
    safeRead(tableName = tableName, filterCondition = filterCondition, reader = reader, default = default)
  }

  private def safeRead(tableName:String, filterCondition:Column, reader:SDSTableReader, default:DataFrame) ={
    val readDF = reader.readOrElse(tableName, default).filter(filterCondition)
    if(readDF.columns.contains(KG_CONTENT_TYPE))
      readDF
    else
      readDF.withColumn(KG_CONTENT_TYPE, lit("data")).withColumn("kg_config",lit(null).cast(StringType))
  }

  def safeReadResolver(tableName: String, filterCondition: Column, reader: SDSTableReader,
                       default: DataFrame = spark.emptyDataFrame
                         .withColumn(UPDATED_AT, lit(null).cast(LongType))
                         .withColumn("p_id", lit(null).cast(StringType))
                         .withColumn("disambiguated_p_id", lit(null).cast(StringType))
                         .withColumn("class", lit(null).cast(StringType))
                         .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
                      ): DataFrame = {
    if (tableName == null) {
      return default
    }
    val readDF = reader.readOrElse(tableName, default)
    readDF
      .filter(filterCondition)
  }


  def preProcesmptyStringtoNull(df:DataFrame):DataFrame ={
    val strCols = df.schema.fields
      .filter(_.dataType.equals(StringType))
      .map(_.name)
    val strColsEmptyRemoved = strCols.map(field => expr(s"""CASE WHEN btrim($field,'\\r\\n\\t ')!='' THEN btrim($field,'\\r\\n\\t ') END""").as(field))
    val otherCols = df.columns.diff(strCols).map(col(_))
    val finalCols = otherCols ++ strColsEmptyRemoved
    df.select(finalCols:_*)
  }

  def findLatestUpdateAttrExpr(invDF:DataFrame, entityConfig: Entity) :DataFrame={
    if(entityConfig.lastUpdateFields.isEmpty)
      invDF
    else {
      val updatedInvProcessed = entityConfig.lastUpdateFields
        .foldLeft(invDF) { (df, field) =>
          if(df.columns.contains(field)){
            val dataType = df.schema(field).dataType
            dataType match {
              case _: StructType => df.withColumn(s"prev_cal__$field", to_json(col(field)))
              case _: ArrayType  => df.withColumn(s"prev_cal__$field", concat_ws("<-->", array_sort(col(field))))
              case _             => df.withColumn(s"prev_cal__$field", col(field).cast(StringType))
            }
          }
          else df.withColumn(s"prev_cal__$field", lit(null).cast(StringType))
        }


      val lastUpdatedAttrExpr = entityConfig.lastUpdateFields
        .map(field => s"'$field',named_struct('isChangedInCurrentRun',COALESCE($PREV_ATTRS.$field,'COALESCE-dummy') != COALESCE(prev_cal__$field,'COALESCE-dummy') AND $PREV_ATTRS.$UPDATED_AT IS NOT NULL,'prev',CASE WHEN COALESCE($PREV_ATTRS.$field,'COALESCE-dummy') != COALESCE(prev_cal__$field,'COALESCE-dummy') AND $PREV_ATTRS.$UPDATED_AT IS NOT NULL THEN named_struct('value',CAST($PREV_ATTRS.$field AS STRING),'$UPDATED_AT',$PREV_ATTRS.$UPDATED_AT,'$LAST_FOUND',$PREV_ATTRS.$LAST_FOUND) ELSE prev__$LAST_UPDATED_ATTRS.$field.prev END,'last_changed',CASE WHEN COALESCE($PREV_ATTRS.$field,'COALESCE-dummy') != COALESCE(prev_cal__$field,'COALESCE-dummy') AND $PREV_ATTRS.$UPDATED_AT IS NOT NULL THEN named_struct('value',prev_cal__$field,'$UPDATED_AT',$UPDATED_AT,'$LAST_FOUND',$LAST_FOUND) ELSE prev__$LAST_UPDATED_ATTRS.$field.last_changed END)")
        .mkString("named_struct(", "," ,")")
      val lastUpdatedAtExpr = entityConfig.lastUpdateFields
        .map(field => s"$LAST_UPDATED_ATTRS.$field.last_changed.$LAST_FOUND").mkString(s"greatest(",",",s",$FIRST_FOUND)")

      updatedInvProcessed.withColumn(LAST_UPDATED_ATTRS,expr(lastUpdatedAttrExpr))
        .withColumn(LAST_UPDATED_DATE,expr(lastUpdatedAtExpr))
        .drop(entityConfig.lastUpdateFields.map(field => s"prev_cal__$field"):_*)
    }
  }

  def removeNullFields(outputDf: DataFrame): DataFrame ={
    val nullFields = outputDf.schema
      .filter(f => f.dataType.isInstanceOf[NullType] || (f.dataType.isInstanceOf[ArrayType] && f.dataType.asInstanceOf[ArrayType].elementType.isInstanceOf[NullType]))
      .map(_.name)
    val nullFieldsRemovedDf = outputDf.drop(nullFields: _*)
    nullFieldsRemovedDf
  }
  def preProcessPreviousInventory(prevInventory:DataFrame, entityConfig: Entity) :DataFrame ={
    LOGGER.info("Pre Processing previous inventory")
    val lastUpdatedAttrSchema = entityConfig
      .lastUpdateFields.map(field => s"`$field`: STRUCT<`prev`: STRUCT<`value`: STRING, `updated_at`: BIGINT, `last_found_date`: BIGINT>, `last_changed`: STRUCT<`value`: STRING, `updated_at`: BIGINT, `last_found_date`: BIGINT>,`isChangedInCurrentRun`:Boolean>").mkString("STRUCT<",",",">")
    val emptyDF = prevInventory.sparkSession.emptyDataFrame
      .withColumn(P_ID,lit(null).cast(StringType))
      .withColumn(LAST_FOUND,lit(null).cast(LongType))
      .withColumn(UPDATED_AT,lit(null).cast(LongType))
      .withColumn(s"prev__$LAST_UPDATED_ATTRS",expr(s"from_json(null,'$lastUpdatedAttrSchema')"))


    val schemaAdjustedDF = SparkUtil.unionByName(prevInventory.withColumnRenamed(LAST_UPDATED_ATTRS,s"prev__$LAST_UPDATED_ATTRS"), emptyDF)
    val lastUpdPrev: List[Column] = entityConfig.lastUpdateFields.map { field =>
      if (schemaAdjustedDF.columns.contains(field)) {
        if (schemaAdjustedDF.schema(field).dataType.isInstanceOf[StructType]) {
          to_json(col(field)).as(field)
        } else if (schemaAdjustedDF.schema(field).dataType.isInstanceOf[ArrayType]) {
          concat_ws("<-->", array_sort(col(field))).as(field)
        } else {
          col(field).cast(StringType).as(field)
        }
      } else {
        lit(null).as(field)
      }
    }
    schemaAdjustedDF.withColumn(PREV_ATTRS,struct(col(LAST_FOUND)+:col(UPDATED_AT)+:lastUpdPrev:_*))
  }
}


object StructFieldReplaceUtils {


  def replaceStructProperties(sourceDF: DataFrame, propsList: Seq[Property]): (DataFrame, Seq[Property]) ={
    val structProps = getStructFields(propsList).diff(Array(LAST_UPDATED_ATTRS))
    val structPropsMap: Map[String, String] = structProps.map { prop =>
      val fieldName = s"ei_temp_${prop.replace(".", "__")}"
      (prop,fieldName)
      }
      .toMap
    val missingStruct = structProps.filter( prop =>{
      try {
        sourceDF.select(prop)
        false
      }
      catch {
        case e:Exception => true
      }
    })
    EILOGGER.info(s"missing struct fields ${missingStruct.toList}")
    val tempStructPropsExpr = structPropsMap.map(prop => {
      val fieldValue = if (missingStruct.contains(prop._1)) "NULL" else prop._1
      expr(s"CASE WHEN btrim(cast($fieldValue as STRING),'[\\t ]') = '' THEN null ELSE $fieldValue END").as(prop._2)
    }).toSeq
    val finalExpr = sourceDF.columns.diff(structPropsMap.map(_._2).toList).map(col(_)) ++ tempStructPropsExpr
    val structFieldsReplacedPropList = propsList.map(prop => {
      val sdsExpr = expr(prop.colExpr).expr
      val newExpr = sdsExpr
        .transformUp {
          case attr: Attribute if (structPropsMap.contains(attr.name)) => val exprStr = structPropsMap.get(attr.name).get
            expr(exprStr).expr
        }
        .transformUp {
          case ca: Cast => new SDSCast(ca.child, ca.dataType, ca.timeZoneId, ca.evalMode )
          case lm: LambdaFunction => new SDSLambdaFunction(lm.function, lm.arguments, lm.hidden)
        }
      prop.copy(colName = prop.colName, colExpr = newExpr.sql, fieldsSpec = prop.fieldsSpec)
    })
    (sourceDF.select(finalExpr:_*), structFieldsReplacedPropList)
  }

  /**
   * returns seq of struct fields used in column expressions
   * @param allProperties
   */
  def getStructFields(propsList: Seq[Property]): Seq[String] = {
    propsList.flatMap(prop => {
      val referenceFields = expr(prop.colExpr).expr.references.map(_.name)
        .filter(_.contains("."))
      referenceFields
    }).distinct
  }

  def getNonStructFields(propsList: Seq[Property]): Seq[String] = {
    propsList.flatMap(prop => {
      val referenceFields = expr(prop.colExpr).expr.references.map(_.name)
        .filter(!_.contains("."))
      referenceFields
    }).distinct
  }

}
