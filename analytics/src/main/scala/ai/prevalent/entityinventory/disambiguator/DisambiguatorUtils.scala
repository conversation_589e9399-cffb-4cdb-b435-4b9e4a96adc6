package ai.prevalent.entityinventory.disambiguator

import ai.prevalent.entityinventory.common.configs.{Entity, Property}
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{ORIGIN, PRECEDENCE, P_ID}
import ai.prevalent.entityinventory.disambiguator.configs.specs._
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.loader.LoaderUtils.applyPropertyTransformations
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.{EILOGGER, EIUtil, SDSCast, SDSLambdaFunction, SchemaEvolutionUtil, SparkUtil}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory}
import org.apache.spark.graphx.{<PERSON>, Graph, VertexId}
import org.apache.spark.rdd.RDD
import org.apache.spark.sql.catalyst.analysis.{UnresolvedAttribute, UnresolvedExtractValue}
import org.apache.spark.sql.catalyst.expressions.{Attribute, Cast, Expression, LambdaFunction, Literal}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row, SparkSession, functions}
import org.apache.spark.storage.StorageLevel

import scala.collection.mutable.{ArrayBuffer, Map => MutableMap}


object DisambiguatorUtils extends LoggerBase{


  final val NODE_FIELD = "node"
  final val VERTICES_ID = "vertices_id"
  val DISAMBIGUATION_GROUP_ID = "inter_dis_group_id"
  val DATA_SOURCE_SUBSET_NAME = "data_source_subset_name"
  val PID_ARRAY = "inter_source_temp_pids"
  val PREV_DISAMBIGUATION_GROUP_ID = "prev_disambiguated_p_id"
  val DEFAULT_ROLLUP_COLUMNS = Array(ORIGIN,DATA_SOURCE_SUBSET_NAME,ORIGIN_ENTITY_ENRICH)

  val DIS_PROCESS_CORE_COLUMNS = Array(PRECEDENCE, P_ID, FIRST_FOUND, LAST_FOUND,
    DISAMBIGUATION_GROUP_ID, PID_ARRAY, PREV_DISAMBIGUATION_GROUP_ID)

  /**
   * Constructs an empty dataframe with fields specified in the config
   *
   * @param invModel           Unioned Inventory Model Dataframe
   * @param disambiguationConf Disambiguation Config
   * @param spark              Spark Session
   * @return l with fields in the config
   */
  def minimumFields(disambiguationConf: Disambiguation): Array[String] = {
    val strategy = disambiguationConf.strategy.getOrElse(Strategy.empty)
    val fieldLevelConfidenceFields = strategy
      .fieldLevelConfidenceMatrix.getOrElse(Array.empty)
    val fieldLevelConfFields = fieldLevelConfidenceFields
      .map(conf => conf.field)
    val singleSourceFields = strategy.singleSource.map(_.field).toArray
    val aggregateFields = strategy.aggregation.map(_.field).toArray

    val valueConfidence = strategy.valueConfidence.getOrElse(Array.empty)
    val valueBasedFields = valueConfidence
      .map(detail => detail.field)
    val rollingUpFields: Set[String] = strategy.rollingUpFields

    aggregateFields ++ singleSourceFields ++ fieldLevelConfFields ++ valueBasedFields ++ rollingUpFields ++
      DIS_PROCESS_CORE_COLUMNS ++ Array(ORIGIN_ENTITY_ENRICH) ++ disambiguationConf.candidateKeys.map(_.name)
  }

  /**
   * Returns disambigDf back with missing fields used in column expression. Default value is null
   * @param disambigDf
   * @param properties
   * @return disambigDf with missing fields
   */
  def addMissingFields(disambigDf: DataFrame, properties: Array[Property], dtype: DataType): DataFrame = {
    val missingCols: Array[String] = properties.map(prop => expr(prop.colExpr).expr)
      .map(_.references.map(_.name.split("[.]")(0)))
      .flatten.distinct
      .diff(disambigDf.columns)
    print(missingCols)
    EILOGGER.jsonMiniPrint("Missing fields added to dataframe",missingCols)
    missingCols
      .foldLeft(disambigDf)((df, field) => df.withColumn(field, lit(null).cast(dtype)))
  }

  /**
   * Recalculates common properties
   * @param disambigDf
   * @param config
   * @return
   */
  def recalculateCommonFields(disambigDf: DataFrame, config: Config): DataFrame = {
    LOGGER.info(s"Applying transformation for resolved entities")
    applyPropTransformation(config,disambigDf)
  }

  def safeLoadPreviousResolve(tableName:String,prevUpdateDate:Long,reader:SDSTableReader,spark: SparkSession):DataFrame={
    val emptySchema = new StructType().add(P_ID,StringType,true)
      .add("disambiguated_p_id",StringType,true)
    val resolveDF = reader.readOrElse(tableName,default = spark.createDataFrame(spark.sparkContext.emptyRDD[Row],emptySchema))
    if(!resolveDF.isEmpty) resolveDF.filter(expr(s"$UPDATED_AT_TS = to_timestamp($prevUpdateDate/1000)")) else resolveDF
  }


  def getTemporalConfidenceFields(config: Config):Array[String]  ={
    val globalTemporalConfidenceMatrix = config.disambiguation.temporalConfidenceMatrix
    val strategy = config.disambiguation.strategy.getOrElse(Strategy.empty)
    val fieldLevelConfidenceMatrix = strategy.fieldLevelConfidenceMatrix.getOrElse(Array.empty)
    val fieldLevelTemporalFields = fieldLevelConfidenceMatrix.flatMap(fieldConf => fieldConf.temporalConfidenceMatrix.getOrElse(Array.empty)).distinct
    (fieldLevelTemporalFields ++ globalTemporalConfidenceMatrix).distinct
  }

  def extractGetArrayItemAttributes(expr: Expression): Seq[String] = {
    val buffer = ArrayBuffer[String]()

    expr.foreach {
      case UnresolvedExtractValue(child, ordinal: Literal) =>
        child match {
          case a: UnresolvedAttribute => buffer += a.name
          case a: Attribute => buffer += a.name
          case _ =>
        }
      case _ =>
    }
    buffer.toSeq
  }

  def addMissingColumnsToInvModels(invModelSpecList: Array[InventoryModelSpec], commonProperties: Array[Property]): Array[InventoryModelSpec] = {
    invModelSpecList.map { invSpec =>
      // Step 1: Identify the missing columns from colExprs
      val missingCols: Array[String] = commonProperties.map(prop => expr(prop.colExpr).expr) // Extract the expression
        .map(_.references.map(_.name.split("[.]")(0))) // Extract the column names from the expressions
        .flatten.distinct // Flatten and get unique column names
        .diff(invSpec.inventoryModel.columns) // Find the columns that are not already present in the DataFrame

      // Log missing columns (for debugging purposes)
      EILOGGER.jsonMiniPrint("Missing fields added to dataframe", missingCols)

      val updatedDF = missingCols.foldLeft(invSpec.inventoryModel) { (tempDf, field) =>
        tempDf.withColumn(field, lit(null))  // Adding a column with NullType
      }

      // Step 2: Resolve the "_resolved" columns
      // For each base column, if it exists in the updatedDF, we create the resolved column
      val resolvedColumns = updatedDF.columns.filter(_.endsWith("_resolved")).map(_.stripSuffix("_resolved"))

      // Create resolved columns if the base columns are available
      val dfWithResolvedCols = resolvedColumns.foldLeft(updatedDF) { (tempDf, baseCol) =>
        if (tempDf.columns.contains(baseCol)) {
          tempDf.withColumn(s"${baseCol}_resolved", col(baseCol))
        } else {
          tempDf
        }
      }

      // Return the updated InventoryModelSpec with the updated DataFrame
      invSpec.copy(inventoryModel = updatedDF)
    }
  }




  /**
   * Disambiguate mini inventories using a given configuration,
   * In the final output there will be only one record corresponding to one entity,
   * duplicates records from different mini inventories gest disambiguated
   *
   * @param invModelSpecList Array of Mini Inventory Models with its name
   * @param config           Disambiguation Config
   * @param spark            Spark Session
   * @return inter source disambiguated model and the resolver table
   */
  def build(invModelSpecList: Array[InventoryModelSpec], prevInv:DataFrame, config: Config, spark: SparkSession, prevNondis:Option[DataFrame] = Option.empty): Artifacts = {
    val reader = SDSTableReaderFactory.getDefault(spark)
    invModelSpecList.foreach(inv => LOGGER.info(f"Schema for ${inv.name}:${inv.inventoryModel.schema}"))
    EILOGGER.info("Building inventory")

    val unionedInvDF = SparkUtil.unionByName(invModelSpecList.map(_.inventoryModel):_*)
    val adjustedinvModelSpecList = invModelSpecList
      .map(spec => InventoryModelSpec(
        SchemaEvolutionUtil.evolveSchema(spec.inventoryModel, unionedInvDF.schema),
        spec.name))
    adjustedinvModelSpecList.foreach(inv => LOGGER.info(f"Adjusted Schema for ${inv.name}:${inv.inventoryModel.schema}"))
    val artifacts:Artifacts = if(config.disambiguation.disambiguationType.equals("Union")){
      Artifacts(unionedInvDF,Option.empty,Option.empty, Option.empty)
    } else {
      val (candidateKeyGroupsDF, resolutionGraph)  = candidateKeyGroupIdentification(unionedInvDF, config, spark)
      val modelWithPrecedenceDF = confidenceMatrix(adjustedinvModelSpecList, config)
      val groupedInvDF = modelWithPrecedenceDF
        .drop(DISAMBIGUATION_GROUP_ID)
        .as("in")
        .join(candidateKeyGroupsDF.as("g"), expr(s"in.$P_ID = g.$P_ID"), "inner")
        .select(col(s"g.$DISAMBIGUATION_GROUP_ID"), col("in.*"))

      val disambiguatedDF = confidenceEntityExtraction(groupedInvDF, config)
        .withColumn("fragments", size(col(PID_ARRAY)))

      val enr = Enrichment.applyEnrichments(disambiguatedDF,config.enrichments,reader)
      val resolver = modelWithDerivedProperties(enr, config).filter(config.output.filter)
        .withColumn("temp", explode_outer(col(PID_ARRAY)))
        .withColumnRenamed(P_ID, "disambiguated_p_id")
        .withColumnRenamed(ORIGIN, "disambiguated_origin")
        .withColumnRenamed("display_label", "disambiguated_display_label")
        .withColumn(P_ID, col(s"temp.$P_ID"))
        .withColumn(PRIMARY_KEY, col(s"temp.$PRIMARY_KEY"))
        .withColumn(ORIGIN, col(s"temp.$ORIGIN"))
        .select("disambiguated_p_id", "disambiguated_origin", P_ID, "disambiguated_display_label", PRIMARY_KEY, ORIGIN, "class", UPDATED_AT)


      val pattern = "([/]|^)([^/]++)[/]?$".r
      val dataSourceName = if(pattern.findFirstMatchIn(config.output.disambiguatedModelLocation).isDefined){
         pattern.findFirstMatchIn(config.output.disambiguatedModelLocation).get.group(2)
      } else config.output.disambiguatedModelLocation


      val resolverDf = resolver.join(groupedInvDF.select(P_ID,"inter_source_temp_precedence"), Seq(P_ID), "left")
        .withColumnRenamed("inter_source_temp_precedence", "precedence_order")
        .withColumn("data_source_name", lit(dataSourceName))
        .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
        .persist(StorageLevel.MEMORY_AND_DISK)
      val resolutionGraphResolved = resolutionGraph.join(resolverDf,P_ID).select(resolutionGraph("*"), resolverDf("disambiguated_p_id"))
      // Check if prevNondis is Some and handle empty DataFrame scenario
      val final_nonResolvedDF: DataFrame = prevNondis match {

        case Some(prevNondisData) =>
          // Get the non-resolved data frame
          val nonResolvedDF = getNonResolvedTable(config, resolverDf, invModelSpecList: Array[InventoryModelSpec])

          // Continue processing
          val lastUpdFieldsAddednonResolvedDF = findLatestUpdatedFields(nonResolvedDF, prevNondisData, config.entity)
            .drop(DISAMBIGUATION_GROUP_ID, PRECEDENCE, PREV_DISAMBIGUATION_GROUP_ID, PID_ARRAY, s"exclude__$PRECEDENCE")

          println("****************************************lastUpdFieldsAddednonResolvedDF****************************")

          // Removing null fields from the final DataFrame
          val final_nonResolvedDF = removeNullFields(lastUpdFieldsAddednonResolvedDF)

          final_nonResolvedDF

        case None =>
          // If prevNondis is None (no data found or fragment location is not provided)
          println("No previous disambiguation data found, skipping final_nonResolvedDF calculation.")

          // Return an empty DataFrame if no previous disambiguation data found
          spark.emptyDataFrame
      }
      Artifacts(
        disambiguatedDF,
        Some(resolverDf),
        Some(resolutionGraphResolved),
        Some(final_nonResolvedDF)
      )
    }

    val missingCommonPropsDF = addMissingFields(artifacts.interSourceDisambiguatedInventoryModel, config.entity.commonProperties ++ config.entity.entitySpecificProperties, StringType)

    val commonPropsRecalculatedDF = recalculateCommonFields(missingCommonPropsDF, config)

    val lastUpdFieldsAddedDF = findLatestUpdatedFields(commonPropsRecalculatedDF,prevInv,config.entity)
    val enrichedDF = Enrichment.applyEnrichments(lastUpdFieldsAddedDF,config.enrichments,reader)
    var transformedDF = modelWithDerivedProperties(enrichedDF, config)
      .drop(DISAMBIGUATION_GROUP_ID, PRECEDENCE, PREV_DISAMBIGUATION_GROUP_ID,s"exclude__$PRECEDENCE")
    transformedDF = if(transformedDF.schema(ORIGIN).dataType.isInstanceOf[ArrayType])
      transformedDF.withColumn(s"count_of_${ORIGIN}", size(col(ORIGIN)))
        .withColumn(s"${ORIGIN}_contribution_type",expr(s"CASE WHEN count_of_${ORIGIN} ==1 THEN 'Unique' WHEN count_of_${ORIGIN} >1 THEN 'Corroborated' ELSE NULL END"))
    else
      transformedDF.withColumn(s"count_of_${ORIGIN}", lit(1))
        .withColumn(s"${ORIGIN}_contribution_type",lit("Unique"))
    val filterCondition = config.output.filter
    val transformedWithNullColumns=addMissingFieldsExpr(transformedDF,filterCondition)
    var finalInv = transformedWithNullColumns.filter(expr(filterCondition)).checkpoint(true)
    artifacts.copy(interSourceDisambiguatedInventoryModel = EIUtil.removeNullFields(finalInv.drop(PID_ARRAY)))
  }

  def getNonResolvedTable(config:Config, interSourceResolver:DataFrame, invModelSpecList: Array[InventoryModelSpec]) :DataFrame ={

    val commonProps = config.entity.commonProperties.map(p => p.copy(colExpr = s"coalesce(${p.colName}, ${p.colExpr})"))
    val entityProps = config.entity.entitySpecificProperties.map(p => p.copy(colExpr = s"coalesce(${p.colName}, ${p.colExpr})"))
    val updConfig = config.copy(entity = config.entity.copy(commonProperties = commonProps, entitySpecificProperties = entityProps))

    val updatedEntitySpecInvModelSpecList = addMissingColumnsToInvModels(invModelSpecList, commonProps ++ entityProps)
    val nonResolvedDF = calculateNonResolvedTransform(updConfig, updatedEntitySpecInvModelSpecList)
    val latestFragmentsGroupingFields = Array("disambiguated_p_id", ORIGIN).map(field => col(field))
    val latestOriginFragmentsWindowSpec = Window.partitionBy(latestFragmentsGroupingFields: _*).orderBy(col("last_found_date").desc, col("p_id").desc)
    val originWindowSpec = Window.partitionBy("disambiguated_p_id").orderBy(col("precedence_order").desc)

    nonResolvedDF.as("nr")
      .join(interSourceResolver.withColumn("origin_index", dense_rank().over(originWindowSpec)).as("resolver"), Seq("p_id"))
      .select(col("nr.*"), col("resolver.disambiguated_p_id"), col("resolver.origin_index"))
      .withColumn("origin_fragment_index", row_number().over(latestOriginFragmentsWindowSpec))

  }

  def calculateNonResolvedTransform(config: Config, invModelSpecList: Array[InventoryModelSpec]): DataFrame = {
    val dfs = invModelSpecList.map(spec => {
      LOGGER.info(s"Applying transform for ${spec.name}, ")
      applyPropTransformation(config,spec.inventoryModel)
    })
    SparkUtil.unionByName(dfs: _*)
  }

  def applyPropTransformation(config:Config, df:DataFrame) ={
    val allProperties = config.entity.commonProperties ++ config.entity.entitySpecificProperties
    val references = allProperties
      .flatMap(p =>expr(p.colExpr).expr.references.map(_.name))
      .distinct

    val possibleArray = config.disambiguation.candidateKeys.map(_.name) ++ config.disambiguation.strategy.getOrElse(Strategy.empty).rollingUpFields
    val updDF = possibleArray.foldLeft(df)((dfUpd,col) => {
      if(df.columns.contains(col) && !df.schema(col).dataType.isInstanceOf[ArrayType])
        dfUpd.withColumn(col, array(col))
      else dfUpd
    })

    var replaceExprns = MutableMap.empty[String, String]
    references.filter(c => updDF.schema(c).dataType.isInstanceOf[ArrayType])
      .foreach(c => replaceExprns.put(c, s"$c[0]"))
    references.filter(c => updDF.columns.contains(s"${c}__resolved"))
      .foreach(c => replaceExprns.put(c, s"${c}__resolved"))
    LOGGER.info(s"resolved ${references.filter(c => updDF.columns.contains(s"${c}__resolved")).toList}")
    LOGGER.info(s"Array cols ${references.filter(c => updDF.schema(c).dataType.isInstanceOf[ArrayType]).toList}")


    LOGGER.info(s"References ${references.toList}, columns - ${updDF.columns.toList}")
    LOGGER.info(s"replace expression $replaceExprns")
    val newProperties = allProperties.map(p => {
      LOGGER.info(s"Applying${p.colName} , ${p.colExpr}")
      try {
        updDF.select(expr(p.colExpr))
        LOGGER.info(s"Applied ${p.colName} , ${p.colExpr}")
        p
      } catch {
        case e: Exception => val newExpr = expr(p.colExpr).expr
            .transformUp {
              case exp:Expression => SparkUtil.expressionReplace(exp,replaceExprns)
            }
            .transformUp {
              case ca: Cast  => new SDSCast(ca.child, ca.dataType, ca.timeZoneId, ca.evalMode)
              case lm: LambdaFunction => new SDSLambdaFunction(lm.function, lm.arguments, lm.hidden)
            }
          p.copy(colExpr = newExpr.sql.replace("__ei__internal__temp", ""))
      }
    })
    LOGGER.info(s"New Property ${newProperties.toList}")
    applyPropertyTransformations(updDF, newProperties)
  }

  def addMissingFieldsExpr(inpDf: DataFrame,Expression: String): DataFrame={
    val filterColumns = expr(Expression).expr.collect {
      case a: Attribute => a.name
    }
    val nonExistingColumns = filterColumns.filterNot(inpDf.columns.contains)
    val dfWithNullColumns = nonExistingColumns.foldLeft(inpDf) { (df, column) =>
      df.withColumn(column, lit(null))
    }
    dfWithNullColumns
  }

  def confidenceMatrix(invModelSpecList: Seq[InventoryModelSpec],
                       config: Config): DataFrame = {
    val strategy = config.disambiguation.strategy.getOrElse(Strategy.empty)
    val fieldLevelConfidenceFields =
      strategy.fieldLevelConfidenceMatrix.getOrElse(Array.empty)
    val valueConfidences = strategy.valueConfidence.getOrElse(Array.empty)
    val rollUpFields = (strategy.rollingUpFields ++ DEFAULT_ROLLUP_COLUMNS)
    val aggregateFields = strategy.aggregation.map(_.field)
    val structParamsMatrix = Array(LAST_FOUND)
    val globalTemporalConfidenceMatrix = config.disambiguation.temporalConfidenceMatrix
    val temporalFields = getTemporalConfidenceFields(config)
    val confDFList = invModelSpecList
      .map(inv => {
        println(s"Processing ${inv.name}")
        val confMap = config.disambiguation.confidenceMap
        EILOGGER.jsonMiniPrint("confMap",confMap)
        val precedence = confMap.getOrElse(inv.name,0)
        val temporalFieldsAddedDf = addMissingFields(inv.inventoryModel, temporalFields.map(Property(_)), LongType)
        val invModel =
          temporalFieldsAddedDf.withColumn(PRECEDENCE, lit(precedence)).withColumn(s"exclude__$PRECEDENCE", expr(s"$PRECEDENCE-1-${confMap.size}"))
        // field level confidence override
        val fieldLevelConfidenceDf = fieldLevelConfidenceFields.foldLeft(invModel)(
          (df, fieldConf) => {
            println(s"Processing ${inv.name} for field ${fieldConf.field}")
            val fieldLevelTemporalConfidenceMatrix = fieldConf.temporalConfidenceMatrix.getOrElse(globalTemporalConfidenceMatrix)

            val fieldLevelfinalStructParamsMatrix = if (fieldLevelTemporalConfidenceMatrix.contains(LAST_FOUND)) {
              fieldLevelTemporalConfidenceMatrix
            } else {
              structParamsMatrix ++ fieldLevelTemporalConfidenceMatrix
            }
            val finalFieldLevelStructFormParams = fieldLevelfinalStructParamsMatrix.zipWithIndex.flatMap { case (value, index) =>
              Array(s"'field${index + 1}'", value)
            }
            val fieldConfidenceMap = if (fieldConf.restrictToConfidenceMatrix) {
              fieldConf.confidenceMap
            }
            else {
              val fieldConfidence = fieldConf.confidenceMatrix
              val fieldsDiff = config.disambiguation.confidenceMatrix.diff(fieldConfidence)
              val finalConfidenceMatrix = fieldConfidence ++ fieldsDiff
              val finalConfidenceMap = finalConfidenceMatrix
                .zip(Array.range(1, finalConfidenceMatrix.length + 1).reverse)
                .toMap
              finalConfidenceMap
            }
            EILOGGER.info(s"Field precedence for ${fieldConf.field} is ${fieldConfidenceMap}")
            val structCol = if (inv.inventoryModel.schema(fieldConf.field).dataType.isInstanceOf[StringType]) {
              when(!col(fieldConf.field).isin(config.disambiguation.excludeValues: _*), expr("named_struct('temp_precedence', temp_precedence," + finalFieldLevelStructFormParams.mkString(",") + s", '$P_ID' ,$P_ID, '${fieldConf.field}' ,${fieldConf.field})"))
              .otherwise(expr(s"named_struct('temp_precedence',exclude__temp_precedence,"+ finalFieldLevelStructFormParams.mkString(",") + s",'$P_ID',$P_ID,'${fieldConf.field}',${fieldConf.field})"))
            } else
              expr(s"named_struct('temp_precedence', temp_precedence, " + finalFieldLevelStructFormParams.mkString(",") + s", '$P_ID',$P_ID,'${fieldConf.field}',${fieldConf.field})")

            val persistNullCondition = if (fieldConf.persistNonNullValue.getOrElse(config.entity.fieldSpec.persistNonNullValue.getOrElse(true))) {
              when(col(fieldConf.field).isNotNull, structCol).otherwise(expr(s"named_struct('temp_precedence',exclude__temp_precedence - ${config.disambiguation.confidenceMap.size}," + finalFieldLevelStructFormParams.mkString(",") + s",'$P_ID',$P_ID,'${fieldConf.field}',${fieldConf.field})"))
            }
            else {
              expr(s"named_struct('temp_precedence', temp_precedence, " + finalFieldLevelStructFormParams.mkString(",") + s", '$P_ID',$P_ID,'${fieldConf.field}',${fieldConf.field})")
            }
            val restrictToConfidenceMatrixCondition = if (fieldConf.restrictToConfidenceMatrix) {
              val diffType = df.schema(fieldConf.field).dataType.sql
              when(!col("temp_precedence").equalTo(0), persistNullCondition).otherwise(lit(null).cast(s"struct<temp_precedence:integer," + fieldLevelfinalStructParamsMatrix.zipWithIndex.flatMap{case (field, index) => Array(s"field${index + 1} : long")}.mkString(",") + s",$P_ID:string,${fieldConf.field}:$diffType>"))
            } else {
              persistNullCondition
            }

            val defaultFieldConfidenceMapVal = if(fieldConfidenceMap.size == 0) -1 else 0

            df.withColumn("temp_precedence", lit(fieldConfidenceMap.getOrElse(inv.name,defaultFieldConfidenceMapVal)))
              .withColumn("exclude__temp_precedence", expr(s"temp_precedence-1-${fieldConfidenceMap.size}-1"))
              .withColumn(fieldConf.field, restrictToConfidenceMatrixCondition)
          }
        )
        fieldLevelConfidenceDf
      })

    val unionDF = SparkUtil.unionByName(confDFList: _*)

    var invDF = config.disambiguation.candidateKeys.map(_.name).foldLeft(unionDF)((df,name) =>{
      val resolvedColumnName = s"${name}__resolved"
      val columnExists = df.columns.contains(resolvedColumnName)
      if (columnExists) {
        df.withColumn(resolvedColumnName, expr(s"coalesce($resolvedColumnName, $name[0])"))
      } else {
        if(df.schema(name).dataType.isInstanceOf[ArrayType])
          df.withColumn(resolvedColumnName, col(name).getItem(0))
        else
          df.withColumn(resolvedColumnName, col(name))
      }
    })

    invDF = if (invDF.columns.contains(DATA_SOURCE_SUBSET_NAME)) {
      if (invDF.schema(DATA_SOURCE_SUBSET_NAME).dataType.isInstanceOf[ArrayType])
        invDF.withColumn(DATA_SOURCE_SUBSET_NAME, expr(s"coalesce($DATA_SOURCE_SUBSET_NAME, array($ORIGIN))"))
      else if (invDF.schema(DATA_SOURCE_SUBSET_NAME).dataType.isInstanceOf[StringType])
        invDF.withColumn(DATA_SOURCE_SUBSET_NAME, expr(s"coalesce($DATA_SOURCE_SUBSET_NAME, $ORIGIN)"))
      else invDF
    }
    else
      invDF.withColumn(DATA_SOURCE_SUBSET_NAME, expr(s"$ORIGIN"))



    val finalStructParamsMatrix = if (globalTemporalConfidenceMatrix.contains(LAST_FOUND)) {
      globalTemporalConfidenceMatrix
    } else {
      structParamsMatrix ++ globalTemporalConfidenceMatrix
    }
    val finalStructFormParams = finalStructParamsMatrix.zipWithIndex.flatMap { case (value, index) =>
      Array(s"'field${index + 1}'", value)
    }

    val invPropertiesCol = invDF.columns
      .diff(DIS_PROCESS_CORE_COLUMNS)
      .diff(rollUpFields.toSeq)
      .diff(valueConfidences.map(_.field))
      .diff(fieldLevelConfidenceFields.map(_.field))
      .diff(config.disambiguation.candidateKeys.map(_.name))
      .diff(aggregateFields)
      .map(field => {
        val excludeStructCol = when(col(field).isin(config.disambiguation.excludeValues: _*), expr(s"named_struct('$PRECEDENCE',exclude__$PRECEDENCE," + finalStructFormParams.mkString(",") + s",'$P_ID',$P_ID,'${field}',${field})"))
          .when(col(field).isNull, expr(s"named_struct('$PRECEDENCE',exclude__$PRECEDENCE - ${config.disambiguation.confidenceMap.size}," + finalStructFormParams.mkString(",") + s",'$P_ID',$P_ID,'${field}',${field})"))
          .otherwise(expr(s"named_struct('$PRECEDENCE', $PRECEDENCE, " + finalStructFormParams.mkString(",") + s", '$P_ID',$P_ID,'${field}',${field})"))

        val structCol =
          if (invDF.schema(field).dataType.isInstanceOf[StringType]) {
            excludeStructCol
          } else
            when(col(field).isNotNull, expr(s"named_struct('$PRECEDENCE', $PRECEDENCE, " + finalStructFormParams.mkString(",") + s", '$P_ID',$P_ID,'${field}',${field})"))
        structCol.as(field)
      })

    val valuePrecedenceColumns = valueConfidences
      .map(fieldDetail => {
        val precedenceExpr = fieldDetail.confidenceMap
          .map(confidenceValue => s"WHEN ${fieldDetail.field} = '${confidenceValue._1}' THEN ${confidenceValue._2}")
          .mkString("CASE ", " ", s" ELSE ${Int.MinValue} END")

        val precedenceValueStruct = expr(s"named_struct('$PRECEDENCE',$precedenceExpr,'${fieldDetail.field}',${fieldDetail.field})")
        precedenceValueStruct.as(fieldDetail.field)
      })

    val rollUpFieldsColumns = rollUpFields.map(field => struct(PRECEDENCE, field).as(field))

    val finalColumns = DIS_PROCESS_CORE_COLUMNS.map(col(_)) ++ fieldLevelConfidenceFields.map(f => col(f.field).as(f.field)) ++
      valuePrecedenceColumns ++ invPropertiesCol ++ aggregateFields.map(col(_)) ++ rollUpFieldsColumns ++
      config.disambiguation.candidateKeys
        .map(field => col(field.name).as(field.name))
    invDF.withColumn(PID_ARRAY,struct(P_ID,PRIMARY_KEY,ORIGIN))
      .select(finalColumns: _*)
  }

  def modelWithDerivedProperties(disambigDf: DataFrame, config: Config): DataFrame = {
    config.derivedProperties
      .foldLeft(disambigDf)((df, property) => {
        df.withColumn(property.colName, expr(property.colExpr))
      })
  }

  def loadInventoryModel(reader :SDSTableReader, currentUpd:Long,spark: SparkSession, inventoryModel: InventoryModelInput): InventoryModelSpec = {
    val inventoryModelDF = EIUtil.safeReadEntity(inventoryModel.path, expr(s"$UPDATED_AT_TS = to_timestamp($currentUpd/1000)"), reader)
      .filter(inventoryModel.filter)
      .filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL")
    EILOGGER.info(s"Loaded data from ${inventoryModel.path}, using updated_at $currentUpd")
    InventoryModelSpec(inventoryModelDF, inventoryModel.name)
  }

  def candidateKeyGroupIdentification(invDF: DataFrame, config: Config, spark: SparkSession): (DataFrame,DataFrame) = {
    EILOGGER.info("Starting Candidate key group identification")
    val tempCandidateKeysMap = config.disambiguation.candidateKeys.map(key => (key.name,s"${key.name}__temp")).toMap
    val candidateKeyExplodedDF = config.disambiguation.candidateKeys.foldLeft(invDF)((df, key) => {
      val arrayTempKey = if (invDF.schema(key.name).dataType.isInstanceOf[ArrayType]) {
        EILOGGER.info(s"Exploding Key ${key.name}")
        s"explode_outer(${key.name})"
      }
      else s"${key.name}"
      df.withColumn(key.name, expr(arrayTempKey))
    })

    var tempCandidateKeyDF = config.disambiguation.candidateKeys.foldLeft(candidateKeyExplodedDF)((df,key) => {
      val exceptionFilter = key.exceptionFilter.getOrElse("false")
      val nullAddedDF=addMissingFieldsExpr(df,exceptionFilter)

      val keyToKeyMatchCandKeyExpr = if (key.keyToKeyMatch)
        s"concat('${key.name}','-->',${key.name})"
      else
        key.name
      nullAddedDF.withColumn(tempCandidateKeysMap(key.name), expr(s"CASE WHEN $exceptionFilter THEN NULL ELSE $keyToKeyMatchCandKeyExpr END"))
    })
    val fieldsToSelect = tempCandidateKeysMap.values.toSet++tempCandidateKeysMap.keySet++config.disambiguation.candidateKeys.flatMap(_.matchAttributesList).toSet
    tempCandidateKeyDF = tempCandidateKeyDF.select((fieldsToSelect++Set(P_ID,ORIGIN)).toSet.toList.map(col(_)):_*)

    tempCandidateKeyDF = config.disambiguation.candidateKeys
      .filter(_.matchAttributesList.nonEmpty)
      .flatMap( k => k.matchAttributesList.map( p => (k,p)))
      .foldLeft(tempCandidateKeyDF)((df,key) => {
        val window = Window.partitionBy(s"${key._1.name}__temp_window")
        val tempName = s"${key._1.name}__win__${key._2}__match_temp"
        LOGGER.info(s"match attribute s$tempName")
        val k=df.withColumn(s"${key._1.name}__temp_window",coalesce(col(key._1.name), col(P_ID)))
          .withColumn(tempName, collect_set(col(key._2)).over(window))
          .withColumn(tempCandidateKeysMap(key._1.name), expr(s"CASE WHEN size($tempName)>1 AND ${key._2} IS NOT NULL THEN NULL ELSE ${tempCandidateKeysMap(key._1.name)} END"))

        k
      })
    val tempCandidateKeys = tempCandidateKeysMap.values.toList
    val tempCandidateKeysCol = tempCandidateKeys.map(col(_))
    val verticesIDDF = tempCandidateKeyDF
      .withColumn(NODE_FIELD, explode(filter(array(tempCandidateKeysCol:+ col(P_ID): _*), ar => ar.isNotNull) ))
      .filter(col(NODE_FIELD).isNotNull)
      .select(NODE_FIELD)
      .distinct()
      .withColumn(VERTICES_ID, monotonically_increasing_id())
      .checkpoint(true)


    val vertCount = verticesIDDF.count()
    val vertIDCOunt = verticesIDDF.select(VERTICES_ID).distinct().count()
    if(vertCount!= vertIDCOunt) {
      EILOGGER.error(s"Duplicate Vertices ID, $vertCount, $vertIDCOunt")
      throw new Exception("Duplicate Vertices ID")
    }

    val vertices: RDD[(VertexId, Vertex)] = tempCandidateKeyDF
      .withColumn(NODE_FIELD, explode(filter(array(tempCandidateKeysCol :+ col(P_ID): _*), ar => ar.isNotNull)))
      .filter(col(NODE_FIELD).isNotNull)
      .groupBy(NODE_FIELD).agg(collect_set(P_ID).as(PID_ARRAY))
      .join(verticesIDDF, NODE_FIELD)
      .rdd
      .map(row => {
        val id = row.getAs[Long](VERTICES_ID)
        val pid = row.getAs[Seq[String]](PID_ARRAY).toList
        (id, Vertex(pid, 0l))
      })


    val edges = tempCandidateKeyDF
      .withColumn("src", explode(filter(array(tempCandidateKeysCol: _*), ar => ar.isNotNull)))
      .withColumn("dest",col(P_ID))
      .filter(col("src").isNotNull)
      .join(verticesIDDF, verticesIDDF(NODE_FIELD) === col("src"),"inner")
      .select(col(VERTICES_ID).as("src"),col("dest"))
      .join(verticesIDDF, verticesIDDF(NODE_FIELD) === col("dest"), "inner")
      .select(col(VERTICES_ID).as("dest"), col("src"))
      .rdd
      .map(row => Edge(row.getAs[Long]("src"), row.getAs[Long]("dest"), ""))

    import spark.implicits._
    val resolutionGraph = tempCandidateKeyDF
      .withColumn("dest", explode(array(tempCandidateKeys.map(k => expr(s"named_struct('candidate_key','${k.replaceAll("__temp","")}','value',$k)")):_*)))
      .select(P_ID,"dest","origin")
    val graph: Graph[Vertex, String] = Graph(vertices, edges)
    val connectedComponent = graph.connectedComponents()

    val connectedGraph = graph.joinVertices(connectedComponent.vertices) { (id, graphVertex, groupID) =>
      Vertex(graphVertex.pid, groupID)
    }

    val groupedDF = connectedGraph.vertices
      .map(_._2)
      .map(row => (row.pid, row.grpID))
      .toDF("temp_pids", DISAMBIGUATION_GROUP_ID)
      .withColumn(P_ID, explode(col("temp_pids")))
      .drop("temp_pids")
      .distinct()
      .checkpoint(true)

    (groupedDF,resolutionGraph)
  }

  def normalizeInventoryModels(inventoryModelSpecs: Array[InventoryModelSpec], spark:SparkSession,
                               config:Disambiguation): Array[InventoryModelSpec] = {
    EILOGGER.info("Normalizing inventory models")
    val allInvSchema = inventoryModelSpecs.flatMap(_.inventoryModel.schema).map(f=>(f.name,f.dataType)).toMap

    inventoryModelSpecs.map( spec =>{
      val emptyRemovedCols = spec.inventoryModel.schema.fields
        .map(field => {
          if (field.dataType == StringType)
            when(trim(col(field.name)) === lit(""), lit(null).cast("string")).otherwise(col(field.name)).as(field.name)
          else col(field.name).as(field.name)
        })

      val emptyRemovedDF = spec.inventoryModel.select(emptyRemovedCols: _*)
      val fieldsNeeded = minimumFields(config).diff(emptyRemovedDF.columns)

      println(s"""missing - ${fieldsNeeded.toList}""")
      val minimumSchema = fieldsNeeded
        .foldLeft(new StructType())((schema, field) => {
          schema.add(StructField(field, allInvSchema.getOrElse(field,StringType), true))
        })
      val minDF = spark.createDataFrame(spark.sparkContext.emptyRDD[Row], minimumSchema)
      val normalizedDF = SparkUtil.unionByName(emptyRemovedDF, minDF)
      val normalizedCandidateKesysDF = config.candidateKeys.filter(!_.caseSensitive).foldLeft(normalizedDF)((df, key) => {
        val exprs = if (normalizedDF.schema(key.name).dataType.isInstanceOf[ArrayType]) {
          s"transform(${key.name}, v -> lower(v))"
        }
        else s"lower(${key.name})"
        df.withColumn(key.name, expr(exprs))
      })
      InventoryModelSpec(normalizedCandidateKesysDF, spec.name)
    })
  }

  def confidenceEntityExtraction(invDF: DataFrame, config: Config): DataFrame = {
    val strategy = config.disambiguation.strategy.getOrElse(Strategy.empty)
    val rollingUpFields: Set[String] = (strategy.rollingUpFields ++ DEFAULT_ROLLUP_COLUMNS)
    val candidateKeys = config.disambiguation.candidateKeys.map(_.name)
    val aggregationFields = strategy.aggregation
    val precedenceFields = invDF.columns.diff(DIS_PROCESS_CORE_COLUMNS)
      .diff(rollingUpFields.toSeq)
      .diff(candidateKeys)
      .diff(aggregationFields.map(_.field))

    EILOGGER.jsonMiniPrint("Rolling up fields",rollingUpFields)
    EILOGGER.info(s"Strategy- ${strategy}, ${config.disambiguation.strategy.isDefined}")
    EILOGGER.jsonMiniPrint("candidateKeys", candidateKeys)

    val pidAggExpr = max(struct(PRECEDENCE,LAST_FOUND,P_ID)).as(P_ID)
    val pidArrayAggExpr = collect_set(col(PID_ARRAY)).as(PID_ARRAY)
    val lastSeenAggExpr = max(LAST_FOUND).as(LAST_FOUND)
    val firstSeenAggExpr = min(FIRST_FOUND).as(FIRST_FOUND)
    val prevDisambgGroupExpr = max(PREV_DISAMBIGUATION_GROUP_ID).as(PREV_DISAMBIGUATION_GROUP_ID)
    val aggregationFieldAggExpr = aggregationFields.map(f => expr(s"${f.function}(${f.field})").as(f.field))

    val rollupFieldAggExprs = rollingUpFields.map(field => collect_set(field).as(field))
    val precedenceAggExprs = precedenceFields.map(f => max(f).as(f))
    val candidateKeysAggExpr = candidateKeys.map(f=> collect_set(f).as(f))

    val finalAggExprs = Array(pidAggExpr, lastSeenAggExpr, firstSeenAggExpr, prevDisambgGroupExpr) ++
      rollupFieldAggExprs ++ precedenceAggExprs ++ candidateKeysAggExpr ++ aggregationFieldAggExpr
    val aggDF = invDF.groupBy(DISAMBIGUATION_GROUP_ID).agg(pidArrayAggExpr, finalAggExprs:_*)

    val pidFinalExpr = col(P_ID).getField(P_ID).as(P_ID)
    val lastSeenFinalExpr = col(LAST_FOUND)
    val firstSeenFinalExpr = col(FIRST_FOUND)
    val pidArrayFinalExpr = col(PID_ARRAY)
    val prevDisambgGroupFInalExpr = col(PREV_DISAMBIGUATION_GROUP_ID).as(PREV_DISAMBIGUATION_GROUP_ID)
    val aggregationFieldFinalExpr = aggregationFields.map(f => col(f.field))

    val rollUpFieldsFinalExpr = rollingUpFields.map(f =>{
      val dType=invDF.schema(f).dataType.asInstanceOf[StructType](f).dataType
      if (dType.isInstanceOf[ArrayType]) {
        when(array_distinct(col(f).getField(f)).notEqual(array(lit(null))),array_distinct(flatten(filter(sort_array(col(f),false).getField(f), ar => ar.isNotNull)))).otherwise(lit(null).cast(dType)).as(f)
      }else{
        when(array_distinct(col(f).getField(f)).notEqual(array(lit(null))),array_distinct( filter(sort_array(col(f),false).getField(f), ar => ar.isNotNull))).otherwise(lit(null).cast(s"array<${dType.typeName}>")).as(f)
      }
    })

    val precedenceFieldsFinalExpr = precedenceFields.map(f => col(f).getField(f).as(f))
    val candidateKeysFinalExpr = candidateKeys.map(f => filter(array_sort(col(f)), ar => ar.isNotNull).as(f))

    val postColumns = Array(pidFinalExpr,lastSeenFinalExpr, firstSeenFinalExpr,pidArrayFinalExpr, prevDisambgGroupFInalExpr) ++
      rollUpFieldsFinalExpr ++ precedenceFieldsFinalExpr ++ candidateKeysFinalExpr ++ aggregationFieldFinalExpr

    val postDF = aggDF.select(postColumns:_*)
    val flattendCandidateKeysDF = candidateKeys.foldLeft(postDF)((df,field) => {
      if(postDF.schema(field).dataType.asInstanceOf[ArrayType].elementType.isInstanceOf[ArrayType])
        df.withColumn(field,array_distinct(filter(flatten(col(field)), ar=>ar.isNotNull)))
      else df
    })
    flattendCandidateKeysDF
      .withColumn(SDSProperties.schema.LIFETIME, when(col("last_active_date").isNull || col("first_seen_date").isNull, lit(null).cast(IntegerType)).otherwise(datediff(from_unixtime(col("last_active_date") / 1000), from_unixtime(col("first_seen_date") / 1000))))
      .withColumn(SDSProperties.schema.RECENCY, datediff(from_unixtime(col(SDSProperties.schema.UPDATED_AT) / 1000), from_unixtime(col(SDSProperties.schema.LAST_FOUND) / 1000)))
      .withColumn(SDSProperties.schema.OBSERVED_LIFETIME, datediff(from_unixtime(col(SDSProperties.schema.LAST_FOUND) / 1000), from_unixtime(col(SDSProperties.schema.FIRST_FOUND) / 1000)))
      .withColumn(SDSProperties.schema.RECENT_ACTIVITY, when(col("last_active_date").isNull, lit(null).cast(IntegerType)).otherwise(datediff(from_unixtime(col(SDSProperties.schema.UPDATED_AT) / 1000), from_unixtime(col("last_active_date") / 1000))))

  }

  def findLatestUpdatedFields(inv: DataFrame, prevInv: DataFrame, entityConfig: Entity): DataFrame = {
    EILOGGER.info("Finding Latest Updated Fields")
    val processedPrevInv = EIUtil.preProcessPreviousInventory(prevInv, entityConfig)

    val prevInvJoind = inv.as("inv").join(processedPrevInv.as("prev"), expr(s"inv.$P_ID=prev.$P_ID"), "left")
      .select("inv.*", s"prev.$PREV_ATTRS",s"prev.prev__$LAST_UPDATED_ATTRS")

    val updatedDF = EIUtil.findLatestUpdateAttrExpr(prevInvJoind,entityConfig)

    updatedDF.drop(PREV_ATTRS,s"prev__$LAST_UPDATED_ATTRS").drop()
  }

  def enrichGraphDetails(interSourceResolver: DataFrame, entityClass: String, nonResolvedDf: DataFrame): (DataFrame, DataFrame) = {
    val interSourceResolverDF = interSourceResolver
      .withColumn("relationship_name", expr(s"'${entityClass} Has Fragment'"))
      .withColumn("inverse_relationship_name", expr(s"'Resolved To ${entityClass}'"))
      .withColumn("graph_id", sha2(functions.concat(col(P_ID), col("disambiguated_p_id"), col("updated_at_ts")), 256))
      .withColumn("source_graph_id", sha2(functions.concat(col("disambiguated_p_id"), col("updated_at_ts")), 256))
      .withColumn("target_graph_id", sha2(functions.concat(col(P_ID), lit("Fragment"), col("updated_at_ts")), 256))

    val enrichedNonResolvedDf = nonResolvedDf
      .withColumn("graph_id", sha2(functions.concat(col(P_ID), lit("Fragment"), col("updated_at_ts")), 256))
    (interSourceResolverDF, enrichedNonResolvedDf)
  }


  case class Artifacts(interSourceDisambiguatedInventoryModel: DataFrame,
                       interSourceResolver: Option[DataFrame],resolutionGraph: Option[DataFrame], nonResolvedDF:Option[DataFrame])

  case class InventoryModelSpec(inventoryModel: DataFrame, name: String)

  case class Vertex(pid: Seq[String], grpID: Long) extends Serializable
}
