package ai.prevalent.entityinventory.disambiguator

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils._
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.disambiguator.configs.specs.{CandidateKeySerializer, Config, InventoryModelInput}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{ORIGIN, ORIGIN_ENTITY_ENRICH, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.utils.EIUtil.{ removeNullFields}
import ai.prevalent.entityinventory.utils.{EILOGGER, EIUtil, SparkUtil}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.StringType
import org.json4s.Formats

object Disambiguator extends SDSSparkBaseConfigurable[EIJobArgs,Config] {

  override def getConfigManifest: Manifest[Config] = manifest[Config]

  override def getInitParams: EIJobArgs = new EIJobArgs

  override def configFormats: Formats = super.configFormats + CandidateKeySerializer

  override def execute(params: EIJobArgs, config: Config): Unit = {
    spark.sparkContext.setCheckpointDir(spark.conf.get("spark.checkpoint.dir"))
    config.configValidator()
    val reader = SDSTableReaderFactory.getDefault(spark)
    EILOGGER.jsonMiniPrint("Disambiguation Config", config)
    LOGGER.info(s"Current Update Date - ${params.currentUpdateDate}, Previous Update Date - ${params.prevUpdateDate}")
    
    val previousInRead = EIUtil.safeReadEntity(config.output.disambiguatedModelLocation, reader = reader)
    val prevInv = EIUtil.readPrevDF(params, previousInRead)
    if(prevInv.isEmpty) LOGGER.info(s"Previous Inventory (${config.output.disambiguatedModelLocation}) is empty")

    val prevNondis: Option[DataFrame] = config.output.fragmentLocation match {
      case Some(fragmentLocation) =>
        // Read the data and wrap it in Some if it's not empty
        val prevNondisData = EIUtil.safeReadEntity(fragmentLocation, expr(s"$UPDATED_AT_TS = to_timestamp(${params.prevUpdateDate}/1000)"), reader)

        // Check if the DataFrame is empty or not
        if (prevNondisData.isEmpty) {
          LOGGER.info(s"Previous Fragments ($fragmentLocation) is empty")
        } else {
          LOGGER.info(s"Previous Fragments ($fragmentLocation) is not empty")
        }
        Some(prevNondisData)

      case None =>
        // If fragmentLocation is None, return None
        None
    }



    val invModelSpecList = readEnrichModels(config.inventoryModelInput, reader, params.currentUpdateDate)
    val normalizedInvModelSpecList = normalizeInventoryModels(invModelSpecList,spark,config.disambiguation)
    val artifacts = build(normalizedInvModelSpecList, prevInv,config, spark,prevNondis)
    val interSourceDisambiguatedDF = artifacts.interSourceDisambiguatedInventoryModel


    EILOGGER.jsonMiniPrint("Disambiguation data frame build success with below schema", interSourceDisambiguatedDF.schema)
    getWriter().overwritePartition(interSourceDisambiguatedDF, config.output.disambiguatedModelLocation, Array(days(col(UPDATED_AT_TS))))
    if (config.output.isFragmentOLAPTable && artifacts.interSourceResolver.isDefined) {
      val entityClass = artifacts.interSourceResolver.get.select("class").first().getString(0)
      val (interSourceResolverDF: DataFrame,enrichedNonResolvedDf: DataFrame)=enrichGraphDetails(artifacts.interSourceResolver.get,entityClass, artifacts.nonResolvedDF.get)
      val fragmentTableProps = Map("graph.vertex.name" -> s"Fragment $entityClass", "graph.cache.enabled" -> "true")
      val resolverTableProps = Map("graph.edge.name" -> s"${entityClass} Has Fragment", "graph.edge.source.name" -> entityClass, "graph.edge.target.name" -> s"Fragment $entityClass", "graph.cache.enabled" -> "true")
      val fragmentLocation: String = config.output.fragmentLocation.getOrElse("")
      getWriter(fragmentTableProps).overwritePartition(enrichedNonResolvedDf, fragmentLocation, Array(days(col(UPDATED_AT_TS))))
      getWriter(resolverTableProps).overwritePartition(interSourceResolverDF, config.output.resolverLocation, Array(days(col(UPDATED_AT_TS))))
    } else if(artifacts.interSourceResolver.isDefined){
      getWriter().overwritePartition(artifacts.interSourceResolver.get, config.output.resolverLocation,
        Array(days(col(UPDATED_AT_TS)), col("class"), col("data_source_name")))
    }
    if(config.output.resolverGraphLocation.isDefined){
      getWriter().overwritePartition(artifacts.resolutionGraph.get.withColumn(UPDATED_AT_TS,expr(s"to_timestamp(${params.currentUpdateDate/1000})")), config.output.resolverGraphLocation.get,
        Array(days(col(UPDATED_AT_TS))))
    }
  }

  def getWriter(tableProps: Map[String, String] = Map.empty): SDSTableWriter = SDSTableWriterFactory.getDefault(
    spark, tableProperties = tableProps, options = Map("partitionOverwriteMode" -> "dynamic"))

  def readEnrichModels(modelInput: Array[InventoryModelInput], reader:SDSTableReader, upd:Long): Array[InventoryModelSpec] ={
    modelInput.map( inp => {
      val df = loadInventoryModel(reader, upd, spark, inp)
      val removeFields = inp.removeFields.filter(f => df.inventoryModel.columns.contains(f))
        .map(f => lit(null).cast(df.inventoryModel.schema(f).dataType).as(f))
        .toArray
      val dfFields  = df.inventoryModel.columns.diff(inp.removeFields).map(col(_))
      val updDF = df.inventoryModel.select((removeFields++dfFields):_*).withColumn(ORIGIN_ENTITY_ENRICH, if(inp.isEnrichSource) col(ORIGIN) else lit(null).cast(StringType))
      df.copy(updDF)
    }).filter(!_.inventoryModel.isEmpty)
  }
}
