package ai.prevalent.entityinventory.multihop

import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.{P_ID, RELATION_ID}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{LAST_FOUND, UPDATED_AT_TS}
import ai.prevalent.entityinventory.multihop.configs.{ColumnEnrichment, ConfigOptimizer, EntityEnrichment}
import ai.prevalent.entityinventory.utils.EIUtil
import ai.prevalent.sdspecore.exception.InvalidConfigSpecification
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.execution.streaming.CommitMetadata.format
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{col, count_distinct, expr, row_number}
import org.apache.spark.sql.types.StructType
import org.json4s.jackson.Serialization.writePretty
import org.slf4j.LoggerFactory

case class MultiHopEnrichment(reader:SDSTableReader, currentUpdatedAt:Long) extends LoggerBase{

  def enrich(entityDF: DataFrame, enrichments: Seq[EntityEnrichment],isEntityDF: Boolean = true): DataFrame = {
    configValidate(enrichments)
    val optimisedEnrichment=ConfigOptimizer.optimize(enrichments)
    Logger.logConfig("Optimised Enrichment Config:",optimisedEnrichment)

    val baseFields = if(isEntityDF){
      val entityClass = entityDF.select("class").first().getString(0)
      Map(entityClass -> s"main.$P_ID")
    } else {
      val row = entityDF.select("source_entity_class","target_entity_class").first()
      Map(row.getString(0) -> s"main.source_p_id", row.getString(1) -> s"main.target_p_id")
    }
    val mainBaseCol = if(isEntityDF) P_ID else RELATION_ID

    optimisedEnrichment.map(e => enrich(AggDFInfo(entityDF.alias("main"), List.empty, Set.empty), enrichment = e, baseFields, "main", Set("main")))
      .map(aggInfo => aggInfo.copy(enriches = aggInfo.enriches.filter( e => aggInfo.dataFrame.columns.contains(e.colName))))
      .filter(_.enriches.nonEmpty)
      .map(k => {
      val enriches = k.enriches
        val aggFunctions  = enriches
          .map(e => {
            if(e.aggFunction.equalsIgnoreCase("count_distinct"))
             count_distinct(expr(e.colName)).as(e.colName)
            else
            expr(s"${e.aggFunction}(${e.colName})").as(e.colName)
          })
        val aggDF = k.dataFrame
          .groupBy(s"main.$mainBaseCol").agg(aggFunctions.head, aggFunctions.tail: _*)
        enriches
          .foldLeft(aggDF)((df, e) => {
            try{
              if(Array("min","max").contains(e.aggFunction) && df.schema(e.colName).dataType.isInstanceOf[StructType])
                df.withColumn(e.colName, col(e.colName).getField(e.colName)).withColumn(e.colName, expr(e.colExpr))
              else df.withColumn(e.colName, expr(e.colExpr))
            } catch {
              case exc:Exception =>
                LOGGER.warn(s"Exception occurred while adding enrichment column ${e.colName} using $e, error = ${exc.getStackTrace.map(_.toString).mkString("\n")}")
                df
            }
          })
      })
      .foldLeft(entityDF)((df, righDF) => df.join(righDF, mainBaseCol,"LEFT"))
  }

  def enrich(aggDFInfo:AggDFInfo, enrichment: EntityEnrichment,
             baseClassField:Map[String,String], prevAlias:String, parentAlias:Set[String]):AggDFInfo = {
    val enrDF = EIUtil.safeReadEntity(enrichment.tableName,expr(s"$UPDATED_AT_TS = to_timestamp($currentUpdatedAt/1000)"),reader)
      .alias(enrichment.alias)
    if(enrDF.isEmpty) {
      LOGGER.warn(s"Enrichment table ${enrichment.tableName} is not available in the datalake, so skipping $enrichment")
      return  aggDFInfo
    }

    if(baseClassField.isEmpty && enrichment.leftCol.isEmpty){
      throw new InvalidConfigSpecification(s"baseCol should be defined for ${enrichment.tableName}")
    }

    val rightDF = enrichment.preTransform.foldLeft(enrDF.filter(enrichment.filter))((df,der) => df.withColumn(der.colName,expr(der.colExpr)))

    val isEntityDF = rightDF.columns.contains("class")

    val  (baseColWoAlias, rightColWoAlias, newBaseClassFields) = if(isEntityDF){

      val rightClass = rightDF.select("class").first().getString(0)
      val newBaseClassField = Map(rightClass -> s"${enrichment.alias}.$P_ID")
      (
        enrichment.leftCol.getOrElse(baseClassField(rightClass)),
        enrichment.rightCol.getOrElse( s"${enrichment.alias}.$P_ID"),
        newBaseClassField
      )
    } else {
      val row = rightDF.select("source_entity_class","target_entity_class").first()

      val newBaseClassField = Map(row.getString(0) -> s"${enrichment.alias}.source_p_id", row.getString(1) -> s"${enrichment.alias}.target_p_id")

      if(row.getString(0).equals(row.getString(1))){
        if(!(enrichment.rightCol.isDefined && enrichment.leftCol.isDefined)){
          throw new InvalidConfigSpecification(s"baseCol and rightCol should be defined for self relationship, ${enrichment.tableName}")
        }
        (enrichment.leftCol.get, s"${enrichment.alias}.${enrichment.rightCol.get}", baseClassField.removedAll(baseClassField.keySet))
      }
      else if(baseClassField.contains(row.getString(0)))
        (
          enrichment.leftCol.getOrElse(baseClassField(row.getString(0))),
          enrichment.rightCol.getOrElse( s"${enrichment.alias}.source_p_id"), newBaseClassField
        )
      else
        ( enrichment.leftCol.getOrElse(baseClassField(row.getString(1))),
          enrichment.rightCol.getOrElse(s"${enrichment.alias}.target_p_id"),
          newBaseClassField
        )
    }


    val  (baseCol, rightCol) = (
        if(baseColWoAlias.contains(".")) baseColWoAlias else s"$prevAlias.$baseColWoAlias",
        if(rightColWoAlias.contains(".")) rightColWoAlias else s"${enrichment.alias}.$rightColWoAlias"
    )


    val updRighDF = if(!isEntityDF)
      rightDF.withColumn(s"${enrichment.alias}_row_num", row_number().over(Window.partitionBy(rightCol).orderBy(rightCol)))
        .withColumn(s"${enrichment.alias}_row_num",expr(s"${enrichment.alias}_row_num=1"))
    else rightDF

    val joinedDF = aggDFInfo.dataFrame.join(updRighDF, expr(s"$baseCol == $rightCol"), enrichment.joinType)

    val aliasCondition = aggDFInfo.appliedAlias
      .diff(parentAlias)
      .map(a => s"${a}_row_num")
      .intersect(joinedDF.columns.toSet)
      .map(a => s"($a OR $a IS NULL)").+("(true)")
      .mkString("("," AND ",")")

    val aggExprEnrichs = enrichment.colEnrichments.collect {
      case e if(e.aggFunction.equalsIgnoreCase("LATEST")) => e.copy(aggFunction = "max", aggExpr = s"CASE WHEN ${e.aggExpr} IS NULL THEN NULL ELSE named_struct('x',${enrichment.alias}.$LAST_FOUND, 'y',${enrichment.alias}.${if(isEntityDF) P_ID else RELATION_ID}, '${e.colName}',${e.aggExpr}) END")
      case e if(e.aggFunction.equalsIgnoreCase("EARLIEST")) => e.copy(aggFunction = "min", aggExpr = s"CASE WHEN ${e.aggExpr} IS NULL THEN NULL ELSE named_struct('x',${enrichment.alias}.$LAST_FOUND,'y', ${enrichment.alias}.${if(isEntityDF) P_ID else RELATION_ID},'${e.colName}', ${e.aggExpr}) END")
      case e:ColumnEnrichment => e
    }.map(e => e.copy(aggExpr = s"CASE WHEN $aliasCondition AND $rightCol IS NOT NULL THEN ${e.aggExpr} END"))

    val aggExprAppliedDF = aggExprEnrichs
      .foldLeft(joinedDF)((df, e) => {
        try{
          df.withColumn(e.colName, expr(e.aggExpr))
        }catch {
          case exc:Exception =>
            LOGGER.warn(s"Exception occurred while adding enrichment column ${e.colName} using $e, error = ${exc.getStackTrace.map(_.toString).mkString(exc.getMessage,"\n","")}")
            df
        }
      })


    val previoudAggExprReApply =  aggDFInfo.enriches.collect {
      case e if(!isEntityDF) => e.copy(aggExpr = s"CASE WHEN ${enrichment.alias}_row_num OR ${enrichment.alias}_row_num IS NULL THEN ${e.colName} END")
      case e  => e.copy(aggExpr = e.colName)
    }.foldLeft(aggExprAppliedDF)((df,e) => {
      if(df.columns.contains(e.colName))
        df.withColumn(e.colName,expr(e.aggExpr))
      else df
    })

    val newAggDFInfo = AggDFInfo(previoudAggExprReApply, aggDFInfo.enriches++aggExprEnrichs, aggDFInfo.appliedAlias+enrichment.alias)
    enrichment.inheritedPropertyEnrichment
      .foldLeft(newAggDFInfo)( (agginfo, e) =>
        enrich( agginfo, enrichment = e , newBaseClassFields, enrichment.alias, parentAlias+enrichment.alias)
      )
  }

  def configValidate(enrichments: Seq[EntityEnrichment]): Unit = {
    val columnEnrichments: Seq[ColumnEnrichment] = enrichments.flatMap(flatMapEnrichment)
    val duplicateColumnList = columnEnrichments.groupBy(_.colName).values.filter(_.size>1)
    if(duplicateColumnList.nonEmpty){
      LOGGER.error(s"Duplicate column for ${duplicateColumnList.mkString(",")}")
      throw new InvalidConfigSpecification(s"Duplicate column for ${duplicateColumnList.mkString(",")}")
    }
  }


  private def flatMapEnrichment(enrichment:EntityEnrichment):Seq[ColumnEnrichment] = {
    enrichment.colEnrichments++enrichment.inheritedPropertyEnrichment.flatMap(flatMapEnrichment)
  }

  case class AggDFInfo(dataFrame: DataFrame, enriches:Seq[ColumnEnrichment], appliedAlias:Set[String])
}

object Logger {
  private val logger = LoggerFactory.getLogger(getClass)

  def logConfig(message: String, enrichments: Seq[EntityEnrichment]): Unit = {
    val json = writePretty(enrichments)
    logger.info(s"$message: $json")
  }
}