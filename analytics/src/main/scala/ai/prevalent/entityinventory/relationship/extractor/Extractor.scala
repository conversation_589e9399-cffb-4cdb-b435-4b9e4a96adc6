package ai.prevalent.entityinventory.relationship.extractor

import ai.prevalent.entityinventory.common.configs.{EIJob<PERSON>rgs, Property}
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.loader.configs.specs.{Config => LoaderConfig}
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.{Config, DisambiguationResolvers, InputSource, InputSourceInfo, MappingProperties, MappingPropertiesSerializer}
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.StructFieldReplaceUtils.{getNonStructFields, replaceStructProperties}
import ai.prevalent.entityinventory.utils.{EILOGGE<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SparkUtil}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriterFactory}
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.execution.streaming.CommitMetadata.format
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{DataTypes, StringType, TimestampType}
import org.json4s.Formats
import org.json4s.jackson.Serialization.writePretty
import org.slf4j.LoggerFactory

import java.io.File

object Extractor extends SDSSparkBaseConfigurable[EIJobArgs,Config]{

  override def execute(jobArgs: EIJobArgs, config: Config): Unit = {
    validateConfig(config)

    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark,tableProperties = Map.empty,options = Map("partitionOverwriteMode"->"dynamic"))

    Logger.logConfig("Relationship Config:",config)
    var prevMinSDMFull = reader.readOrElse(config.output.prevMiniSDM,spark.emptyDataFrame
      .withColumn(UPDATED_AT_TS,lit(null).cast(DataTypes.TimestampType))
      .withColumn(KG_CONTENT_TYPE, lit(null).cast(StringType))
      .withColumn("kg_config",lit(null).cast(StringType))
    )

    prevMinSDMFull = if(prevMinSDMFull.columns.contains(KG_CONTENT_TYPE))
      prevMinSDMFull
    else
      prevMinSDMFull.withColumn(KG_CONTENT_TYPE, lit("data")).withColumn("kg_config",lit(null).cast(StringType))

    val prevMinSDM = EIUtil.readPrevDF(args = jobArgs, eiDF = prevMinSDMFull)
      .withColumn(UPDATED_AT,expr(s"UNIX_MILLIS($UPDATED_AT_TS)"))
    LOGGER.info(s"Previous MiniSDM columns: ${prevMinSDM.columns.mkString(", ")}")

    val prevConfig: Option[String] = EIUtil.getPrevConfig(prevMinSDM, jobArgs)

    val (inputSources,prevMiniSDMDf) = readInputSources(config.inputSourceInfo,prevMinSDM,reader, jobArgs)
    val resolvers = {
      val latestFilter = expr(s"$UPDATED_AT_TS = to_timestamp(${jobArgs.currentUpdateDate}/1000)")
      val intraDF = EIUtil.safeReadResolver(config.intraSourcePath, latestFilter, reader)
      val interSourceDF = EIUtil.safeReadResolver(config.interSourcePath, latestFilter, reader)
      val interTargetDF = EIUtil.safeReadResolver(config.interTargetPath, latestFilter, reader)
      val interDF = SparkUtil.unionByName(interSourceDF, interTargetDF)
      DisambiguationResolvers(intraDF, interDF)
    }

    val relationInfo = config.execute(inputSources, prevMiniSDMDf.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL"), config ,resolvers, jobArgs, prevConfig)

    val outputDF = relationInfo.relationDF
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    val nullRemovedOutDf  = removeNullFields(outputDF)
    writer.overwritePartition(nullRemovedOutDf,config.output.outputTable, Array(days(col(UPDATED_AT_TS))))

    val miniSDMDF = relationInfo.miniSDM
      .withColumn(UPDATED_AT, lit(jobArgs.currentUpdateDate))
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
    val nullRemovedMiniSDMDf  = removeNullFields(miniSDMDF)

    val finalMiniSDM = EIUtil.prepareInvDFWithConfig(config = config.toString, invDF = nullRemovedMiniSDMDf, args = jobArgs)
    writer.overwritePartition(finalMiniSDM,config.output.prevMiniSDM, Array(days(col(UPDATED_AT_TS)), col(KG_CONTENT_TYPE)))
  }

  def readInputSources(inputSourcesInfo: List[InputSourceInfo],prevMiniSDM: DataFrame, reader: SDSTableReader, jobArgs: EIJobArgs): (List[InputSource], DataFrame) = {
    val inputSourcesWithMiniSDM = inputSourcesInfo.filter(info => reader.isTableExists(info.sdmPath)).map { info =>
      LOGGER.info(s"Processing table: ${info.sdmPath}")

      val baseDF = reader.read(info.sdmPath)
        .filter(col(info.dataIntervalTimestampKey).between(
          to_timestamp(lit(jobArgs.srdmHistoricalParsedIntervalStartEpoch).divide(1000)),
          to_timestamp(lit(jobArgs.parsedIntervalEndEpoch).divide(1000))))
        .filter(info.filter)
      val relationTempPropsAddedDF = applyTempTransform(baseDF, info.temporaryProperties,info.uniqueRecordIdentifierKey)
      val (sourceJoinedDF, prevSourceSdm) = applyJoin(relationTempPropsAddedDF, prevMiniSDM, info.sourceMappingInfo, "source", jobArgs.currentUpdateDate, reader)
      val (targetJoinedDF, prevMiniSDMUpdated) = applyJoin(sourceJoinedDF, prevSourceSdm, info.targetMappingInfo, "target", jobArgs.currentUpdateDate, reader)
      val df= if (!targetJoinedDF.columns.contains("event_timestamp_epoch")) {
        targetJoinedDF.withColumn("event_timestamp_epoch", expr(s"unix_millis(${info.dataEventTimestampKey})"))
      }else targetJoinedDF
      val finalDf= df.withColumn(s"${ORIGIN}__temp", lit(info.origin))
        .withColumn("event_timestamp_epoch", expr("cast(event_timestamp_epoch as bigint)"))
        .withColumn(UPDATED_AT, lit(jobArgs.currentUpdateDate))
        .withColumn("is_incremental_delta", col(info.dataIntervalTimestampKey) >= to_timestamp(lit(jobArgs.parsedIntervalStartEpoch).divide(1000)))
        .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
      val enrichedDF = Enrichment.applyEnrichments(finalDf, info.enrichments, reader)
      val sourceLoaders = if (info.sourceMappingInfo.configPath.nonEmpty) {
        info.sourceMappingInfo.configPath.map(path => ConfigUtils.getConfig(spark, path, manifest[LoaderConfig], Loader.configFormats).asInstanceOf[LoaderConfig])
      } else List.empty[LoaderConfig]

      val targetLoaders = if (info.targetMappingInfo.configPath.nonEmpty) {
        info.targetMappingInfo.configPath.map(path => ConfigUtils.getConfig(spark, path, manifest[LoaderConfig], Loader.configFormats).asInstanceOf[LoaderConfig])
      } else List.empty[LoaderConfig]
      val temporaryAttributesRef =  info.temporaryProperties.map(_.colName)
      val sourceJoinCondition = Option(info.sourceMappingInfo).flatMap(src => Option(src.joinCondition))
      val targetJoinCondition = Option(info.targetMappingInfo).flatMap(trg => Option(trg.joinCondition))
      val joinCondition = Array(sourceJoinCondition, targetJoinCondition).flatten

      (InputSource(enrichedDF, info.dataEventTimestampKey,temporaryAttributesRef, sourceLoaders, targetLoaders, joinCondition),prevMiniSDMUpdated)
    }
    val inputSources = inputSourcesWithMiniSDM.map(_._1)
    val updatedPrevMiniSDM = inputSourcesWithMiniSDM.map(_._2).reduceOption(_.unionByName(_)).getOrElse(prevMiniSDM)
    (inputSources, updatedPrevMiniSDM)
  }


  def applyTempTransform(sourceDF: DataFrame, temporaryProps: Array[Property], uuidColumn: String): DataFrame = {

    EILOGGER.info(s"""Adding temporary variables for fields -  ${temporaryProps.map(_.colName).toList}""".stripMargin)
    val nonStructFields = getNonStructFields(temporaryProps)
    val missingFields = nonStructFields.diff(sourceDF.columns)
    val nonStructFieldsAddedDf = missingFields.foldLeft(sourceDF)((df, field) => {
      df.withColumn(field, lit(null))
    })
    val tempDf=temporaryProps.foldLeft(nonStructFieldsAddedDf)((df, prop) => {
      val (tempStructFieldsAddedDF, structFieldsReplacedPropList) = replaceStructProperties(df, Seq(prop))
      tempStructFieldsAddedDF.withColumn(prop.colName, expr(structFieldsReplacedPropList(0).colExpr))
    })
    val uuidAddedTempDf = tempDf.withColumn(UUID, col(uuidColumn))
    uuidAddedTempDf
  }

  def applyJoin(df: DataFrame, prevminiSDM:DataFrame,mapping: MappingProperties, entityPrefix: String, currentUpdateDate:Long, reader: SDSTableReader): (DataFrame, DataFrame) = {
    if (mapping.tableName != null) {
      val joinTable = reader.read(mapping.tableName).filter(s"$UPDATED_AT_TS = to_timestamp(${currentUpdateDate}/1000)")
      val joinedPrevMiniSDM =if (prevminiSDM.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL").isEmpty)
        prevminiSDM
      else prevminiSDM.as("s").join(joinTable.as("e"), expr(mapping.joinCondition)).select(col("s.*"))

      val joinedDF = df.as("s")
        .join(joinTable.as("e"), expr(mapping.joinCondition))
        .select(col("s.*"),
          col("e.p_id").as(s"${entityPrefix}_p_id"),
          col("e.class").as(s"${entityPrefix}_entity_class")
        )
      (joinedDF,joinedPrevMiniSDM)
    } else (df,prevminiSDM)
  }




  override def configFormats: Formats = super.configFormats + MappingPropertiesSerializer
  override def getInitParams: EIJobArgs = new EIJobArgs()

  override def getConfigManifest: Manifest[Config] = manifest[Config]

  def validateConfig(config: Config): Unit = {
    if (config.inputSourceInfo.size > 1) {
      val hasJoinCondition = config.inputSourceInfo.map { info =>
        val sourceJoinCondition = Option(info.sourceMappingInfo).map(_.joinCondition).exists(_ != null)
        val targetJoinCondition = Option(info.targetMappingInfo).map(_.joinCondition).exists(_ != null)
        sourceJoinCondition || targetJoinCondition
      }
      if (hasJoinCondition.contains(true) && hasJoinCondition.contains(false)) {
        throw new IllegalArgumentException("Invalid configuration: Some InputSourceInfo objects have joinCondition specified (in either sourceMappingInfo or targetMappingInfo) while others do not. KG does not support such mixed cases.")
      }
    }
    LOGGER.info("Config validation passed.")
  }


  object Logger {
    private val logger = LoggerFactory.getLogger(getClass)
    def logConfig(message: String,config: Config): Unit = {
      val json = writePretty(config)
      logger.info(s"$message: $json")
    }
  }

}
