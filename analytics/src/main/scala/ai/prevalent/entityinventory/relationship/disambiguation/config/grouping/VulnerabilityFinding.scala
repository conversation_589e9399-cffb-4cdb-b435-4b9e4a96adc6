package ai.prevalent.entityinventory.relationship.disambiguation.config.grouping

import ai.prevalent.entityinventory.delta.Change
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.DISAMBIGUATION_GROUP_ID
import ai.prevalent.entityinventory.relationship.disambiguation.config.{Config, DisambiguationGrouping, DisambiguationGroupingDelta}
import org.apache.spark.sql.{DataFrame, SparkSession}
import org.apache.spark.sql.expressions.Window
import org.apache.spark.sql.functions.{coalesce, col, collect_set, expr, first, lag, last, lead, lit, max, monotonically_increasing_id, sum, when}

case class VulnerabilityFinding(blockVariables: List[String], statusField: String, blockCloseVariables: List[String] = List("rel_source_name", "start_epoch")) extends DisambiguationGrouping {

  override def generateGroup(nonResolvedDF: DataFrame, config: Config): DataFrame = {

    val selectVars = Array(col("rel_source_name"), col("last_reopened_date"), col("start_epoch"), col("relationship_last_seen_date"), col("relationship_id"), col(statusField)) ++ blockVariables.map(col(_))

    val lastReopenedFieldAddedDf = if (!nonResolvedDF.columns.contains("last_reopened_date")) nonResolvedDF.withColumn("last_reopened_date", expr("cast(null as bigint)")) else nonResolvedDF
    val blockVariableDF = lastReopenedFieldAddedDf
      .select(selectVars: _*)
      .distinct()

    val blockCloseWindow = Window.partitionBy((blockVariables ++ blockCloseVariables).distinct.map(col): _*).orderBy(col("relationship_last_seen_date").asc, col("relationship_id").asc)
    val blockWindow = Window.partitionBy(blockVariables.map(col): _*).orderBy(col("relationship_last_seen_date").asc, col("relationship_id").asc)
    val groupedDF = blockVariableDF.withColumn("prev_status", lag(col(statusField), 1).over(blockCloseWindow))
      .withColumn("block_status", expr("case " +
        "when current_status=prev_status then null " +
        "when prev_status is null and current_status='Closed' then null " +
        "when prev_status is null then current_status else current_status end")
      ).withColumn("is_closed_sum", sum(expr("case when block_status='Open' then 1 when block_status='Closed' then -1 else 0 end")).over(blockWindow))
      .withColumn("closed_status", expr("case when is_closed_sum=0 then 'Closed' end"))
      .withColumn("block_id", first(expr("case when is_closed_sum=0 then relationship_id end"), ignoreNulls = true).over(blockWindow.rowsBetween(Window.currentRow, Window.unboundedFollowing)))
      .withColumn(s"new__$statusField", first(expr("case when is_closed_sum=0 then 'Closed' end"), ignoreNulls = true).over(blockWindow.rowsBetween(Window.currentRow, Window.unboundedFollowing)))
      .withColumn(s"old__$statusField", col(statusField))
      .withColumn(statusField, coalesce(col(s"new__$statusField"), lit("Open")))
      .withColumn(DISAMBIGUATION_GROUP_ID, coalesce(col("block_id"), last(col("relationship_id")).over(blockWindow.rowsBetween(Window.currentRow, Window.unboundedFollowing))))

    groupedDF.select(DISAMBIGUATION_GROUP_ID, "relationship_id", statusField)
  }

  override def getConfigDelta(otherConfig: DisambiguationGrouping): DisambiguationGroupingDelta = {
    otherConfig match {
      case other: VulnerabilityFinding => {
        val blockVariablesChanged = blockVariables != other.blockVariables
        val addedBlockVariables = blockVariables.diff(other.blockVariables)
        val removedBlockVariables = other.blockVariables.diff(blockVariables)
        if (blockVariablesChanged) {
          DisambiguationGroupingDelta(
            Some(Change("Disambiguation Grouping", "Vulnerability Finding Change", Option.empty, Map("Old variables" -> other.blockVariables.mkString(","), "Added variables" -> addedBlockVariables.mkString(",")), "Attribute"))
            , Some(Change("Disambiguation Grouping", "Vulnerability Finding Change", Option.empty, Map("Old variables" -> other.blockVariables.mkString(","), "Removed variables" -> removedBlockVariables.mkString(",")), "Attribute")),
            if (statusField != other.statusField) Some(Change("Disambiguation Grouping", "Vulnerability Finding Change", Option.empty, Map("Old status field" -> other.statusField, "New status field" -> statusField), "Attribute")) else Option.empty
          )
        } else {
          DisambiguationGroupingDelta()
        }
      }
      case _ =>  DisambiguationGroupingDelta(
        groupingTypeChange = Some(Change("Disambiguation Grouping", f"Disambiguation Grouping Type Changed to ${otherConfig.getClass.getSimpleName}", Option.empty, Map("Old Grouping Type" -> "VulnerabilityFinding", "New Grouping Type" -> this.getClass.getSimpleName), "Attribute"))
      )
    }
  }

  override def getConfigDelta(deltas: Seq[Change]): DisambiguationGroupingDelta = null
}
