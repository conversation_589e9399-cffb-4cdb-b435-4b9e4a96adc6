package ai.prevalent.entityinventory.lineage


case class ColumnLineageDefinition(sourcePlatform: String = "hive", sourceDataset: String,
                                   targetPlatform: String = "hive", targetDataset: String,
                                   columnMappings: List[ColumnMapping])

case class ColumnMapping(sourceColumns: List[String],
                         targetColumns: List[String], transformation: String)
