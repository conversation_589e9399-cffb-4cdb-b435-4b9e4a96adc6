package ai.prevalent.entityinventory.enrichment

import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.ORIGIN_LOOKUP_ENRICH
import ai.prevalent.entityinventory.common.configs.{EIConfig, Property}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.UPDATED_AT_TS
import ai.prevalent.entityinventory.utils.{EILOGGER, EIUtil}
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.{array, array_union, col, expr, lit, to_timestamp, when}
import ai.prevalent.entityinventory.delta.{Change, EnrichmentDelta}

case class LookupInfo(tableName: String,name: Option[String],filter: String = "true", preTransform: Array[Property] = Array.empty, enrichmentColumns: Array[String])

case class Enrichment(lookupInfo: LookupInfo, sourcePreTransform: Array[Property] = Array.empty, joinType: String = "LEFT",
                      joinCondition: String) extends EIConfig[Enrichment, EnrichmentDelta]{
  def enrich(source: DataFrame,  reader: SDSTableReader): DataFrame = {
    import source.sparkSession.implicits._
    val sourceTransformed = transform(source, sourcePreTransform)
    var lookupDF = reader.readOrElse(lookupInfo.tableName, source.sparkSession.emptyDataFrame)
    lookupDF = EIUtil.preProcesmptyStringtoNull(lookupDF)
    if (lookupDF.isEmpty && joinType.toUpperCase == "LEFT") {
      sourceTransformed
    } else if (lookupDF.isEmpty && joinType.toUpperCase == "INNER") {
      sourceTransformed.limit(1).filter("false")
    } else {
      val lookupTransformed = transform(lookupDF.filter(lookupInfo.filter), lookupInfo.preTransform)
      val lookupDf=lookupInfo.name match {
        case Some(lookupName) =>
          if (lookupTransformed.columns.contains(ORIGIN_LOOKUP_ENRICH)) {
            lookupTransformed.withColumn(ORIGIN_LOOKUP_ENRICH,
              when(col(ORIGIN_LOOKUP_ENRICH).isNotNull, array_union(col(ORIGIN_LOOKUP_ENRICH), array(lit(lookupName))))
                .otherwise(array(lit(lookupName))))
          } else {
            lookupTransformed.withColumn(ORIGIN_LOOKUP_ENRICH, array(lit(lookupName)))
          }
        case None =>
          lookupTransformed.withColumn(ORIGIN_LOOKUP_ENRICH, array(lit(null)))
      }
      val finalCols = lookupInfo.enrichmentColumns.map(f => col(s"e.$f")) ++ Seq(col("s.*"), col(ORIGIN_LOOKUP_ENRICH))
      val isUpdatedAtTsPresent = lookupDf.columns.contains(UPDATED_AT_TS)
      val joinExpr = s"($joinCondition)" + (if (isUpdatedAtTsPresent) s" AND e.$UPDATED_AT_TS = s.$UPDATED_AT_TS" else "")
      sourceTransformed.as("s")
        .join(lookupDf.as("e"), expr(joinExpr), joinType)
        .select(finalCols: _*)
    }
  }

  def transform(df: DataFrame, transform: Array[Property]): DataFrame = {
      transform.foldLeft(df)((dfp, prop) => {
        dfp.withColumn(prop.colName, expr(prop.colExpr))
      })
  }

  override def getConfigDelta(otherConfig: Enrichment): EnrichmentDelta = ???

  override def getConfigDelta(deltas: Seq[Change]): EnrichmentDelta = ???
}

object Enrichment {
  def applyEnrichments(source: DataFrame, enrichemnts: Seq[Enrichment], reader: SDSTableReader) : DataFrame ={
    source.printSchema()
    enrichemnts.foldLeft(source)((df,enr) => {
      enr.enrich(df,reader)
    })
  }
}