package ai.prevalent.entityinventory.loader

import ai.prevalent.entityinventory.common.configs.EIJobArgs
import ai.prevalent.entityinventory.disambiguator.DisambiguatorUtils.DATA_SOURCE_SUBSET_NAME
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{EVENT_TIMESTAMP_TS, KG_CONTENT_TYPE, ORIGIN, PARSED_INTERVAL_TIMESTAMP_TS, RECENCY, UPDATED_AT, UPDATED_AT_TS}
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.utils.EIUtil.removeNullFields
import ai.prevalent.entityinventory.utils.{EILOGGER, EIUtil, SparkUtil}
import ai.prevalent.sdspecore.sparkbase.SDSSparkBaseConfigurable
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriterFactory}
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, NullType, TimestampType}

import java.io.File

object Loader extends SDSSparkBaseConfigurable[EIJobArgs, Config] {

  def build(jobArgs: EIJobArgs, config: Config, reader:SDSTableReader): DataFrame = {
    val previousInRead = EIUtil.safeReadEntity(config.outputTable, reader = reader)
    val previousInventory = EIUtil.readPrevDF(jobArgs, previousInRead)
    val prevConfig: Option[String] = EIUtil.getPrevConfig(previousInventory, jobArgs)

    val sourceDF: DataFrame = reader.read(config.dataSource.get.srdm).filter(
      col(config.dataSource.get.dataIntervalTimestampKey) <= to_timestamp(lit(jobArgs.parsedIntervalEndEpoch).divide(1000)))
    EILOGGER.jsonMiniPrint("Source schema", sourceDF.schema)

    val latestInventory = LoaderUtils.build(sourceDF, previousInventory.filter(s"$KG_CONTENT_TYPE='data' OR $KG_CONTENT_TYPE IS NULL"), config, prevConfig, jobArgs, reader)(spark)
      .withColumn(UPDATED_AT_TS, expr(s"to_timestamp($UPDATED_AT/1000)"))
      .withColumn("fragments", lit(1))
      .withColumn(DATA_SOURCE_SUBSET_NAME,expr(ORIGIN))
    EILOGGER.jsonMiniPrint("Inventory dataframe build success with schema", latestInventory.schema)

    latestInventory
  }

  override def execute(jobArgs: EIJobArgs, config: Config): Unit = {
    config.configValidator()
    spark.conf.set("spark.sql.caseSensitive", "true")
    val reader = SDSTableReaderFactory.getDefault(spark)
    val writer = SDSTableWriterFactory.getDefault(spark, tableProperties = Map.empty, options = Map("partitionOverwriteMode" -> "dynamic"))
    EILOGGER.jsonMiniPrint("Loader Config", config)

    val outDF = build(jobArgs, config, reader)
    val outputDF = removeNullFields(outDF)
    EILOGGER.jsonMiniPrint("Inventory dataframe build by removing void fields schema", outputDF.schema)
    val finalEntityDF = config.operationalMode match {
      case "dailyReset" =>
        outputDF.filter(s"""${RECENCY}==0""")
      case _ =>
        outputDF
    }

    val finalDF = EIUtil.prepareInvDFWithConfig(config = config.toString, invDF = finalEntityDF, args = jobArgs)

    writer.overwritePartition(finalDF, config.outputTable, Array(days(col(UPDATED_AT_TS)),col(KG_CONTENT_TYPE)))
  }

  override def getInitParams: EIJobArgs = new EIJobArgs()

  override def getConfigManifest: Manifest[Config] = manifest[Config]


}
