package ai.prevalent.entityinventory.loader

import ai.prevalent.entityinventory.common.configs.{EIJobArgs, Property}
import ai.prevalent.entityinventory.delta.LoaderConfigDelta
import ai.prevalent.entityinventory.disambiguator.configs.SDSDisambiguatorProperties.schema.P_ID
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import ai.prevalent.entityinventory.loader.Loader.spark
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.loader.configs.specs.Config
import ai.prevalent.entityinventory.loader.configs.SDSProperties
import ai.prevalent.entityinventory.utils.StructFieldReplaceUtils.{getNonStructFields, replaceStructProperties}
import ai.prevalent.entityinventory.utils.{CasePropertyExpressionReplacer, EILOGGER, EIUtil, SparkUtil}
import ai.prevalent.sdspecore.jobbase.LoggerBase
import ai.prevalent.sdspecore.sparkbase.table.SDSTableReader
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, LongType, StringType, TimestampType}
import org.apache.spark.sql.{DataFrame, SparkSession, functions}

import scala.collection.mutable.{HashSet, ListBuffer}
import scala.util.control.Exception.allCatch

object LoaderUtils extends LoggerBase {

  /**
   * Add core fields to the source df
   *
   * @param sourceDF
   * @param startDateEpoch
   * @param endDateEpoch
   * @param config
   * @return
   */


  def addRunCoreProperties(sourceDF: DataFrame, startDateEpoch: Long, endDateEpoch: Long, config: Config): DataFrame = {
    sourceDF.withColumn(SDSProperties.schema.UPDATED_AT, expr(s"$endDateEpoch"))
      .withColumn(SDSProperties.schema.ANALYSIS_PERIOD_START_EPOCH, expr(s"$startDateEpoch").cast(LongType))
      .withColumn(SDSProperties.schema.ORIGIN, expr(config.origin))
  }

  /**
   * Added temporary fields to source dataframe
   *
   * @param sourceDF
   * @param config
   * @param isInventoryDerived
   * @return
   */
  def applyTempTransform(sourceDF: DataFrame, config: Config,
                         isInventoryDerived: Boolean): DataFrame = {
    val temporaryProperties = config.temporaryProperties
      .filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)

    EILOGGER.info(s"""Adding temporary variables for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"}, fields -  ${temporaryProperties.map(_.colName).toList}""".stripMargin)
    val nonStructFields = getNonStructFields(temporaryProperties)
    val missingFields = nonStructFields.diff(sourceDF.columns)
    val nonStructFieldsAddedDf = missingFields.foldLeft(sourceDF)((df, field) => {
      df.withColumn(field, lit(null))
    })
    temporaryProperties.foldLeft(nonStructFieldsAddedDf)((df, prop) => {
      val (tempStructFieldsAddedDF, structFieldsReplacedPropList) = replaceStructProperties(df, Seq(prop))
      tempStructFieldsAddedDF.withColumn(prop.colName, expr(structFieldsReplacedPropList.head.colExpr))
    })
  }

  /**
   * Map inventory fields with their corresponding expression to the dataframe according to the config
   *
   * @param stage1DF
   * @param config
   * @param properties
   * @param startDateEpoch
   * @param endDateEpoch
   * @param isInventoryDerived
   * @return transformed df
   */


  def applyTransformations(stage1DF: DataFrame, config: Config, properties: Seq[Property], startDateEpoch: Long,
                           endDateEpoch: Long, isInventoryDerived: Boolean): DataFrame = {
    val sourceWithCoreFieldsDF = if (!isInventoryDerived) addRunCoreProperties(stage1DF, startDateEpoch, endDateEpoch, config) else stage1DF
    val filteredProperties = properties
      .filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)
    LOGGER.info(s"""Adding transformation for ${if (isInventoryDerived) "Inventory fields" else "SDM fields"}, fields -  ${filteredProperties.map(_.colName).toList}""".stripMargin)
    val transformedDF = applyPropertyTransformations(sourceWithCoreFieldsDF, filteredProperties)
    transformedDF
  }

  def applyPropertyTransformations(
                                    sourceDF: DataFrame,
                                    properties: Seq[Property]
                                  ): DataFrame = {
      val propertiesReplaced = SparkUtil.propertyExpressionReplace(properties)
      val propertiesExpr = propertiesReplaced.map(prop => expr(prop.colExpr).as(prop.colName))
      val dfColumns = sourceDF
              .columns
              .diff(properties.map(_.colName))  // Exclude the columns of the filtered properties
              .intersect(sourceDF.columns)     // Ensure that the remaining columns exist in the DataFrame
              .map(name => expr(s"`$name`").as(name)) // Map each column to a column expression

      val requiredCols = dfColumns ++ propertiesExpr
      sourceDF.select(requiredCols: _*)

    }



  /**
   * Add salting to primary key according to skew factor
   *
   * @param transformedDf
   * @param primaryKey
   * @param skewFactor
   * @return salted dataframe
   */
  def saltedDataframe(transformedDf: DataFrame, primaryKey: String, skewFactor: String): DataFrame = {
    transformedDf
      .withColumn("salt_id", (lit(skewFactor) * rand()).cast("int"))
      .withColumn(s"salted_${primaryKey}", functions.concat(col(primaryKey), lit("_"), col("salt_id")))
  }

  /**
   * Builds latest inventory according to persist/update spec, last_found and uuid params. Aggregation for fields is also calculated here
   *
   * @param inDF
   * @param primaryKeyColumn
   * @param config
   * @param spark
   * @return latest inv dataframe
   */
  def buildLatestInventory(inDF: DataFrame, primaryKeyColumn: String, config: Config)(spark: SparkSession): DataFrame = {
    val aggrFunValuesProps: Array[Property] = (config.sourceSpecificProperties ++ config.entitySpecificProperties ++
      config.commonProperties ++ config.corePropertiesExpression)
      .filter(_.fieldsSpec.aggregateFunction.isDefined)
    val invExtraColumns = inDF.columns
      .diff(config.allProperties.filter(!_.fieldsSpec.isInventoryDerived).map(_.colName))
      .diff((Array(primaryKeyColumn, config.dataSource.get.uniqueRecordIdentifierKey)))
      .map(name => Property(colName = name))
    val finalColumns: Array[Property] = invExtraColumns ++ config.allProperties.filter(!_.fieldsSpec.isInventoryDerived)
    val aggCols = finalColumns.diff(aggrFunValuesProps).map(prop => {
      val structCol = struct(LAST_FOUND, config.dataSource.get.uniqueRecordIdentifierKey, prop.colName)
      val updCol = if (prop.fieldsSpec.persistNonNullValue.getOrElse(true))
        when(col(prop.colName).isNotNull, structCol)
      else structCol
      max(updCol).as(prop.colName)
    }) ++ Array(config.dataSource.get.uniqueRecordIdentifierKey).map(name => max(name).as(name))

    val aggregrateFunCols = aggrFunValuesProps.map(prop => {
      val colName = prop.colName
      val aggregateFunc = prop.fieldsSpec.aggregateFunction.get
      expr(s"$aggregateFunc($colName)").as(colName)
    })

    val finalAggCols = aggregrateFunCols ++ aggCols
    val aggStructColSelect = finalColumns.diff(aggrFunValuesProps)
      .map(prop => col(prop.colName).getField(prop.colName).as(prop.colName))

    val finalSelectCols = aggStructColSelect ++ Array(config.dataSource.get.uniqueRecordIdentifierKey, primaryKeyColumn).map(name => col(name).as(name)) ++ (aggrFunValuesProps).map(prop => col(prop.colName))
    val aggDF = inDF.groupBy(primaryKeyColumn).agg(finalAggCols.head, finalAggCols.tail: _*)
      .select(finalSelectCols: _*)
    aggrFunValuesProps.map(_.colName).foldLeft(aggDF)((df, field) => {
      aggDF.schema(field).dataType match {
        case a: ArrayType => if(a.elementType.isInstanceOf[ArrayType]) df.withColumn(field, array_distinct(filter(flatten(col(field)), ar => ar.isNotNull))) else df
        case _ => df
      }
    })
  }

  def getSparkConf(confKey: String)(spark: SparkSession): Option[String] = allCatch.opt(spark.conf.get(confKey))

  /**
   * Fields used in common field calculation expression but are missing in dataframe are added with null value.
   *
   * @param df
   * @param config
   * @return
   */
  def addMinimumFields(df: DataFrame, config: Config, isInventoryDerived: Boolean): DataFrame = {
    val commonProperties = config.allProperties.filter(_.fieldsSpec.isInventoryDerived == isInventoryDerived)
    val identicalColumnSequences: Seq[Seq[String]] = extractIdenticalColumnSequences(df)
    val caseInSensitiveReplacedProperties = CasePropertyExpressionReplacer.caseSensitivePropertyExpressionReplace(commonProperties, identicalColumnSequences)
    EILOGGER.jsonMiniPrint("Properties from addMinimumFields function", commonProperties)
    EILOGGER.jsonMiniPrint("Case sensitive Replacer added properties from addMinimumFields function", caseInSensitiveReplacedProperties)
    val commonCols: Array[String] = caseInSensitiveReplacedProperties
      .flatMap(prop => expr(prop.colExpr).expr.references.map(_.name.split("[.]")(0)))
      .distinct
      .diff(df.columns)
      .toArray
    val commonColsAddedDF = commonCols
      .foldLeft(df)((df, field) => df.withColumn(field, lit(null)))

    config.entity.lastUpdateFields
      .diff(commonColsAddedDF.columns)
      .foldLeft(commonColsAddedDF)((df, field) => df.withColumn(field, lit(null)))
  }

  /**
   * Adds sdm derieved fields to the inv dataframe
   *
   * @param sourceDF
   * @param config
   * @param jobArgs
   * @return
   */
  def generateSDMDerivedFields(sourceDF: DataFrame, config: Config, jobArgs: EIJobArgs, reader: SDSTableReader): DataFrame = {
    val enrichedDF = Enrichment.applyEnrichments(sourceDF, config.enrichments, reader)
    val identicalColumnSequences: Seq[Seq[String]] = extractIdenticalColumnSequences(enrichedDF)
    val filteredProperties = config.allProperties
      .filter(!_.fieldsSpec.isInventoryDerived)
    val caseInSensitiveReplacedProperties = CasePropertyExpressionReplacer.caseSensitivePropertyExpressionReplace(filteredProperties, identicalColumnSequences)
    EILOGGER.jsonMiniPrint("Case InSensitive Replaced Properties", caseInSensitiveReplacedProperties)

    val emptyRemovedDF = EIUtil.preProcesmptyStringtoNull(enrichedDF)
    val (tempStructFieldsAddedDF, structFieldsReplacedPropList) = replaceStructProperties(emptyRemovedDF, caseInSensitiveReplacedProperties)
    val primaryKeyAddedDF = generatePrimaryKey(tempStructFieldsAddedDF, config)
    if (config.dataSource.get.dataEventTimestampKey != EVENT_TIMESTAMP_TS && !primaryKeyAddedDF.schema(config.dataSource.get.dataEventTimestampKey).dataType.isInstanceOf[TimestampType]) {
      EILOGGER.error(s"Invalid spec for: ${config.dataSource.get.dataEventTimestampKey}")
      throw new UnsupportedConfig(s"Column ${config.dataSource.get.dataEventTimestampKey} must be of TimestampType")
    }

    val timestampKeyAddedDF = primaryKeyAddedDF
      .withColumn("event_timestamp_epoch", when(lit(config.dataSource.get.dataEventTimestampKey) === lit(EVENT_TIMESTAMP_TS), expr("cast(event_timestamp_epoch as bigint)")
      ).otherwise(expr(s"unix_millis(${config.dataSource.get.dataEventTimestampKey})")))
      .filter(col(config.dataSource.get.dataEventTimestampKey) <= to_timestamp(lit(jobArgs.currentUpdateDate).divide(1000)))
      .filter(config.filterBy)
      .filter(s"(CASE WHEN btrim(${config.primaryKey},'[\r\n\t\f ]')!='' THEN btrim(${config.primaryKey},'[\r\n\t\f ]') END) IS NOT NULL")
    //      .repartition(col(PRIMARY_KEY))
    if (!timestampKeyAddedDF.columns.contains(config.dataSource.get.uniqueRecordIdentifierKey)) {
      throw new UnsupportedConfig(
        s"The UUID Column `${config.dataSource.get.uniqueRecordIdentifierKey}` is not present in the input source. " +
          s"Add it in temporary variables and map it in `uniqueRecordIdentifierKey` under `datasource` in the loader config."
      )
    }
    val transformedSourceDF = applyTransformations(timestampKeyAddedDF, config, structFieldsReplacedPropList, jobArgs.parsedIntervalStartEpoch, jobArgs.currentUpdateDate, isInventoryDerived = false)
    val enrichmentColumnsSeq: Array[String] = config.enrichments
      .flatMap { enrichment =>
        val columns = enrichment.lookupInfo.enrichmentColumns ++ Array(ORIGIN_LOOKUP_ENRICH)
        if (columns.nonEmpty) {
          LOGGER.info(s"Adding enrichment columns from table '${enrichment.lookupInfo.tableName}': ${columns.mkString(", ")}")
          columns.intersect(transformedSourceDF.columns)
        } else {
          LOGGER.info(s"No enrichment columns to add for table '${enrichment.lookupInfo.tableName}'.")
          Array.empty[String]
        }
      }
    val sdmTransformFields = (Array(ORIGIN, PRIMARY_KEY, UPDATED_AT, FIRST_FOUND,
      LAST_FOUND, ANALYSIS_PERIOD_START_EPOCH, config.dataSource.get.uniqueRecordIdentifierKey) ++ config.allProperties.filter(!_.fieldsSpec.isInventoryDerived).map(_.colName) ++ enrichmentColumnsSeq).distinct
    transformedSourceDF.select(sdmTransformFields.map(col(_)): _*)
  }

  def generatePrimaryKey(df: DataFrame, config: Config): DataFrame = {
    val tempDf = applyTempTransform(df, config, isInventoryDerived = false)
    addMinimumFields(tempDf, config, isInventoryDerived = false)
      .withColumn(SDSProperties.schema.PRIMARY_KEY, expr(config.primaryKey))
  }

  def extractIdenticalColumnSequences(df: DataFrame): Seq[Seq[String]] = {
    val possibleFields = ListBuffer[String]()
    val processedCols = HashSet[String]()
    val columnSequences = ListBuffer[Seq[String]]()

    def traverseSchema(path: Seq[String], schema: org.apache.spark.sql.types.DataType): Unit = {
      schema match {
        case structType: org.apache.spark.sql.types.StructType =>
          structType.fields.foreach { field =>
            val fullPath = path :+ field.name
            possibleFields += fullPath.mkString(".")
            traverseSchema(fullPath, field.dataType)
          }
        case _ =>
      }
    }

    df.schema.fields.foreach { field =>
      val path = Seq(field.name)
      possibleFields += path.head
      traverseSchema(path, field.dataType)
    }
    possibleFields.groupBy(_.toLowerCase).values.foreach { identicalColumns =>
      columnSequences += identicalColumns.toSeq
      processedCols ++= identicalColumns.map(_.toLowerCase)
    }

    columnSequences.toSeq
  }

  def addMissingFields(invDf: DataFrame, properties: Array[Property]): DataFrame = {
    val missingCols: Array[String] = properties.map(prop => expr(prop.colExpr).expr)
      .map(_.references.map(_.name))
      .flatten.filter(!_.contains(".")).distinct
      .diff(invDf.columns)
    missingCols
      .foldLeft(invDf)((df, field) => df.withColumn(field, lit(null).cast(StringType)))
  }

  def generatePID(df: DataFrame, config: Config): DataFrame = {
    df.withColumn("primary_key_attribute", lit(config.primaryKey))
      .withColumn("p_id", expr(SDSProperties.GUID_EXPR))
      .drop("primary_key_attribute")
  }

  /**
   * Generate inv derived fields from the existing fields in the inv
   *
   * @param latestInventoryDf
   * @param config
   * @param jobArgs
   * @return
   */
  def generateInventoryDerivedFields(latestInventoryDf: DataFrame, config: Config, jobArgs: EIJobArgs): DataFrame = {
    val coreRunFieldsAddedInv = addRunCoreProperties(latestInventoryDf, jobArgs.parsedIntervalStartEpoch, jobArgs.currentUpdateDate, config)

    val pidAddedDF = generatePID(coreRunFieldsAddedInv, config).drop("Description")
    val minimumFieldsAddedInv = addMinimumFields(pidAddedDF, config, isInventoryDerived = true)


    val tempAddedDF = applyTempTransform(minimumFieldsAddedInv, config, isInventoryDerived = true)
    val invProperties = SparkUtil.propertyExpressionReplace(config.allProperties.filter(_.fieldsSpec.isInventoryDerived))
    val inventoryFieldsWithoutLastUpd = invProperties
      .filter(prop => {
        !expr(prop.colExpr).expr.references.flatMap(_.references.map(_.name.split("[.]")(0))).toArray.contains(LAST_UPDATED_ATTRS)
      })

    val (tempStructFieldsAddedDF, structFieldsReplacedPropListWithoutLastUpd) = replaceStructProperties(tempAddedDF, inventoryFieldsWithoutLastUpd)

    val inventoryTransformedDF = applyTransformations(tempStructFieldsAddedDF, config, structFieldsReplacedPropListWithoutLastUpd, jobArgs.parsedIntervalStartEpoch, jobArgs.parsedIntervalEndEpoch, isInventoryDerived = true)
    val invWithMissingFieldsDf = addMissingFields(inventoryTransformedDF, Array(Property("first_seen_date", "first_seen_date"), Property("last_active_date", "last_active_date")))

    val lastUpdDF = EIUtil.findLatestUpdateAttrExpr(invWithMissingFieldsDf, config.entity)
    val inventoryFieldsWithLastUpd = invProperties
      .filter(prop => {
        expr(prop.colExpr).expr.references.flatMap(_.references.map(_.name.split("[.]")(0))).toArray.contains(LAST_UPDATED_ATTRS)
      })

    val (lastupdDFAddedStruct, structFieldsReplacedPropListWithLastUpd) = replaceStructProperties(lastUpdDF, inventoryFieldsWithLastUpd)
    val lastupdDepnProperDF = applyTransformations(lastupdDFAddedStruct, config, structFieldsReplacedPropListWithLastUpd, jobArgs.parsedIntervalStartEpoch, jobArgs.parsedIntervalEndEpoch, isInventoryDerived = true)
    val configProperties = config.allProperties.map(_.colName)
    val invCoreCols = Array(ORIGIN, PRIMARY_KEY, P_ID, UPDATED_AT, FIRST_FOUND,
      LAST_FOUND, LIFETIME, RECENCY, OBSERVED_LIFETIME, RECENT_ACTIVITY, ANALYSIS_PERIOD_START_EPOCH, LAST_UPDATED_ATTRS, LAST_UPDATED_DATE)
    val enrichmentColumnsSeq: Array[String] = config.enrichments
      .flatMap { enrichment =>
        val columns = enrichment.lookupInfo.enrichmentColumns ++ Array(ORIGIN_LOOKUP_ENRICH)
        if (columns.nonEmpty) {
          LOGGER.info(s"Adding enrichment columns from table '${enrichment.lookupInfo.tableName}': ${columns.mkString(", ")}")
          columns.intersect(lastupdDepnProperDF.columns)
        } else {
          LOGGER.info(s"No enrichment columns to add for table '${enrichment.lookupInfo.tableName}'.")
          Array.empty[String]
        }
      }
    val finalCols = (configProperties ++ invCoreCols ++ enrichmentColumnsSeq).distinct.toList
    lastupdDepnProperDF.select(finalCols.map(col(_)): _*)
  }


  def build(sourceDF: DataFrame, previousInventoryDF: DataFrame, config: Config, prevConfig: Option[String], jobArgs: EIJobArgs, reader: SDSTableReader)(spark: SparkSession): DataFrame = {
    val sdmTransformSelectedDF = buildSRDMInventorTransoformation(sourceDF, previousInventoryDF, config, prevConfig, jobArgs, reader)
    val skewFactor = getSparkConf(SDSProperties.sds.spark.conf.SALTING)(spark)
    val latestInventoryDf = if (skewFactor.isDefined) {
      val saltedDF = saltedDataframe(sdmTransformSelectedDF, SDSProperties.schema.PRIMARY_KEY, skewFactor.get)
      val saltedDFWithNewKey = buildLatestInventory(saltedDF, s"salted_${SDSProperties.schema.PRIMARY_KEY}", config)(spark)
      buildLatestInventory(saltedDFWithNewKey, SDSProperties.schema.PRIMARY_KEY, config)(spark)
        .drop(s"salted_${SDSProperties.schema.PRIMARY_KEY}", "salt_id")
    } else {
      buildLatestInventory(sdmTransformSelectedDF, SDSProperties.schema.PRIMARY_KEY, config)(spark)
    }
    generateInventoryDerivedFields(latestInventoryDf, config, jobArgs)
  }


  def getConfigDelta(config: Config, prevConfigStr: Option[String]): Option[Config] = {
    if (prevConfigStr.isDefined) {
      val prevConfig: Config = ConfigUtils.getConfigFromJSON(prevConfigStr.get, Loader.configFormats)(Loader.getConfigManifest)
      val delta = LoaderConfigDelta(prevConfig, config)
      val srdmMappedFields = config.allProperties(true).filter(!_.fieldsSpec.isInventoryDerived)

      val srdmChangedFields = delta.properties
        .filter(_.category.eq("Attribute"))
        .map(_.name)
        .intersect(srdmMappedFields.map(_.colName))
      val srdmChangedProps = srdmMappedFields.filter(p => srdmChangedFields.contains(p.colName))

      val primaryKeyProperty = Property(PRIMARY_KEY, config.primaryKey)
      val primaryKeyTempProps = SparkUtil.getDependendProperty(config.temporaryProperties :+ primaryKeyProperty, PRIMARY_KEY)
      val primaryKeyProperties = config.temporaryProperties.filter(p => primaryKeyTempProps.contains(p.colName))
      val tempPropDelta = config.temporaryProperties.intersect(srdmChangedProps)
      val finalTemPropDeltasWoOrder = primaryKeyProperties ++ tempPropDelta

      val finalTemPropDelta = config.temporaryProperties.intersect(finalTemPropDeltasWoOrder)
      val finalSrdcmChangedProps = srdmChangedProps.diff(finalTemPropDelta)

      val deltaConfig = config.copy(deltaProperties = finalSrdcmChangedProps, sourceSpecificProperties = Array.empty,
        entitySpecificProperties = Array.empty, commonProperties = Array.empty, temporaryProperties = finalTemPropDelta)
      Some(deltaConfig)
    }
    else Option.empty
  }

  def buildSRDMInventorTransoformation(sourceDF: DataFrame, previousInventoryDF: DataFrame,
                                       config: Config, prevConfig: Option[String],
                                       jobArgs: EIJobArgs, reader: SDSTableReader) = {
    val configDelta = getConfigDelta(config, prevConfig)
    val deltaDF = sourceDF.filter(col(config.dataSource.get.dataIntervalTimestampKey) >= to_timestamp(lit(jobArgs.parsedIntervalStartEpoch).divide(1000)))
    val srdmInv = generateSDMDerivedFields(deltaDF, config, jobArgs, reader)
    val unionSRDMWithPrevInv = (srdm: DataFrame, inv: DataFrame) => {
      val processdInv = EIUtil.preProcessPreviousInventory(inv, config.entity)
        .drop(config.allProperties.filter(_.fieldsSpec.isInventoryDerived).map(_.colName): _*)
      SparkUtil.unionByName(srdm, processdInv)
    }


    if (configDelta.isDefined && !configDelta.get.deltaProperties.isEmpty) {
      LOGGER.info(s" Config Delta - ${configDelta.get.toString}")
      val historicalSRDM = sourceDF.filter(col(config.dataSource.get.dataIntervalTimestampKey) >= to_timestamp(lit(jobArgs.srdmHistoricalParsedIntervalStartEpoch).divide(1000)))
      val historicalSRDMInv = generateSDMDerivedFields(historicalSRDM, configDelta.get, jobArgs, reader)
      val srdmInvWithDelta = SparkUtil.unionByName(srdmInv, buildLatestInventory(historicalSRDMInv, SDSProperties.schema.PRIMARY_KEY, configDelta.get)(spark))
      val previousInventoryDFWithoutDeltas = previousInventoryDF.drop(configDelta.get.deltaProperties.map(_.colName): _*)
      unionSRDMWithPrevInv(srdmInvWithDelta, previousInventoryDFWithoutDeltas)
    }
    else unionSRDMWithPrevInv(srdmInv, previousInventoryDF)
  }
}


