package ai.prevalent.entityinventory.loader.configs.specs

import ai.prevalent.entityinventory.common.configs.{DataSource, EIConfig, Entity, FieldsSpec, Property}
import ai.prevalent.entityinventory.delta.{Change, LoaderConfigDelta}
import ai.prevalent.entityinventory.enrichment.Enrichment
import ai.prevalent.entityinventory.exceptions.UnsupportedConfig
import ai.prevalent.entityinventory.loader.Loader
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema._
import ai.prevalent.entityinventory.utils.EILOGGER
import org.json4s.jackson.Serialization.write


case class Config(primaryKey: String = null,
                  filterBy: String = "true",
                  entity: Entity = Entity.defaultEntityConfig,
                  origin: String = null,
                  operationalMode: String = "carryForward",
                  dataSource: Option[DataSource] = Option.empty,
                  outputTable: String = null,
                  commonProperties: Array[Property] = Array.empty[Property],
                  entitySpecificProperties: Array[Property] = Array.empty[Property],
                  sourceSpecificProperties: Array[Property] = Array.empty[Property],
                  temporaryProperties: Array[Property] = Array.empty[Property],
                  deltaProperties: Array[Property] = Array.empty[Property],
                  enrichments: Array[Enrichment] = Array.empty
                 ) extends EIConfig[Config, LoaderConfigDelta] {
  def allProperties: Array[Property] = {
    val commonFunctionAdded = commonProperties ++ corePropertiesExpression ++ Array(Property("class", s"'${entity.name}'"))
    commonFunctionAdded ++ entitySpecificProperties ++ sourceSpecificProperties ++ deltaProperties
  }

  def allProperties(includeTempProperties: Boolean): Array[Property] = if (includeTempProperties) allProperties ++ temporaryProperties else allProperties

  def corePropertiesExpression = {
    val dateFieldSpec = FieldsSpec(
      aggregateFunction = Option.empty[String],
      isInventoryDerived = false,
      persistNonNullValue = Option.empty[Boolean],
      replaceExpression = Option.empty[Boolean]
    )

    val firstFoundSpec = dateFieldSpec.copy(aggregateFunction = Some("min"))
    val lastFoundSpec = dateFieldSpec.copy(aggregateFunction = Some("max"))
    val invFieldSpec = FieldsSpec(aggregateFunction = Option.empty[String], isInventoryDerived = true, persistNonNullValue = Option.empty[Boolean], replaceExpression = Option.empty[Boolean])
    Array(
      Property(FIRST_FOUND, "event_timestamp_epoch", firstFoundSpec),
      Property(LAST_FOUND, "event_timestamp_epoch", lastFoundSpec),
      Property(LIFETIME, "CASE WHEN ((last_active_date IS NOT NULL) AND (first_seen_date IS NOT NULL)) THEN datediff(from_unixtime((last_active_date / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((first_seen_date / 1000), 'yyyy-MM-dd HH:mm:ss')) ELSE CAST(NULL AS INT) END", invFieldSpec),
      Property(RECENCY, s"datediff(from_unixtime(($UPDATED_AT / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime(($LAST_FOUND / 1000), 'yyyy-MM-dd HH:mm:ss'))", invFieldSpec),
      Property(OBSERVED_LIFETIME, s"datediff(from_unixtime(($LAST_FOUND / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime(($FIRST_FOUND / 1000), 'yyyy-MM-dd HH:mm:ss'))", invFieldSpec),
      Property(RECENT_ACTIVITY, s"CASE WHEN (last_active_date IS NOT NULL) THEN datediff(from_unixtime(($UPDATED_AT / 1000), 'yyyy-MM-dd HH:mm:ss'), from_unixtime((last_active_date / 1000), 'yyyy-MM-dd HH:mm:ss')) ELSE CAST(NULL AS INT) END", invFieldSpec),
    )
  }

  def configValidator(): Unit = {
    val properties = allProperties
    val invDerivedFields = properties.filter(prop => prop.fieldsSpec.isInventoryDerived)
    val invDerivedPropsWithAgg = invDerivedFields.filter(_.fieldsSpec.aggregateFunction.isDefined)
    val invDerivedPropsWithUpdateSpec = invDerivedFields.filter(prop => prop.fieldsSpec.persistNonNullValue.isDefined)
    if (!invDerivedPropsWithAgg.isEmpty) {
      EILOGGER.error(s"Invalid spec for: ${invDerivedPropsWithAgg.map(_.colName).mkString(",")}")
      throw new UnsupportedConfig(s"InvalidSpecCombination: Cannot combine aggregate spec with isInventoryDerived")
    }
    if (!invDerivedPropsWithUpdateSpec.isEmpty) {
      EILOGGER.error(s"Following inventory derived fields have persistence spec: ${invDerivedPropsWithUpdateSpec.map(_.colName).mkString(",")}")
      throw new UnsupportedConfig("InvalidSpecCombination: Cannot combine persistence spec with isInventoryDerived")
    }

    val tempFieldsWithWarning = temporaryProperties.filter(prop => {
      if (prop.fieldsSpec.persistNonNullValue.isDefined || prop.fieldsSpec.replaceExpression.isDefined || prop.fieldsSpec.aggregateFunction.isDefined)
        true
      else false
    })
    if (!tempFieldsWithWarning.isEmpty) {
      EILOGGER.error(s"Inapplicable fieldspecs for ${tempFieldsWithWarning.mkString(",")}")
      throw new UnsupportedConfig("InvalidSpec: temporary props only support isInventoryDerived and convertEmptyToNull field spec")
    }
  }

  override def toString: String = write(this)(Loader.configFormats)

  override def getConfigDelta(otherConfig: Config): LoaderConfigDelta = LoaderConfigDelta(otherConfig, this)

  override def getConfigDelta(deltas: Seq[Change]): LoaderConfigDelta = LoaderConfigDelta(this, deltas)

}

