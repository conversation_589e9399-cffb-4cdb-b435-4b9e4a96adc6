package ai.prevalent.entityinventory.delta

case class Change(changeType:String, name:String, reason:Option[String],message: Map[String,String]=Map.empty, category:String) {

  override def equals(obj: Any): Boolean = obj match {
    case that: Change =>
      this.changeType == that.changeType &&
        this.name == that.name &&
        this.category == that.category
    case _ => false
  }

  override def hashCode(): Int = {
    val state = Seq(changeType, name, category)
    state.map(_.hashCode()).foldLeft(0)((a, b) => 31 * a + b)
  }
  def mergeIfEqual(otherChange:Change): Change = {
    if(this.equals(otherChange)){
      println(s"other $otherChange")
      val newReason = if(otherChange.reason.isDefined) otherChange.reason else this.reason
      val newMessage = this.message ++ otherChange.message
      println(s"$newReason $newMessage, ${this.message}")
      this.copy(reason = newReason, message = newMessage)
    } else this
  }
}