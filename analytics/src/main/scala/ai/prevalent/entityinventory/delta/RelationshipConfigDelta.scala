package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.Property
import ai.prevalent.entityinventory.relationship.extractor.configs.specs.{Attribute, Config, InputSourceInfo}
import ai.prevalent.entityinventory.utils.SparkUtil

import scala.collection.immutable.Seq

case class RelationshipConfigDelta(properties: Seq[Change], inputSourceInfoChanges:Seq[Change]=Seq.empty) extends  Delta

object RelationshipConfigDelta  extends Delta {

  def apply(clientConfig: Config, deltas: Seq[Change]) ={
    val attrs = clientConfig.optionalAttributes.map((prop => (prop.name,prop))).toMap
    val attrChanges = checkAttrInheritedChanges(deltas, attrs)
    val propsChange = attrChanges

    new RelationshipConfigDelta(propsChange)
  }

  def apply(prevConfig:Config, newConfig:Config):RelationshipConfigDelta = {
    val attrOld = prevConfig.optionalAttributes.map((prop => (prop.name,prop))).toMap
    val attrNew = newConfig.optionalAttributes.map((prop => (prop.name,prop))).toMap

    val attrExpressionChange = checkAttributeExpressionChange(attrOld, attrNew)
    val attrOccChange = checkAttributeOccuranceChange(attrOld, attrNew)
    val newAttributes = checkNewAttribute(attrOld, attrNew)
    val properties = attrExpressionChange ++ attrOccChange ++ newAttributes
    val inputSourceInfo = checkInputSourceInfoChanges(inputSourceOld = prevConfig.inputSourceInfo, inputSourceInfoNew = newConfig.inputSourceInfo)
    new RelationshipConfigDelta(properties, inputSourceInfoChanges = inputSourceInfo)
  }



  def checkAttributeExpressionChange(oldConfigProps: Map[String, Attribute], newConfigProps: Map[String, Attribute]) = {
    oldConfigProps
      .filter(p => newConfigProps.isDefinedAt(p._1))
      .map( p => {
        val expr2 = newConfigProps.get(p._1).get.exp
        if (!p._2.exp.equals(expr2)) {
          Some(Change("Expression", p._1, Option.empty, Map("Old Expression" -> p._2.exp, "New Expression" -> expr2),"Attribute"))
        } else Option.empty
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkAttributeOccuranceChange(oldConfigProps: Map[String, Attribute], newConfigProps: Map[String, Attribute]) = {
    oldConfigProps
      .filter(p => newConfigProps.isDefinedAt(p._1))
      .map( p => {
        val expr2 = newConfigProps.get(p._1).get.occurrence
        if (!p._2.occurrence.equals(expr2)) {
          Some(Change("Expression", p._1, Option.empty, Map("Old Occurrence" -> p._2.occurrence, "New Occurrence" -> expr2),"Attribute"))
        } else Option.empty
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkNewAttribute(oldConfigProps: Map[String, Attribute], newConfigProps: Map[String, Attribute])= {
    newConfigProps
      .filter(p => !oldConfigProps.isDefinedAt(p._1))
      .map( p => {
        Some(Change("New Field", p._1, Option.empty, Map("Expression" -> p._2.exp),"Attribute"))
      })
      .filter(_.isDefined)
      .map(_.get)
      .toSeq
  }

  def checkAttrInheritedChanges(changedProperties: Seq[Change], newConfigProps: Map[String, Attribute]): Seq[Change] = {
    val graph = SparkUtil.createPropertyGraph(newConfigProps.values.map(p => Property(p.name,p.exp)).toList)
    val changedFields = changedProperties.map(_.name).toSet
    newConfigProps
      .values
      .map(p => {
        val depFields = SparkUtil.getDependendProperty(p.name,graph).drop(1)
        (p.name,depFields)
      })
      .flatMap( f => {
        f._2.intersect(changedFields).map( (f._1, _))
      })
      .map( c => Some(Change("Cascaded Change", c._1, Some(s"The logic for the field ${c._2} is changed, which is used to derive this field directly or indirectly"), Map.empty,"Attribute")))
      .map(_.get)
      .toList
  }

  def checkInputSourceInfoChanges(inputSourceOld:List[InputSourceInfo], inputSourceInfoNew: List[InputSourceInfo]):Seq[Change] = {
    val oldMap = inputSourceOld.map(i =>(i.sdmPath,i)).toMap
    val newMap = inputSourceInfoNew.map(i =>(i.sdmPath,i)).toMap

    val delSourceChange = inputSourceOld.map(_.sdmPath).diff(inputSourceInfoNew.map(_.sdmPath))
      .map(name => Change(changeType = "Deleted Source", name=name, Option.empty,Map("Input Source Info" -> oldMap(name).toString ),category = "Data Source"))
    val newSrcChange = inputSourceInfoNew.map(_.sdmPath).diff(inputSourceOld.map(_.sdmPath))
      .map(name => Change(changeType = "New Source", name=name, Option.empty,Map("Input Source Info" -> newMap(name).toString ),category = "Data Source"))

    val loaderChanges = newMap.flatMap( newSrc => {
      val srcChanges = checkStringListChanges(oldList = oldMap.getOrElse(newSrc._1, InputSourceInfo()).sourceMappingInfo.configPath, newSrc._2.sourceMappingInfo.configPath,"Source Loader")
      val tarChanges = checkStringListChanges(oldList = oldMap.getOrElse(newSrc._1, InputSourceInfo()).targetMappingInfo.configPath, newSrc._2.targetMappingInfo.configPath,"Target Loader")
      (srcChanges ++ tarChanges).map(c => c.copy(message = c.message+("Data Source" -> newSrc._1)))
    })
    delSourceChange ++ newSrcChange ++ loaderChanges
  }
}
