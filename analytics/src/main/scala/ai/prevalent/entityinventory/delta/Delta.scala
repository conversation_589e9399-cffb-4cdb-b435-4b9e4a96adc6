package ai.prevalent.entityinventory.delta

import ai.prevalent.entityinventory.common.configs.{DictionaryAttribute, Property}
import ai.prevalent.entityinventory.delta.LoaderConfigDelta.{checkFieldSpecChange, checkNewProperties, checkPropertyExpressionChange}
import ai.prevalent.entityinventory.loader.configs.SDSProperties.schema.{LIFETIME, OBSERVED_LIFETIME, RECENCY, RECENT_ACTIVITY}
import ai.prevalent.entityinventory.utils.SparkUtil

import scala.reflect.runtime.universe.{TermSymbol, runtimeMirror}
import scala.reflect.runtime.{universe => ru}

 class Delta {
  def changes(): Seq[Change] = {
    val mirror = runtimeMirror(this.getClass.getClassLoader)
    val instanceMirror = mirror.reflect(this)
    val classSymbol = mirror.classSymbol(this.getClass)
    val fields = classSymbol.toType.members.collect{
      case m: TermSymbol if m.isVal || m.isVar => m
    }
    fields.map(field => instanceMirror.reflectField(field.asTerm).get)
      .collect {
        case value:Option[Change] if value.isDefined => Array(value.get)
        case value:Seq[Change] => value.toArray
      }
      .flatten.toList.distinct
  }

   def checkPropertyChanges(oldProperties:Seq[Property], newProperties: Seq[Property], includeFieldSpec:Boolean=false):Seq[Change] = {
     val props1 = oldProperties.map((prop => (prop.colName,prop))).toMap
     val props2 = newProperties.map(prop => (prop.colName,prop)).toMap
     val expression = checkPropertyExpressionChange(props1,props2)
     val newFields = checkNewProperties(props1,props2)
     val deletedFields = checkDeletedProperties(props1,props2)
     val spec = checkFieldSpecChange(props1,props2)
     val existingProperty = props2.view.filterKeys(props1.keySet).toMap
     val newAndDelted = (newFields++deletedFields).map( c => if(includeFieldSpec) c else c.copy(message = c.message.removed("Field Spec")))
     val inheri = checkPropertyInheritedChanges(expression ++ spec, existingProperty)

     inheri ++ expression ++ spec ++ newAndDelted
   }

   def checkPropertyExpressionChange(config1Props: Map[String, Property], config2Props: Map[String, Property]): Seq[Change] = {
     config1Props
       .filter(p => config2Props.isDefinedAt(p._1))
       .map( p => {
         val expr2 = config2Props.get(p._1).get.colExpr
         if (!p._2.colExpr.equals(expr2)) {
           Some(Change("Expression", p._1, Option.empty, Map("Old Expression" -> p._2.colExpr, "New Expression" -> expr2),"Attribute"))
         } else Option.empty
       })
       .filter(_.isDefined)
       .map(_.get)
       .toSeq
   }

   def checkNewProperties(config1Props: Map[String, Property], config2Props: Map[String, Property]): Seq[Change] = {
     config2Props
       .filter(p => !config1Props.isDefinedAt(p._1))
       .map( p => {
         Some(Change("New Field", p._1, Option.empty, Map("Expression" -> p._2.colExpr, "Field Spec" -> p._2.fieldsSpec.toString),"Attribute"))
       })
       .filter(_.isDefined)
       .map(_.get)
       .toSeq
   }

   def checkDeletedProperties(config1Props: Map[String, Property], config2Props: Map[String, Property]): Seq[Change] = {

     config1Props
       .filter(p => !config2Props.isDefinedAt(p._1))
       .map( p => {
         Some(Change("Deleted Field", p._1, Option.empty, Map("Expression" -> p._2.colExpr, "Field Spec" -> p._2.fieldsSpec.toString),"Attribute"))
       })
       .filter(_.isDefined)
       .map(_.get)
       .toSeq
   }

   def checkFieldSpecChange(config1Props: Map[String, Property], config2Props: Map[String, Property]) ={
     config1Props
       .filter(p => config2Props.isDefinedAt(p._1))
       .map( p => {
         val fieldSpec2 = config2Props.get(p._1).get.fieldsSpec
         if (!p._2.fieldsSpec.equals(fieldSpec2)) {
           Some(Change("Field Spec", p._1, Option.empty, Map("Old Field Spec" -> p._2.fieldsSpec.toString, "New Field Spec" -> fieldSpec2.toString),"Attribute"))
         } else Option.empty
       })
       .filter(_.isDefined)
       .map(_.get)
       .toSeq
   }

   def checkPropertyInheritedChanges(changedProperties: Seq[Change], config2Props: Map[String, Property]): Seq[Change] = {
     val coreFields = Set(LIFETIME,RECENCY,OBSERVED_LIFETIME,RECENT_ACTIVITY)
     val coreRemovedProps = config2Props.view
       .filter( p => !coreFields.contains(p._1))

     val graph = SparkUtil.createPropertyGraph(coreRemovedProps.values.toList)
     val changedFields = changedProperties.filter(!_.changeType.equals("Rename")).map(_.name).toSet
     val cascadedChanges = coreRemovedProps
       .values
       .map(p => {
         val depFields = SparkUtil.getDependendProperty(p.colName,graph)
           .diff(Set(p.colName))
           .intersect(changedFields)
         val change = Change("Cascaded Change", p.colName, Some(s"The logic for the reference fields ${depFields.mkString(", ")} is changed, which is used to derive this field directly or indirectly"),
           Map(s"${p.colName}"->p.colExpr, "Reference Fields" -> depFields.mkString(", ")),"Attribute")
         (change,depFields)
       })
       .filter(_._2.nonEmpty)
       .map(_._1)

     val solRename =  changedProperties.filter(_.changeType.equals("Rename")).map(c => (c.name,c.message)).toMap
     val renameClientChanges = coreRemovedProps.flatMap(p => {
       val depFields = SparkUtil.getDependendProperty(p._1,graph).toList.toSet.intersect(solRename.keySet)
       depFields.map(r => (p._2,r))
     }).map(change => {
       Change("Rename", change._2, Some(s"Field renamed in solution"),
         solRename(change._2),"Attribute")
     }).toSeq
     val solExprChange = changedProperties.filter(k => k.changeType.contains("New")||k.changeType.equals("Expression"))
       .map(k => (k.name, k.message.map {case (key,value) => (s"Solution $key",value)})).toMap
     val newExprs =coreRemovedProps.view
       .filterKeys(solExprChange.keySet)
       .map(p => Change("Updated Version Available in Solution",p._1,reason = Some("A new Expression Available in solution"),
         message = solExprChange(p._1)+("Expression in Client" -> p._2.colExpr), "Attribute")
       )

     renameClientChanges ++ cascadedChanges ++ newExprs
   }

   def checkStringListChanges(oldList:Seq[String], newList:Seq[String], changeType:String):Seq[Change] ={
     val newValues = newList.diff(oldList)
       .map( p => Change(s"New $changeType", p, Option.empty, Map(s"Old $changeType" -> oldList.mkString(", "), s"New $changeType" -> newList.mkString(", ")), changeType))

     val deleted = oldList.diff(newList)
       .map( p => Change(s"Deleted $changeType", p, Option.empty, Map(s"Old $changeType" -> oldList.mkString(", "), s"New $changeType" -> newList.mkString(", ")), changeType))
     newValues ++ deleted
   }

   def checkDictionaryAttributeChanges(oldAttributes:Map[String,DictionaryAttribute], newAttributes:Map[String,DictionaryAttribute]): Seq[Change] = {
     val newAttrChanges = newAttributes.keySet.diff(oldAttributes.keySet)
       .map( p => Change(s"New Field", p, Option.empty, Map("Caption" -> newAttributes(p).caption.getOrElse(""), "Description" -> newAttributes(p).description.getOrElse("")), "Attribute"))
       .toSeq
     val deletedAttrChanges = oldAttributes.keySet.diff(newAttributes.keySet)
       .map( p => Change(s"Deleted Field", p, Option.empty, Map("Caption" -> oldAttributes(p).caption.getOrElse(""), "Description" -> oldAttributes(p).description.getOrElse("")), "Attribute"))
       .toSeq

     val commonItems = newAttributes.keySet.intersect(oldAttributes.keySet)
     val attrChanges = commonItems.flatMap(item => newAttributes(item).getConfigDelta(oldAttributes(item), item)).toSeq

     attrChanges++newAttrChanges++deletedAttrChanges
   }
}
