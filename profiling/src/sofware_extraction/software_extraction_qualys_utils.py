import re
from pyspark.sql.functions import udf, col, expr, concat_ws
from pyspark.sql.types import StringType, ArrayType
from rapidfuzz import process, fuzz
from pyspark.sql import functions as F
import pandas as pd
from src.sofware_extraction import config
from src.sofware_extraction.constants import UPDATED_AT_TS
from src.sofware_extraction.common_utils import write_table
from src.sofware_extraction.software_extraction_utils import normalize_software_details


def read_table_qualys(args, spark):
    input_paths = args.inputPath.split(";")
    hv_df = spark.read.table(input_paths[0]).filter(
        f"parsed_interval_timestamp_ts >= to_timestamp({args.parsedIntervalStartEpoch}/1000) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid").withColumn(
        UPDATED_AT_TS, F.expr(f"to_timestamp({args.eventTimestampEndEpoch}/1000)"))

    kb_df = (spark.read.table(input_paths[1]).filter("isValid").withColumn("row", F.expr(
        "row_number() over (partition by QID order "
        "by event_timestamp_epoch desc)"))
             .filter("row=1")
             .select("CVE_LIST", "QID", "SOFTWARE_LIST", "TITLE"))
    nvd_df = (spark.read.table(input_paths[2]).filter(
        f"(parsed_interval_timestamp_ts >= to_timestamp(({args.parsedIntervalStartEpoch}/1000)-(14*86400))) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid and lower(vulnStatus) not  in ('received','rejected','deferred','awaiting analysis')")
              .withColumn("row", F.expr("row_number() over (partition by id order "
                                        "by event_timestamp_epoch desc)"))
              .filter("row=1")
              .select("id", "configurations"))
    cisa_df = (spark.read.table(input_paths[3]).filter(
        f"parsed_interval_timestamp_ts >= to_timestamp(({args.parsedIntervalStartEpoch}/1000)-(2*86400)) and parsed_interval_timestamp_ts <= to_timestamp({args.parsedIntervalEndEpoch}/1000) and event_timestamp_ts <= to_timestamp({args.eventTimestampEndEpoch}/1000) and isValid")
     .withColumn("row", F.expr("row_number() over (partition by cveMetadata.cveId order "
                              "by event_timestamp_epoch desc)"))
     .filter("row=1"))
    return hv_df, kb_df, nvd_df, cisa_df


# Get kb and nvd info
def derive_cpe_and_kb_software_list(kb_df, nvd_df, hv_df, cisa_df, software_update_dict):
    kb_df = kb_df.withColumn('cve', F.expr(r"regexp_extract_all(CVE_LIST.CVE, '(CVE-[0-9]+-[0-9]+)', 1)"))
    kb_df = kb_df.withColumn("cve", F.explode_outer(col("cve"))).dropDuplicates()
    nvd_df = nvd_df.withColumn("cpe_list_nvd", F.expr("flatten(transform(transform(configurations.nodes, node -> node.cpeMatch), x -> x[0].criteria))"))    
    
    kb_df = kb_df.join(nvd_df.select("id", "cpe_list_nvd"), kb_df["cve"] == nvd_df["id"], "left")
    kb_df = kb_df.join(cisa_df, kb_df["cve"] == cisa_df["cve_id_cisa"], "left").drop("cve_id_cisa")  
    
    kb_df = kb_df.withColumn("cpe_list",F.when((F.size(F.col("cpe_list_nvd")) >= 1), F.col("cpe_list_nvd")).otherwise(F.col("cpe_list_cisa")))
    kb_df = cpe_to_string(kb_df, software_update_dict)
    
    # add software list from cisa
    kb_df = kb_df.withColumn("cpe_software_list",F.when((F.size(F.col("cpe_software_list")) >= 1), F.col("cpe_software_list")).otherwise(F.col("sw_cisa")))    
    kb_df = kb_df.withColumn("cpe_software_list",F.when(F.col("cpe_software_list").isNull(),F.lit(F.array([]))).otherwise(F.col("cpe_software_list")))
    
    kb_df = kb_df.groupBy("QID").agg(
        F.collect_set("cve").alias("cve"),
        F.first("SOFTWARE_LIST").alias("software_list"),
        F.expr("array_distinct(flatten(collect_list(cpe_software_list)))").alias("cpe_software_list"),
        F.first("TITLE").alias("title"))

    kb_df = kb_df.withColumnRenamed('QID', 'kb_qid')
    hv_df = hv_df.join(kb_df, kb_df["kb_qid"] == hv_df["qid"], how='left').drop("kb_qid")
    return hv_df


# derive product-vendor pairs from kb software_list
def derive_vendor_product_list(df):
    df = df.withColumn("software_list", (F.regexp_replace(col("software_list"), "'", '"')))
    df = df.withColumn('product', F.expr(r"regexp_extract_all(software_list, 'PRODUCT\":\\s?\"?([^\",]*)', 1)"))
    df = df.withColumn('vendor', F.expr(r"regexp_extract_all(software_list, 'VENDOR\":\\s?\"?([^\",]*)', 1)"))
    df = df.withColumn("kb_software_list", F.expr(
        "transform(arrays_zip(vendor, product), pair -> concat_ws('|', pair.vendor, pair.product))"))
    df = df.withColumn("kb_software_list", F.expr(
        "transform(kb_software_list, x -> CASE WHEN x RLIKE '(^.*?[|]windows)_(?:server)?_?[0-9._h]+' THEN regexp_extract(x, '(^.*?[|]windows)_(?:server)?_?[0-9._h]+', 1) ELSE x END)"))
    df = df.withColumn("kb_software_list", F.expr(
        "transform(kb_software_list, x -> CASE WHEN x RLIKE '(^.*?_[0-9h]+)$' THEN regexp_extract(x, '(^.*?)_[0-9h]+$', 1) ELSE x END)"))
    df = df.drop("product", "vendor")
    return df


# Extract all software names from cpe
def cpe_to_string(df, software_update_dict):
    df = df.withColumn("cpe_objects", F.when(F.col("cpe_list").isNull(), F.array([])).otherwise(
        F.expr("split(trim('[]', cast(cpe_list as string)), ', ')")))
    
    df = df.withColumn("cpe_software_list",F.expr("""transform(cpe_objects, x -> CASE WHEN size(split(x, ':')) > 4 THEN CASE WHEN regexp_extract(split(x, ':')[1], '^([0-9])') != '' THEN concat_ws('|', split(x, ':')[3], split(x, ':')[4]) ELSE concat_ws('|', split(x, ':')[2], split(x, ':')[3]) END ELSE null END)"""))
    for key, value in software_update_dict.items():
        df = df.withColumn("cpe_software_list", F.expr("FILTER(cpe_software_list, x -> x IS NOT NULL)"))
        df = df.withColumn("cpe_software_list",
                           F.expr(f"transform(cpe_software_list, x -> regexp_replace(x, '{key}', '{value}'))"))

    df = df.withColumn("cpe_software_list", F.expr(
        "transform(cpe_software_list, x -> CASE WHEN x RLIKE '(^.*?[|]windows)_(?:server)?_?[0-9._h]+' THEN regexp_extract(x, '(^.*?[|]windows)_(?:server)?_?[0-9._h]+', 1) ELSE x END)"))
    df = df.withColumn("cpe_software_list", F.expr(
        "transform(cpe_software_list, x -> CASE WHEN x RLIKE '(^.*?_[0-9h]+)$' THEN regexp_extract(x, '(^.*?)_[0-9h]+$', 1) ELSE x END)"))
    df = df.withColumn("cpe_software_list", F.array_distinct(F.col("cpe_software_list")))
    df = df.drop("cpe_objects")
    return df


# Identify the software using fuzzy matching and extract version
def extract_rapidfuzz_match(results, title, cpe_list, kb_software_list, os):
    match_found = 0
    cpe = sorted(cpe_list, key=len, reverse=True)
    software_list = (cpe + [kb_sof for kb_sof in kb_software_list if
                            ("|" in kb_sof and not kb_sof.split("|")[1] in str(cpe))]) if kb_software_list else cpe
    desc = (str(results) + " " + str(title if not " and " in str(title) else " ")).lower()
    software_list = [s for s in software_list if s]
    software_cleaned = [s.replace("_", " ").replace("-", " ").replace("|", " ").replace(".", " ") for s in software_list
                        if s]

    substitutions = [(r"[\\]+microsoft office[\\]+office16[\\]+", " "), ("windows[ ]?apps?", " "),
                     ("[.0-9\\\\/:<>_-]", " "), ("middleware|oracle|microsoft|windows|amazon|debian|redhat", " ")]
    for pattern, repl in substitutions:
        desc = re.sub(pattern, repl, str(desc))

    potential_match = process.extractOne(desc, software_cleaned, scorer=fuzz.token_ratio)
    if not pd.isnull(potential_match):
        software = software_list[software_cleaned.index(potential_match[0])] + "|"

        # special case- windows
        if software == "microsoft|windows|" and os:
            win_version = re.findall("[Ww]indows (\d+\.?\d?|Server \d+)", str(os))
            if win_version:
                software = software.replace("windows", "windows " + (win_version[0]).lower())

        # special case- office
        if title and "microsoft office" in title.lower() or re.findall(
                "Outlook|Excel|Word|OneNote|Access|Powerpoint|Publisher|Skype for Business", str(title)):
            major_version = (" 20" + re.findall("(?:Office|OFFICE)\\\\?([0-9]{2})", str(results))[0]) if len(
                re.findall("(?:Office|OFFICE)\\\\?([0-9]{2})", str(results))) > 0 else ""
            product_matches = [
                f"{product.split(' ', 1)[0]}|{product.split(' ', 1)[1]}{major_version}|{version}"
                for product, files in config.office_products.items()
                for version in re.findall(fr"(?:{'|'.join((file) for file in files)})(?: [vV]ersion is ([.0-9]+))?",
                                          str(results).lower())]
            if len(product_matches) == 0:
                product_matches = ["microsoft|" + res[0] + major_version + "|" + res[1] for res in (
                    re.findall("Application Name Version Location Microsoft ([a-zA-Z]+) ([.0-9]+)", str(results)))]
            if len(product_matches) > 0:
                software = product_matches
                match_found = 1

        if match_found == 0:
            # extract version
            for pattern in config.versions_qualys:
                version_matches = list(set(re.findall(pattern, str(results))))
                if version_matches and len(version_matches) < 10:
                    match_found = 1
                    software = [str(software) + v for v in version_matches]
                    break
            software = [software] if match_found == 0 else software
        return software
    return None


def derive_qualys_software(hv_df, kb_df, nvd_df, cisa_df, args, spark):
    cisa_df = cisa_df.withColumn("cve_id_cisa",F.expr("cveMetadata.cveId"))
    cisa_df = cisa_df.withColumn("cpe_list_cisa", F.expr("COALESCE(flatten(containers.cna.affected.cpes),flatten(transform(FILTER(containers.adp, x -> x.affected.cpes IS NOT NULL),x -> flatten(x.affected.cpes))))"))
    cisa_df = cisa_df.withColumn("sw_cisa",  F.expr("CASE WHEN size(containers.adp.title)>1 THEN coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(filter(containers.adp,x->x.title in ('CISA ADP Vulnrichment'))[0].affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) ELSE coalesce(CASE WHEN transform(filter(containers.cna.affected,x->lower(x.vendor) in ('n/a','unknown') and lower(x.product) in ('n/a','unknown')), x->(x.product, x.vendor,x.versions.version))!=Array() THEN NULL ELSE transform(containers.cna.affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', '')))) END, transform(containers.adp[0].affected, x->(concat_ws('|',regexp_replace(x.vendor, '\\\\||[uU]nknown|n/a|N/A', ''),regexp_replace(x.product, '\\\\||[uU]nknown|n/a|N/A', ''))))) END"))
    cisa_df = cisa_df.withColumn("sw_cisa", F.expr("FILTER(sw_cisa, x -> x !='')"))
    cisa_df = cisa_df.select("cve_id_cisa", "cpe_list_cisa","sw_cisa")
    
    hv_df = derive_cpe_and_kb_software_list(kb_df, nvd_df, hv_df, cisa_df, config.software_update_dict)
    
    hv_df = derive_vendor_product_list(hv_df)
    extract_rapidfuzz_match_udf = udf(extract_rapidfuzz_match, ArrayType(StringType()))
    write_table(hv_df, args.outputPath + '_intermediate', None, None, spark)
    hv_df = spark.read.table(args.outputPath + '_intermediate')
    hv_df = hv_df.withColumn("software",
                             F.when(F.col("type") != "INFO", extract_rapidfuzz_match_udf(col("results"), col("title"),
                                                                                         col("cpe_software_list"),
                                                                                         col("kb_software_list"),
                                                                                         col("os"))).otherwise(None))

    # derive vendor, product, version and software full name
    hv_df = hv_df.withColumn("software", F.explode_outer(col("software")))
    hv_df = hv_df.withColumn("software", F.regexp_replace(col("software"), "(?:[nN]one|multi-vendor)", ""))
    hv_df = hv_df.withColumn("software", F.regexp_replace(col("software"), r"_", " "))
    hv_df = hv_df.withColumn("software", F.regexp_replace(col("software"), r"notepad%2b%2b", "notepad++"))
    hv_df = hv_df.withColumn("software_vendor", F.lower(F.split(col("software"), "\\|")[0]))
    hv_df = hv_df.withColumn("software_name", F.lower(F.split(col("software"), "\\|")[1]))
    hv_df = hv_df.withColumn("software_version", F.split(col("software"), "\\|")[2])
    hv_df = normalize_software_details(hv_df)
    hv_df = hv_df.select("host_id", "qid", "software_vendor", "software_name", "software_product", "software_full_name", "software_version", "updated_at_ts").distinct()
    return hv_df
