from pyspark.sql import DataFrame
from pyspark.sql.session import SparkSession
spark = SparkSession.builder.getOrCreate()
sc = spark.sparkContext
sc.addPyFile(spark.conf.get("spark.sds.dependencies_file"))
from pyspark.sql.functions import col, lit, explode, when, concat_ws, to_timestamp, days
import logging
from pyspark.sql.types import StringType
from src.utils.common_utils import get_args, read_table, write_table, get_configs


def temporal_analysis(df: DataFrame, entity_class: str, time_columns_list: list) -> DataFrame:
    """
    Analyzes temporal dependencies between specified columns in a DataFrame and identifies rows that violate the temporal order.
    ...
    This function checks the temporal dependencies among multiple sets of columns specified in the `time_columns_list`.
    It returns a DataFrame containing the rows with invalid temporal order along with additional metadata.
    ...
    Parameters:
    df (DataFrame): The input Spark DataFrame containing the data to be analyzed.
    entity_class (str): A string representing the entity class to be added to the result DataFrame.
    time_columns_list (list): A list of lists, where each sublist contains the column names to be checked for temporal order.
    ...
    Returns:
    DataFrame: A DataFrame containing invalid rows, their corresponding `p_id`, the combined time columns, and additional metadata.
               The DataFrame includes the following columns:
               - 'p_id': The ID of the record with the invalid temporal order.
               - 'outlier': A constant column with value "Temporal Outlier" indicating the type of outlier.
               - 'outlier_description': A constant column with an empty string as the description.
               - 'class': A constant column with the value of the `entity_class` parameter.
               - 'attribute': The names of the time columns causing the violation.
               - 'attribute_value': The concatenated values of the time columns causing the violation.
    """

    def temporal_analysis_column(df: DataFrame, entity_class: str, time_columns: list) -> DataFrame:
        missing_columns = [col for col in time_columns if col not in df.columns]
        if missing_columns:
            return df.select(lit(None).cast(StringType()).alias("p_id"), lit("Temporal Outlier").alias("outlier"),
                             lit("Missing Attributes: " + (",".join(missing_columns))).alias("outlier_description"),
                             lit(entity_class).alias("class"), lit(",".join(time_columns)).alias("attribute"),
                             lit(None).cast(StringType()).alias("attribute_value")).limit(1)
        for column in time_columns:
            df = df.withColumn(column, col(column).cast("long"))
        conditions = []
        for i in range(len(time_columns) - 1):
            conditions.append(col(time_columns[i]) <= col(time_columns[i + 1]))
        combined_condition = conditions[0]
        for condition in conditions[1:]:
            combined_condition = combined_condition & condition
        result_df = df.filter(~combined_condition)
        result_df = result_df.select("p_id", *time_columns)
        result_df = result_df.withColumn("attribute_value", concat_ws(",", *time_columns)) \
            .withColumn("attribute", lit(','.join(time_columns))) \
            .withColumn("outlier", lit("Temporal Outlier")) \
            .withColumn("outlier_description", lit(None).cast(StringType())) \
            .withColumn("class", lit(entity_class))
        if result_df.count() == 0:
            result_df = df.select(lit(None).cast(StringType()).alias("p_id"), lit("Temporal Outlier").alias("outlier"),
                                  lit("Success").alias("outlier_description"),
                                  lit(entity_class).alias("class"), lit(",".join(time_columns)).alias("attribute"),
                                  lit(None).cast(StringType()).alias("attribute_value")).limit(1)
        return result_df.select("p_id", "outlier", "outlier_description", "class", "attribute", "attribute_value")

    invalid_values_df = None
    for time_columns in time_columns_list:
        if invalid_values_df is None:
            invalid_values_df = temporal_analysis_column(df, entity_class, time_columns)
        else:
            invalid_values_df = invalid_values_df.unionByName(temporal_analysis_column(df, entity_class, time_columns))
    return invalid_values_df.filter("outlier!=''") if invalid_values_df is not None else invalid_values_df


if __name__ == "__main__":
    logger = logging.getLogger("temporal_analysis")
    spark = SparkSession.builder.getOrCreate()
    args = get_args()
    entity_class = ' '.join(args.tableName.split("__")[-1].capitalize().split("_")).title()
    temporal_fields = get_configs(spark, args.configURL).get("temporal_analysis")
    dataframe = read_table(args.tableName, args.startEpoch, args.endEpoch, spark)
    outputDF = (temporal_analysis(dataframe, entity_class, temporal_fields)
                .withColumn("updated_at", lit(args.endEpoch))
                .withColumn("updated_at_ts", to_timestamp(col("updated_at") / 1000)))
    write_table(outputDF, args.outputPath, ["updated_at_ts", "class", "outlier"],
                [days(col("updated_at_ts")), "class", "outlier"],
                spark)
