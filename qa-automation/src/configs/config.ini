[data_setup]
environment = EKS
timestamp = 1686268799999
sdm_start_time = 0000000000000
sdm_schema = sdm
ei_schema = ei
bucket_name = DATALAKE_URI
updated_at = UPDATED_AT
sdm_schema_name = SDM_SCHEMA_NAME
ei_schema_name = EI_SCHEMA_NAME
srdm_schema_name = SRDM_SCHEMA_NAME

[ui_setup]
browser = chrome
headless_mode = true
timeout = 5

[credentials]
username = SELENIUM_USERNAME
password = SELENIUM_PASSWORD
api_client_id = KC_CLIENT_ID
api_client_secret = KC_CLIENT_SECRET
dremio_username = DREMIO_USERNAME
dremio_password = DREMIO_PASSWORD
gh_token = GIT_PASSWORD
jira_secret = JIRA_SECRET
ssl_verify = SSL_VERIFY
keycloak_ssl_verify = KEYCLOAK_SSL_VERIFY
qa_local_mode=False


[url]
base_url = SELENIUM_SDS3_URL
druid_api_url = DRUID_API_URL
keycloak_host = KEYCLOAK_HOST
internal_url=CONFIG_ARTIFACTORY_URI
keycloak_realm = KEYCLOAK_REALM
api_endpoint_url = AIRFLOW_CONN_ADMIN_API_BASE_URL
config_endpoint_url = MANAGEMENT_API_UI_MAPPING_ENDPOINT
dremio_api_url = DREMIO_SERVICE_NAME