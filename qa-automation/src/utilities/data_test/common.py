from pyspark.sql import SparkSession
from datetime import datetime, timezone
import os
from core.common.logging_utils import setup_logger

logger = setup_logger(__name__)
 
class Common:
    ICEBERG_WAREHOUSE = os.environ["ICEBERG_WAREHOUSE"]
    ICEBERG_CATALOG_URI = os.environ["ICEBERG_CATALOG_URI"]
    HADOOP_FS_IMPL = os.environ["HADOOP_FS_IMPL"]
    HADOOP_FS_END_POINT = os.environ["HADOOP_FS_END_POINT"]
    EXECUTOR_MEMORY = os.environ["EXECUTOR_MEMORY"]
    DRIVER_MEMORY = os.environ["DRIVER_MEMORY"]
    NAMESPACE = os.environ["NAMESPACE"]
    EXECUTOR_INSTANCES = str(os.environ["EXECUTOR_INSTANCES"]).strip("n")
    IMAGE = os.environ["IMAGE"]
    HOSTNAME = os.environ["HOSTNAME"]
    POD_IP = os.getenv("POD_IP")
    SERVICE_ACCOUNT = os.environ["SERVICE_ACCOUNT"]
    APP_NAME = os.environ["APP_NAME"]
    UPDATED_AT = (int(os.environ["UPDATED_AT"]) * 1000) + 999
    ICEBERG_CATALOG_TYPE = os.environ["ICEBERG_CATALOG_TYPE"]
 
    def create_spark_session(self):
        # Set up the SparkSession
        logger.info("Entered spark creation function")
        spark = SparkSession.builder\
            .appName(self.APP_NAME) \
            .enableHiveSupport().config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") \
            .config("spark.kubernetes.namespace",f"{self.NAMESPACE}") \
            .config("spark.master", "k8s://https://kubernetes.default.svc.cluster.local:443") \
            .config("spark.kubernetes.authenticate.serviceAccountName",self.SERVICE_ACCOUNT)\
            .config("spark.kubernetes.container.image", f"{self.IMAGE}") \
            .config("spark.kubernetes.container.image.pullSecrets", "docker-secret") \
            .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions") \
            .config("spark.sql.catalog.iceberg_catalog", self.ICEBERG_CATALOG_TYPE) \
            .config("spark.sql.catalog.iceberg_catalog.warehouse", f"{self.ICEBERG_WAREHOUSE}") \
            .config("spark.sql.catalog.iceberg_catalog.type", "hive") \
            .config("spark.sql.catalog.iceberg_catalog.uri", f"thrift://{self.ICEBERG_CATALOG_URI}") \
            .config("spark.hadoop.fs.s3a.impl", self.HADOOP_FS_IMPL) \
            .config("spark.driver.port","22321")\
            .config("spark.submit.deployMode", "client") \
            .config("spark.blockManager.port","22322")\
            .config("spark.driver.host",f"{self.POD_IP}") \
            .config("spark.hadoop.fs.s3a.endpoint", f"{self.HADOOP_FS_END_POINT}") \
            .config("spark.kubernetes.executor.limit.cores","4")\
            .config("spark.executor.cores","1")\
            .config("spark.executor.memory", f"{self.EXECUTOR_MEMORY}") \
            .config("spark.kubernetes.executor.limit.memory","32g")\
            .config("spark.driver.memory", f"{self.DRIVER_MEMORY}") \
            .config("spark.executor.instances", f"{self.EXECUTOR_INSTANCES}") \
            .getOrCreate()
        logger.info("Spark Session Creation Successful")
        return spark
 
    def stopsparksession(self, spark):
        logger.info("Entered function to stop spark")
        spark.stop()
        logger.info("Spark Session stopped")
 
    def convert_epoch_to_ts(self, epoch_time):
        return datetime.fromtimestamp((int(epoch_time)/1000), timezone.utc)
