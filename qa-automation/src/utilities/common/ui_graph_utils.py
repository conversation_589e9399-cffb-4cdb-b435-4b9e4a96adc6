import requests
from core.common.common_utils import CommonUtils
import time
from core.ui.basePage import BasePage
from selenium.webdriver.common.by import By
from selenium.common.exceptions import WebDriverException
import traceback

common_utils = CommonUtils()
app_json = 'application/json'

class UiGraphUtils(BasePage):
    
    def _check_tooltip_with_normal_hover(self, plot, xpath_graph_tooltip):
        try:
            """Try normal hover and return tooltip text if found"""
            self.actions.move_to_element(plot).perform()
            time.sleep(0.1)
            tooltip_elem = self._driver.find_elements(By.XPATH, xpath_graph_tooltip)
            
            if tooltip_elem:
                for elem in tooltip_elem:
                    if elem.text and elem.text.strip():
                        return elem.text
            return None
        except WebDriverException as e:
            self.handle_webdriver_exception(e)
    
    def _create_dot(self, x, y):
        """Create a visual dot at specified coordinates"""
        create_dot_js = """
        function createDot(x, y) {
            const dot = document.createElement('div');
            dot.style.position = 'absolute';
            dot.style.left = x + 'px';
            dot.style.top = y + 'px';
            dot.style.width = '3px';
            dot.style.height = '3px';
            dot.style.backgroundColor = 'red';
            dot.style.borderRadius = '50%';
            dot.style.zIndex = '10000';
            dot.className = 'debug-dot';
            document.body.appendChild(dot);
        }
        document.querySelectorAll('.debug-dot').forEach(dot => dot.remove());
        createDot(arguments[0], arguments[1]);
        """
        self._driver.execute_script(create_dot_js, x, y)
        
    def _clean_dots(self):
        """Remove all debug dots"""
        self._driver.execute_script("document.querySelectorAll('.debug-dot').forEach(dot => dot.remove());")

    def _check_tooltip_with_pixel_movement(self, plot, xpath_graph_tooltip):
        try:
            """Try pixel by pixel movement and return tooltip text if found"""
            rect = plot.rect
            max_distance = int(rect['width']/2)
            
            # Try moving left
            for distance in range(2, max_distance, 2):
                self.actions.move_to_element(plot).perform()
                self.actions.move_by_offset(-distance, 0).perform()
                
                location = plot.location
                size = plot.size
                x = location['x'] + size['width']/2 - distance
                y = location['y'] + size['height']/2
                self._create_dot(x, y)
                
                time.sleep(0.01)
                tooltip_elem = self._driver.find_elements(By.XPATH, xpath_graph_tooltip)
                if tooltip_elem:
                    for elem in tooltip_elem:
                        if elem.text and elem.text.strip():
                            return elem.text
            
            # Try moving right if left didn't work
            for distance in range(2, max_distance, 2):
                self.actions.move_to_element(plot).perform()
                self.actions.move_by_offset(distance, 0).perform()
                
                location = plot.location
                size = plot.size
                x = location['x'] + size['width']/2 + distance
                y = location['y'] + size['height']/2
                self._create_dot(x, y)
                
                time.sleep(0.01)
                tooltip_elem = self._driver.find_elements(By.XPATH, xpath_graph_tooltip)
                if tooltip_elem:
                    for elem in tooltip_elem:
                        if elem.text and elem.text.strip():
                            return elem.text

            self._clean_dots()
            return None
        except WebDriverException as e:
            self.handle_webdriver_exception(e)
    
    def _parse_chart_tooltip_text(self, tooltip_text):
        try:
            """Convert tooltip text to structured format"""
            lines = tooltip_text.strip().split('\n')
            tooltip_data = {}
            i = 0
            while i < len(lines):
                # Get type (first line)
                type_name = lines[i]  
                # Skip "Count" line
                i += 2
                # Get count value and remove commas
                count = int(lines[i].replace(',', ''))
                # Skip "Percentage" line
                i += 2
                # Get percentage and convert from string (e.g., "0.33%") to float
                percentage = float(lines[i].rstrip('%'))
                tooltip_data[type_name] = {
                    'count': count,
                    'percentage': percentage
                } 
                i += 1 
            return tooltip_data
        except WebDriverException as e:
            self.handle_webdriver_exception(e)
    

    def _format_api_type_data(self, api_response):
        try:
            api_data = {}
            for item in api_response:
                api_data[item['type']] = {
                    'count': item['p_id_COUNTDISTINCT'],
                    'percentage': round(item['percentage'], 2)
                }
            #Replace None key with "Unknown"
            if None in api_data:
                api_data["Unknown"] = api_data.pop(None)
            return api_data
        except WebDriverException as e:
            self.handle_webdriver_exception(e)
    
    
    def format_api_data(self, api_response, type_field='type', count_field='p_id_COUNTDISTINCT', percentage_field='percentage', unknown_key='Unknown', round_digits=2):
        formatted_data = {}
        for item in api_response:
            # Get type value, defaulting to None if field doesn't exist
            type_value = item.get(type_field)
            # Handle missing or invalid fields gracefully
            try:
                percentage = float(item[percentage_field])
                # Format percentage to preserve trailing zeros
                percentage_formatted = float(format(percentage, f'.{round_digits}f'))
                
                formatted_data[type_value] = {
                    'count': item[count_field],
                    'percentage': percentage_formatted
                }
            except (KeyError, ValueError, TypeError) as e:
                self.handle_webdriver_exception(e)
                continue
        # Replace None key with specified unknown_key if present
        if None in formatted_data:
            formatted_data[unknown_key] = formatted_data.pop(None)
        return formatted_data
    
    def get_horizontal_bar_chart_data(self, graph_points, tooltip):
        ui_tooltip_texts = []
        plot_points = self.find_elements(graph_points)
        for plot in plot_points:
            self.actions.move_to_element(plot).perform()
            tooltip_elem = self._driver.find_elements(By.XPATH, tooltip)
            for elem in tooltip_elem:
                if elem.text and elem.text.strip():
                    ui_tooltip_texts.append(elem.text)
        combined_tooltip_text = '\n'.join(ui_tooltip_texts)
        return combined_tooltip_text
    
    def sort_by_count(self, data, field):
    # Sort items by count in descending order
        sorted_items = sorted(data.items(), key=lambda x: x[1][field], reverse=True)
        # Convert back to dictionary
        return dict(sorted_items)