import configparser
import os
from core.common.common_utils import CommonUtils
from src.utilities.data_test.constants import CommonConstants

common_utils = CommonUtils()
constants=CommonConstants()
config_path = CommonConstants.CONFIG_PATH

class ReadConfig():

    def __init__(self):
        self.config = configparser.ConfigParser()
        self.config.read(config_path)
    
    def get_config_value(self, section, key, bool_val):
        val = common_utils.get_config_value(section, key, bool_val)
        return val
    
    def get_deployment_url(self):
        base_url = self.get_config_value('url', 'internal_url', True)
        config_endpoint = base_url+'/sds_mgmnt/config-manager/api/v1/config-item/deployment_config?solution=ei'
        return config_endpoint

    def get_spark_jobs_url(self):
        base_url = self.get_config_value('url', 'internal_url', True)
        config_endpoint = base_url+'/sds_mgmnt/config-manager/api/v1/config-item/spark-job-configs'
        return config_endpoint
        
    def get_env(self):
        environment = self.config['data_setup']['environment']
        return environment
    
    def get_config(self,key,value):
        config_val = self.config[key][value]
        return config_val
    
    
    
