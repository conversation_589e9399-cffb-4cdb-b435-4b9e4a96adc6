Feature: Inter Source Validation

# @host @runner.continue_after_failed_step @AUT-T2342 @AUT-T2343 @AUT-T2344 @AUT-T2345 @AUT-T2346 @AUT-T2347 @AUT-T2348 @AUT-T2351 @AUT-T2352 @AUT-T2353 @AUT-T2354
# Scenario: To Validate the Inter Source Disambiguation - Host
#    Given Read the inter host config json
#    Then Validation of count - disambiguated_p_id count and final intersource output and in resolver
#    And    Validation of count - p_id count in Unioned output and resolver
#    And    Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated
#    And    Validation of Exception - Validate whether the records with matching keys but is an exception case is not disambiguated
#    And    Validation of Disambiguation - Validate that all the canidate_keys having same value is disambiguated to one p_id
#    And    Validate the time aggregation fields
#    And    Validate that there is no duplicate p_id and both primary_key and p_id is not null
#    And    Validation of Disambiguation - Validate that in a disambiguate group, a record is  having atleast one matching candidate key value
#    And    Generate the inter report

# @identity @runner.continue_after_failed_step @AUT-T2724 @AUT-T2725 @AUT-T2726 @AUT-T2727 @AUT-T2728 @AUT-T2729 @AUT-T2730 @AUT-T2731 @AUT-T2732 @AUT-T2733 @AUT-T2734 @AUT-T2735 @AUT-T2736
# Scenario: To Validate the Inter Source Disambiguation - Identity
#    Given  Read the inter identity config json
#    Then   Validation of count - disambiguated_p_id count and final intersource output and in resolver
#    And    Validation of count - p_id count in Unioned output and resolver
#    And    Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated
#    And    Validation of Exception - Validate whether the records with matching keys but is an exception case is not disambiguated
#    And    Validation of Disambiguation - Validate that all the canidate_keys having same value is disambiguated to one p_id
#    And    Validate the time aggregation fields
#    And    Validate that there is no duplicate p_id and both primary_key and p_id is not null
#    And    Validation of Disambiguation - Validate that in a disambiguate group, a record is  having atleast one matching candidate key value
#    And    Generate the inter report

# @person @runner.continue_after_failed_step  @AUT-T2711 @AUT-T2712 @AUT-T2713 @AUT-T2714 @AUT-T2715 @AUT-T2716 @AUT-T2717 @AUT-T2718 @AUT-T2719 @AUT-T2720 @AUT-T2721 @AUT-T2722 @AUT-T2723
# Scenario: To Validate the Inter Source Disambiguation - Person
#    Given  Read the inter person config json
#    Then   Validation of count - disambiguated_p_id count and final intersource output and in resolver
#    And    Validation of count - p_id count in Unioned output and resolver
#    And    Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated
#    And    Validation of Exception - Validate whether the records with matching keys but is an exception case is not disambiguated
#    And    Validation of Disambiguation - Validate that all the canidate_keys having same value is disambiguated to one p_id
#    And    Validate the time aggregation fields
#    And    Validate that there is no duplicate p_id and both primary_key and p_id is not null
#    And    Validation of Disambiguation - Validate that in a disambiguate group, a record is  having atleast one matching candidate key value
#    And    Generate the inter report

# @vulnerability @runner.continue_after_failed_step  @AUT-T2698 @AUT-T2699 @AUT-T2700 @AUT-T2701 @AUT-T2702 @AUT-T2703 @AUT-T2704 @AUT-T2705 @AUT-T2706 @AUT-T2707 @AUT-T2708 @AUT-T2709 @AUT-T2710
# Scenario: To Validate the Inter Source Disambiguation - Vulnerability
#    Given  Read the inter vulnerability config json
#    Then   Validation of count - disambiguated_p_id count and final intersource output and in resolver
#    And    Validation of count - p_id count in Unioned output and resolver
#    And    Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated
#    And    Validation of Exception - Validate whether the records with matching keys but is an exception case is not disambiguated
#    And    Validation of Disambiguation - Validate that all the canidate_keys having same value is disambiguated to one p_id
#    And    Validate the time aggregation fields
#    And    Validate that there is no duplicate p_id and both primary_key and p_id is not null
#    And    Validation of Disambiguation - Validate that in a disambiguate group, a record is  having atleast one matching candidate key value
#    And    Generate the inter report

# @cloud @runner.continue_after_failed_step  @AUT-T2698 @AUT-T2699 @AUT-T2700 @AUT-T2701 @AUT-T2702 @AUT-T2703 @AUT-T2704 @AUT-T2705 @AUT-T2706 @AUT-T2707 @AUT-T2708 @AUT-T2709 @AUT-T2710
# Scenario: To Validate the Inter Source Disambiguation - Cloud
#    Given  Read the inter cloud config json
#    Then   Validation of count - disambiguated_p_id count and final intersource output and in resolver
#    And    Validation of count - p_id count in Unioned output and resolver
#    And    Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated
#    And    Validation of Exception - Validate whether the records with matching keys but is an exception case is not disambiguated
#    And    Validation of Disambiguation - Validate that all the canidate_keys having same value is disambiguated to one p_id
#    And    Validate the time aggregation fields
#    And    Validate that there is no duplicate p_id and both primary_key and p_id is not null
#    And    Validation of Disambiguation - Validate that in a disambiguate group, a record is  having atleast one matching candidate key value
#    And    Generate the inter report

@inter @EGS-T7630 @EGS-T7629 @EGS-T7633 @EGS-T7627 @EGS-T7641 @EGS-T7639 @EGS-T7645 @EGS-T7628 @EGS-T7644 @prod @runner.continue_after_failed_step
Scenario: To validate cloud_storage inter
   Given  the "cloud_storage" inter config is read
   Then   validate if the disambiguated_p_id count in resolver and final intersource output is the same
   And    validate if the p_id count of output after union of loaders and resolver is the same
   And    validate that the records under entity exception should not be disambiguated
   And    validate if all the candidate keys having the same value is disambiguated into one p_id
   And    validate that all date and time fields are aggregated based on min or max
   And    check whether the p_id is unique
   And    validate that the display_label, primary_key and p_id is not null
   And    validate that in the disambiguated group, a record is having at least one matching candidate key value
   And    validate that lifetime, recency, recent_activity, observed_lifetime are not negative
   #And    generate the inter report
   And    attach file to allure
