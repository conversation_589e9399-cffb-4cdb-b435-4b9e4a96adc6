from behave import userdata
from behave.model import Scenario
from src.utilities.data_test.common import Common
from behave.model_core import Status
from core.jira.tc_execution_status import jira_status_update
from core.common.common_utils import CommonUtils
from src.utilities.common.readProperties import ReadConfig
from core.common.logging_utils import setup_logger
logger = setup_logger(__name__)

common = Common()
common_utils=CommonUtils()
readConfig=ReadConfig()

exception_list = ['non_disambiguated_relationship','inter']


def before_all(context):
    logger.info("Reached start of before all")
    # ENV = common_utils.get_config_value('data_setup', 'environment')
    ENV = readConfig.get_env()
    logger.info("Env retrieved:")
    logger.info(ENV)
    if ENV=="AKS":
        context.spark = common.create_sparksession_azure_dev()
    else:
        context.spark = common.create_spark_session()    #EKS
    context.i=0
    logger.info("Reached end of before all")

def after_step(context, step):
    logger.info("Reached start of after step")
    tag = str(context.scenario.tags[context.i])
    context.i = context.i+1
    if('config is read' not in step.name):
        status = context.status
        if 'non_disambiguated_relationship' in context.scenario.tags:
            jira_status_update(tag, status,"EGS", "EGS_Data_ND_Rel")
        elif 'inter' in context.scenario.tags:
            jira_status_update(tag, status,"EGS", "EGS_Data_Inter")
    context.status = ''
    logger.info("Reached end of after step")

def after_scenario(context, scenario):
    logger.info("Reached start of after scenario")
    if all(string not in context.scenario.tags for string in exception_list):
        tag = str(scenario.tags)
        status = scenario.status
        output_list = tag.strip('[]').replace("'", "").split(", ")
        if hasattr(context, 'status') and context.status == "Not Executed":
            status = "Not Executed"
        elif scenario.status == Status.passed:
            status = "Pass"
        elif scenario.status == Status.failed:
            status = "Fail"
        else:
            print("Scenario status:", scenario.status)

        for tc in output_list:
            jira_status_update(tc, status, "DF","DF-R28")

        print("\n ---------------------------------------------------------------------------")
        if scenario.status == 'passed':
            print(f"Scenario '{scenario.tags}' passed")
        elif scenario.status == 'failed':
            print(f"Scenario '{scenario.tags}' failed due to exception")
        print("\n ---------------------------------------------------------------------------")
        logger.info("Reached end of after scenario")

def after_all(context):
    logger.info("Reached start of after scenario")
    common.stopsparksession(context.spark)
    logger.info("Reached end of after scenario")
