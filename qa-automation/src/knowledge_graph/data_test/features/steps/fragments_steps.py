from behave import given, then
from src.knowledge_graph.data_test.tests.knowledge_validation import KnowledgeGraphValidation
from src.utilities.data_test.common import Common
from core.common.common_utils import CommonUtils
from core.api.api_client import APIClient
import re
from src.utilities.common.readProperties import ReadConfig 
from src.utilities.common.api_utils import APIUtils
from core.common.logging_utils import setup_logger 

logger = setup_logger(__name__)


common=CommonUtils()
mismatch_error_message = "Test Case Failed"
readConfig = ReadConfig()
env = readConfig.get_config('data_setup', 'environment')
apiclient = APIClient()
api_utils= APIUtils()


@given('The deployement and inter config is read')
def read_configs_json(context):
    logger.info("entered function to read configss::::::")
    # ssl_verify_str=readConfig.get_config_value('url','ssl_verify', True)
    # ssl_verify = ssl_verify_str.lower() in ('true', '1', 'yes')
    url = readConfig.get_deployment_url()
    headers = api_utils.set_headers()
    logger.info(url)
    logger.info(headers)
    # logger.info(ssl_verify)
    context.deployement_config = apiclient.get(url, headers=headers)
    logger.info(f"Deployment Config Response: {context.deployement_config}")
    if 'config_value' not in context.deployement_config:
        logger.error("'config_value' key is missing in deployment config response!")
        raise KeyError("'config_value' key is missing in deployment config response!")									
    inter_table = context.deployement_config['config_value']['spark_job_configs']['source_models']['intersource_disambiguated_models']   
    logger.info(f"Extracted inter_table: {inter_table}")
    url = readConfig.get_spark_jobs_url() + "/" + inter_table[0]
    headers = api_utils.set_headers()
    context.inter_config = apiclient.get(url, headers=headers)
    publish_table=context.deployement_config['config_value']['spark_job_configs']['source_models']['publisher']
    url=readConfig.get_spark_jobs_url() + "/" + publish_table[0]
    headers=api_utils.set_headers()
    context.publish_config = apiclient.get(url, headers=headers)

    logger.info(f"Inter Config Response: {context.inter_config}")
    context.kg_validation = KnowledgeGraphValidation()


@then('Validate if the inidvidual fragments tables are available and the count are correct')
def fragment_count_compare(context):   
    mismatch_flag, context.pass_output, context.fail_output = context.kg_validation.fragment_count_comp(context.spark, context.deployement_config,context.inter_config)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"
        
        
@then('Validate the publish count')
def publish_count_check(context):   
    mismatch_flag, context.pass_output, context.fail_output = context.kg_validation.publish_count(context.spark, context.deployement_config, context.publish_config,context.pass_output, context.fail_output)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

   
@then('Generate the report')
def generate_report(context):   
    context.kg_validation.generate_report(context.pass_output,context.fail_output)
    print("File Written Successfully")
								 
		   
								 

									
										
																		 
					  
																															   
																														 
																 
		   
																																		
																																  
																 