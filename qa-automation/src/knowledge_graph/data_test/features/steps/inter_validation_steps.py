from behave import given, then
from core.common.common_utils import CommonUtils
from src.utilities.common.common_utilities import CommonUtilities
from src.knowledge_graph.data_test.tests.intra_inter_validation import IntraInterValidation
from src.utilities.common.readProperties import ReadConfig
from src.utilities.data_test.common import Common
 

utils_common = CommonUtils()
utils_common_data = CommonUtilities()
mismatch_error_message = "Test Case Failed"
readConfig = ReadConfig()
env = readConfig.get_env()



@given(u'the "{inter}" inter config is read')
def read_json(context, inter):
    inter_str = str(inter)
    json_files = f"src/entity_inventory/data_test/test_data/inter_source/{inter_str}.json"
    json_file = utils_common_data.update_json_file(json_files, str(Common.UPDATED_AT))
    print("updated json : ", json_file)
    context.inter_config = utils_common.jsonreader(json_files)
    context.intra_inter_validation = IntraInterValidation(context.spark, context.inter_config)

@given('Read the inter host config json')
def read_inter_host_json(context):
    json_file = "src/entity_inventory/data_test/test_data/inter_source/sds_qa_ei__host__job_config_qa.json"
    context.inter_config = utils_common.jsonreader(json_file)

@given('Read the inter identity config json')
def read_inter_identity_json(context):
    json_file = 'src/entity_inventory/data_test/test_data/Inter_source/sds_qa_ei__identity__job_config.json'
    context.inter_config = utils_common.jsonreader(json_file)

@given('Read the inter person config json')
def read_inter_person_json(context):
    json_file = "src/entity_inventory/data_test/test_data/Inter_source/sds_qa_ei__person__job_config.json"
    context.inter_config = utils_common.jsonreader(json_file)

@given('Read the inter vulnerability config json')
def read_inter_person_json(context):
    json_file = "src/entity_inventory/data_test/test_data/Inter_source/sds_qa_ei__vulnerability__job_config.json"
    context.inter_config = utils_common.jsonreader(json_file)

@given('Read the inter cloud config json')
def read_inter_person_json(context):
    json_file = "src/entity_inventory/data_test/test_data/Inter_source/sds_qa_ei__cloud__job_config.json"
    context.inter_config = utils_common.jsonreader(json_file)

@then('validate if the disambiguated_p_id count in resolver and final intersource output is the same')
def inter_out_resolver_count_comp(context):   
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_out_resolver_count_comp()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('validate if the p_id count of output after union of loaders and resolver is the same')
def inter_union_resolver_count_comp(context):
    mismatch_flag = True
    context.count_file_fail, context.report_name, mismatch_flag = context.intra_inter_validation.intra_inter_union_resolver_count_comp()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated')
def inter_exception_other_matching_keys(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_exception_other_matching_keys()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"
    print("Validation of Exception - Validate whether the records with matching candidate keys other than exception case is disambiguated Done")
    
@then('validate that the records under entity exception should not be disambiguated')
def inter_exception_no_other_matching_keys(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_exception_no_other_matching_keys(context.inter_config)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('validate if all the candidate keys having the same value is disambiguated into one p_id')
def inter_disamb_samekeys_one_p_id(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_disamb_samekeys_one_p_id(context.inter_config)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('validate that all date and time fields are aggregated based on min or max')
def inter_agg(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_aggregate(context.inter_config)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('check whether the p_id is unique')
def inter_p_id_check(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_out_p_id_check()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"
    
@then('validate that the display_label, primary_key and p_id is not null')
def inter_p_id_primary_key_display_label_check(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.intra_inter_out_p_id_primary_key_display_label_check()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('validate that lifetime, recency, recent_activity, observed_lifetime are not negative')
def lifetime_recency_recent_activity_observed_lifetime_negative_check(context):
    context.count_file_fail, context.x, mismatch_flag = context.intra_inter_validation.lifetime_recency_recent_activity_observed_lifetime_negative_check()
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('validate that in the disambiguated group, a record is having at least one matching candidate key value')
def inter_disamb_at_least_matching_key_in_a_groups(context):
    context.count_file_success, context.count_file_fail, context.report_name, mismatch_flag, context.x = context.intra_inter_validation.intra_inter_disamb_at_least_matching_key_in_a_group(context.inter_config)
    if mismatch_flag:
        context.status = "Fail"
    else:
        context.status = "Pass"

@then('generate the inter report')
def copyinterreporttos3_adls(context):
    # copyfiletoS3(self, s3path, report_name, tempfilepath, test_type):
    if env == "EKS":
        utils_common.copyfiletos3("pai-sol-integration-datalake", context.report_name, context.count_file_success, "ei_test_success")
        utils_common.copyfiletos3("pai-sol-integration-datalake", context.report_name, context.count_file_fail, "ei_test_fail")
        context.intra_inter_validation.write_to_report(context.x)
    else:
        utils_common.copy_to_adls(context.count_file_success, 'sdstpdevincesa', 'sds-tpdev-datalake', "data_test/inter_report.txt")
        utils_common.copy_to_adls(context.count_file_fail, 'sdstpdevincesa', 'sds-tpdev-datalake', "data_test/inter_report.txt")
        context.intra_inter_validation.write_to_report(context.x)