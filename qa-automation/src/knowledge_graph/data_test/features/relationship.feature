# Feature: Relationship Validation
#    @runner.continue_after_failed_step
#    Sc<PERSON><PERSON>: To Validate the Relationship.
#       Given  Read the vulnerability findings defender config json
#       Then   Validation of count of SDM build blocks and relationship block
#       And    Validation the uniquness of relationship_id
#       And    Validate first_seen, last_seen, lifetime and recency and Status field population of Relationship
#       And    Generate the relationship report
