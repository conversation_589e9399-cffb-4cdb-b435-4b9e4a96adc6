{"caption": "Identity", "description": "The Inventory Dictionary defines attributes and includes references to the events and objects in which they are used", "extends": "", "attributes": {"p_id": {"caption": "Entity ID", "description": "The 128 bit unique identifier of every entity in inventory.", "examples": ["0000030cd18cad4c6c1e696d85fac4fa3ef6e53e380040d12c8655e93b76bb6e", "112bfd47e969ccaad185b1f087dd9c8b6e23bf6f882ccb59343af3476159e8cc"], "group": "common", "ui_visibility": true, "enable_hiding": true, "type": "string_t"}, "display_label": {"caption": "Display Label", "description": "The derived and best known identifier or name, based on the attribute that best uniquely identifies it. Could be account name for identity.", "examples": ["HYD-JOHDOE-MOB", "<PERSON>"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "class": {"caption": "Class", "description": "The abstract super-type of Entity.", "examples": ["Host", "Person", "Identity", "Vulenrability"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "type": {"caption": "Type", "description": "The specific type of the entity", "examples": ["NORMAL_USER_ACCOUNT", "MACHINE_ACCOUNT"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "string_t"}, "first_seen_date": {"caption": "First Seen", "description": "The date at which the entity was first observed in the inventory", "group": "common", "examples": "*************", "ui_visibility": true, "enable_hiding": false, "type": "timestamp_t"}, "last_seen_date": {"caption": "Last Seen", "description": "The date at which the entity was last observed in the inventory.", "group": "common", "examples": "*************", "ui_visibility": true, "enable_hiding": false, "type": "timestamp_t"}, "lifetime": {"caption": "Lifetime", "description": "The number of days an entity has existed", "examples": "5", "group": "common", "ui_visibility": true, "type": "range_t"}, "recency": {"caption": "Recency", "description": "How long ago (in days) was the entity last observed in the inventory.", "examples": "10", "group": "common", "ui_visibility": true, "type": "range_t"}, "origin": {"caption": "Origin", "description": "A comma seperated list of source names from which the entity was recorded.", "examples": ["MS Intune", "Defender"], "group": "common", "ui_visibility": true, "enable_hiding": false, "type": "list_t"}, "entity_tag": {"caption": "Entity Tag", "description": "The tag to give context to an entity.", "examples": {"Account Name": "<EMAIL>", "Account Type": "NORMAL_USER_ACCOUNT"}, "group": "common", "ui_visibility": true, "type": "string_t"}, "identity_provider": {"caption": "Identity Provider", "description": "An Identity Provider (IDP) is a system that manages digital identities and provides authentication services for users.", "examples": ["AD", "AAD"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "service": {"caption": "Service", "description": "The system, service or application, that has an account that is identifed by the username(What is being accessed/logged into)", "examples": ["azure_ad", "ad_domain"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "account_name": {"caption": "Account Name", "description": "The name/login used to access the system or service", "examples": ["<EMAIL>", "d1624f15-b162-4ae1-a964-3c72ae096c85"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": false, "candidate_key": true}, "account_type": {"caption": "Account Type", "description": "The type of account being accessed", "examples": ["Service Account", "MACHINE_ACCOUNT", "NORMAL_USER_ACCOUNT"], "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "Saviynt IGA"], "derived_field": false, "candidate_key": false}, "status": {"caption": "Identity Status", "description": "The status of the host", "enum_t": {"0": {"caption": "Unknown"}, "1": {"caption": "Active"}, "2": {"caption": "Disabled"}}, "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": true, "candidate_key": false}, "activity_status": {"caption": "Activity Status", "description": "Indicates whether the account is active in last 180 days", "enum_t": {"0": {"caption": "Unknown"}, "1": {"caption": "Active"}, "2": {"caption": "Inactive"}}, "group": "entity_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": true, "candidate_key": false}, "last_active_date": {"caption": "Last Active Date", "description": "The last active date of the account", "examples": "*************", "group": "entity_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "SuccessFactors", "Windows Security Logs", "Saviynt IGA", "GlobalProtect", "MS Intune", "MS Defender", "ServiceNow ITSM", "MS Azure AD"], "derived_field": true, "candidate_key": false}, "distinguished_name": {"caption": "Distinguished Name", "description": "The name that uniquely identifies an entry in the directory.", "examples": ["CN=DELTA$,CN=Users,DC=corp,DC=lyman,DC=com", "CN=<PERSON><PERSON>,OU=DEV-USER,OU=Users,OU=Montreal,OU=Locations,OU=_Corp,DC=corp,DC=lyman,DC=com"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory"], "derived_field": false, "candidate_key": false}, "when_created_date": {"caption": "When Created Date", "description": "The date when this object was created", "examples": ["*************", "*************"], "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "account_expires_date": {"caption": "Account Expires Date", "description": "The date in which the account expires", "examples": ["*************", "*************"], "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "Saviynt IGA"], "derived_field": false, "candidate_key": false}, "last_password_change_date": {"caption": "Last Password Change Date", "description": "The date at which the password is last changed", "examples": ["*************", "*************"], "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["MS Active Directory", "Saviynt IGA"], "derived_field": false, "candidate_key": false}, "sam_account_name": {"caption": "Sam Account Name", "description": "The logon name used to support clients and servers", "examples": ["1E61DE7C6EE24BCF948", "<EMAIL>", "SOF-VESGENITA-MOB$"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "Windows Security Logs", "Saviynt IGA", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "domain": {"caption": "Domain", "description": "The Domain of the account", "examples": ["corp", "lyman", "com"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "MS Azure AD", "GlobalProtect"], "derived_field": false, "candidate_key": false}, "sam_account_name_with_domain": {"caption": "Sam Account Name With Domain", "description": "The logon name used to support clients and servers along with domain of the account ", "examples": ["corp\\7e2368c8-5e11-456c-989e-50352f1c48e0", "lyman\\MTL-JONDEJ1LA-MOB$", "PROD\\TOR-PROD-PSM-03$"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Active Directory", "Windows Security Logs"], "derived_field": false, "candidate_key": false}, "user_principal_name": {"caption": "User Principal Name", "description": "The sign in name used by the users when they sign in (log in) to their accounts", "examples": ["corp\\7e2368c8-5e11-456c-989e-50352f1c48e0", "lyman\\MTL-JONDEJ1-MOB$", "PROD\\TOR-PROD-PSM-03$"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Saviynt IGA"], "derived_field": false, "candidate_key": false}, "account_id": {"caption": "Account Id", "description": "The unique number used to identify the account", "examples": ["CN=<PERSON>,OU=GEN-USER,OU=Users,OU=Montreal,OU=Locations,OU=_Corp,DC=corp,DC=lyman,DC=com", "0054G00000AOBOCQA5", "9030"], "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["Saviynt IGA"], "derived_field": false, "candidate_key": false}, "last_logon_date": {"caption": "Last Logon Date", "description": "The last date at which the user logged in to the account", "examples": ["Shared Account", "Service Account", "Test Account"], "group": "source_specific", "ui_visibility": true, "type": "timestamp_t", "source": ["Saviynt IGA", "MS Active Directory", "MS Azure AD"], "derived_field": false, "candidate_key": false}, "password_policy": {"caption": "Password Policy", "description": "A password policy is a set of rules and requirements that an organization or system enforces to ensure the security of passwords used to access its resources. ", "examples": "", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Azure AD"], "derived_field": false, "candidate_key": false}, "last_sync_date": {"caption": "Last Sync Date", "description": "The most recent date that a device or application synchronized data with another device, server, or system.", "examples": "*************", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Azure AD"], "derived_field": false, "candidate_key": false}, "on_premises_sync_enabled_status": {"caption": "On Premises Sync Enabled Status", "description": "The current status of a synchronization feature that allows data to be synchronized between on-premises servers or systems and cloud-based applications or services.", "examples": "True", "group": "source_specific", "ui_visibility": true, "source": ["MS Azure AD"], "derived_field": false, "candidate_key": false, "type": "string_t"}, "enrollment_type": {"caption": "Enrollment Type", "description": "The method by which a user or device is registered and enrolled in a system, program, or service. ", "examples": "", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Azure AD"], "derived_field": false, "candidate_key": false}, "security_identifier": {"caption": "Security Identifier", "description": "A Security Identifier (SID) is a unique value that is assigned to a user or group account in a Windows operating system environment.", "examples": "S-1-5-21-**********-**********-********-1013", "group": "source_specific", "ui_visibility": true, "type": "string_t", "source": ["MS Azure AD"], "derived_field": false, "candidate_key": false}}}