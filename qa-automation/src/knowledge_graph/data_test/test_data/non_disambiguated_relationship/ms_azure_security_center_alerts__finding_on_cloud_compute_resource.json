{"block_variables": ["source_p_id", "target_p_id"], "iceberg_catalog_name": "iceberg_catalog", "schema_name": "ei", "is_source_intra_resolver_present": "true", "is_target_intra_resolver_present": "true", "intra_resolver_table_name": "sds_ei_intra_source_resolver", "inter_resolver_table_name": "sds_ei_inter_source_resolver", "source_inter_table_name": "sds_ei__finding", "target_inter_table_name": "sds_ei__cloud_compute", "relationship_table_name": "sds_ei__rel__ms_azure_security_center_alerts__finding_on_cloud_compute_resource", "override_columns": {}, "temporary_properties": {"temp_resource_identifiers": "explode(properties.resourceIdentifiers)", "temp_resource_id": "temp_resource_identifiers.azureResourceId"}, "srdm": {"iceberg_catalog_name": "iceberg_catalog", "schema_name": "srdm", "table_name": "microsoft_azure__security_center_alerts", "end_event_timestamp_ts": "2024-06-05 23:59:59.999", "start_event_timestamp_ts": "1970-01-01 00:00:00.000", "end_parsed_interval_timestamp_ts": "2024-06-06 06:00:00.000", "columns_to_select": []}, "loader": {"source_table_name": "sds_ei__finding__ms_azure_security_center_alerts__id", "source_primary_key_logic": "LOWER(id)", "target_table_name": "sds_ei__cloud_compute__ms_azure_security_alerts__azure_resource_id", "target_primary_key_logic": "lower(temp_resource_id)"}, "common_attributes": {"relationship_name": "Finding On Cloud Compute Resource", "inverse_relationship_name": "Cloud Compute Resource Has Finding", "relationship_origin": "MS Azure", "relationship_first_seen_date": "min(event_timestamp_epoch)", "relationship_last_seen_date": "max(event_timestamp_epoch)", "source_entity_class": "Finding", "target_entity_class": "Cloud Compute"}, "optional_attributes": {"relationship_first_seen_date": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.startTimeUtc)))", "relationship_last_seen_date": "UNIX_MILLIS(TIMESTAMP(to_timestamp(properties.endTimeUtc)))"}}