{"inp_table_list": {"df_ms_azure": "sds_ei__cloud_storage__ms_azure", "df_aws": "sds_ei__cloud_storage__aws"}, "class": "Cloud Storage", "resolver_table": "sds_ei_inter_source_resolver", "schema": "ei_edm_sit_v4", "inter_table": "sds_ei__cloud_storage", "candidate_keys": "resource_id", "min_agg_fields": "first_seen_date,first_found_date", "max_agg_fields": "last_found_date,last_active_date", "updated_at": "1717545599999", "exception_filter": "", "exception_keys": "host_name,aad_device_id", "exception_expression": {"ex_host_name": "CASE WHEN (lower(host_name_inp) like '%redmi%' or lower(host_name_inp) like '%huawei%' or lower(host_name_inp) like '%iphone%' or lower(host_name_inp) like '%android%' or lower(host_name_inp) like '%ipad%' or lower(host_name_inp) like '%macbook%' or lower(host_name_inp) like '%galaxy%' or lower(host_name_inp) like '%samsung%' or lower(host_name_inp) ='wrk' or lower(os_inp) like '%android%' or lower(os_inp) like '%appleios%' or lower(os_inp) like '%tizen%' or host_name_inp RLIKE '(?i)pro([^a-zA-Z0-9]|$)' or host_name_inp ='USER DELETED FOR THIS DEVICE or cloud_resource_id IS NOT NULL OR cloud_instance_id IS NOT NULL') THEN True ELSE False END", "ex_aad_device_id": "CASE WHEN (aad_device_id_inp = '00000000-0000-0000-0000-000000000000') THEN True ELSE False END"}}