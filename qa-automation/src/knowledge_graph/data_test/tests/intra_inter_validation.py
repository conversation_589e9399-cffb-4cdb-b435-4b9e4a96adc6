from prettytable import PrettyTable
from pyspark.sql.window import Window
from pyspark.sql.functions import lit, expr, col, lag, lead, explode_outer
from io import StringIO
import tempfile
import sys
import pyspark.sql.functions as F
import pandas as pd
import numpy as np
import csv

class IntraInterValidation:
    def __init__(self, spark, data):
        updated_at = data['updated_at']
        table_list = data['inp_table_list']
        schema_name = data['schema']
        self.type_of_test = data['resolver_table']
        if(self.type_of_test) == 'sds_ei_intra_source_resolver':
            self.class_name = data['schema']+'.'+data['intra_table']
        else:
            self.class_name = data['class']
        df = spark.createDataFrame([(1, "test")],["id_test", "label"])
        df = df.filter(df.id_test != df.first().id_test)
        candidate_keys1 = data['candidate_keys']
        self.candidate_keys = [x for x in candidate_keys1.split(",")]
        for df_name, table_name in table_list.items():
            df_name = spark.sql(f"select * from iceberg_catalog.{schema_name}.{table_name} where updated_at= {updated_at}")
            #explode the candidate_keys
            for key_name in self.candidate_keys:
                column_names = df_name.columns
                array_column_names = [key_name]
                selected_columns = [col(column_name) for column_name in column_names if column_name not in array_column_names]
                is_array_column = key_name in df_name.schema.fieldNames() and \
                df_name.schema[key_name].dataType.typeName() == "array"
                if is_array_column:
                    df_name = df_name.select(*selected_columns, explode_outer(col(f"{key_name}")).alias(key_name))
            # handling of array, array of struct columns etc
            fields_1 = set(df.columns)
            fields_2 = set(df_name.columns)                   
            common_fields = fields_1.intersection(fields_2)
            for field in common_fields:
                data_type_1 = df.schema[field].dataType
                data_type_2 = df_name.schema[field].dataType
                if data_type_1 != data_type_2:
                    df_name = df_name.withColumnRenamed(field, f"{field}_{df_name}")
            df = df.unionByName(df_name, allowMissingColumns=True)
        df = df.toDF(*(c.replace(c, c + '_inp') for c in df.columns))
        for key_name in self.candidate_keys:
            window_spec = Window.orderBy(f"{key_name}_inp")
            df = df.withColumn(f"lag_{key_name}", lag(f"{key_name}_inp", 1).over(window_spec))
            df = df.withColumn(f"lead_{key_name}", lead(f"{key_name}_inp", 1).over(window_spec))
            new_column = expr(f"CASE WHEN ({key_name}_inp!= ' ' and {key_name}_inp!= '' and {key_name}_inp == lag_{key_name}) or ({key_name}_inp!= ' ' and {key_name}_inp == lead_{key_name}) THEN True ELSE False END")
            df = df.withColumn(f"is_{key_name}_match", new_column)
        if data['exception_filter']:
            for field_name, expression in data['exception_expression'].items():
                new_column = expr(data['exception_expression'][field_name])
                df = df.withColumn(field_name, new_column)
        rename_list = ['origin', 'primary_key', 'first_seen', 'last_seen', 'p_id', 'analysis_period_end','analysis_period_start', 'class', 'display_label', 'lifetime', 'recency', 'type','updated_at']
        rename_list_bri = ['origin', 'primary_key']
        table_name_bri = data['resolver_table']
        self.df_bri = spark.sql(f"select * from iceberg_catalog.{schema_name}.{table_name_bri} where updated_at= {updated_at} and class ='{self.class_name}'")
        for field_name in rename_list_bri:
            self.df_bri = self.df_bri.withColumnRenamed(f"{field_name}", f"{field_name}_bri")
        rf = df.join(self.df_bri, [df.p_id_inp == self.df_bri.p_id])
        if(self.type_of_test) == 'sds_ei_intra_source_resolver':
            table_name_intra = data['intra_table']
            self.df_intra = spark.sql(f"select * from iceberg_catalog.{schema_name}.{table_name_intra} where updated_at= {updated_at}")
            for field_name in rename_list:
                self.df_intra = self.df_intra.withColumnRenamed(f"{field_name}", f"{field_name}_intra")
            self.rf = rf.join(self.df_intra, [rf.disambiguated_p_id == self.df_intra.p_id_intra])
        else:
            table_name_inter = data['inter_table']
            self.df_inter = spark.sql(f"select * from iceberg_catalog.{schema_name}.{table_name_inter} where updated_at= {updated_at}")
            for field_name in rename_list:
                self.df_inter = self.df_inter.withColumnRenamed(f"{field_name}", f"{field_name}_inter")
            self.rf = rf.join(self.df_inter, [rf.disambiguated_p_id == self.df_inter.p_id_inter])
        ####### Unioning for validating the count ###############
        df_count = spark.createDataFrame([(1, "test")],["id_test", "label"])
        df_count = df_count.filter(df_count.id_test != df_count.first().id_test)
        for df_name, table_name in table_list.items():
            df_name = spark.sql(f"select * from iceberg_catalog.{schema_name}.{table_name} where updated_at= {updated_at}")
            # handling of array, array of struct columns etc
            fields_1 = set(df_count.columns)
            fields_2 = set(df_name.columns)                   
            common_fields = fields_1.intersection(fields_2)
            for field in common_fields:
                data_type_1 = df_count.schema[field].dataType
                data_type_2 = df_name.schema[field].dataType
                if data_type_1 != data_type_2:
                    df_name = df_name.withColumnRenamed(field, f"{field}_{df_name}")
            df_count = df_count.unionByName(df_name, allowMissingColumns=True)
        self.df_count = df_count.toDF(*(c.replace(c, c + '_inp') for c in df_count.columns))
        self.rf = self.rf.withColumn('count', F.count('p_id_inp').over(Window.partitionBy('disambiguated_p_id')))

    def intra_inter_out_resolver_count_comp(self):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f1, tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            x = PrettyTable()
            mismatch_flag = False
            original_stdout = sys.stdout
            x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
            csv_name ='failed_result.csv'
            # 1. Validating the count
            if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                TEST_CASE = "Validation of Count between intra Source Output and intra Source Resolver"
                # a. All the p_id's in the resolver table should be present in the intra final out
            else:
                TEST_CASE = "Validation of Count between Inter Source Output and Inter Source Resolver"
                # a. All the p_id's in the resolver table should be present in the inter final out
            disamb_p_id_resolver = self.df_bri.select("disambiguated_p_id").distinct().count()
            print('Count of Disambguited p_id in Resolver =', disamb_p_id_resolver)
            if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                disamb_p_id_intra = self.df_intra.select("p_id_intra").count()
                print('Count of Disambguited p_id in final out =', disamb_p_id_intra)
                if (disamb_p_id_resolver == disamb_p_id_intra):
                    x.add_row([self.class_name, TEST_CASE, "PASS"])
                else:
                    x.add_row([self.class_name, TEST_CASE, "FAIL"])
                    mismatch_flag = True
            else:
                disamb_p_id_inter = self.df_inter.select("p_id_inter").count()
                print('Count of Disambguited p_id in final out =', disamb_p_id_inter)
                if (disamb_p_id_resolver == disamb_p_id_inter):
                    x.add_row([self.class_name, TEST_CASE, "PASS"])
                else:
                    x.add_row([self.class_name, TEST_CASE, "FAIL"])
                    mismatch_flag = True
            f1.write(str(x))
            sys.stdout = f2
            if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                if disamb_p_id_resolver > disamb_p_id_intra:
                    additional_rows_resolver = (self.df_bri.select("disambiguated_p_id").distinct()).subtract(self.df_intra.select("p_id_intra"))
                    print(f"******************************{self.class_name}*****************************")
                    print(TEST_CASE)
                    print("Additional rows in Resolver Table:")
                    additional_rows_resolver.show(truncate=False)
                    df_test = additional_rows_resolver.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                elif disamb_p_id_resolver < disamb_p_id_intra:
                    additional_rows_intra_out = self.df_intra.select("p_id_intra").subtract(self.df_bri.select("disambiguated_p_id").distinct())
                    print(f"******************************{self.class_name}*****************************")
                    print(TEST_CASE)
                    print("Additional rows in intra Source Table:")
                    additional_rows_intra_out.show(truncate=False)                    
                    df_test = additional_rows_intra_out.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
            else:
                if disamb_p_id_resolver > disamb_p_id_inter:
                    additional_rows_resolver = (self.df_bri.select("disambiguated_p_id").distinct()).subtract(self.df_inter.select("p_id_inter"))
                    print(f"******************************{self.class_name}*****************************")
                    print(TEST_CASE)
                    print("Additional rows in Resolver Table:")
                    additional_rows_resolver.show(truncate=False)
                    df_test = additional_rows_resolver.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                elif disamb_p_id_resolver < disamb_p_id_inter:
                    additional_rows_inter_out = self.df_inter.select("p_id_inter").subtract(self.df_bri.select("disambiguated_p_id").distinct())
                    print(f"******************************{self.class_name}*****************************")
                    print(TEST_CASE)
                    print("Additional rows in Inter Source Table:")
                    additional_rows_inter_out.show(truncate=False)                    
                    df_test = additional_rows_inter_out.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
            sys.stdout = original_stdout
            print(str(x))
            temp_file_path2 = f2.name
        return temp_file_path2, x, mismatch_flag

    def intra_inter_union_resolver_count_comp(self):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            x = PrettyTable()
            mismatch_flag = False
            original_stdout = sys.stdout
            x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
            if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                TEST_CASE = "Validation of Count between Unioned Output and intra Source Resolver"
            else:
                TEST_CASE = "Validation of Count between Unioned Output and Inter Source Resolver"
            original_stdout = sys.stdout
            mismatch_flag = False
            csv_name ='failed_result1.csv'
            # b. All the p_id's in the resolver table should be present in the union table out
            p_id_resolver = self.df_bri.select("p_id").distinct().count()
            print('Count of non disambiguated p_id in Resolver', p_id_resolver)
            p_id = self.df_count.select("p_id_inp").count()
            print('Count of p_id in Union out', p_id)
            if p_id_resolver == p_id:
                x.add_row([self.class_name, TEST_CASE, "PASS"])
            else:
                x.add_row([self.class_name, TEST_CASE, "FAIL"])
                mismatch_flag = True
            sys.stdout = f2
            if p_id_resolver > p_id:
                additional_rows_resolver = (self.df_bri.select("p_id").distinct()).subtract(self.df_count.select("p_id_inp"))
                print(f"******************************{self.class_name}*****************************")
                print(TEST_CASE)
                print("Additional rows in Resolver Table:")
                additional_rows_resolver.show(truncate=False)
                # additional_rows_resolver.toPandas() 
                # df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                # with allure.step("Attach DataFrame to Allure"):
                #     allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
            elif p_id_resolver < p_id:
                additional_rows_union_out = self.df_count.select("p_id_inp").subtract(self.df_bri.select("p_id").distinct())
                print(f"******************************{self.class_name}*****************************")
                print(TEST_CASE)
                print("Additional rows in Union Table:")
                additional_rows_union_out.show(truncate=False)
                ##### For disaplaying failed cases in the output ##################
                # df_test= additional_rows_union_out.toPandas() 
                # df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                # with allure.step("Attach DataFrame to Allure"):
                #     allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
        sys.stdout = original_stdout
        print(str(x))
        temp_file_path2 = f2.name
        return temp_file_path2, x, mismatch_flag
    # 2. Check whether the exception is correct

    def intra_inter_exception_other_matching_keys(self, data):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            x = PrettyTable()
            mismatch_flag = False
            original_stdout = sys.stdout
            x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
            mismatch_flag = False
            csv_name ='failed_result2.csv'
            candidate_keys = [x for x in self.candidate_keys.split(",")]
            # a. Checks whether the records with matching candidate keys other than exception case is
            # disambiguated. Records with exception filter but with other matching keys, so count will be !=1,
            # ie will be disambiguated.
            if data['exception_filter'] == 'True':
                exception_keys = data['exception_keys']
                exception_keys = [x for x in exception_keys.split(",")]
                count = 0
                variables = {}
                for ex_key_name in exception_keys:
                    variables['condition_' + ex_key_name] = ""
                    for var_name, var_value in variables.items():
                        for key_name in candidate_keys:
                            if key_name != ex_key_name:
                                if (count == 0):
                                    var_value = var_value + f"is_{key_name}_match == True"
                                    count = count + 1
                                else:
                                    var_value = var_value + " " + f"or is_{key_name}_match == True"
                        count = 0
                        var_value = "(" + var_value + ")"
                        for ex_key_name_inner in exception_keys:
                            if ex_key_name == ex_key_name_inner:
                                var_value = var_value + f"and ex_{ex_key_name_inner} ==True "
                            else:
                                var_value = var_value + f"and ex_{ex_key_name_inner} !=True "
                        var_value = '"' + var_value + '"'
                        var_value = eval(var_value)
                    temp_df = self.rf.filter(var_value).select("count").distinct().sort("count")
                    if temp_df.select(col("*")).count() != 0:
                        for index, row_iterator in temp_df.toPandas().iterrows():
                            count_temp = (row_iterator[0], row_iterator[0])
                            sys.stdout = f2
                            if count_temp == (1, 1):
                                x.add_row([self.class_name,f"Records with exception filter for {ex_key_name} but disambiguated with other ""matching keys", "FAIL"])
                                mismatch_flag = True
                                exception_with_other_matching_keys = (rf.filter(var_value).filter("count = 1").select("disambiguated_p_id","p_id_inp").sort("disambiguated_p_id"))
                                print(f"******************************{self.class_name}*****************************")
                                print("Records with an exception key but also have other matching candidate keys, but didn't get disambiguated")
                                exception_with_other_matching_keys.show(100, truncate=False)
                                df_test= exception_with_other_matching_keys.toPandas() 
                                df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                                with allure.step("Attach DataFrame to Allure"):
                                    allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                                break
                            else:
                                x.add_row([self.class_name,f"Records with exception filter for {ex_key_name} but disambiguated with other ""matching keys", "PASS"])
                                break
                    else:
                        x.add_row([self.class_name,f"Records with exception filter for {ex_key_name} but disambiguated with other ""matching keys", "PASS"])
            else:
                x.add_row([self.class_name, f"There is no exception filter for Class {self.class_name}", "PASS"])
        sys.stdout = original_stdout
        print(str(x))
        print("mismatch_flag final", mismatch_flag ,"#####################################")
        temp_file_path2 = f2.name
        return temp_file_path2, x, mismatch_flag

    def intra_inter_exception_no_other_matching_keys(self, data):
        try:
            with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
                x = PrettyTable()
                original_stdout = sys.stdout
                x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
                mismatch_flag = False
                print("A")
                csv_name ='failed_result.csv'
                candidate_keys = data['candidate_keys']
                candidate_keys1 = [z for z in candidate_keys.split(",")]
                # b. Checks whether the records with matching keys but it’s an exception case is not disambiguated.
                # Records with exception filter but with no matching keys, so count =1, ie will not be disambiguated.
                if data['exception_filter'] == 'True':
                    exception_keys = data['exception_keys']
                    exception_keys = [y for y in exception_keys.split(",")]
                    count = 0
                    variables = {}
                    for ex_key_name in exception_keys:
                        variables['condition_' + ex_key_name] = ""
                        for var_name, var_value in variables.items():
                            for key_name in candidate_keys1:
                                if key_name != ex_key_name:
                                    if count == 0:
                                        var_value = var_value + f"is_{key_name}_match != True"
                                        count = count + 1
                                    else:
                                        var_value = var_value + " " + f"AND is_{key_name}_match != True"
                            count = 0
                            var_value = "(" + var_value + ")"
                            for ex_key_name_inner in exception_keys:
                                if ex_key_name == ex_key_name_inner:
                                    var_value = var_value + f"and ex_{ex_key_name_inner} ==True "
                            var_value = '"' + var_value + '"'
                            var_value = eval(var_value)
                        temp_df = self.rf.filter(var_value).select("count").distinct().sort("count")
                        if temp_df.select(col("*")).count() != 0:
                            sys.stdout = f2
                            for index, row_iterator in temp_df.toPandas().iterrows():
                                count_temp = (row_iterator[0], row_iterator[0])
                                if count_temp != (1, 1):
                                    x.add_row([self.class_name,f"Exception filter {ex_key_name} and no other matching keys is not disambiguated","FAIL"])
                                    mismatch_flag = True
                                    exception_with_other_matching_keys = (self.rf.filter(var_value).filter("count != 1").select("disambiguated_p_id","p_id_inp").sor("disambiguated_p_id"))
                                    print(f"******************************{self.class_name}*****************************")
                                    print(f"Exception filter {ex_key_name} and no other matching keys is not disambiguated")
                                    exception_with_other_matching_keys.show(100, truncate=False)
                                    df_test= exception_with_other_matching_keys.toPandas() 
                                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                                    with allure.step("Attach DataFrame to Allure"):
                                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                                    break
                                else:
                                    x.add_row([self.class_name,f"Exception filter {ex_key_name} and no other matching keys is not disambiguated","PASS"])
                                    break
                        else:
                            x.add_row([self.class_name,f"Exception filter {ex_key_name} and no other matching keys is not disambiguated","PASS"])
                else:
                    print("There is no exception filter for Class")
                    x.add_row([self.class_name, f"There is no exception filter for Class {self.class_name}", "PASS"])
                sys.stdout = original_stdout
                print(str(x))
                temp_file_path2 = f2.name
                print("B")
            return temp_file_path2, x, mismatch_flag
        except Exception as e:
             print ("Error in the function", e)

    def intra_inter_disamb_samekeys_one_p_id(self, data):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            original_stdout = sys.stdout
            x = PrettyTable()
            x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
            mismatch_flag = False
            csv_name ='failed_result.csv'
            print("C")
            candidate_keys = data['candidate_keys']
            candidate_keys = [x for x in candidate_keys.split(",")]
            exception_keys = data['exception_keys']
            exception_keys = [x for x in exception_keys.split(",")]
            # a. Checks whether records with matching candidate keys are disambiguated to a single group.
            sys.stdout = f2
            for key_name in candidate_keys:
                if data['exception_filter'] == 'True':
                    if key_name in exception_keys:
                        filter_df = self.rf.filter(f" ex_{key_name}!=True and {key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct()
                        for index, row_iterator in filter_df.toPandas().iterrows():
                            count_temp = (row_iterator[1], row_iterator[1])
                            if count_temp != (1, 1):
                                x.add_row([self.class_name,f"Records with matching candidate keys are disambiguated to a single group for candidate key for key_name in exception_keys: {key_name}","FAIL"])
                                mismatch_flag = True
                                self.rf.filter(f" ex_{key_name}!=True and {key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con", ascending=False).distinct().show(100, truncate=False)
                                result = self.rf.filter(f" ex_{key_name}!=True and {key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con", ascending=False).distinct()
                                df_test= result.toPandas() 
                                df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                                with allure.step("Attach DataFrame to Allure"):
                                    allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                                break
                            else:
                                x.add_row([self.class_name,f"Records with matching candidate keys are disambiguated to a single group for candidate key for key_name in exception_keys: {key_name}","PASS"])
                                break
                    else:
                        filter_df = self.rf.filter(f"{key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct()
                        for index, row_iterator in filter_df.toPandas().iterrows():
                            count_temp = (row_iterator[1], row_iterator[1])
                            if count_temp != (1, 1):
                                x.add_row([self.class_name,f"Records with matching candidate keys are disambiguated to a single group for candidate key for key_name not in exception_keys: {key_name}","FAIL"])
                                mismatch_flag = True
                                self.rf.filter(f"{key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct().show(100, truncate=False)
                                result.rf.filter(f"{key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct()
                                df_test= result.toPandas() 
                                df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                                with allure.step("Attach DataFrame to Allure"):
                                    allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                                break
                            else:
                                x.add_row([self.class_name,f"Records with matching candidate keys are disambiguated to a single group for candidate key for key_name not in exception_keys: {key_name}","PASS"])
                                break
                else:
                    filter_df_1 = self.rf.filter(f"{key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct()
                    for index, row_iterator in filter_df_1.toPandas().iterrows():
                        count_temp1 = (row_iterator[1], row_iterator[1])
                        if count_temp1 != (1, 1):
                            x.add_row([self.class_name,f"Records with matching candidate keys are disambiguated to a single group for candidate key for exception filter condition false: {key_name}","FAIL"])
                            mismatch_flag = True
                            self.rf.filter(f"{key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct().show(100, truncate=False)
                            result =  self.rf.filter(f"{key_name}_inp is not null and {key_name}_inp!= '' and length(trim({key_name}_inp)) > 0").groupby(f"{key_name}_inp").agg(F.countDistinct("disambiguated_p_id").alias("con")).sort("con",ascending=False).distinct()
                            df_test= result.toPandas() 
                            df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                            with allure.step("Attach DataFrame to Allure"):
                                allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                            break
                        else:
                            x.add_row([self.class_name,f"Records with matching candidate keys are disambiguated to a single group for candidate key for exception filter condition false: {key_name}","PASS"])
                            break
            sys.stdout = original_stdout
            print(str(x))
            temp_file_path2 = f2.name
            print("D")
        return  temp_file_path2,  x, mismatch_flag

    def intra_inter_aggregate(self, data):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            x = PrettyTable()
            x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
            original_stdout = sys.stdout
            mismatch_flag = False
            csv_name ='failed_result.csv'
            if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                w = Window.partitionBy("p_id_intra")
            else:
                w = Window.partitionBy("p_id_inter")
            min_agg_field = data['min_agg_fields']
            print(min_agg_field)
            min_agg_fields = [x for x in min_agg_field.split(",")]
            case_desc_min = "Min of Aggregation field"
            for key_name in min_agg_fields:
                self.rf = self.rf.withColumn(f'{key_name}_test', F.min(f'{key_name}_inp').over(w))
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    filter_df= self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra")
                else:
                    filter_df= self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter")
                if filter_df.select(col("*")).count() != 0:
                    x.add_row([self.class_name,f"{case_desc_min} {key_name}","MIN_FAIL"])
                    mismatch_flag = True
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra",f"{key_name}").show(truncate=False)
                        result = self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra",f"{key_name}")
                    else:
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
                        result = self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}")
                    df_test= result.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                else:
                    x.add_row([self.class_name,f"{case_desc_min} {key_name}","MIN_PASS"])
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra",f"{key_name}").show(truncate=False)
                    else:
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
            max_agg_field = data['max_agg_fields']
            max_agg_fields = [x for x in max_agg_field.split(",")]   
            case_desc_max = "Max of Aggregation field"
            for key_name in max_agg_fields:
                self.rf = self.rf.withColumn(f'{key_name}_test', F.max(f'{key_name}_inp').over(w))
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    filter_df= self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra")
                else:
                    filter_df= self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter")
                if filter_df.select(col("*")).count() != 0:
                    x.add_row([self.class_name,f"{case_desc_max} {key_name}","MAX_FAIL"])
                    mismatch_flag = True
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra",f"{key_name}").show(truncate=False)
                        result = self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra",f"{key_name}")
                    else:
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
                        result = self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}")
                    df_test= result.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                else:
                    x.add_row([self.class_name,f"{case_desc_max} {key_name}","MAX_PASS"])
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_intra",f"{key_name}").show(truncate=False)
                    else:
                        self.rf.filter(f"{key_name}_test!={key_name}").select("p_id_inter",f"{key_name}").show(truncate=False)
            sys.stdout = original_stdout
            print(str(x))
            temp_file_path2 = f2.name
        return  temp_file_path2, x, mismatch_flag

    def intra_inter_out_p_id_check(self):
            with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
                x = PrettyTable()
                x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
                original_stdout = sys.stdout
                mismatch_flag = False
                csv_name ='failed_result.csv'
                print(self.class_name)
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    intra_out_count = self.df_intra.count()
                    intra_p_id_count = self.df_intra.select("p_id_intra").distinct().count()
                    print(intra_out_count)
                    TEST_CASE_NAME = "Validation of p_id Uniqueness"
                    if intra_out_count == intra_p_id_count:
                        x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                    else:
                        x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                        mismatch_flag = True
                else:
                    inter_out_count = self.df_inter.count()
                    inter_p_id_count = self.df_inter.select("p_id_inter").distinct().count()
                    print(inter_out_count)
                    TEST_CASE_NAME = "Validation of p_id Uniqueness"
                    if inter_out_count == inter_p_id_count:
                        x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                    else:
                        x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                        mismatch_flag = True
                sys.stdout = f2
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    if intra_out_count != intra_p_id_count:
                        duplicate_p_id_rows_intra = (self.df_intra.groupby("p_id_intra").agg(F.count("p_id_intra").alias("con")).filter("con!=1").sort("con",ascending=False).show(truncate=False))
                        print(f"******************************{self.class_name}*****************************")
                        print("Validation of p_id Uniqueness")
                        print("Duplicate p_id's in Intra output:")
                        duplicate_p_id_rows_intra.show(truncate=False)
                        df_test= duplicate_p_id_rows_intra.toPandas() 
                        df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                        with allure.step("Attach DataFrame to Allure"):
                            allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                else:
                    if inter_out_count != inter_p_id_count:
                        duplicate_p_id_rows_inter = (self.df_inter.groupby("p_id_inter").agg(F.count("p_id_inter").alias("con")).filter("con!=1").sort("con",ascending=False).show(truncate=False))
                        print(f"******************************{self.class_name}*****************************")
                        print("Validation of p_id Uniqueness")
                        print("Duplicate p_id's in Intra output:")
                        duplicate_p_id_rows_inter.show(truncate=False)
                        df_test= duplicate_p_id_rows_inter.toPandas() 
                        df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                        with allure.step("Attach DataFrame to Allure"):
                            allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
            sys.stdout = original_stdout
            print(str(x))
            temp_file_path2 = f2.name
            return  temp_file_path2, x, mismatch_flag

    def intra_inter_out_p_id_primary_key_display_label_check(self):
            with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
                x = PrettyTable()
                x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
                original_stdout = sys.stdout
                mismatch_flag = False
                mismatch_flag_disp_label = False
                mismatch_flag_primary_key = False
                mismatch_flag_p_id = False
                csv_name ='failed_result.csv'
                print(self.class_name)
                # Validate p_id is not null and Validate that the primary_key is not null#
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    p_id_is_null = self.df_intra.filter("p_id_intra is null or trim(p_id_intra)='' or p_id_intra RLIKE '^\\s*$'").select("p_id_intra").count()
                else:
                    p_id_is_null = self.df_inter.filter("p_id_inter is null or trim(p_id_inter)='' or p_id_inter RLIKE '^\\s*$'").select("p_id_inter").count()
                TEST_CASE_NAME = "Validate that p_id is not null"
                if p_id_is_null == 0:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                else:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                    mismatch_flag_p_id = True
                # Validate primary_key is not null
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    primary_key_is_null = self.df_intra.filter("primary_key is null or trim(primary_key)='' or primary_key RLIKE '^\\s*$'").select("p_id_intra").count()
                else:
                    primary_key_is_null = self.df_inter.filter("primary_key is null or trim(primary_key)='' or primary_key RLIKE '^\\s*$'").select("p_id_inter").count()
                TEST_CASE_NAME = "Validate that primary_key is not null"
                if primary_key_is_null == 0:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                else:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                    mismatch_flag_primary_key = True
                # Validate display_label is not null
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    display_label_is_null = self.df_intra.filter("display_label is null or trim(display_label)='' or display_label RLIKE '^\\s*$'").select("p_id_intra").count()
                else:
                    display_label_is_null = self.df_inter.filter("display_label is null or trim(display_label)='' or display_label RLIKE '^\\s*$'").select("p_id_inter").count()
                TEST_CASE_NAME = "Validate that display_label is not null"
                if display_label_is_null == 0:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                else:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                    mismatch_flag_disp_label = True
                sys.stdout = f2
                if p_id_is_null != 0:
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        p_id_null = (self.df_intra.filter("p_id_intra is null or trim(p_id_intra)='' or p_id_intra RLIKE '^\\s*$'").select("p_id_intra", "class","primary_key","origin"))
                    else:
                        p_id_null = (self.df_inter.filter("p_id_inter is null or trim(p_id_inter)='' or p_id_inter RLIKE '^\\s*$'").select("p_id_inter", "class","primary_key","origin"))
                    print(f"******************************{self.class_name}*****************************")
                    print("Validate that p_id is not null")
                    print(" Blank p_id's:")
                    p_id_null.show(truncate=False)
                    df_test= p_id_null.toPandas() 
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                elif primary_key_is_null != 0:
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        p_id_null = (self.df_intra.filter("primary_key is null or trim(primary_key)='' or primary_key RLIKE '^\\s*$'").select("p_id_intra", "class", "primary_key", "origin"))
                    else:
                        p_id_null = (self.df_inter.filter("primary_key is null or trim(primary_key)='' or primary_key RLIKE '^\\s*$'").select("p_id_inter", "class", "primary_key", "origin"))
                    print(f"****************************** {self.class_name}*****************************")
                    print("Validation of primary_key is not null")
                    print("Blank primary_key's:")
                    p_id_null.show(truncate=False)
                    df_test= p_id_null.toPandas()
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                elif display_label_is_null != 0:
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        p_id_null = (self.df_intra.filter("display_label is null or trim(display_label)='' or display_label RLIKE '^\\s*$'").select("p_id_intra", "class", "display_label", "origin"))
                    else:
                        p_id_null = (self.df_inter.filter("display_label is null or trim(display_label)='' or display_label RLIKE '^\\s*$'").select("p_id_inter", "class", "display_label", "origin"))
                    print(f"****************************** {self.class_name}*****************************")
                    print("Validation of display_label is not null")
                    print("Blank display_label's:")
                    p_id_null.show(truncate=False)
                    df_test= p_id_null.toPandas()
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
            if(mismatch_flag_disp_label == mismatch_flag_primary_key == mismatch_flag_p_id == False):   
                mismatch_flag = False
            else:
                mismatch_flag = True
            sys.stdout = original_stdout
            print(str(x))
            temp_file_path2 = f2.name
            return  temp_file_path2, x, mismatch_flag

    def lifetime_recency_recent_activity_observed_lifetime_negative_check(self):
            with tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
                x = PrettyTable()
                x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
                original_stdout = sys.stdout
                mismatch_flag = False
                csv_name ='failed_result.csv'
                if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                    neg_counter = self.df_intra.filter("recency_intra<0 or lifetime_intra<0 or recent_activity<0 or observed_lifetime<0").count()
                else:
                    neg_counter = self.df_inter.filter("recency_inter<0 or lifetime_inter<0 or recent_activity<0 or observed_lifetime<0").count()
                TEST_CASE_NAME = "Validation of lifetime recency recent_activity and observed_lifetime negative values"
                if neg_counter == 0:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "PASS"])
                else:
                    x.add_row([ self.class_name, TEST_CASE_NAME, "FAIL"])
                    mismatch_flag = True
                sys.stdout = f2
                if neg_counter != 0:
                    if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                        negative_values = (self.df_intra.groupby("p_id_intra").agg(F.count("p_id_intra").alias("con")).filter("recency_intra<0 or lifetime_intra<0 or recent_activity_intra<0 or observed_lifetime_intra<0").sort("con",ascending=False).show(truncate=False))
                    else:
                        negative_values = (self.df_inter.groupby("p_id_inter").agg(F.count("p_id_inter").alias("con")).filter("recency_inter<0 or lifetime_inter<0 or recent_activity_inter<0 or observed_lifetime_inter<0").sort("con",ascending=False).show(truncate=False))
                    print(f"******************************{self.class_name}*****************************")
                    print("Validation of lifetime, recency, recent_activity and observed_lifetime negative values")
                    print("Negative values in output:")
                    negative_values.show(truncate=False)
                    df_test= negative_values.toPandas()
                    df_test.to_csv(csv_name, index=False, encoding='utf-8')
                    with allure.step("Attach DataFrame to Allure"):
                        allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
            sys.stdout = original_stdout
            print(str(x))
            temp_file_path2 = f2.name
            return temp_file_path2, x, mismatch_flag

    def intra_inter_disamb_at_least_matching_key_in_a_group(self, data):
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f1, tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            x = PrettyTable()
            x.field_names = {"CLASS", 'TEST_CASE', "STATUS"}
            mismatch_flag = False
            csv_name ='failed_result.csv'
            original_stdout = sys.stdout
            candidate_keys = data['candidate_keys']
            candidate_keys = [x for x in candidate_keys.split(",")]
            exception_keys = data['exception_keys']
            exception_keys = [x for x in exception_keys.split(",")]
            for key_name in candidate_keys:
                window_spec = Window.partitionBy("disambiguated_p_id").orderBy(f"{key_name}_inp","last_found_date_inp")
                self.rf = self.rf.withColumn(f"lag_{key_name}_joined", lag(f"{key_name}_inp", 1).over(window_spec))
                self.rf = self.rf.withColumn(f"lead_{key_name}_joined", lead(f"{key_name}_inp", 1).over(window_spec))
                new_column = expr(f"CASE WHEN (length(trim({key_name}_inp))>0 and {key_name}_inp is not null and {key_name}_inp == lag_{key_name}_joined ) or (length(trim({key_name}_inp))>0 and {key_name}_inp is not null and {key_name}_inp == lead_{key_name}_joined) THEN True ELSE False END")
                self.rf = self.rf.withColumn(f"is_{key_name}_joined", new_column)
                # c.Checks whether any disambiguated group contains any record with no matching candidate keys, ie it identifies any records which doesn’t belong to that group
                # For disambiguated records is_join should be True
            # rf.printSchema()
            count_temp = 0
            var_value = ""
            for key_name in candidate_keys:
                if data['exception_filter'] == True:
                    if key_name in exception_keys:
                        if count_temp == 0:
                            var_value = var_value + f"(is_{key_name}_joined == True and ex_{key_name}!=True)"
                            count_temp = count_temp + 1
                        else:
                            var_value = var_value + " " + f"or (is_{key_name}_joined == True and ex_{key_name}!=True) "
                    else:
                        if count_temp == 0:
                            var_value = var_value + f"is_{key_name}_joined == True"
                            count_temp = count_temp + 1
                        else:
                            var_value = var_value + " " + f"or is_{key_name}_joined == True"
                else:
                    if count_temp == 0:
                        var_value = var_value + f"is_{key_name}_joined == True"
                        count_temp = count_temp + 1
                    else:
                        var_value = var_value + " " + f"or is_{key_name}_joined == True"
            var_value = "(" + var_value + ")"
            var_value = '"' + var_value + '"'
            var_value = eval(var_value)
            new_column = expr(var_value)
            self.rf = self.rf.withColumn("is_join", new_column)
            temp_df = self.rf.filter("count!=1").select("is_join").sort("is_join").distinct()
            sys.stdout = f2
            TEST_CASE = "No records found which doesn’t belong to a disambiguated group"
            if temp_df.select(col("*")).count() != 0:
                for index, row_iterator in temp_df.toPandas().iterrows():
                    count_temp = (row_iterator[0], row_iterator[0])
                    if count_temp != (True, True):
                        x.add_row([self.class_name,TEST_CASE,"FAIL"])
                        mismatch_flag = True
                        self.rf.filter("count!=1 and is_join=False").select("disambiguated_p_id", "p_id_inp").sort("disambiguated_p_id").distinct().show(100, truncate=False)
                        result = self.rf.filter("count!=1 and is_join=False").select("disambiguated_p_id", "p_id_inp").sort("disambiguated_p_id").distinct()
                        df_test= result.toPandas() 
                        df_test.to_csv(csv_name, index=False, encoding='utf-8')  
                        with allure.step("Attach DataFrame to Allure"):
                            allure.attach(open(csv_name, 'rb').read(), csv_name, allure.attachment_type.CSV)
                        break
                    else:
                        x.add_row([self.class_name,TEST_CASE,"PASS"])
                        break
            else:
                x.add_row([self.class_name,TEST_CASE,"PASS"])
            sys.stdout = original_stdout
            print(str(x))
            f1.write(str(x))
            temp_file_path2 = f2.name
            temp_file_path1 = f1.name
            if(self.type_of_test) == 'sds_ei_intra_source_resolver':
                report_name = f"intra_report_{self.class_name}"
            else:
                report_name = f"inter_report_{self.class_name}"
        return temp_file_path1, temp_file_path2, report_name, mismatch_flag, x

    def test_case_status(self, mismatch_flag, error_message ):
        if mismatch_flag:
            with allure.step(error_message):
                assert False

    def write_to_report(self, x):
        data = [x.get_string(fields=x.field_names)]
        df = pd.read_csv(StringIO('\n'.join(data)))  
        # Save Pandas DataFrame to CSV
        csv_name = 'final_test_result.csv'
        df.to_csv(csv_name, index=False, encoding='utf-8')
        # Attach CSV to Allure
        with allure.step("Attach PrettyTable to Allure"):
            allure.attach(open(csv_name, 'rb').read(), 'final_test_result.csv', allure.attachment_type.CSV)