from prettytable import PrettyTable
from pyspark.sql.window import Window
from pyspark.sql.functions import lit, expr, col, lag, lead, explode_outer,concat_ws
from io import StringIO
import tempfile
import sys
import pyspark.sql.functions as F
import pandas as pd
import numpy as np
import re
from src.utilities.data_test.common import Common
from src.utilities.common.common_utilities import CommonUtilities
from pyspark.sql import Row
import os
import sys
from datetime import datetime
import fnmatch
from src.utilities.common.readProperties import ReadConfig 
from src.utilities.common.api_utils import APIUtils 
from core.api.api_client import APIClient
from core.common.logging_utils import setup_logger
logger = setup_logger(__name__)

collector = CommonUtilities()
sys.stdout = collector
readConfig = ReadConfig()
api_utils= APIUtils()
apiclient = APIClient()


class KnowledgeGraphValidation:

       
    def fragment_count_comp(self, spark, deployement_config,inter_config):
        with tempfile.NamedTemporaryFile(mode='a', delete=False) as f1, tempfile.NamedTemporaryFile(mode='a', delete=False) as f2:
            mismatch_flag = False
            original_stdout = sys.stdout            
            schema_frag =  inter_config['output']['fragmentLocation'].split('.')[0]
            inter_table = deployement_config['config_value']['spark_job_configs']['source_models']['intersource_disambiguated_models']        
            logger.info(f"Retrieved fragment schema: {schema_frag}")
            logger.info(f"Retrieved inter_table list: {inter_table}")
            TEST_CASE = "Validation of Count between Union of Loader/Intra and the Fragment output"
            updated_at = Common.UPDATED_AT
            # updated_at= '1721260799999'
            logger.info("Updated at received is:::")
            logger.info(updated_at)
            query = f"SHOW TABLES IN iceberg_catalog.{schema_frag}"
            logger.info(f"Executing SQL Query: {query}")
            available_tables = [ row.tableName for row in spark.sql(query).collect()]
            logger.info(f"Available tables in schema: {available_tables}")
            count =0
            for table in inter_table:
                logger.info(f"Processing table: {table}")
                result = re.search(r"(?<=sds_ei__).*", table)
                entity = result.group().strip() 
                logger.info(f"Extracted entity: {entity}")   
                fragment_table_name = f'sds_ei__fragment__{entity}'
                resolver_table_name = f'sds_ei__resolver__{entity}'
                logger.info(f"Checking for tables: {fragment_table_name}, {resolver_table_name}")                
                if fragment_table_name in available_tables and resolver_table_name in available_tables :
                    logger.info(f"Tables {fragment_table_name} and {resolver_table_name} found. Proceeding with count validation.")
                    df_frag = spark.sql(f"SELECT * FROM iceberg_catalog.{schema_frag}.{fragment_table_name} WHERE updated_at = {updated_at}")
                    df_resolver =  spark.sql(f"SELECT * FROM iceberg_catalog.{schema_frag}.{resolver_table_name} WHERE updated_at = {updated_at}")
                    frag_count = df_frag.count()
                    resolver_count = df_resolver.count()
                    logger.info(f"{entity} - Fragment Count: {frag_count}, Resolver Count: {resolver_count}")
                    if count==0 :                        
                        if (frag_count == resolver_count):
                            #print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - PASS")
                            data = [Row(CLASS=entity, TEST_CASE=TEST_CASE, STATUS="PASS")]
                            out_txt = spark.createDataFrame(data)
                            count=count +1
                            
                        else:
                            data = [Row(CLASS=entity, TEST_CASE=TEST_CASE, STATUS="FAIL")]
                            print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - FAIL")
                            out_txt = spark.createDataFrame(data)
                            count=count +1
                            mismatch_flag = True    
                    else :
                        if (frag_count == resolver_count):
                            #print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - PASS")
                            out_txt = CommonUtilities.add_row(spark,out_txt, {"CLASS": entity, "TEST_CASE": TEST_CASE, "STATUS": "PASS"})
                        else:
                            print(f"{entity} - Resolver Count: {resolver_count}, Fragment Count: {frag_count} - FAIL")
                            out_txt = CommonUtilities.add_row(spark,out_txt, {"CLASS": entity, "TEST_CASE": TEST_CASE, "STATUS": "FAIL"})
                            mismatch_flag = True  
            
            
            sys.stdout = original_stdout
            fail_messages = collector.get_content()
                          

        return  mismatch_flag, out_txt, fail_messages
    
    


    def publish_count(self, spark, deployement_config, publish_config,out_txt, fail_messages):
        try:
            original_stdout = sys.stdout 
            # schema_publish = 'kg_publish_edm_sit'
            schema_publish=publish_config['outputTableInfo']['outputTableName'].split('.')[0]
            publish_table = deployement_config['config_value']['spark_job_configs']['source_models']['publisher']
            logger.info(f"Retrieved entity publish table list: {publish_table}")
            query=f"SHOW TABLES IN iceberg_catalog.{schema_publish}"
            logger.info(f"Executing SQL Query: {query}")
            available_tables = [row.tableName for row in spark.sql(query).collect()]
            logger.info(f"Available tables in schema: {available_tables}")
            # updated_at = '1721260799999'  
            updated_at = Common.UPDATED_AT
            TEST_CASE = 'Publish Table Count'
            mismatch_flag = False  
            for table in publish_table:
                if table in available_tables:
                    logger.info(f"Tables {table} found. Proceeding with count validation.")
                    df_publish = spark.sql(f"SELECT * FROM iceberg_catalog.{schema_publish}.{table} WHERE updated_at = {updated_at}")
                    publish_count = df_publish.count()

                    if publish_count > 0:
                        logger.info("publish count is non zero")
                        out_txt = CommonUtilities.add_row(spark,out_txt, {"TABLE": table, "TEST_CASE": TEST_CASE, "STATUS": "PASS"})
                    else:
                        logger.info("publish count is zero")
                        out_txt = CommonUtilities.add_row(spark,out_txt, {"TABLE": table, "TEST_CASE": TEST_CASE, "STATUS": "FAIL"})
                        mismatch_flag = True  
                else:
                    logger.info(f"Table {table} not found")
                    out_txt = CommonUtilities.add_row(spark,out_txt, {"TABLE": table, "TEST_CASE": TEST_CASE, "STATUS": "FAIL"})
                    mismatch_flag = True 
            sys.stdout = original_stdout 
            fail_messages_new = collector.get_content()
            logger.info(f"fail_messages: {fail_messages}")
            logger.info(f"fail_messages_new: {fail_messages_new}")
            # fail_messages += "\n" + "".join(fail_messages_new)
            fail_messages.extend(fail_messages_new)
            logger.info(f"fail_messages::::{fail_messages}")
            return mismatch_flag, out_txt, fail_messages
        except Exception as e:
            logger.error("Exception occurred in publish_count", exc_info=True)
            return True, out_txt, [f"Error processing the DataFrame: {e}"]     
               
    
    def generate_report(self,out_txt,fail_messages):
        
        logger.info("Entered report generation function")
        current_directory = os.getcwd()
        folder_name = 'output'
        folder_path = os.path.join(current_directory, folder_name)
        logger.info(f"Current directory: {current_directory}")
        logger.info(f"Output folder path: {folder_path}")
        os.makedirs(folder_path, exist_ok=True)
        
        # Define file paths
        pass_file = os.path.join(folder_path, 'pass.txt')
        fail_file = os.path.join(folder_path, 'fail.txt')
        
        #PASS File write
        # with open(pass_file, "w") as f:
        #         out_txt.write.mode("overwrite").text(pass_file)

        # out_txt=out_txt.withColumn("merged_column", concat_ws(", ", out_txt.columns))
        # out_txt.select("merged_column").write.mode("overwrite").text(pass_file)

        #out_txt.write.mode("overwrite").option("header", True).csv(pass_file)
        
        #FAIl File write       
        with open(fail_file, "w") as f:
                f.write("\n".join(fail_messages) + "\n")
